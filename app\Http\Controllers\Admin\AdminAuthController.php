<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\HasApiTokens;
use App\Models\Admin;

class AdminAuthController extends Controller
{
    /**
     * Show admin login form.
     */
    public function showLogin()
    {
        // If already logged in as admin, redirect to admin dashboard
        if (Auth::guard('admin')->check()) {
            return redirect()->route('admin.dashboard');
        }

        // If regular user is logged in, logout them first
        if (Auth::guard('web')->check()) {
            Auth::guard('web')->logout();
            request()->session()->invalidate();
            request()->session()->regenerateToken();
            session()->flash('info', 'Please login with admin credentials to access the admin panel.');
        }

        return view('admin.auth.login');
    }

    /**
     * Handle admin login request.
     */
    public function login(Request $request)
    {
        \Log::info('Admin login attempt', ['email' => $request->email]);

        try {
            $request->validate([
                'email' => 'required|email',
                'password' => 'required|string',
            ]);
            \Log::info('Admin login validation passed');

            $key = 'admin-login.' . $request->ip();

            // Rate limiting - 5 attempts per minute
            if (RateLimiter::tooManyAttempts($key, 5)) {
                $seconds = RateLimiter::availableIn($key);
                \Log::warning('Admin login rate limited', ['ip' => $request->ip()]);
                throw ValidationException::withMessages([
                    'email' => "Too many login attempts. Please try again in {$seconds} seconds.",
                ]);
            }

            $credentials = $request->only('email', 'password');
            \Log::info('Admin login credentials extracted', ['email' => $credentials['email']]);

            // Find admin by email - make sure it's actually an admin
            $admin = Admin::where('email', $credentials['email'])
                         ->where('is_active', true)
                         ->first();

            // Also check if this email belongs to a regular user (not admin)
            $regularUser = \App\Models\User::where('email', $credentials['email'])
                                          ->where('is_admin', false)
                                          ->first();

            if ($regularUser && !$admin) {
                \Log::warning('Regular user tried to login to admin panel', ['email' => $credentials['email']]);
                RateLimiter::hit($key);
                throw ValidationException::withMessages([
                    'email' => 'This email belongs to a regular user account. Please use the regular login page.',
                ]);
            }

            if (!$admin) {
                \Log::warning('Admin not found or inactive', ['email' => $credentials['email']]);
                RateLimiter::hit($key, 60);
                throw ValidationException::withMessages([
                    'email' => 'These credentials do not match our admin records.',
                ]);
            }

            \Log::info('Admin found', ['admin_id' => $admin->id, 'admin_name' => $admin->name]);

            // Check password
            if (!Hash::check($credentials['password'], $admin->password)) {
                \Log::warning('Admin password check failed', ['admin_id' => $admin->id]);
                RateLimiter::hit($key, 60);
                throw ValidationException::withMessages([
                    'email' => 'These credentials do not match our records.',
                ]);
            }

            \Log::info('Admin password check passed');

            // Check if admin is suspended
            if ($admin->isSuspended()) {
                \Log::warning('Admin account suspended', ['admin_id' => $admin->id]);
                RateLimiter::hit($key, 60);
                throw ValidationException::withMessages([
                    'email' => 'Your admin account has been suspended.',
                ]);
            }

            // Clear rate limiter on successful login
            RateLimiter::clear($key);
            \Log::info('Admin login successful, logging in...');

            // Login admin with admin guard
            Auth::guard('admin')->login($admin, $request->boolean('remember'));
            \Log::info('Admin logged in successfully', ['admin_id' => $admin->id]);

            // Update last login info
            $admin->updateLastActivity();

            // Regenerate session
            $request->session()->regenerate();

            \Log::info('Admin login complete, redirecting to dashboard');
            return redirect()->intended(route('admin.dashboard'))->with('success', 'Welcome back, ' . $admin->name . '!');

        } catch (\Exception $e) {
            \Log::error('Admin login error', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            throw $e;
        }
    }

    /**
     * Handle admin logout.
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login')->with('success', 'You have been logged out successfully.');
    }
}
