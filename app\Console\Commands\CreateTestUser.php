<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create-test {--name=Test User} {--email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user for development';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->option('name');
        $email = $this->option('email');

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            $this->info("User already exists: {$existingUser->name} ({$existingUser->email})");
            $this->info("User ID: {$existingUser->id}");
            return 0;
        }

        // Create user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make('password'),
            'user_type' => 'user',
            'application' => 'ALL',
            'is_active' => true,
            'email_verified' => true,
            'user_info' => json_encode([
                'name' => $name,
                'application' => 'ALL',
                'subscription_plan' => 'باقة شاهد المميزة',
                'plan' => 'باقة شاهد المميزة',
            ]),
            'login_time' => now(),
            'last_login_at' => now(),
        ]);

        $this->info("✅ Successfully created test user!");
        $this->info("👤 Name: {$user->name}");
        $this->info("📧 Email: {$user->email}");
        $this->info("🔑 Password: password");
        $this->info("🆔 User ID: {$user->id}");

        $this->info("\n🚀 Next steps:");
        $this->info("1. Run: php artisan subscription:setup-test --user-id={$user->id}");
        $this->info("2. Login with email: {$user->email} and password: password");

        return 0;
    }
}
