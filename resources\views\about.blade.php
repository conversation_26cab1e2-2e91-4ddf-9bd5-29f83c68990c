<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About StreamVibe - Your Entertainment Platform</title>
    <meta name="description" content="Learn more about StreamVibe, the ultimate streaming platform for movies, TV shows, and live entertainment.">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('css/landing-page.css') }}">
    
    <style>
        .about-hero {
            padding: 8rem 0 4rem;
            background: linear-gradient(135deg, #141414 0%, #000000 100%);
            text-align: center;
        }

        .about-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .about-hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 4rem 0;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            color: var(--text-gray);
            margin-top: 0.5rem;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 4rem 0;
        }

        .team-card {
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-10px);
        }

        .team-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--gradient-primary);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .timeline {
            position: relative;
            margin: 4rem 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--primary-color);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 2rem 0;
            display: flex;
            align-items: center;
        }

        .timeline-item:nth-child(odd) {
            flex-direction: row-reverse;
        }

        .timeline-content {
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 16px;
            width: 45%;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .timeline-year {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .timeline::before {
                left: 20px;
            }

            .timeline-item {
                flex-direction: column !important;
                align-items: flex-start;
                padding-left: 3rem;
            }

            .timeline-content {
                width: 100%;
            }

            .timeline-year {
                left: 20px;
                transform: translateX(-50%);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <div class="nav-container">
            <div class="logo">StreamVibe</div>
            
            <nav class="nav-menu">
                <a href="{{ route('landing') }}">Home</a>
                <a href="#about">About</a>
                <a href="#team">Team</a>
                <a href="#contact">Contact</a>
            </nav>
            
            <div class="auth-buttons">
                <a href="{{ route('login') }}" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </a>
                <a href="{{ route('register') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    Get Started
                </a>
            </div>
            
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- About Hero -->
    <section class="about-hero" id="about">
        <div class="about-content">
            <h1>About StreamVibe</h1>
            <p class="lead">
                We're revolutionizing the way you experience entertainment. StreamVibe brings together 
                the best movies, TV shows, and live content in one seamless platform.
            </p>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="features">
        <div class="features-container">
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">10M+</span>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">50K+</span>
                    <div class="stat-label">Movies & Shows</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">200+</span>
                    <div class="stat-label">Live Channels</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">99.9%</span>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mission Section -->
    <section class="cta">
        <div class="cta-container">
            <h2>Our Mission</h2>
            <p>
                To make premium entertainment accessible to everyone, everywhere. We believe that great 
                content should be available at your fingertips, whether you're at home or on the go.
            </p>
        </div>
    </section>

    <!-- Timeline Section -->
    <section class="features">
        <div class="features-container">
            <div class="section-header">
                <h2>Our Journey</h2>
                <p>From a simple idea to a global streaming platform</p>
            </div>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">2020</div>
                    <div class="timeline-content">
                        <h3>The Beginning</h3>
                        <p>StreamVibe was founded with a vision to democratize entertainment access worldwide.</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2021</div>
                    <div class="timeline-content">
                        <h3>Platform Launch</h3>
                        <p>Launched our beta platform with 1,000 movies and TV shows, gaining our first 100K users.</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2022</div>
                    <div class="timeline-content">
                        <h3>Global Expansion</h3>
                        <p>Expanded to 50 countries and added live TV streaming capabilities.</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2023</div>
                    <div class="timeline-content">
                        <h3>Innovation</h3>
                        <p>Introduced 4K streaming, offline downloads, and AI-powered recommendations.</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2024</div>
                    <div class="timeline-content">
                        <h3>The Future</h3>
                        <p>Reaching 10M+ users and continuing to innovate with new features and content.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="features" id="team">
        <div class="features-container">
            <div class="section-header">
                <h2>Meet Our Team</h2>
                <p>The passionate people behind StreamVibe</p>
            </div>
            
            <div class="team-grid">
                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>Alex Johnson</h3>
                    <p class="team-role">CEO & Founder</p>
                    <p>Visionary leader with 15+ years in tech and entertainment industry.</p>
                </div>
                
                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Sarah Chen</h3>
                    <p class="team-role">CTO</p>
                    <p>Tech expert specializing in scalable streaming infrastructure and AI.</p>
                </div>
                
                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Mike Rodriguez</h3>
                    <p class="team-role">Head of Design</p>
                    <p>Creative director focused on user experience and interface design.</p>
                </div>
                
                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Emily Davis</h3>
                    <p class="team-role">Head of Content</p>
                    <p>Content strategist with deep connections in the entertainment industry.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="cta-container">
            <h2>Ready to Join Us?</h2>
            <p>Be part of the streaming revolution and discover unlimited entertainment</p>
            <div class="hero-buttons">
                <a href="{{ route('register') }}" class="btn btn-primary btn-large">
                    <i class="fas fa-rocket"></i>
                    Start Your Journey
                </a>
                <a href="{{ route('landing') }}" class="btn btn-outline btn-large">
                    <i class="fas fa-arrow-left"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="social-links">
                <a href="#" aria-label="Facebook">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" aria-label="Twitter">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" aria-label="Instagram">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" aria-label="YouTube">
                    <i class="fab fa-youtube"></i>
                </a>
            </div>
            <p>&copy; 2024 StreamVibe. All rights reserved.</p>
            <p>Your ultimate destination for movies, TV shows, and live entertainment.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ asset('js/landing-page.js') }}"></script>
</body>
</html>
