<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Player - شاهد</title>
    <style>
        body {
            margin: 0;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
        }
        .loading {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }
        .error {
            color: #ff4444;
            max-width: 500px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div id="status">
        <div class="loading">
            <h2>🎬 تحضير المشغل...</h2>
            <p>جاري تحديد نوع الجهاز واستخراج البيانات المناسبة</p>
        </div>
    </div>

    <script>
        // Device detection
        function detectDevice() {
            const ua = navigator.userAgent;
            
            if (/iPhone|iPad|iPod/i.test(ua)) {
                return { type: 'ios', name: 'iOS', drm: 'FairPlay' };
            } else if (/Android/i.test(ua)) {
                return { type: 'android', name: 'Android', drm: 'Widevine' };
            } else if (/Windows/i.test(ua)) {
                return { type: 'windows', name: 'Windows', drm: 'Widevine' };
            } else if (/Mac/i.test(ua)) {
                return { type: 'macos', name: 'macOS', drm: 'FairPlay' };
            } else {
                return { type: 'unknown', name: 'Unknown', drm: 'Widevine' };
            }
        }

        // Get content ID from URL
        function getContentId() {
            const pathParts = window.location.pathname.split('/');
            return pathParts[pathParts.length - 1];
        }

        // Update status
        function updateStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `
                <div class="${isError ? 'error' : 'loading'}">
                    <h2>${isError ? '❌' : '🎬'} ${message}</h2>
                    ${isError ? '' : '<p>يرجى الانتظار...</p>'}
                </div>
            `;
        }

        // Main initialization
        async function initializeUniversalPlayer() {
            try {
                const device = detectDevice();
                const contentId = getContentId();
                
                console.log('🔍 Device detected:', device);
                console.log('🎬 Content ID:', contentId);
                
                updateStatus(`تم اكتشاف جهاز ${device.name} - استخراج بيانات ${device.drm}`);

                // Call universal player API
                const response = await fetch(`/universal/player-data/${contentId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    console.log('✅ Player data extracted:', result);
                    
                    updateStatus(`تم استخراج البيانات - توجيه للمشغل المناسب`);
                    
                    // Redirect to appropriate player
                    setTimeout(() => {
                        window.location.href = result.player_url;
                    }, 1000);
                    
                } else {
                    throw new Error(result.error || 'Failed to extract player data');
                }

            } catch (error) {
                console.error('❌ Universal player error:', error);
                updateStatus(`خطأ في تحضير المشغل: ${error.message}`, true);
                
                // Show retry button after 3 seconds
                setTimeout(() => {
                    const statusDiv = document.getElementById('status');
                    statusDiv.innerHTML += `
                        <div style="margin-top: 20px;">
                            <button onclick="window.location.reload()" style="
                                background: #007bff;
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 5px;
                                cursor: pointer;
                            ">🔄 إعادة المحاولة</button>
                        </div>
                    `;
                }, 3000);
            }
        }

        // Start when page loads
        document.addEventListener('DOMContentLoaded', initializeUniversalPlayer);
    </script>
</body>
</html>