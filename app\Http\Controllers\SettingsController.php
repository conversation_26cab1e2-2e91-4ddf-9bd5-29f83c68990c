<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ShahidAPI;
use App\Models\Setting;
use App\Helpers\SettingsHelper;

class SettingsController extends Controller
{
    private $shahidAPI;

    public function __construct()
    {
        $this->middleware('auth');
        $this->shahidAPI = new ShahidAPI();
    }

    /**
     * Show settings page
     */
    public function index()
    {
        return view('settings.index');
    }

    /**
     * Update application settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required'
        ]);

        try {
            Setting::updateOrCreate(
                ['key' => $request->key],
                ['value' => $request->value]
            );

            return response()->json([
                'success' => true,
                'message' => 'Settings saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving settings: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get application settings
     */
    public function get($key = null)
    {
        try {
            if ($key) {
                $setting = Setting::where('key', $key)->first();
                return response()->json([
                    'success' => true,
                    'data' => $setting ? $setting->value : null
                ]);
            } else {
                $settings = Setting::all()->pluck('value', 'key');
                return response()->json([
                    'success' => true,
                    'data' => $settings
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching settings: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test decryption API connection
     */
    public function testDecryptionAPI(Request $request)
    {
        $request->validate([
            'url' => 'required|url'
        ]);

        try {
            $url = $request->url;

            // Save the URL to settings first
            Setting::setValue('decryption_api_url', $url, 'string', 'Decryption API URL for DRM-protected content');

            // Try to ping the health endpoint
            $healthUrl = rtrim($url, '/') . '/health';

            $client = new \GuzzleHttp\Client([
                'timeout' => 10,
                'verify' => false
            ]);

            // Try a simple GET request to check if the API is reachable
            $response = $client->get($healthUrl, [
                'headers' => [
                    'Accept' => 'application/json',
                    'User-Agent' => 'Shahid-Laravel-App/1.0'
                ]
            ]);

            $statusCode = $response->getStatusCode();

            if ($statusCode >= 200 && $statusCode < 300) {
                return response()->json([
                    'success' => true,
                    'message' => 'Decryption API is available and working correctly',
                    'status_code' => $statusCode
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Decryption API is not available (HTTP ' . $statusCode . ')',
                    'status_code' => $statusCode
                ]);
            }

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to connect to decryption API - check URL'
            ]);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return response()->json([
                'success' => false,
                'message' => 'API request error: ' . $e->getMessage()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error testing API: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get decryption API status
     */
    public function getDecryptionApiStatus()
    {
        try {
            $status = SettingsHelper::getDecryptionApiStatus();

            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error checking API status: ' . $e->getMessage()
            ]);
        }
    }
}
