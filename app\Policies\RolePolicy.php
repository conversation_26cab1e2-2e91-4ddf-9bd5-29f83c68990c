<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Admin;
use Spatie\Permission\Models\Role;
use Illuminate\Auth\Access\HandlesAuthorization;

class RolePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any roles.
     */
    public function viewAny($user): bool
    {
        if (!$user) {
            return false;
        }

        // Handle both User and Admin models
        if (!($user instanceof User) && !($user instanceof Admin)) {
            return false;
        }

        if ($user->isSuperAdmin()) {
            return true;
        }

        if (method_exists($user, 'safeHasPermissionTo')) {
            return $user->safeHasPermissionTo('view roles') ||
                   $user->hasRole(['admin', 'role-manager']);
        }

        return $user->hasPermission('view roles') ||
               $user->hasRole(['admin', 'role-manager']);
    }

    /**
     * Determine whether the user can view the role.
     */
    public function view($user, Role $role): bool
    {
        if (!$user) {
            return false;
        }

        // Handle both User and Admin models
        if (!($user instanceof User) && !($user instanceof Admin)) {
            return false;
        }

        // Super admin can view all roles
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Cannot view super-admin role unless you are super admin
        if (in_array($role->name, ['super-admin', 'Super Admin'])) {
            return false;
        }

        if (method_exists($user, 'safeHasPermissionTo')) {
            return $user->safeHasPermissionTo('view roles') ||
                   $user->hasRole(['admin', 'role-manager']);
        }

        return $user->hasPermission('view roles') ||
               $user->hasRole(['admin', 'role-manager']);
    }

    /**
     * Determine whether the user can create roles.
     */
    public function create($user): bool
    {
        if (!$user) {
            return false;
        }

        // Handle both User and Admin models
        if (!($user instanceof User) && !($user instanceof Admin)) {
            return false;
        }

        if ($user->isSuperAdmin()) {
            return true;
        }

        if (method_exists($user, 'safeHasPermissionTo')) {
            return $user->safeHasPermissionTo('create roles');
        }

        return $user->hasPermission('create roles');
    }

    /**
     * Determine whether the user can update the role.
     */
    public function update($user, Role $role): bool
    {
        if (!$user) {
            return false;
        }

        // Handle both User and Admin models
        if (!($user instanceof User) && !($user instanceof Admin)) {
            return false;
        }

        // Super admin can update all roles
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Cannot update super-admin role unless you are super admin
        if (in_array($role->name, ['super-admin', 'Super Admin'])) {
            return false;
        }

        // Cannot update admin role unless you are super admin or have specific permission
        if (in_array($role->name, ['admin', 'Admin'])) {
            if (method_exists($user, 'safeHasPermissionTo')) {
                return $user->safeHasPermissionTo('update admin roles');
            }
            return $user->hasPermission('update admin roles');
        }

        if (method_exists($user, 'safeHasPermissionTo')) {
            return $user->safeHasPermissionTo('update roles');
        }

        return $user->hasPermission('update roles');
    }

    /**
     * Determine whether the user can delete the role.
     */
    public function delete(?User $user, Role $role): bool
    {
        if (!$user) {
            return false;
        }

        // Cannot delete system roles
        $systemRoles = ['Super Admin', 'Admin', 'User'];
        if (in_array($role->name, $systemRoles)) {
            return false;
        }

        // Super admin can delete any non-system role
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Check if user has delete permission
        if (!$user->safeHasPermissionTo('delete roles')) {
            return false;
        }

        // Regular users with permission can only delete roles that are not in use
        return $role->users()->count() === 0;
    }

    /**
     * Determine whether the user can assign the role.
     */
    public function assign(?User $user, Role $role): bool
    {
        if (!$user) {
            return false;
        }

        // Cannot assign super-admin role unless you are super admin
        if ($role->name === 'super-admin' && !$user->isSuperAdmin()) {
            return false;
        }

        // Cannot assign admin role unless you are super admin or have specific permission
        if ($role->name === 'admin' && !$user->isSuperAdmin()) {
            return $user->safeHasPermissionTo('assign admin roles');
        }

        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('assign roles') ||
               $user->hasRole(['admin', 'role-manager']);
    }

    /**
     * Determine whether the user can revoke the role.
     */
    public function revoke(?User $user, Role $role): bool
    {
        if (!$user) {
            return false;
        }

        // Cannot revoke super-admin role unless you are super admin
        if ($role->name === 'super-admin' && !$user->isSuperAdmin()) {
            return false;
        }

        // Cannot revoke admin role unless you are super admin or have specific permission
        if ($role->name === 'admin' && !$user->isSuperAdmin()) {
            return $user->safeHasPermissionTo('revoke admin roles');
        }

        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('revoke roles') ||
               $user->hasRole(['admin', 'role-manager']);
    }

    /**
     * Determine whether the user can manage permissions for the role.
     */
    public function managePermissions($user, Role $role): bool
    {
        if (!$user) {
            return false;
        }

        // Handle both User and Admin models
        if (!($user instanceof User) && !($user instanceof Admin)) {
            return false;
        }

        // Super admin can manage all permissions
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Cannot manage super-admin role permissions unless you are super admin
        if (in_array($role->name, ['super-admin', 'Super Admin'])) {
            return false;
        }

        // Cannot manage admin role permissions unless you are super admin
        if (in_array($role->name, ['admin', 'Admin'])) {
            if (method_exists($user, 'safeHasPermissionTo')) {
                return $user->safeHasPermissionTo('manage admin permissions');
            }
            return $user->hasPermission('manage admin permissions');
        }

        // Check if user has permission to manage role permissions
        if (method_exists($user, 'safeHasPermissionTo')) {
            return $user->safeHasPermissionTo('manage role permissions');
        }

        return $user->hasPermission('manage role permissions');
    }
}
