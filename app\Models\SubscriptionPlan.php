<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'currency',
        'duration_days',
        'features',
        'allowed_applications',
        'max_devices',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'duration_days' => 'integer',
        'features' => 'array',
        'allowed_applications' => 'array',
        'max_devices' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get active user subscriptions for this plan.
     */
    public function activeUserSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class)->where('status', 'active');
    }

    /**
     * Scope to get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->price, 2);
    }

    /**
     * Get duration in human readable format.
     */
    public function getDurationTextAttribute(): string
    {
        if ($this->duration_days >= 365) {
            $years = floor($this->duration_days / 365);
            return $years . ' ' . ($years == 1 ? 'Year' : 'Years');
        } elseif ($this->duration_days >= 30) {
            $months = floor($this->duration_days / 30);
            return $months . ' ' . ($months == 1 ? 'Month' : 'Months');
        } else {
            return $this->duration_days . ' ' . ($this->duration_days == 1 ? 'Day' : 'Days');
        }
    }

    /**
     * Check if plan allows specific application.
     */
    public function allowsApplication(string $application): bool
    {
        if (empty($this->allowed_applications)) {
            return true; // If no restrictions, allow all
        }

        return in_array('ALL', $this->allowed_applications) || 
               in_array($application, $this->allowed_applications);
    }

    /**
     * Get subscribers count.
     */
    public function getSubscribersCountAttribute(): int
    {
        return $this->activeUserSubscriptions()->count();
    }
}
