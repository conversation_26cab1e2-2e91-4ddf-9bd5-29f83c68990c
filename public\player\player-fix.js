/**
 * JW Player Fix for Hosting Issues
 * Addresses common problems with JW Player on shared hosting
 */

// Fix for chunk loading issues
(function() {
    'use strict';
    
    // Store original methods
    const originalSetup = jwplayer.prototype.setup;
    const originalLoad = jwplayer.prototype.load;
    
    // Enhanced error handling
    function enhancedErrorHandler(error) {
        console.error('🔧 Player Error Handler:', error);
        
        // Handle specific error codes
        switch(error.code) {
            case 101102:
                console.log('🔄 Attempting to recover from setup error...');
                // Try to reinitialize with simpler config
                setTimeout(() => {
                    this.setup({
                        file: this.getConfig().file || this.getConfig().playlist[0].file,
                        width: '100%',
                        height: '100%',
                        autostart: false,
                        mute: false,
                        controls: true,
                        displaytitle: false,
                        displaydescription: false
                    });
                }, 1000);
                break;
                
            case 100001:
                console.log('🔄 Network error, retrying...');
                setTimeout(() => {
                    this.load(this.getConfig().playlist || this.getConfig().file);
                }, 2000);
                break;
                
            default:
                console.log('⚠️ Unhandled error code:', error.code);
        }
    }
    
    // Enhanced setup method
    jwplayer.prototype.setup = function(config) {
        console.log('🔧 Enhanced setup called with config:', config);
        
        // Add error handlers to config
        const enhancedConfig = {
            ...config,
            // Force specific settings for stability
            preload: 'metadata',
            controls: true,
            displaytitle: false,
            displaydescription: false,
            // Add error handling
            events: {
                onSetupError: enhancedErrorHandler.bind(this),
                onError: enhancedErrorHandler.bind(this),
                onReady: function() {
                    console.log('✅ Player ready with enhanced config');
                },
                ...(config.events || {})
            }
        };
        
        try {
            return originalSetup.call(this, enhancedConfig);
        } catch (error) {
            console.error('🔧 Setup failed, trying fallback:', error);
            
            // Fallback with minimal config
            const fallbackConfig = {
                file: config.file || (config.playlist && config.playlist[0] && config.playlist[0].file),
                width: config.width || '100%',
                height: config.height || '100%',
                autostart: false,
                controls: true
            };
            
            return originalSetup.call(this, fallbackConfig);
        }
    };
    
    // Enhanced load method
    jwplayer.prototype.load = function(playlist) {
        console.log('🔧 Enhanced load called with:', playlist);
        
        try {
            return originalLoad.call(this, playlist);
        } catch (error) {
            console.error('🔧 Load failed:', error);
            
            // Try to reload with delay
            setTimeout(() => {
                try {
                    originalLoad.call(this, playlist);
                } catch (retryError) {
                    console.error('🔧 Retry load also failed:', retryError);
                }
            }, 1000);
        }
    };
    
    // Global error handler for uncaught player errors
    window.addEventListener('error', function(event) {
        if (event.filename && event.filename.includes('jwplayer')) {
            console.error('🔧 Global JW Player error caught:', event.error);
            event.preventDefault();
        }
    });
    
    // Promise rejection handler for async errors
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.toString().includes('jwplayer')) {
            console.error('🔧 Unhandled JW Player promise rejection:', event.reason);
            event.preventDefault();
        }
    });
    
    console.log('🔧 JW Player fixes applied');
    
})();

// Additional utility functions
window.PlayerUtils = {
    // Force reload player
    forceReload: function(playerId) {
        const player = jwplayer(playerId);
        if (player) {
            const config = player.getConfig();
            player.remove();
            setTimeout(() => {
                jwplayer(playerId).setup(config);
            }, 500);
        }
    },
    
    // Check player health
    checkHealth: function(playerId) {
        const player = jwplayer(playerId);
        if (!player) {
            console.error('❌ Player not found:', playerId);
            return false;
        }
        
        const state = player.getState();
        console.log('🔍 Player health check:', {
            id: playerId,
            state: state,
            position: player.getPosition(),
            duration: player.getDuration(),
            buffer: player.getBuffer()
        });
        
        return state !== 'error';
    },
    
    // Simple setup with error handling
    simpleSetup: function(playerId, videoUrl) {
        try {
            jwplayer(playerId).setup({
                file: videoUrl,
                width: '100%',
                height: '100%',
                autostart: false,
                controls: true,
                preload: 'metadata'
            }).on('ready', function() {
                console.log('✅ Simple setup successful for:', playerId);
            }).on('error', function(error) {
                console.error('❌ Simple setup error:', error);
            });
        } catch (error) {
            console.error('❌ Simple setup failed:', error);
        }
    }
};

console.log('🔧 Player utilities loaded');
