<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            // Add missing columns for admin management
            $table->string('department')->nullable()->after('username');
            $table->string('position')->nullable()->after('department');
            $table->string('phone')->nullable()->after('position');
            $table->text('bio')->nullable()->after('phone');
            $table->string('avatar_url')->nullable()->after('bio');
            $table->timestamp('last_activity_at')->nullable()->after('last_login_ip');
            $table->timestamp('password_changed_at')->nullable()->after('last_activity_at');
            $table->boolean('force_password_change')->default(false)->after('password_changed_at');
            $table->boolean('can_manage_admins')->default(false)->after('is_super_admin');
            $table->foreignId('created_by')->nullable()->constrained('admins')->onDelete('set null')->after('can_manage_admins');
            $table->foreignId('suspended_by')->nullable()->constrained('admins')->onDelete('set null')->after('suspended_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['suspended_by']);
            $table->dropColumn([
                'department',
                'position',
                'phone',
                'bio',
                'avatar_url',
                'last_activity_at',
                'password_changed_at',
                'force_password_change',
                'can_manage_admins',
                'created_by',
                'suspended_by'
            ]);
        });
    }
};
