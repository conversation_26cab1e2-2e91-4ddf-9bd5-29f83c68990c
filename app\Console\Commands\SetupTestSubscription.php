<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Carbon\Carbon;

class SetupTestSubscription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:setup-test {--user-id= : User ID to setup subscription for} {--days=15 : Days until expiration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup test subscription for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        $days = (int) $this->option('days');

        // Get user
        if ($userId) {
            $user = User::find($userId);
        } else {
            $user = User::first();
        }

        if (!$user) {
            $this->error('User not found!');
            return 1;
        }

        // Create subscription plans if they don't exist
        $this->info('Creating subscription plans...');
        $plans = [
            [
                'name' => 'باقة شاهد الأساسية',
                'description' => 'باقة أساسية للوصول إلى المحتوى',
                'price' => 29.99,
                'currency' => 'USD',
                'duration_days' => 30,
                'features' => ['HD Quality', 'Mobile Access', '1 Device'],
                'allowed_applications' => ['SHAHID'],
                'max_devices' => 1,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'باقة شاهد المميزة',
                'description' => 'باقة مميزة مع المزيد من المميزات',
                'price' => 49.99,
                'currency' => 'USD',
                'duration_days' => 30,
                'features' => ['4K Quality', 'All Devices', '3 Devices', 'Download'],
                'allowed_applications' => ['SHAHID', 'ALL'],
                'max_devices' => 3,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'باقة شاهد الذهبية',
                'description' => 'باقة ذهبية مع جميع المميزات',
                'price' => 99.99,
                'currency' => 'USD',
                'duration_days' => 90,
                'features' => ['4K Quality', 'All Devices', 'Unlimited Devices', 'Download', 'Premium Content'],
                'allowed_applications' => ['ALL'],
                'max_devices' => 10,
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($plans as $planData) {
            SubscriptionPlan::firstOrCreate(
                ['name' => $planData['name']],
                $planData
            );
        }

        // Delete existing subscriptions for this user
        UserSubscription::where('user_id', $user->id)->delete();
        $this->info('Deleted existing subscriptions for user.');

        // Get the premium plan
        $plan = SubscriptionPlan::where('name', 'باقة شاهد المميزة')->first();

        if (!$plan) {
            $this->error('Could not create subscription plan!');
            return 1;
        }

        // Create subscription
        $subscription = UserSubscription::create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'starts_at' => now()->subDays(15),
            'expires_at' => now()->addDays($days),
            'status' => 'active',
            'allowed_applications' => $plan->allowed_applications,
            'max_devices' => $plan->max_devices,
            'amount_paid' => $plan->price,
            'payment_method' => 'credit_card',
            'payment_reference' => 'TEST_' . uniqid(),
            'notes' => 'Test subscription created by command',
            'activated_at' => now()->subDays(15),
        ]);

        // Update user info
        $userInfo = json_decode($user->user_info ?? '{}', true);
        $userInfo['subscription_plan'] = $plan->name;
        $userInfo['plan'] = $plan->name;

        $user->update([
            'user_info' => json_encode($userInfo),
            'subscription_expiry' => now()->addDays($days),
            'remaining_days' => $days,
            'is_active' => true,
        ]);

        $this->info("✅ Successfully created test subscription!");
        $this->info("👤 User: {$user->name} (ID: {$user->id})");
        $this->info("📦 Plan: {$plan->name}");
        $this->info("📅 Expires: " . now()->addDays($days)->format('Y-m-d H:i:s'));
        $this->info("⏰ Days remaining: {$days}");

        if ($days <= 7) {
            $this->warn("⚠️  This subscription will show a warning (expires in {$days} days)");
        } elseif ($days <= 3) {
            $this->error("🚨 This subscription will show a danger alert (expires in {$days} days)");
        }

        return 0;
    }
}
