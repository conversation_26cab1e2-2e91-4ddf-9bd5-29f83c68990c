<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>اختبار الآيفون - المحتوى المشفر</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .device-info {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .player-container {
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        #player {
            width: 100%;
            height: 300px;
            background: #000;
        }
        
        .controls {
            padding: 15px;
        }
        
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .log {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            #player { height: 250px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍎 اختبار الآيفون - المحتوى المشفر</h1>
        
        <div class="device-info" id="device-info">
            <h3>📱 معلومات الجهاز</h3>
            <div id="device-details"></div>
        </div>
        
        <div class="player-container">
            <div id="player"></div>
            <div class="controls">
                <button class="btn" onclick="testClearKey()">اختبار Clear Key</button>
                <button class="btn" onclick="testFairPlay()" id="fairplay-btn">اختبار FairPlay</button>
                <button class="btn" onclick="testDASH()" id="dash-btn">اختبار DASH</button>
                <button class="btn" onclick="testDASHClearKey()">اختبار DASH + Clear Key</button>
                <button class="btn" onclick="testHLS()">اختبار HLS عادي</button>
                <button class="btn" onclick="clearPlayer()">مسح المشغل</button>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <!-- Load scripts -->
    <script src="/player/js/jwplayer.js"></script>
    <script src="/player/ios-drm-support.js"></script>
    
    <script>
        // Logging system
        const logDiv = document.getElementById('log');
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console[type === 'error' ? 'error' : type === 'warn' ? 'warn' : 'log'](message);
        }
        
        // Device detection
        function detectDevice() {
            const details = document.getElementById('device-details');
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            const supportsFairPlay = isIOS && window.WebKitMediaKeys;
            
            details.innerHTML = `
                <div>🔍 User Agent: ${navigator.userAgent}</div>
                <div>📱 iOS Device: ${isIOS ? '✅ نعم' : '❌ لا'}</div>
                <div>🌐 Safari Browser: ${isSafari ? '✅ نعم' : '❌ لا'}</div>
                <div>🔐 FairPlay Support: ${supportsFairPlay ? '✅ مدعوم' : '❌ غير مدعوم'}</div>
                <div>📺 PiP Support: ${('pictureInPictureEnabled' in document) ? '✅ مدعوم' : '❌ غير مدعوم'}</div>
                <div>🌐 Connection: ${navigator.connection ? navigator.connection.effectiveType : 'غير معروف'}</div>
            `;
            
            // Disable FairPlay button if not supported
            if (!supportsFairPlay) {
                document.getElementById('fairplay-btn').disabled = true;
            }
            
            addLog('info', `Device detected: iOS=${isIOS}, Safari=${isSafari}, FairPlay=${supportsFairPlay}`);
        }
        
        // Clear player
        function clearPlayer() {
            try {
                jwplayer('player').remove();
                addLog('info', 'Player cleared');
            } catch (error) {
                addLog('error', 'Failed to clear player: ' + error.message);
            }
        }
        
        // Test Clear Key DRM
        function testClearKey() {
            addLog('info', 'Testing Clear Key DRM...');
            
            clearPlayer();
            
            const config = {
                file: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
                drm: {
                    clearkey: {
                        keyId: '11111111111111111111111111111111',
                        key: '22222222222222222222222222222222'
                    }
                },
                width: '100%',
                height: '100%',
                autostart: false,
                controls: true,
                playsinline: true
            };
            
            // Apply iOS optimizations
            const iOSConfig = window.iOSDRMSupport.setupiOSPlayer(config);
            
            try {
                jwplayer('player').setup(iOSConfig)
                .on('ready', () => addLog('success', 'Clear Key player ready'))
                .on('play', () => addLog('success', 'Clear Key playback started'))
                .on('error', (error) => addLog('error', 'Clear Key error: ' + error.message))
                .on('setupError', (error) => addLog('error', 'Clear Key setup error: ' + error.message));
                
            } catch (error) {
                addLog('error', 'Clear Key setup failed: ' + error.message);
            }
        }
        
        // Test FairPlay DRM
        function testFairPlay() {
            addLog('info', 'Testing FairPlay DRM...');
            
            if (!window.iOSDRMSupport.supportsFairPlay()) {
                addLog('error', 'FairPlay not supported on this device');
                return;
            }
            
            clearPlayer();
            
            const config = {
                file: 'https://fps.ezdrm.com/demo/hls/BigBuckBunny_320x180.m3u8',
                drm: {
                    fairplay: {
                        certificateUrl: 'https://fps.ezdrm.com/demo/video/ezdrm.cer',
                        licenseUrl: 'https://fps.ezdrm.com/api/licenses/09cc0377-6dd4-40cb-b09d-b582236e70fe'
                    }
                },
                width: '100%',
                height: '100%',
                autostart: false,
                controls: true,
                playsinline: true
            };
            
            // Apply iOS optimizations
            const iOSConfig = window.iOSDRMSupport.setupiOSPlayer(config);
            
            try {
                jwplayer('player').setup(iOSConfig)
                .on('ready', () => addLog('success', 'FairPlay player ready'))
                .on('play', () => addLog('success', 'FairPlay playback started'))
                .on('error', (error) => addLog('error', 'FairPlay error: ' + error.message))
                .on('setupError', (error) => addLog('error', 'FairPlay setup error: ' + error.message));
                
            } catch (error) {
                addLog('error', 'FairPlay setup failed: ' + error.message);
            }
        }
        
        // Test regular HLS
        function testHLS() {
            addLog('info', 'Testing regular HLS...');
            
            clearPlayer();
            
            const config = {
                file: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
                width: '100%',
                height: '100%',
                autostart: false,
                controls: true,
                playsinline: true
            };
            
            // Apply iOS optimizations
            const iOSConfig = window.iOSDRMSupport.setupiOSPlayer(config);
            
            try {
                jwplayer('player').setup(iOSConfig)
                .on('ready', () => addLog('success', 'HLS player ready'))
                .on('play', () => addLog('success', 'HLS playback started'))
                .on('error', (error) => addLog('error', 'HLS error: ' + error.message))
                .on('setupError', (error) => addLog('error', 'HLS setup error: ' + error.message));
                
            } catch (error) {
                addLog('error', 'HLS setup failed: ' + error.message);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', 'iOS test page loaded');
            detectDevice();
        });
    </script>
</body>
</html>
