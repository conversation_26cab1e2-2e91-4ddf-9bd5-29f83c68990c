@extends('admin.layouts.app')

@section('title', 'Session Expired - 419')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <div class="error-icon mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #9b59b6, #8e44ad); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-clock text-white" style="font-size: 60px;"></i>
                        </div>
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-purple mb-3" style="color: #9b59b6;">419</h1>
                    
                    <!-- Error Title -->
                    <h2 class="h3 text-dark mb-3">Session Expired</h2>
                    
                    <!-- Error Message -->
                    <p class="text-muted mb-4 lead">
                        Your session has expired for security reasons.
                        <br>Please refresh the page to continue.
                    </p>

                    <!-- Session Info -->
                    <div class="alert alert-light border-0 mb-4" style="background-color: #f8f9fa;">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-shield-alt text-purple fa-2x" style="color: #9b59b6;"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">Why did this happen?</h6>
                                <ul class="mb-0 text-muted small">
                                    <li>Your session expired due to inactivity</li>
                                    <li>This is a security measure to protect your account</li>
                                    <li>CSRF token validation failed</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Security Notice -->
                    <div class="alert alert-info border-0 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-info-circle text-info fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">Security Notice:</h6>
                                <p class="mb-0 text-muted small">
                                    For your security, we automatically log out inactive sessions. 
                                    This helps protect your account from unauthorized access.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <button onclick="location.reload()" class="btn btn-primary px-4">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Page
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-primary px-4">
                            <i class="fas fa-home me-2"></i>Go to Dashboard
                        </a>
                        @if(!auth()->check())
                        <a href="{{ route('admin.login') }}" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-sign-in-alt me-2"></i>Login Again
                        </a>
                        @endif
                    </div>

                    <!-- Auto Refresh Notice -->
                    <div class="mt-4 pt-4 border-top">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <small class="text-muted">
                                Auto-refreshing in <span id="countdown">10</span> seconds...
                            </small>
                        </div>
                        <div class="mt-2">
                            <button onclick="cancelAutoRefresh()" class="btn btn-link btn-sm text-muted">
                                Cancel auto-refresh
                            </button>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}



.btn {
    border-radius: 8px;
    font-weight: 500;
}

.text-purple {
    color: #9b59b6 !important;
}
</style>

<script>
let countdownTimer;
let countdownValue = 10;

function startCountdown() {
    const countdownElement = document.getElementById('countdown');
    
    countdownTimer = setInterval(function() {
        countdownValue--;
        countdownElement.textContent = countdownValue;
        
        if (countdownValue <= 0) {
            location.reload();
        }
    }, 1000);
}

function cancelAutoRefresh() {
    if (countdownTimer) {
        clearInterval(countdownTimer);
        document.querySelector('.spinner-border').style.display = 'none';
        document.querySelector('.mt-4.pt-4.border-top').innerHTML = 
            '<small class="text-muted">Auto-refresh cancelled</small>';
    }
}

// Start countdown when page loads
document.addEventListener('DOMContentLoaded', function() {
    startCountdown();
});
</script>
@endsection
