<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Allow concurrent sessions - no need to check web guard

        // Check if admin is authenticated
        if (!Auth::guard('admin')->check()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin authentication required'
                ], 401);
            }

            return redirect()->route('admin.login');
        }

        $admin = Auth::guard('admin')->user();

        // Check if admin account is active
        if (!$admin->is_active) {
            Auth::guard('admin')->logout();
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin account deactivated'
                ], 403);
            }
            return redirect()->route('admin.login')->with('error', 'Your admin account has been deactivated.');
        }

        // Check if admin is suspended
        if ($admin->isSuspended()) {
            Auth::guard('admin')->logout();
            $message = 'Your admin account has been suspended.';
            if ($admin->suspended_reason) {
                $message .= ' Reason: ' . $admin->suspended_reason;
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'suspended' => true
                ], 403);
            }
            return redirect()->route('admin.login')->with('error', $message);
        }

        // Update last activity
        $admin->updateLastActivity();

        return $next($request);
    }
}
