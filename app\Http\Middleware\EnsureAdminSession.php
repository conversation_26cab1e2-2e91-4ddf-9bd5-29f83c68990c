<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureAdminSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('admin.login')->with('error', 'Please login to access admin panel');
        }

        $user = Auth::user();

        // If user is not admin, redirect to user portal WITHOUT logout
        if (!$user->isAdmin()) {
            return redirect()->route('dashboard')->with('info', 'Regular users should use the user dashboard');
        }

        // Check if this is a proper admin session
        if (!$request->session()->has('is_admin_session')) {
            // Admin user but no admin session - redirect to admin login
            return redirect()->route('admin.login')->with('info', 'Please login through admin portal.');
        }

        // Verify session admin ID matches authenticated user
        $sessionAdminId = $request->session()->get('admin_user_id');
        if ($sessionAdminId && $sessionAdminId !== $user->id) {
            // Session mismatch for admin - logout required
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            return redirect()->route('admin.login')->with('error', 'Session mismatch. Please login again.');
        }

        // Check if admin is active
        if (!$user->is_active) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            return redirect()->route('admin.login')->with('error', 'Your admin account has been deactivated.');
        }

        // Allow access for admin users with proper session
        return $next($request);
    }
}
