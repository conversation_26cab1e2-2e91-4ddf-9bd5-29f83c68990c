@extends('layouts.app')

@section('title', 'اختبار FairPlay DRM - شاهد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">🍎 اختبار FairPlay DRM - شاهد</h4>
                    <small>اختبار نظام الحماية FairPlay للآيفون والآيباد</small>
                </div>
                
                <div class="card-body">
                    <!-- Test Controls -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contentId">Asset ID:</label>
                                <input type="text" id="contentId" class="form-control" value="{{ $sample_content_id }}" placeholder="أدخل Asset ID">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="country">Country Code:</label>
                                <input type="text" id="country" class="form-control" value="{{ $sample_country }}" placeholder="مثال: EG, SA, AE">
                            </div>
                        </div>
                    </div>

                    <!-- Test Buttons -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <button class="btn btn-primary" onclick="testShahidLicense()">🔐 اختبار ترخيص شاهد</button>
                            <button class="btn btn-success" onclick="getStreamUrl()">📺 جلب رابط HLS</button>
                            <button class="btn btn-warning" onclick="createCompleteSetup()">🚀 إعداد كامل</button>
                            <button class="btn btn-info" onclick="getDRMInfo()">📊 معلومات DRM</button>
                            <button class="btn btn-secondary" onclick="testCertificate()">📜 اختبار الشهادة</button>
                            <button class="btn btn-danger btn-lg" onclick="extractRealKey()">🔓 استخراج المفتاح الحقيقي</button>
                            <button class="btn btn-success" onclick="testInPlayer()">🎮 اختبار في المشغل</button>
                            <button class="btn btn-dark" onclick="healthCheck()">🏥 فحص النظام</button>
                            <button class="btn btn-outline-danger" onclick="clearLog()">🗑️ مسح السجل</button>
                        </div>
                        <div class="col-12 mt-2">
                            <div class="alert alert-warning">
                                <strong>🔑 استخراج المفتاح الحقيقي:</strong>
                                <button class="btn btn-outline-danger ms-2" onclick="extractRealKey()">🔓 استخراج المفتاح الحقيقي</button>
                                <small class="d-block mt-1">هذا سيحاول استخراج مفتاح فك التشفير الفعلي من خدمة FairPlay</small>
                            </div>
                        </div>
                    </div>

                    <!-- Key Extraction Results -->
                    <div id="result" class="mb-4"></div>

                    <!-- Results Tabs -->
                    <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="log-tab" data-bs-toggle="tab" data-bs-target="#log" type="button" role="tab">📝 سجل الأحداث</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="response-tab" data-bs-toggle="tab" data-bs-target="#response" type="button" role="tab">📨 آخر استجابة</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">⚙️ إعدادات المشغل</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="resultTabsContent">
                        <!-- Log Tab -->
                        <div class="tab-pane fade show active" id="log" role="tabpanel">
                            <div class="log-container mt-3">
                                <pre id="logOutput" class="log-output"></pre>
                            </div>
                        </div>

                        <!-- Response Tab -->
                        <div class="tab-pane fade" id="response" role="tabpanel">
                            <div class="response-container mt-3">
                                <pre id="responseOutput" class="response-output"></pre>
                            </div>
                        </div>

                        <!-- Config Tab -->
                        <div class="tab-pane fade" id="config" role="tabpanel">
                            <div class="config-container mt-3">
                                <pre id="configOutput" class="config-output"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.log-output, .response-output, .config-output {
    background: #1e1e1e;
    color: #fff;
    padding: 15px;
    border-radius: 6px;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    border: 1px solid #333;
}

.log-container, .response-container, .config-container {
    min-height: 400px;
}

.success { color: #4CAF50; }
.error { color: #f44336; }
.warning { color: #ff9800; }
.info { color: #2196F3; }

.btn {
    margin: 2px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}
</style>

<script>
// Global variables
let logOutput = document.getElementById('logOutput');
let responseOutput = document.getElementById('responseOutput');
let configOutput = document.getElementById('configOutput');

// Utility functions
function addLog(type, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
    logOutput.textContent += logEntry;
    logOutput.scrollTop = logOutput.scrollHeight;
    
    console[type === 'error' ? 'error' : type === 'warn' ? 'warn' : 'log'](message);
}

function displayResponse(data, title = 'Response') {
    responseOutput.textContent = `=== ${title} ===\n${JSON.stringify(data, null, 2)}`;
}

function displayConfig(config, title = 'Player Config') {
    configOutput.textContent = `=== ${title} ===\n${JSON.stringify(config, null, 2)}`;
}

function clearLog() {
    logOutput.textContent = '';
    responseOutput.textContent = '';
    configOutput.textContent = '';
    addLog('info', 'سجل الأحداث تم مسحه');
}

function getInputValues() {
    return {
        content_id: document.getElementById('contentId').value,
        asset_id: document.getElementById('contentId').value, // Same as content_id for compatibility
        country: document.getElementById('country').value
    };
}

// API functions
async function testShahidLicense() {
    addLog('info', 'بدء اختبار ترخيص شاهد...');
    
    const data = getInputValues();
    
    try {
        const response = await fetch('/fairplay/api/shahid-license', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLog('success', 'تم الحصول على ترخيص شاهد بنجاح!');
            displayResponse(result.data, 'Shahid License Data');
            
            if (result.data.data && result.data.data.fairplay) {
                addLog('info', `FairPlay Certificate URL: ${result.data.data.fairplay}`);
            }
            if (result.data.data && result.data.data.signature) {
                addLog('info', `License URL: ${result.data.data.signature.substring(0, 100)}...`);
            }
        } else {
            addLog('error', `فشل في الحصول على الترخيص: ${result.error}`);
            displayResponse(result, 'Error Response');
        }
        
    } catch (error) {
        addLog('error', `خطأ في الطلب: ${error.message}`);
    }
}

async function getStreamUrl() {
    addLog('info', 'جلب رابط HLS من شاهد...');

    const data = getInputValues();

    try {
        const response = await fetch('/fairplay/api/stream-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            addLog('success', 'تم الحصول على رابط HLS بنجاح!');
            displayResponse(result.data, 'HLS Stream Data');

            if (result.data.hls_url) {
                addLog('info', `HLS URL: ${result.data.hls_url.substring(0, 100)}...`);
            }
            if (result.data.playout_data) {
                addLog('info', `Duration: ${result.data.playout_data.durationSeconds} seconds`);
                addLog('info', `DRM: ${result.data.playout_data.drm ? 'Enabled' : 'Disabled'}`);
                addLog('info', `HD: ${result.data.playout_data.hd ? 'Available' : 'Not Available'}`);
            }
        } else {
            addLog('error', `فشل في جلب رابط HLS: ${result.error}`);
            displayResponse(result, 'Error Response');
        }

    } catch (error) {
        addLog('error', `خطأ في جلب رابط HLS: ${error.message}`);
    }
}

async function createCompleteSetup() {
    addLog('info', 'إنشاء إعداد FairPlay كامل...');

    const data = getInputValues();

    try {
        const response = await fetch('/fairplay/api/complete-setup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            addLog('success', 'تم إنشاء الإعداد الكامل بنجاح! 🎉');
            displayResponse(result.data, 'Complete FairPlay Setup');
            displayConfig(result.data.player_config, 'Ready-to-Use Player Config');

            const setupData = result.data;
            addLog('success', `✅ HLS URL: ${setupData.hls_url ? 'Ready' : 'Missing'}`);
            addLog('success', `✅ DRM Info: ${setupData.drm_info ? 'Ready' : 'Missing'}`);
            addLog('success', `✅ Player Config: ${setupData.player_config ? 'Ready' : 'Missing'}`);

            if (setupData.drm_info) {
                addLog('info', `Certificate URL: ${setupData.drm_info.certificate_url}`);
                addLog('info', `License URL: ${setupData.drm_info.license_url ? setupData.drm_info.license_url.substring(0, 100) + '...' : 'N/A'}`);
            }

            addLog('success', '🍎 الإعداد جاهز للاستخدام مع مشغل iOS!');
        } else {
            addLog('error', `فشل في إنشاء الإعداد: ${result.error}`);
            if (result.data && result.data.step) {
                addLog('error', `فشل في المرحلة: ${result.data.step}`);
            }
            displayResponse(result, 'Error Response');
        }

    } catch (error) {
        addLog('error', `خطأ في إنشاء الإعداد: ${error.message}`);
    }
}

async function testFairPlayDRM() {
    addLog('info', 'بدء اختبار FairPlay DRM الكامل...');
    
    const data = getInputValues();
    
    try {
        const response = await fetch('/fairplay/api/test-license', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLog('success', 'اختبار FairPlay DRM مكتمل بنجاح!');
            displayResponse(result.data, 'FairPlay DRM Test Results');
            
            // Display certificate test results
            if (result.data.certificate_test) {
                const certTest = result.data.certificate_test;
                if (certTest.success) {
                    addLog('success', `شهادة FairPlay تم تحميلها بنجاح! الحجم: ${certTest.size} bytes`);
                } else {
                    addLog('error', `فشل في تحميل الشهادة: ${certTest.error}`);
                }
            }
        } else {
            addLog('error', `فشل اختبار FairPlay: ${result.error}`);
            displayResponse(result, 'Error Response');
        }
        
    } catch (error) {
        addLog('error', `خطأ في اختبار FairPlay: ${error.message}`);
    }
}

async function getDRMInfo() {
    addLog('info', 'الحصول على معلومات DRM...');
    
    const data = getInputValues();
    
    try {
        const response = await fetch('/fairplay/api/drm-info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLog('success', 'تم الحصول على معلومات DRM بنجاح!');
            displayResponse(result.data, 'DRM Information');
            
            const drmData = result.data;
            addLog('info', `نوع DRM: ${drmData.drm_type}`);
            addLog('info', `Brand GUID: ${drmData.brand_guid}`);
            if (drmData.expires) {
                addLog('info', `تاريخ الانتهاء: ${drmData.expires}`);
            }
        } else {
            addLog('error', `فشل في الحصول على معلومات DRM: ${result.error}`);
            displayResponse(result, 'Error Response');
        }
        
    } catch (error) {
        addLog('error', `خطأ في الحصول على معلومات DRM: ${error.message}`);
    }
}

async function testCertificate() {
    addLog('info', 'اختبار شهادة FairPlay...');
    
    const certificateUrl = 'https://shahid-ga.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4';
    
    try {
        const response = await fetch('/fairplay/api/test-certificate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ certificate_url: certificateUrl })
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLog('success', `شهادة FairPlay تم اختبارها بنجاح! الحجم: ${result.data.size} bytes`);
            displayResponse(result.data, 'Certificate Test Results');
        } else {
            addLog('error', `فشل اختبار الشهادة: ${result.error}`);
            displayResponse(result, 'Error Response');
        }
        
    } catch (error) {
        addLog('error', `خطأ في اختبار الشهادة: ${error.message}`);
    }
}

async function extractRealKey() {
    addLog('info', '🔑 بدء استخراج المفتاح الحقيقي من FairPlay...');

    const data = getInputValues();

    try {
        const response = await fetch('/fairplay/api/extract-real-key', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success && result.data.key) {
            addLog('success', '🎉 تم استخراج المفتاح الحقيقي بنجاح!');

            const keyData = result.data;

            // Display key information
            let keyTypeIcon = '';
            let keyTypeClass = '';

            switch(keyData.key_type) {
                case 'CKC':
                    keyTypeIcon = '🔑';
                    keyTypeClass = 'success';
                    addLog('success', '✅ تم العثور على Content Key Context (CKC)');
                    break;
                case 'HEX_KEY':
                    keyTypeIcon = '🔐';
                    keyTypeClass = 'info';
                    addLog('info', '🔐 تم استخراج مفتاح HEX');
                    break;
                case 'BINARY_KEY':
                    keyTypeIcon = '📦';
                    keyTypeClass = 'warning';
                    addLog('warning', '📦 مفتاح binary تم استخراجه');
                    break;
                default:
                    keyTypeIcon = '📄';
                    keyTypeClass = 'secondary';
                    addLog('info', '📄 بيانات خام تم استخراجها');
            }

            addLog('info', `نوع المفتاح: ${keyData.key_type} (${keyData.key_format})`);
            addLog('info', `الوصف: ${keyData.key_description}`);

            // Create detailed result display
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h4>🎉 تم استخراج المفتاح الحقيقي بنجاح!</h4>

                    <div class="mt-3">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-${keyTypeClass} me-2">${keyTypeIcon} ${keyData.key_type}</span>
                            <small class="text-muted">${keyData.key_description}</small>
                        </div>

                        <label class="form-label"><strong>المفتاح (${keyData.key_format.toUpperCase()}):</strong></label>
                        <div class="input-group">
                            <textarea class="form-control font-monospace" rows="4" readonly>${keyData.key}</textarea>
                            <button class="btn btn-outline-secondary" onclick="copyToClipboard('${keyData.key}')">نسخ المفتاح</button>
                        </div>
                    </div>

                    ${keyData.key_type === 'CKC' ? `
                    <div class="alert alert-info mt-3">
                        <h6>🎯 كيفية استخدام هذا CKC:</h6>
                        <ul class="mb-0">
                            <li>هذا هو <strong>Content Key Context</strong> لـ FairPlay</li>
                            <li>استخدمه مباشرة مع <code>AVContentKeySession</code> في iOS/tvOS</li>
                            <li>أو مع مشغلات متوافقة مع FairPlay مثل Video.js، Shaka Player</li>
                            <li>المفتاح جاهز للاستخدام في فك التشفير</li>
                        </ul>
                    </div>
                    ` : ''}

                    <div class="mt-3">
                        <label class="form-label"><strong>الشهادة (Base64):</strong></label>
                        <textarea class="form-control font-monospace" rows="3" readonly>${keyData.certificate}</textarea>
                    </div>
                    <div class="mt-3">
                        <label class="form-label"><strong>استجابة الترخيص (Base64):</strong></label>
                        <textarea class="form-control font-monospace" rows="3" readonly>${keyData.license_response}</textarea>
                    </div>
                </div>
            `;

            displayResponse(result.data, 'Real Key Extraction Results');

        } else {
            addLog('error', `فشل في استخراج المفتاح: ${result.error || 'خطأ غير معروف'}`);
            displayResponse(result, 'Error Response');
        }

    } catch (error) {
        addLog('error', `خطأ في استخراج المفتاح: ${error.message}`);
    }
}

async function healthCheck() {
    addLog('info', 'فحص صحة النظام...');

    try {
        const response = await fetch('/fairplay/api/health');
        const result = await response.json();

        if (result.success) {
            addLog('success', 'النظام يعمل بشكل طبيعي!');
            displayResponse(result.health, 'System Health Check');

            const health = result.health;
            addLog('info', `دعم FairPlay: ${health.fairplay_support ? 'متاح' : 'غير متاح'}`);
            addLog('info', `خدمة Shahid DRM: ${health.shahid_drm_service ? 'متاحة' : 'غير متاحة'}`);
            addLog('info', `ملف الجهاز: ${health.device_file_exists ? 'موجود' : 'غير موجود'}`);
        } else {
            addLog('error', `فشل فحص النظام: ${result.error}`);
            displayResponse(result, 'Error Response');
        }

    } catch (error) {
        addLog('error', `خطأ في فحص النظام: ${error.message}`);
    }
}

// Utility functions
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        addLog('success', '✅ تم نسخ النص إلى الحافظة');
    }, function(err) {
        addLog('error', 'فشل في نسخ النص: ' + err);
    });
}

async function testInPlayer() {
    addLog('info', '🎮 تحضير البيانات للمشغل (FairPlay)...');

    const data = getInputValues();

    if (!data.asset_id) {
        addLog('error', 'يرجى إدخال Asset ID أولاً');
        return;
    }

    try {
        // افتح نافذة فارغة فور النقر لتجنب حظر النوافذ المنبثقة على الموبايل
        let playerWindow = window.open('', '_blank');
        const popupBlocked = !playerWindow;
        if (!popupBlocked) {
            try {
                playerWindow.document.write('<html><head><title>Loading Player...</title></head><body style="background:#000;color:#fff;font-family:Arial, sans-serif;display:flex;align-items:center;justify-content:center;height:100vh;">⏳ Loading player...</body></html>');
            } catch (e) {
                // ignore
            }
        }

        // اجلب إعداد FairPlay الكامل (يتضمن hls_url و drm_info)
        const response = await fetch('/fairplay/api/complete-setup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ content_id: data.asset_id, country: data.country })
        });

        const result = await response.json();

        if (result.success && result.data) {
            const setup = result.data;
            const drm = setup.drm_info || {};
            const hlsUrl = setup.hls_url;

            if (!hlsUrl) {
                addLog('error', '❌ لم يتم العثور على رابط HLS في الإعداد.');
                displayResponse(result, 'Complete Setup Response');
                return;
            }

            // حفظ البيانات المستخرجة في sessionStorage
            const extractedData = {
                title: `Shahid Asset ${data.asset_id}`,
                hls: hlsUrl,
                certificateUrl: drm.certificate_url || 'https://shahid.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
                licenseUrl: drm.license_url || '',
                kid: drm.kid || '827802f4-e896-4769-ba77-f1e656ed1bd4',
                iv: drm.iv || '1f3a1d7ce87a4cd3846038fcf4d05f82',
                brandGuid: drm.brand_guid || '2be49af0-6fbd-4511-8e11-3d6523185bb4',
                extractedAt: new Date().toISOString(),
                assetId: data.asset_id
            };
            
            try {
                sessionStorage.setItem('fairplay_extracted_data', JSON.stringify(extractedData));
                addLog('success', '💾 تم حفظ البيانات المستخرجة في sessionStorage');
            } catch (e) {
                addLog('warning', 'تعذر حفظ البيانات في sessionStorage: ' + e.message);
            }

            // ابنِ رابط المشغل مع البيانات كـ URL parameters (backup)
            const params = new URLSearchParams({
                title: extractedData.title,
                hls: extractedData.hls,
                keyId: extractedData.kid,
                licenseUrl: extractedData.licenseUrl,
                certificateUrl: extractedData.certificateUrl,
                iv: extractedData.iv,
                brandGuid: extractedData.brandGuid
            });

            const playerUrl = `${window.location.origin}/shahid/fairplay-static?${params.toString()}`;

            addLog('success', '✅ تم تحضير رابط المشغل الجديد');
            addLog('info', playerUrl);

            // افتح المشغل مع البيانات المستخرجة
            if (!popupBlocked && playerWindow) {
                try { 
                    playerWindow.location.replace(playerUrl);
                    addLog('success', '🎮 تم فتح المشغل مع البيانات المستخرجة');
                } catch(e) { 
                    playerWindow.location.href = playerUrl; 
                }
            } else {
                addLog('warning', '⚠️ المتصفح منع فتح نافذة جديدة.');
                
                // إنشاء رابط للفتح اليدوي
                const link = document.createElement('a');
                link.href = playerUrl;
                link.target = '_blank';
                link.rel = 'noopener';
                link.className = 'btn btn-primary';
                link.innerHTML = '🎮 فتح المشغل (بيانات مستخرجة)';
                const logContainer = document.getElementById('log');
                if (logContainer.lastElementChild) {
                    logContainer.lastElementChild.appendChild(link);
                }
                
                // محاولة فتح تلقائي
                try {
                    const a = document.createElement('a');
                    a.href = playerUrl; a.target = '_blank'; a.rel = 'noopener';
                    document.body.appendChild(a); a.click(); a.remove();
                } catch(_) {}
            }

            // زر نسخ الرابط
            const copyButton = document.createElement('button');
            copyButton.className = 'btn btn-sm btn-outline-primary ms-2';
            copyButton.innerHTML = '📋 نسخ رابط المشغل';
            copyButton.onclick = () => copyToClipboard(playerUrl);
            
            // إضافة معلومات البيانات المستخرجة للـ log
            addLog('info', '📊 البيانات المستخرجة:');
            addLog('info', `HLS: ${hlsUrl.substring(0, 80)}...`);
            addLog('info', `Certificate: ${drm.certificate_url || 'default'}`);
            addLog('info', `License: ${drm.license_url ? drm.license_url.substring(0, 80) + '...' : 'default'}`);
            addLog('info', `KID: ${drm.kid || 'default'}`);
            addLog('info', `IV: ${drm.iv || 'default'}`);
            const logContainer = document.getElementById('log');
            if (logContainer.lastElementChild) {
                logContainer.lastElementChild.appendChild(copyButton);
            }

        } else {
            addLog('error', `فشل في إنشاء الإعداد: ${result.error || 'غير معروف'}`);
            displayResponse(result, 'Error Response');
        }

    } catch (error) {
        addLog('error', `خطأ في تحضير المشغل: ${error.message}`);
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    addLog('info', 'صفحة اختبار FairPlay DRM جاهزة');
    addLog('info', 'تأكد من إدخال Asset ID صحيح قبل الاختبار');

    // Auto health check
    setTimeout(healthCheck, 1000);
});
</script>
@endsection
