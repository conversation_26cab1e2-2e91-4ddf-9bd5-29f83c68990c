/**
 * DRM Fix Script for Shahid Player
 * Handles common DRM issues and provides fallback mechanisms
 */

console.log('🔧 DRM Fix Script loaded');

// Global error handler for DRM issues
window.addEventListener('error', function(event) {
    if (event.error && event.error.message) {
        const message = event.error.message.toLowerCase();
        if (message.includes('drm') || message.includes('clearkey') || message.includes('241403')) {
            console.error('🚨 DRM Error detected:', event.error);
            handleDRMError(event.error);
        }
    }
});

// Handle specific DRM errors
function handleDRMError(error) {
    console.log('🔄 Attempting DRM error recovery...');
    
    // Try to reload player with different configuration
    setTimeout(() => {
        const playButton = document.getElementById('playStreamButton');
        if (playButton) {
            console.log('🔄 Retrying player initialization...');
            
            // Clear any existing player instance
            try {
                const player = jwplayer('player');
                if (player) {
                    player.remove();
                }
            } catch (e) {
                console.log('No existing player to remove');
            }
            
            // Retry with simplified configuration
            playButton.click();
        }
    }, 3000);
}

// Enhanced ClearKey validation
function validateClearKeys(keyData) {
    if (!keyData || typeof keyData !== 'string') {
        console.warn('⚠️ Invalid key data format');
        return false;
    }
    
    // Check for KID:KEY format
    if (keyData.includes(':')) {
        const parts = keyData.split(':');
        if (parts.length === 2 && parts[0].length === 32 && parts[1].length === 32) {
            console.log('✅ Valid KID:KEY format detected');
            return true;
        }
    }
    
    // Check for JSON format
    try {
        const parsed = JSON.parse(keyData);
        if (parsed && typeof parsed === 'object') {
            console.log('✅ Valid JSON key format detected');
            return true;
        }
    } catch (e) {
        // Not JSON, continue
    }
    
    console.warn('⚠️ Unrecognized key format');
    return false;
}

// Manifest URL validator
function validateManifestUrl(url) {
    if (!url || typeof url !== 'string') {
        console.error('❌ Invalid manifest URL');
        return false;
    }

    // Check for valid MPD URL
    if (url.includes('.mpd') || url.includes('manifest')) {
        console.log('✅ Valid manifest URL detected');
        return true;
    }

    // Check for valid HLS URL
    if (url.includes('.m3u8') || url.includes('playlist')) {
        console.log('✅ Valid HLS URL detected');
        return true;
    }

    // Check for dynamic channel proxy URLs
    if (url.includes('/shahid/channel/') && (url.includes('playlist.m3u8') || url.includes('manifest.mpd'))) {
        console.log('✅ Valid dynamic channel proxy URL detected');
        return true;
    }

    console.warn('⚠️ Suspicious manifest URL format');
    return false;
}

// Network connectivity check
function checkNetworkConnectivity() {
    return fetch('/proxy/manifest?url=' + encodeURIComponent('https://httpbin.org/get'), {
        method: 'HEAD',
        mode: 'no-cors'
    }).then(() => {
        console.log('✅ Network connectivity OK');
        return true;
    }).catch(() => {
        console.error('❌ Network connectivity issues');
        return false;
    });
}

// Enhanced error reporting
function reportDRMError(errorCode, errorMessage, additionalData = {}) {
    const errorReport = {
        timestamp: new Date().toISOString(),
        errorCode: errorCode,
        errorMessage: errorMessage,
        userAgent: navigator.userAgent,
        url: window.location.href,
        additionalData: additionalData
    };
    
    console.error('📊 DRM Error Report:', errorReport);
    
    // You can send this to your logging service
    // fetch('/api/log-error', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(errorReport)
    // });
}

// Export functions for use in main player
window.DRMFix = {
    validateClearKeys,
    validateManifestUrl,
    checkNetworkConnectivity,
    reportDRMError,
    handleDRMError
};

console.log('✅ DRM Fix Script ready');
