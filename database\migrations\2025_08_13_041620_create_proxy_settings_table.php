<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('proxy_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // اسم البروكسي
            $table->string('host'); // IP أو hostname
            $table->integer('port'); // Port number
            $table->string('username')->nullable(); // اسم المستخدم
            $table->string('password')->nullable(); // كلمة المرور
            $table->enum('type', ['http', 'https', 'socks4', 'socks5'])->default('http'); // نوع البروكسي
            $table->boolean('is_active')->default(false); // مفعل أم لا
            $table->boolean('is_default')->default(false); // البروكسي الافتراضي
            $table->text('description')->nullable(); // وصف البروكسي
            $table->json('test_results')->nullable(); // نتائج اختبار البروكسي
            $table->timestamp('last_tested_at')->nullable(); // آخر اختبار
            $table->timestamps();

            // فهرس للبحث السريع
            $table->index(['is_active', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('proxy_settings');
    }
};
