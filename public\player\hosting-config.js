/**
 * Hosting Configuration for JW Player
 * Optimized settings for shared hosting environments
 */

window.HostingConfig = {
    // Player settings optimized for shared hosting
    playerDefaults: {
        preload: 'metadata',
        controls: true,
        displaytitle: false,
        displaydescription: false,
        sharing: false,
        related: false,
        advertising: false,
        abouttext: '',
        aboutlink: '',
        logo: false,
        // Reduce memory usage
        hlsjsdefault: false,
        // Disable analytics to reduce requests
        analytics: false,
        // Reduce buffering for better performance
        bufferlength: 10,
        // Disable captions by default to reduce complexity
        captions: false
    },

    // Error recovery settings
    errorRecovery: {
        maxRetries: 3,
        retryDelay: 2000,
        fallbackDelay: 5000
    },

    // Network optimization
    network: {
        timeout: 30000,
        maxConnections: 2,
        // Use lower quality for initial load
        initialQuality: 'auto'
    },

    // Apply hosting-friendly configuration
    applyConfig: function(baseConfig) {
        console.log('🔧 Applying hosting-friendly configuration...');
        
        const optimizedConfig = {
            ...this.playerDefaults,
            ...baseConfig,
            // Override potentially problematic settings
            width: baseConfig.width || '100%',
            height: baseConfig.height || '100%'
        };

        // Ensure playlist format is correct
        if (baseConfig.file && !baseConfig.playlist) {
            optimizedConfig.playlist = [{
                file: baseConfig.file,
                title: baseConfig.title || 'Video'
            }];
            delete optimizedConfig.file;
        }

        // Add error handling events
        optimizedConfig.events = {
            ...optimizedConfig.events,
            onSetupError: this.handleSetupError.bind(this),
            onError: this.handleError.bind(this),
            onReady: this.handleReady.bind(this)
        };

        console.log('🔧 Optimized config:', optimizedConfig);
        return optimizedConfig;
    },

    // Handle setup errors
    handleSetupError: function(error) {
        console.error('🔧 Hosting Config - Setup Error:', error);
        
        switch(error.code) {
            case 101102:
                console.log('🔄 Chunk loading error - attempting simplified setup...');
                this.attemptSimplifiedSetup();
                break;
            case 100001:
                console.log('🔄 Network error - checking connectivity...');
                this.checkConnectivity();
                break;
            default:
                console.log('⚠️ Unknown setup error:', error.code);
        }
    },

    // Handle runtime errors
    handleError: function(error) {
        console.error('🔧 Hosting Config - Runtime Error:', error);
        
        if (error.code === 100001) {
            console.log('🔄 Network error during playback - attempting recovery...');
            setTimeout(() => {
                this.attemptReload();
            }, this.errorRecovery.retryDelay);
        }
    },

    // Handle ready event
    handleReady: function() {
        console.log('✅ Player ready with hosting configuration');
        
        // Apply additional optimizations after ready
        this.optimizeAfterReady();
    },

    // Attempt simplified setup for error recovery
    attemptSimplifiedSetup: function() {
        console.log('🔄 Attempting simplified setup...');
        
        const playerElement = document.getElementById('player');
        if (!playerElement) return;

        // Get current config
        const currentPlayer = jwplayer('player');
        const currentConfig = currentPlayer.getConfig();
        
        // Create minimal config
        const minimalConfig = {
            file: currentConfig.file || (currentConfig.playlist && currentConfig.playlist[0] && currentConfig.playlist[0].file),
            width: '100%',
            height: '100%',
            autostart: false,
            controls: true
        };

        // Remove current player and recreate
        currentPlayer.remove();
        
        setTimeout(() => {
            try {
                jwplayer('player').setup(minimalConfig);
                console.log('✅ Simplified setup successful');
            } catch (error) {
                console.error('❌ Simplified setup failed:', error);
                this.showErrorMessage('Player initialization failed. Please refresh the page.');
            }
        }, 1000);
    },

    // Attempt to reload current content
    attemptReload: function() {
        console.log('🔄 Attempting content reload...');
        
        const player = jwplayer('player');
        if (!player) return;

        try {
            const currentConfig = player.getConfig();
            const playlist = currentConfig.playlist || currentConfig.file;
            
            player.load(playlist);
            console.log('✅ Content reload attempted');
        } catch (error) {
            console.error('❌ Content reload failed:', error);
        }
    },

    // Check connectivity
    checkConnectivity: function() {
        console.log('🔍 Checking connectivity...');
        
        fetch('/health.php', { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    console.log('✅ Connectivity OK');
                } else {
                    console.log('⚠️ Server response issue:', response.status);
                }
            })
            .catch(error => {
                console.error('❌ Connectivity check failed:', error);
                this.showErrorMessage('Network connectivity issue detected.');
            });
    },

    // Optimize after player is ready
    optimizeAfterReady: function() {
        const player = jwplayer('player');
        if (!player) return;

        // Disable unnecessary features
        try {
            // Reduce quality if needed for performance
            const levels = player.getQualityLevels();
            if (levels && levels.length > 1) {
                // Start with medium quality
                const mediumLevel = levels.find(level => level.height <= 720) || levels[0];
                player.setCurrentQuality(mediumLevel.index);
                console.log('🔧 Set initial quality to:', mediumLevel.label);
            }
        } catch (error) {
            console.log('⚠️ Could not optimize quality:', error);
        }
    },

    // Show error message to user
    showErrorMessage: function(message) {
        const playerElement = document.getElementById('player');
        if (playerElement) {
            playerElement.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    background: #1a1a1a;
                    color: #fff;
                    text-align: center;
                    padding: 20px;
                    border-radius: 8px;
                ">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                        <div style="font-size: 18px; margin-bottom: 16px;">${message}</div>
                        <button onclick="location.reload()" style="
                            background: #ff6b35;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 16px;
                        ">إعادة تحميل الصفحة</button>
                    </div>
                </div>
            `;
        }
    }
};

console.log('🔧 Hosting configuration loaded');
