// Permission Management JavaScript
class PermissionManagement {
    constructor() {
        this.currentPage = 1;
        this.perPage = 20;
        this.filters = {};
        this.init();
    }

    init() {
        this.loadStatistics();
        this.loadPermissions();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Search and filters
        document.getElementById('search-input').addEventListener('input', 
            this.debounce(() => this.applyFilters(), 500));
        
        document.getElementById('apply-filters').addEventListener('click', () => this.applyFilters());
        document.getElementById('clear-filters').addEventListener('click', () => this.clearFilters());
        document.getElementById('view-categories').addEventListener('click', () => this.viewCategories());

        // Form submissions
        const createForm = document.getElementById('create-permission-form');
        if (createForm) {
            createForm.addEventListener('submit', (e) => this.handleCreatePermission(e));
        }

        const bulkCreateForm = document.getElementById('bulk-create-form');
        if (bulkCreateForm) {
            bulkCreateForm.addEventListener('submit', (e) => this.handleBulkCreate(e));
        }

        const editForm = document.getElementById('edit-permission-form');
        if (editForm) {
            editForm.addEventListener('submit', (e) => this.handleEditPermission(e));
        }

        // Template selection
        const templateSelect = document.getElementById('permission-template');
        if (templateSelect) {
            templateSelect.addEventListener('change', (e) => this.handleTemplateChange(e));
        }

        const resourceNameInput = document.getElementById('resource-name');
        if (resourceNameInput) {
            resourceNameInput.addEventListener('input', (e) => this.updatePermissionsPreview());
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async loadStatistics() {
        try {
            const response = await fetch('/admin/permissions/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.updateStatistics(data.data);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateStatistics(stats) {
        document.getElementById('total-permissions').textContent = stats.total_permissions || 0;
        document.getElementById('used-permissions').textContent = (stats.permissions_with_roles + stats.permissions_with_users) || 0;
        document.getElementById('unused-permissions').textContent = stats.unused_permissions || 0;
        document.getElementById('system-permissions').textContent = stats.system_permissions || 0;
    }

    async loadPermissions(page = 1) {
        try {
            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage,
                ...this.filters
            });

            const response = await fetch(`/admin/permissions/api?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderPermissions(data.data.data);
                this.renderPagination(data.data);
                this.currentPage = page;
            }
        } catch (error) {
            console.error('Error loading permissions:', error);
            this.showAlert('Error loading permissions', 'danger');
        }
    }

    renderPermissions(permissions) {
        const tbody = document.getElementById('permissions-tbody');
        tbody.innerHTML = '';

        permissions.forEach(permission => {
            const row = this.createPermissionRow(permission);
            tbody.appendChild(row);
        });
    }

    createPermissionRow(permission) {
        const row = document.createElement('tr');
        
        // System permission styling
        const isSystemPermission = [
            'manage admins', 'manage roles', 'manage permissions', 'super admin access'
        ].includes(permission.name);
        
        if (isSystemPermission) {
            row.classList.add('system-permission');
        }

        // Check if unused
        const isUnused = permission.roles_count === 0 && permission.users_count === 0;
        if (isUnused) {
            row.classList.add('unused-permission');
        }

        // Category detection
        const category = this.detectCategory(permission.name);

        row.innerHTML = `
            <td>
                <div class="fw-bold">${permission.name}</div>
                ${isSystemPermission ? '<small class="text-warning">System Permission</small>' : ''}
            </td>
            <td>
                <span class="badge bg-info">${permission.guard_name}</span>
            </td>
            <td>
                <span class="badge bg-primary">${permission.roles_count}</span>
            </td>
            <td>
                <span class="badge bg-success">${permission.users_count}</span>
            </td>
            <td>
                <span class="badge bg-secondary">${category}</span>
            </td>
            <td>
                <small class="text-muted">${new Date(permission.created_at).toLocaleDateString()}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="permissionManagement.viewPermission(${permission.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${!isSystemPermission ? `
                        <button class="btn btn-outline-warning" onclick="permissionManagement.editPermission(${permission.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${isUnused ? `
                            <button class="btn btn-outline-danger" onclick="permissionManagement.deletePermission(${permission.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    ` : ''}
                </div>
            </td>
        `;

        return row;
    }

    detectCategory(permissionName) {
        if (permissionName.includes('user')) return 'User Mgmt';
        if (permissionName.includes('admin')) return 'Admin Mgmt';
        if (permissionName.includes('role')) return 'Role Mgmt';
        if (permissionName.includes('permission')) return 'Permission Mgmt';
        if (permissionName.includes('subscription')) return 'Subscription Mgmt';
        return 'System';
    }

    renderPagination(paginationData) {
        const container = document.getElementById('pagination-container');
        if (!paginationData.links || paginationData.links.length <= 3) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<ul class="pagination justify-content-center">';
        
        paginationData.links.forEach(link => {
            const isActive = link.active ? 'active' : '';
            const isDisabled = !link.url ? 'disabled' : '';
            const page = this.extractPageFromUrl(link.url);
            
            paginationHTML += `
                <li class="page-item ${isActive} ${isDisabled}">
                    <a class="page-link" href="#" onclick="permissionManagement.loadPermissions(${page}); return false;">
                        ${link.label}
                    </a>
                </li>
            `;
        });
        
        paginationHTML += '</ul>';
        container.innerHTML = paginationHTML;
    }

    extractPageFromUrl(url) {
        if (!url) return 1;
        const match = url.match(/page=(\d+)/);
        return match ? parseInt(match[1]) : 1;
    }

    applyFilters() {
        this.filters = {
            search: document.getElementById('search-input').value,
            category: document.getElementById('category-filter').value,
            guard: document.getElementById('guard-filter').value,
        };

        // Remove empty filters
        Object.keys(this.filters).forEach(key => {
            if (!this.filters[key]) {
                delete this.filters[key];
            }
        });

        this.loadPermissions(1);
    }

    clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('category-filter').value = '';
        document.getElementById('guard-filter').value = '';
        
        this.filters = {};
        this.loadPermissions(1);
    }

    async handleCreatePermission(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        try {
            const response = await fetch('/admin/permissions/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Permission created successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createPermissionModal')).hide();
                e.target.reset();
                this.loadPermissions();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error creating permission', 'danger');
            }
        } catch (error) {
            console.error('Error creating permission:', error);
            this.showAlert('Error creating permission', 'danger');
        }
    }

    handleTemplateChange(e) {
        const template = e.target.value;
        const resourceNameGroup = document.getElementById('resource-name-group');
        
        if (template === 'crud' || template === 'custom') {
            resourceNameGroup.style.display = 'block';
        } else {
            resourceNameGroup.style.display = 'none';
        }
        
        this.updatePermissionsPreview();
    }

    updatePermissionsPreview() {
        const template = document.getElementById('permission-template').value;
        const resourceName = document.getElementById('resource-name').value;
        const previewContainer = document.getElementById('permissions-preview');
        
        let permissions = [];
        
        switch (template) {
            case 'crud':
                if (resourceName) {
                    permissions = [
                        `view ${resourceName}`,
                        `create ${resourceName}`,
                        `update ${resourceName}`,
                        `delete ${resourceName}`
                    ];
                }
                break;
            case 'user_management':
                permissions = [
                    'view users', 'create users', 'update users', 'delete users',
                    'suspend users', 'reset user passwords', 'manage user roles'
                ];
                break;
            case 'content_management':
                permissions = [
                    'view content', 'create content', 'update content', 'delete content',
                    'publish content', 'moderate content'
                ];
                break;
        }
        
        if (permissions.length > 0) {
            previewContainer.innerHTML = permissions.map(p => 
                `<div class="permission-item">${p}</div>`
            ).join('');
        } else {
            previewContainer.innerHTML = '<p class="text-muted">Select a template to see permissions preview</p>';
        }
    }

    async handleBulkCreate(e) {
        e.preventDefault();
        
        const template = document.getElementById('permission-template').value;
        const resourceName = document.getElementById('resource-name').value;
        const guardName = e.target.guard_name.value;
        
        let permissions = [];
        
        // Generate permissions based on template
        switch (template) {
            case 'crud':
                if (resourceName) {
                    permissions = [
                        { name: `view ${resourceName}`, guard_name: guardName },
                        { name: `create ${resourceName}`, guard_name: guardName },
                        { name: `update ${resourceName}`, guard_name: guardName },
                        { name: `delete ${resourceName}`, guard_name: guardName }
                    ];
                }
                break;
            case 'user_management':
                permissions = [
                    'view users', 'create users', 'update users', 'delete users',
                    'suspend users', 'reset user passwords', 'manage user roles'
                ].map(name => ({ name, guard_name: guardName }));
                break;
            case 'content_management':
                permissions = [
                    'view content', 'create content', 'update content', 'delete content',
                    'publish content', 'moderate content'
                ].map(name => ({ name, guard_name: guardName }));
                break;
        }

        if (permissions.length === 0) {
            this.showAlert('Please select a template and provide required information', 'warning');
            return;
        }

        try {
            const response = await fetch('/admin/permissions/api/bulk-create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ permissions })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert(`${result.data.length} permissions created successfully!`, 'success');
                bootstrap.Modal.getInstance(document.getElementById('bulkCreateModal')).hide();
                e.target.reset();
                this.loadPermissions();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error creating permissions', 'danger');
            }
        } catch (error) {
            console.error('Error creating permissions:', error);
            this.showAlert('Error creating permissions', 'danger');
        }
    }

    async viewCategories() {
        try {
            const response = await fetch('/admin/permissions/api/categories');
            const data = await response.json();
            
            if (data.success) {
                this.showCategoriesModal(data.data);
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            this.showAlert('Error loading categories', 'danger');
        }
    }

    showCategoriesModal(categories) {
        const modal = new bootstrap.Modal(document.getElementById('categoriesModal'));
        const content = document.getElementById('categories-content');
        
        let html = '';
        Object.keys(categories).forEach(categoryKey => {
            const category = categories[categoryKey];
            html += `
                <div class="card permission-category-card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">${category.name} (${category.permissions.length})</h6>
                    </div>
                    <div class="card-body">
                        ${category.permissions.map(p => 
                            `<span class="badge bg-secondary permission-badge">${p}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        });
        
        content.innerHTML = html;
        modal.show();
    }

    async deletePermission(permissionId) {
        if (!confirm('Are you sure you want to delete this permission? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/admin/permissions/api/${permissionId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Permission deleted successfully!', 'success');
                this.loadPermissions();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error deleting permission', 'danger');
            }
        } catch (error) {
            console.error('Error deleting permission:', error);
            this.showAlert('Error deleting permission', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.permissionManagement = new PermissionManagement();
});
