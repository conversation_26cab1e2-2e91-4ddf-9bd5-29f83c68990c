<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class SubscriptionPlanController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
        // Allow both web and api middleware groups
        $this->middleware('web')->only(['index', 'store', 'show', 'update', 'destroy', 'toggleStatus', 'statistics']);
    }

    /**
     * Display a listing of subscription plans.
     */
    public function index(Request $request): JsonResponse
    {
        // Check if user has permission to view subscriptions
        if (!auth('admin')->user()->isSuperAdmin() && !auth('admin')->user()->safeHasPermissionTo('view subscriptions')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to view subscription plans'
            ], 403);
        }
        $query = SubscriptionPlan::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->get('status') === 'active');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $plans = $query->withCount('activeUserSubscriptions')->paginate(
            $request->get('per_page', 15)
        );

        return response()->json([
            'success' => true,
            'data' => $plans,
            'message' => 'Subscription plans retrieved successfully'
        ]);
    }

    /**
     * Get simple list of subscription plans for dropdowns
     */
    public function getPlansForSelect(): JsonResponse
    {
        try {
            // Check if user has permission to view subscriptions
            if (!auth('admin')->user()->isSuperAdmin() && !auth('admin')->user()->safeHasPermissionTo('view subscriptions')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view subscription plans'
                ], 403);
            }

            $plans = SubscriptionPlan::where('is_active', true)
                ->select('id', 'name', 'price', 'currency', 'duration_days')
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $plans,
                'message' => 'Subscription plans retrieved successfully'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting subscription plans for select: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving subscription plans'
            ], 500);
        }
    }

    /**
     * Store a newly created subscription plan.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Check if user has permission to create subscriptions
            if (!auth('admin')->user()->isSuperAdmin() && !auth('admin')->user()->safeHasPermissionTo('create subscriptions')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to create subscription plans'
                ], 403);
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:subscription_plans',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'currency' => 'required|string|size:3',
                'duration_days' => 'required|integer|min:1',
                'features' => 'nullable|array',
                'allowed_applications' => 'nullable|array',
                'max_devices' => 'required|integer|min:1',
                'is_active' => 'nullable|in:0,1,true,false',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            // Convert is_active to boolean
            if (isset($validated['is_active'])) {
                $validated['is_active'] = in_array($validated['is_active'], ['1', 'true', true], true);
            } else {
                $validated['is_active'] = true;
            }

            // Set default values
            $validated['sort_order'] = $validated['sort_order'] ?? 0;
            $validated['allowed_applications'] = $validated['allowed_applications'] ?? ['ALL'];

            $plan = SubscriptionPlan::create($validated);

            return response()->json([
                'success' => true,
                'data' => $plan,
                'message' => 'Subscription plan created successfully'
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error creating subscription plan: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the subscription plan'
            ], 500);
        }
    }

    /**
     * Display the specified subscription plan.
     */
    public function show(SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        $subscriptionPlan->load(['userSubscriptions' => function ($query) {
            $query->with('user:id,name,email')
                  ->latest()
                  ->limit(10);
        }]);

        $subscriptionPlan->loadCount('activeUserSubscriptions');

        return response()->json([
            'success' => true,
            'data' => $subscriptionPlan,
            'message' => 'Subscription plan retrieved successfully'
        ]);
    }

    /**
     * Update the specified subscription plan.
     */
    public function update(Request $request, SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('subscription_plans')->ignore($subscriptionPlan->id)
            ],
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'duration_days' => 'required|integer|min:1',
            'features' => 'nullable|array',
            'allowed_applications' => 'nullable|array',
            'max_devices' => 'required|integer|min:1',
            'is_active' => 'nullable|in:0,1,true,false',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Convert is_active to boolean
        if (isset($validated['is_active'])) {
            $validated['is_active'] = in_array($validated['is_active'], ['1', 'true', true], true);
        }

        $subscriptionPlan->update($validated);

        return response()->json([
            'success' => true,
            'data' => $subscriptionPlan,
            'message' => 'Subscription plan updated successfully'
        ]);
    }

    /**
     * Remove the specified subscription plan.
     */
    public function destroy(SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        // Check if plan has active subscriptions
        if ($subscriptionPlan->activeUserSubscriptions()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete plan with active subscriptions'
            ], 422);
        }

        $subscriptionPlan->delete();

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan deleted successfully'
        ]);
    }

    /**
     * Toggle plan status.
     */
    public function toggleStatus(SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        $subscriptionPlan->update([
            'is_active' => !$subscriptionPlan->is_active
        ]);

        return response()->json([
            'success' => true,
            'data' => $subscriptionPlan,
            'message' => 'Plan status updated successfully'
        ]);
    }

    /**
     * Get plan statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_plans' => SubscriptionPlan::count(),
            'active_plans' => SubscriptionPlan::where('is_active', true)->count(),
            'inactive_plans' => SubscriptionPlan::where('is_active', false)->count(),
            'total_subscribers' => SubscriptionPlan::withCount('activeUserSubscriptions')
                                                  ->get()
                                                  ->sum('active_user_subscriptions_count'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Statistics retrieved successfully'
        ]);
    }
}
