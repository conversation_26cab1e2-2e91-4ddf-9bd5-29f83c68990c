@extends('admin.layouts.app')

@section('title', 'Server Error - 500')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <div class="error-icon mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #e74c3c, #c0392b); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-exclamation-triangle text-white" style="font-size: 60px;"></i>
                        </div>
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-danger mb-3">500</h1>
                    
                    <!-- Error Title -->
                    <h2 class="h3 text-dark mb-3">Internal Server Error</h2>
                    
                    <!-- Error Message -->
                    <p class="text-muted mb-4 lead">
                        Something went wrong on our end. We're working to fix this issue.
                        <br>Please try again in a few moments.
                    </p>

                    <!-- Error Details -->
                    <div class="alert alert-light border-0 mb-4" style="background-color: #f8f9fa;">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-tools text-danger fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">What happened?</h6>
                                <ul class="mb-0 text-muted small">
                                    <li>The server encountered an unexpected condition</li>
                                    <li>This error has been automatically logged</li>
                                    <li>Our technical team has been notified</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Troubleshooting Steps -->
                    <div class="alert alert-info border-0 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-question-circle text-info fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">What can you do?</h6>
                                <ul class="mb-0 text-muted small">
                                    <li>Refresh the page after a few seconds</li>
                                    <li>Clear your browser cache and cookies</li>
                                    <li>Try accessing a different page</li>
                                    <li>Contact support if the problem persists</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <button onclick="location.reload()" class="btn btn-outline-primary px-4">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Page
                        </button>
                        <button onclick="history.back()" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary px-4">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                    </div>

                    <!-- Error ID for Support -->
                    <div class="mt-4 pt-4 border-top">
                        <small class="text-muted">
                            Error ID: <code>{{ Str::random(8) }}</code> | 
                            Time: <code>{{ now()->format('Y-m-d H:i:s') }}</code>
                            @if(auth()->user())
                                | User: <code>{{ auth()->user()->name }}</code>
                            @endif
                        </small>
                    </div>

                    <!-- Contact Support -->
                    @if(auth()->user() && auth()->user()->isSuperAdmin())
                    <div class="mt-3">
                        <a href="#" class="btn btn-outline-warning btn-sm" onclick="showErrorDetails()">
                            <i class="fas fa-bug me-1"></i>Show Technical Details
                        </a>
                    </div>
                    @endif
            </div>
        </div>
    </div>
</div>

<!-- Technical Details Modal (for Super Admin) -->
@if(auth()->user() && auth()->user()->isSuperAdmin())
<div class="modal fade" id="errorDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Technical Error Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <strong>Note:</strong> This information is only visible to Super Administrators.
                </div>
                <h6>Request Information:</h6>
                <ul class="list-unstyled">
                    <li><strong>URL:</strong> {{ request()->url() }}</li>
                    <li><strong>Method:</strong> {{ request()->method() }}</li>
                    <li><strong>User Agent:</strong> {{ request()->userAgent() }}</li>
                    <li><strong>IP Address:</strong> {{ request()->ip() }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endif

<style>
.error-icon {
    animation: shake 0.5s infinite alternate;
}

@keyframes shake {
    0% { transform: translateX(0); }
    100% { transform: translateX(5px); }
}



.btn {
    border-radius: 8px;
    font-weight: 500;
}
</style>

<script>
function showErrorDetails() {
    @if(auth()->user() && auth()->user()->isSuperAdmin())
    var modal = new bootstrap.Modal(document.getElementById('errorDetailsModal'));
    modal.show();
    @endif
}
</script>
@endsection
