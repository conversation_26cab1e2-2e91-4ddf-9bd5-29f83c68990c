<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Admin management fields
            $table->string('avatar')->nullable()->after('email');
            $table->text('bio')->nullable()->after('avatar');
            $table->string('phone')->nullable()->after('bio');
            $table->string('department')->nullable()->after('phone');
            $table->string('position')->nullable()->after('department');
            $table->json('settings')->nullable()->after('permissions');
            $table->timestamp('last_activity_at')->nullable()->after('last_login_ip');
            $table->boolean('is_super_admin')->default(false)->after('is_admin');
            $table->boolean('can_manage_admins')->default(false)->after('is_super_admin');
            $table->boolean('email_verified')->default(false)->after('can_manage_admins');
            $table->timestamp('suspended_at')->nullable()->after('force_password_change');
            $table->string('suspended_reason')->nullable()->after('suspended_at');
            $table->foreignId('suspended_by')->nullable()->constrained('users')->onDelete('set null')->after('suspended_reason');
            $table->foreignId('created_by_admin')->nullable()->constrained('users')->onDelete('set null')->after('suspended_by');
            
            // Indexes for better performance
            $table->index(['user_type', 'is_active']);
            $table->index(['is_admin', 'is_active']);
            $table->index(['is_super_admin']);
            $table->index(['last_activity_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['suspended_by']);
            $table->dropForeign(['created_by_admin']);
            
            $table->dropIndex(['user_type', 'is_active']);
            $table->dropIndex(['is_admin', 'is_active']);
            $table->dropIndex(['is_super_admin']);
            $table->dropIndex(['last_activity_at']);
            
            $table->dropColumn([
                'avatar',
                'bio',
                'phone',
                'department',
                'position',
                'settings',
                'last_activity_at',
                'is_super_admin',
                'can_manage_admins',
                'email_verified',
                'suspended_at',
                'suspended_reason',
                'suspended_by',
                'created_by_admin'
            ]);
        });
    }
};
