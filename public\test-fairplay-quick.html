<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار سريع - FairPlay DRM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .btn.success { background: #4CAF50; }
        .btn.info { background: #2196F3; }
        .btn.warning { background: #ff9800; }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 14px;
        }
        
        .log {
            background: #000;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        
        h1 { color: #ff6b35; }
        h3 { color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍎 اختبار سريع - FairPlay DRM</h1>
        
        <div class="card">
            <h3>📝 بيانات الاختبار</h3>
            <div class="input-group">
                <input type="text" id="contentId" value="****************" placeholder="Asset ID">
            </div>
            <div class="input-group">
                <input type="text" id="country" value="EG" placeholder="Country Code">
            </div>
        </div>
        
        <div class="card">
            <h3>🚀 اختبارات سريعة</h3>
            <button class="btn" onclick="testAPI()">🔗 اختبار API</button>
            <button class="btn success" onclick="getHLS()">📺 جلب HLS</button>
            <button class="btn info" onclick="getDRM()">🔐 جلب DRM</button>
            <button class="btn warning" onclick="completeSetup()">🎯 إعداد كامل</button>
            <button class="btn" onclick="clearLog()" style="background: #666;">🗑️ مسح</button>
        </div>
        
        <div class="card">
            <h3>📊 النتائج</h3>
            <div class="log" id="log"></div>
        </div>
    </div>

    <script>
        const log = document.getElementById('log');
        
        function addLog(type, message) {
            const time = new Date().toLocaleTimeString();
            log.textContent += `[${time}] ${type.toUpperCase()}: ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.textContent = '';
            addLog('info', 'تم مسح السجل');
        }
        
        function getData() {
            return {
                content_id: document.getElementById('contentId').value,
                country: document.getElementById('country').value
            };
        }
        
        async function testAPI() {
            addLog('info', 'اختبار اتصال API...');
            
            try {
                const response = await fetch('/fairplay/api/health');
                const result = await response.json();
                
                if (result.success) {
                    addLog('success', 'API يعمل بشكل طبيعي ✅');
                    addLog('info', `FairPlay Support: ${result.health.fairplay_support}`);
                    addLog('info', `Device File: ${result.health.device_file_exists}`);
                } else {
                    addLog('error', 'API لا يعمل ❌');
                }
            } catch (error) {
                addLog('error', `خطأ في API: ${error.message}`);
            }
        }
        
        async function getHLS() {
            addLog('info', 'جلب رابط HLS...');
            
            const data = getData();
            
            try {
                const response = await fetch('/fairplay/api/stream-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('success', 'تم جلب HLS بنجاح ✅');
                    addLog('info', `URL: ${result.data.hls_url.substring(0, 80)}...`);
                    
                    if (result.data.playout_data) {
                        const playout = result.data.playout_data;
                        addLog('info', `Duration: ${playout.durationSeconds}s`);
                        addLog('info', `DRM: ${playout.drm ? 'Yes' : 'No'}`);
                        addLog('info', `HD: ${playout.hd ? 'Yes' : 'No'}`);
                    }
                } else {
                    addLog('error', `فشل جلب HLS: ${result.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        async function getDRM() {
            addLog('info', 'جلب معلومات DRM...');
            
            const data = getData();
            
            try {
                const response = await fetch('/fairplay/api/drm-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('success', 'تم جلب DRM بنجاح ✅');
                    const drm = result.data;
                    addLog('info', `Type: ${drm.drm_type}`);
                    addLog('info', `Certificate: ${drm.certificate_url ? 'Available' : 'Missing'}`);
                    addLog('info', `License: ${drm.license_url ? 'Available' : 'Missing'}`);
                    addLog('info', `Brand GUID: ${drm.brand_guid}`);
                } else {
                    addLog('error', `فشل جلب DRM: ${result.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        async function completeSetup() {
            addLog('info', 'إنشاء إعداد كامل...');
            
            const data = getData();
            
            try {
                const response = await fetch('/fairplay/api/complete-setup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('success', 'إعداد كامل جاهز! 🎉');
                    const setup = result.data;
                    addLog('success', `✅ HLS URL: Ready`);
                    addLog('success', `✅ DRM Info: Ready`);
                    addLog('success', `✅ Player Config: Ready`);
                    addLog('info', `Certificate: ${setup.drm_info.certificate_url}`);
                    addLog('info', `License: ${setup.drm_info.license_url.substring(0, 80)}...`);
                    addLog('success', '🍎 جاهز للاستخدام مع iOS!');
                } else {
                    addLog('error', `فشل الإعداد: ${result.error}`);
                    if (result.data && result.data.step) {
                        addLog('error', `فشل في: ${result.data.step}`);
                    }
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        function getCSRFToken() {
            const meta = document.querySelector('meta[name="csrf-token"]');
            if (meta) {
                return meta.getAttribute('content');
            }
            
            // Try to get from cookie
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'XSRF-TOKEN') {
                    return decodeURIComponent(value);
                }
            }
            
            return 'test-token'; // Fallback
        }
        
        // Auto-start
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', 'صفحة الاختبار السريع جاهزة');
            setTimeout(testAPI, 500);
        });
    </script>
</body>
</html>
