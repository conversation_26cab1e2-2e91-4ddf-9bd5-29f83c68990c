<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 8px; }
        .success { border-color: #4CAF50; background: rgba(76, 175, 80, 0.1); }
        .error { border-color: #f44336; background: rgba(244, 67, 54, 0.1); }
        .warning { border-color: #ff9800; background: rgba(255, 152, 0, 0.1); }
        #player { width: 100%; height: 400px; background: #000; margin: 20px 0; }
        .log { background: #2a2a2a; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔧 Player Debug Test</h1>
    
    <div class="test-section" id="file-check">
        <h3>📁 File Availability Check</h3>
        <div id="file-results"></div>
    </div>

    <div class="test-section" id="jwplayer-check">
        <h3>🎬 JW Player Loading Check</h3>
        <div id="jwplayer-results"></div>
    </div>

    <div class="test-section">
        <h3>🎥 Player Test</h3>
        <div id="player"></div>
    </div>

    <div class="test-section">
        <h3>📋 Console Log</h3>
        <div class="log" id="console-log"></div>
    </div>

    <script>
        // Console logging
        const logDiv = document.getElementById('console-log');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div style="color: ${type === 'error' ? '#f44336' : type === 'warn' ? '#ff9800' : '#4CAF50'}">[${timestamp}] ${type.toUpperCase()}: ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('log', args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('error', args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('warn', args.join(' '));
        };

        // File availability check
        async function checkFiles() {
            const files = [
                '/player/js/jwplayer.js',
                '/player/player_original.js',
                '/player/player-config.js'
            ];

            const fileResults = document.getElementById('file-results');
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const status = response.ok ? 'success' : 'error';
                    const statusText = response.ok ? '✅ Available' : `❌ Error ${response.status}`;
                    fileResults.innerHTML += `<div class="${status}">${file}: ${statusText}</div>`;
                } catch (error) {
                    fileResults.innerHTML += `<div class="error">${file}: ❌ Network Error - ${error.message}</div>`;
                }
            }
        }

        // JW Player check
        function checkJWPlayer() {
            const jwResults = document.getElementById('jwplayer-results');
            
            // Check if jwplayer is loaded
            if (typeof jwplayer !== 'undefined') {
                jwResults.innerHTML += '<div class="success">✅ JW Player object is available</div>';
                jwResults.innerHTML += `<div class="success">📦 JW Player version: ${jwplayer.version || 'Unknown'}</div>`;
            } else {
                jwResults.innerHTML += '<div class="error">❌ JW Player object not found</div>';
                return;
            }

            // Try to create a player instance
            try {
                const testPlayer = jwplayer('player');
                jwResults.innerHTML += '<div class="success">✅ Player instance created successfully</div>';
                
                // Test basic setup
                testPlayer.setup({
                    file: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                    width: '100%',
                    height: '400px'
                }).on('ready', function() {
                    jwResults.innerHTML += '<div class="success">✅ Player setup completed successfully</div>';
                }).on('setupError', function(error) {
                    jwResults.innerHTML += `<div class="error">❌ Setup Error: ${error.message}</div>`;
                }).on('error', function(error) {
                    jwResults.innerHTML += `<div class="error">❌ Player Error: ${error.message}</div>`;
                });
                
            } catch (error) {
                jwResults.innerHTML += `<div class="error">❌ Failed to create player: ${error.message}</div>`;
            }
        }

        // Load JW Player script
        function loadJWPlayer() {
            console.log('🔄 Loading JW Player...');
            
            const script = document.createElement('script');
            script.src = '/player/js/jwplayer.js';
            script.onload = function() {
                console.log('✅ JW Player script loaded');
                checkJWPlayer();
            };
            script.onerror = function() {
                console.error('❌ Failed to load JW Player script');
                document.getElementById('jwplayer-results').innerHTML += '<div class="error">❌ Failed to load JW Player script</div>';
            };
            
            document.head.appendChild(script);
        }

        // Initialize tests
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Starting debug tests...');
            checkFiles();
            loadJWPlayer();
        });
    </script>
</body>
</html>
