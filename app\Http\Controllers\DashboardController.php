<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Content;
use App\Models\User;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show dashboard
     */
    public function index()
    {
        $user = Auth::guard('web')->user();

        // Check if user is authenticated
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please login to access dashboard');
        }

        $userData = json_decode($user->user_info ?? '{}', true);

        // Get active subscription
        $activeSubscription = \App\Models\UserSubscription::where('user_id', $user->id)
            ->where('status', 'active')
            ->where('starts_at', '<=', now())
            ->where('expires_at', '>', now())
            ->with('subscriptionPlan')
            ->first();

        // Calculate remaining days and subscription status
        $remainingDays = 0;
        $subscriptionStatus = "غير مشترك";
        $statusColor = "secondary";
        $subscriptionPlan = "غير محدد";

        if ($activeSubscription) {
            // Use active subscription data
            $subscriptionPlan = $activeSubscription->subscriptionPlan->name ?? 'Unknown Plan';
            $remainingDays = $activeSubscription->remaining_days;

            if ($remainingDays > 30) {
                $subscriptionStatus = "Active";
                $statusColor = "success";
            } elseif ($remainingDays > 7) {
                $subscriptionStatus = "Expires Soon";
                $statusColor = "warning";
            } elseif ($remainingDays > 0) {
                $subscriptionStatus = "Expires Very Soon";
                $statusColor = "danger";
            } else {
                $subscriptionStatus = "Expired";
                $statusColor = "danger";
            }
        } elseif ($user->subscription_expiry) {
            // Fallback to old subscription system
            $expiryDate = $user->subscription_expiry;
            $remainingDays = now()->diffInDays($expiryDate, false);

            // Get subscription plan from user_info
            if (!empty($userData['subscription_plan'])) {
                $subscriptionPlan = $userData['subscription_plan'];
            } elseif (!empty($userData['plan'])) {
                $subscriptionPlan = $userData['plan'];
            } else {
                $subscriptionPlan = "باقة أساسية";
            }

            if ($remainingDays > 0) {
                if ($remainingDays > 30) {
                    $subscriptionStatus = "Active";
                    $statusColor = "success";
                } elseif ($remainingDays > 7) {
                    $subscriptionStatus = "Expires Soon";
                    $statusColor = "warning";
                } else {
                    $subscriptionStatus = "Expires Very Soon";
                    $statusColor = "danger";
                }
            } else {
                $subscriptionStatus = "Expired";
                $statusColor = "danger";
                $remainingDays = 0;
            }
        } else {
            // Check if user has valid subscription info in user_info
            if (!empty($userData['subscription_plan']) || !empty($userData['plan'])) {
                $subscriptionPlan = $userData['subscription_plan'] ?? $userData['plan'] ?? "باقة مجانية";
                $subscriptionStatus = "نشط (غير محدود)";
                $statusColor = "success";
                $remainingDays = 999; // Unlimited
            }
        }

        // Statistics
        $stats = [
            'total_content' => Content::count(),
            'movies_count' => Content::where('type', 'movie')->count(),
            'series_count' => Content::where('type', 'series')->count(),
            'coming_soon_count' => Content::where('is_coming_soon', true)->count(),
        ];

        // Shahid API Status
        $shahidService = new \App\Services\ShahidService();
        $shahidStatus = [
            'connected' => $shahidService->hasValidToken(),
            'token_status' => $shahidService->hasValidToken() ? 'متصل' : 'غير متصل',
            'status_color' => $shahidService->hasValidToken() ? 'success' : 'danger'
        ];

        // System info
        $systemInfo = [
            'platform' => PHP_OS,
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        ];

        // Recent activity
        $recentActivity = [
            [
                'action' => 'تسجيل دخول',
                'time' => $user->login_time,
                'icon' => 'fas fa-sign-in-alt',
                'color' => 'success'
            ]
        ];

        // Startup status - only show if there's an issue or important info
        $startupStatus = null;

        if ($remainingDays == 999) {
            // Unlimited subscription - show success message
            $startupStatus = [
                'title' => 'اشتراك نشط',
                'description' => "اشتراكك في باقة {$subscriptionPlan} نشط ومفعل بنجاح.",
                'icon' => '<i class="fas fa-check-circle"></i>',
                'color' => 'success'
            ];
        } elseif ($remainingDays > 30) {
            // Active subscription with more than 30 days - no need to show alert
            $startupStatus = null;
        } elseif ($remainingDays > 7) {
            // Expiring soon - show warning
            $startupStatus = [
                'title' => 'Notice: Subscription Expires Soon',
                'description' => "Your {$subscriptionPlan} subscription will expire in {$remainingDays} day(s). Please renew to avoid service interruption.",
                'icon' => '<i class="fas fa-exclamation-triangle"></i>',
                'color' => 'warning'
            ];
        } elseif ($remainingDays > 0) {
            // Expiring very soon - show danger
            $startupStatus = [
                'title' => 'Warning: Subscription Expires Very Soon',
                'description' => "Your {$subscriptionPlan} subscription will expire in only {$remainingDays} day(s)! Please renew immediately.",
                'icon' => '<i class="fas fa-exclamation-circle"></i>',
                'color' => 'danger'
            ];
        } elseif ($subscriptionStatus === "Expired") {
            // Expired subscription
            $startupStatus = [
                'title' => 'Subscription Expired',
                'description' => "Your {$subscriptionPlan} subscription has expired. Please renew to continue using the service.",
                'icon' => '<i class="fas fa-times-circle"></i>',
                'color' => 'danger'
            ];
        } elseif ($subscriptionStatus === "غير مشترك") {
            // No subscription
            $startupStatus = [
                'title' => 'لا يوجد اشتراك نشط',
                'description' => 'لم يتم العثور على اشتراك نشط. يرجى الاشتراك في إحدى الباقات للاستمتاع بالخدمة.',
                'icon' => '<i class="fas fa-info-circle"></i>',
                'color' => 'info'
            ];
        }

        return view('dashboard.index', compact(
            'userData',
            'remainingDays',
            'subscriptionStatus',
            'statusColor',
            'subscriptionPlan',
            'stats',
            'systemInfo',
            'recentActivity',
            'startupStatus',
            'shahidStatus'
        ));
    }

    /**
     * Show system info page
     */
    public function systemInfo()
    {
        $systemDetails = [
            'platform' => PHP_OS,
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'server_admin' => $_SERVER['SERVER_ADMIN'] ?? 'Unknown',
        ];

        // Memory info (simplified for web environment)
        $memoryInfo = [
            'memory_limit' => ini_get('memory_limit'),
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . ' MB',
        ];

        // Disk info (simplified)
        $diskInfo = [
            'free_space' => disk_free_space('.') ? round(disk_free_space('.') / 1024 / 1024 / 1024, 2) . ' GB' : 'Unknown',
            'total_space' => disk_total_space('.') ? round(disk_total_space('.') / 1024 / 1024 / 1024, 2) . ' GB' : 'Unknown',
        ];

        return view('dashboard.system-info', compact(
            'systemDetails',
            'memoryInfo',
            'diskInfo'
        ));
    }
}
