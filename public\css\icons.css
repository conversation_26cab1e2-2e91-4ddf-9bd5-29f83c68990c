/* Local Icon Fallbacks for Admin Panel */

/* Base icon styles */
.icon-fallback {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    margin-right: 0.5rem;
}

/* Admin Panel Icons */
.icon-crown::before { content: '👑'; }
.icon-dashboard::before { content: '📊'; }
.icon-users::before { content: '👥'; }
.icon-tags::before { content: '🏷️'; }
.icon-credit-card::before { content: '💳'; }
.icon-external-link::before { content: '🔗'; }
.icon-logout::before { content: '🚪'; }
.icon-success::before { content: '✅'; }
.icon-warning::before { content: '⚠️'; }
.icon-info::before { content: 'ℹ️'; }
.icon-plus::before { content: '➕'; }
.icon-edit::before { content: '✏️'; }
.icon-trash::before { content: '🗑️'; }
.icon-eye::before { content: '👁️'; }
.icon-search::before { content: '🔍'; }
.icon-settings::before { content: '⚙️'; }
.icon-home::before { content: '🏠'; }
.icon-user::before { content: '👤'; }
.icon-email::before { content: '📧'; }
.icon-phone::before { content: '📞'; }
.icon-calendar::before { content: '📅'; }
.icon-clock::before { content: '🕐'; }
.icon-star::before { content: '⭐'; }
.icon-heart::before { content: '❤️'; }
.icon-download::before { content: '⬇️'; }
.icon-upload::before { content: '⬆️'; }
.icon-save::before { content: '💾'; }
.icon-print::before { content: '🖨️'; }
.icon-share::before { content: '📤'; }
.icon-lock::before { content: '🔒'; }
.icon-unlock::before { content: '🔓'; }
.icon-key::before { content: '🔑'; }
.icon-shield::before { content: '🛡️'; }
.icon-check::before { content: '✓'; }
.icon-times::before { content: '✕'; }
.icon-arrow-left::before { content: '←'; }
.icon-arrow-right::before { content: '→'; }
.icon-arrow-up::before { content: '↑'; }
.icon-arrow-down::before { content: '↓'; }

/* FontAwesome fallback mapping */
.fas.fa-crown, .fa.fa-crown { 
    font-family: inherit !important;
}
.fas.fa-crown::before, .fa.fa-crown::before { 
    content: '👑'; 
}

.fas.fa-tachometer-alt, .fa.fa-tachometer-alt { 
    font-family: inherit !important;
}
.fas.fa-tachometer-alt::before, .fa.fa-tachometer-alt::before { 
    content: '📊'; 
}

.fas.fa-users, .fa.fa-users { 
    font-family: inherit !important;
}
.fas.fa-users::before, .fa.fa-users::before { 
    content: '👥'; 
}

.fas.fa-tags, .fa.fa-tags { 
    font-family: inherit !important;
}
.fas.fa-tags::before, .fa.fa-tags::before { 
    content: '🏷️'; 
}

.fas.fa-credit-card, .fa.fa-credit-card { 
    font-family: inherit !important;
}
.fas.fa-credit-card::before, .fa.fa-credit-card::before { 
    content: '💳'; 
}

.fas.fa-external-link-alt, .fa.fa-external-link-alt { 
    font-family: inherit !important;
}
.fas.fa-external-link-alt::before, .fa.fa-external-link-alt::before { 
    content: '🔗'; 
}

.fas.fa-sign-out-alt, .fa.fa-sign-out-alt { 
    font-family: inherit !important;
}
.fas.fa-sign-out-alt::before, .fa.fa-sign-out-alt::before { 
    content: '🚪'; 
}

.fas.fa-check-circle, .fa.fa-check-circle { 
    font-family: inherit !important;
}
.fas.fa-check-circle::before, .fa.fa-check-circle::before { 
    content: '✅'; 
}

.fas.fa-exclamation-circle, .fa.fa-exclamation-circle { 
    font-family: inherit !important;
}
.fas.fa-exclamation-circle::before, .fa.fa-exclamation-circle::before { 
    content: '⚠️'; 
}

.fas.fa-info-circle, .fa.fa-info-circle { 
    font-family: inherit !important;
}
.fas.fa-info-circle::before, .fa.fa-info-circle::before { 
    content: 'ℹ️'; 
}

.fas.fa-plus, .fa.fa-plus { 
    font-family: inherit !important;
}
.fas.fa-plus::before, .fa.fa-plus::before { 
    content: '➕'; 
}

.fas.fa-edit, .fa.fa-edit { 
    font-family: inherit !important;
}
.fas.fa-edit::before, .fa.fa-edit::before { 
    content: '✏️'; 
}

.fas.fa-trash, .fa.fa-trash { 
    font-family: inherit !important;
}
.fas.fa-trash::before, .fa.fa-trash::before { 
    content: '🗑️'; 
}

.fas.fa-eye, .fa.fa-eye { 
    font-family: inherit !important;
}
.fas.fa-eye::before, .fa.fa-eye::before { 
    content: '👁️'; 
}

.fas.fa-search, .fa.fa-search { 
    font-family: inherit !important;
}
.fas.fa-search::before, .fa.fa-search::before { 
    content: '🔍'; 
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .icon-fallback {
        margin-right: 0.25rem;
    }
}
