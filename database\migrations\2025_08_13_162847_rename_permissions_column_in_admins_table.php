<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            // Add new column
            $table->text('legacy_permissions')->nullable();
        });

        // Copy data from old column to new column
        DB::statement('UPDATE admins SET legacy_permissions = permissions');

        Schema::table('admins', function (Blueprint $table) {
            // Drop old column
            $table->dropColumn('permissions');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            // Add back old column
            $table->text('permissions')->nullable();
        });

        // Copy data back
        DB::statement('UPDATE admins SET permissions = legacy_permissions');

        Schema::table('admins', function (Blueprint $table) {
            // Drop new column
            $table->dropColumn('legacy_permissions');
        });
    }
};
