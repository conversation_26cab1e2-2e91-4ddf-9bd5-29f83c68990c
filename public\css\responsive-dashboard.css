/*
===========================================
Shahid <PERSON> - Responsive Dashboard CSS
===========================================
Complete responsive design for all devices
Desktop | Laptop | Tablet | Mobile | iPhone | iPad
===========================================
*/

/* ===== ROOT VARIABLES ===== */
:root {
    /* Colors */
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, #007bff, #0056b3);
    --gradient-success: linear-gradient(45deg, #28a745, #20c997);
    --gradient-danger: linear-gradient(45deg, #dc3545, #c82333);
    --gradient-play: linear-gradient(45deg, #ff6b6b, #ee5a24);
    --gradient-dark: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    --gradient-hover: linear-gradient(to top, rgba(255, 107, 107, 0.8), transparent);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
    --shadow-xl: 0 12px 24px rgba(0,0,0,0.25);
    --shadow-hover: 0 15px 35px rgba(0,0,0,0.2);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-round: 50%;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

/* ===== CONTAINER SYSTEM ===== */
.container-fluid {
    width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
    margin-right: auto;
    margin-left: auto;
}

/* ===== CARD SYSTEM ===== */
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: none;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-8px);
}

/* ===== MOVIE & SERIES CARDS ===== */
.movie-card,
.series-card {
    height: auto;
    cursor: pointer;
    transition: all var(--transition-normal);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: white;
}

.movie-card:hover,
.series-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
}

/* ===== POSTER CONTAINERS ===== */
.movie-poster-container,
.series-poster-container {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    aspect-ratio: 2/3;
}

.movie-poster-container img,
.series-poster-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all var(--transition-normal);
}

.movie-poster-container:hover img,
.series-poster-container:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.1);
}

/* ===== GRADIENT OVERLAYS ===== */
.bg-gradient-dark {
    background: var(--gradient-dark);
    transition: all var(--transition-normal);
}

.movie-poster-container:hover .bg-gradient-dark,
.series-poster-container:hover .bg-gradient-dark {
    background: var(--gradient-hover);
}

/* ===== PLAY OVERLAY SYSTEM ===== */
.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-normal);
    backdrop-filter: blur(2px);
    z-index: 2;
}

.movie-poster-container:hover .play-overlay,
.series-poster-container:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: var(--gradient-play);
    border-radius: var(--radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.8);
    transition: all var(--transition-normal);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.movie-poster-container:hover .play-button,
.series-poster-container:hover .play-button {
    transform: scale(1);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
    animation: pulse 2s infinite;
}

.play-button i {
    color: white;
    font-size: 28px;
    margin-left: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0% { box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4); }
    50% { box-shadow: 0 12px 35px rgba(255, 107, 107, 0.8); }
    100% { box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* ===== BADGES & LABELS ===== */
.badge {
    backdrop-filter: blur(10px);
    background: rgba(0,0,0,0.7) !important;
    transition: all var(--transition-normal);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.movie-poster-container:hover .badge,
.series-poster-container:hover .badge {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(15px);
}

/* ===== TITLES & TEXT ===== */
.movie-title,
.series-title {
    transition: all var(--transition-normal);
    font-size: 1rem;
    line-height: 1.3;
    font-weight: 600;
}

.movie-poster-container:hover .movie-title,
.series-poster-container:hover .series-title {
    transform: translateY(-2px);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    font-weight: 700;
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all var(--transition-normal);
    border: none;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* ===== LOADING STATES ===== */
.spinner-border {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ALERTS ===== */
.alert {
    border-radius: var(--radius-md);
    border: none;
    animation: fadeIn 0.5s ease;
}

/* ===== MODALS ===== */
.modal-content {
    border-radius: var(--radius-xl);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* ===== EXTRA LARGE SCREENS (1400px+) ===== */
@media (min-width: 1400px) {
    .container-fluid {
        max-width: 1320px;
        padding-right: var(--spacing-xl);
        padding-left: var(--spacing-xl);
    }

    .col-xxl-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }

    .play-button {
        width: 90px;
        height: 90px;
    }

    .play-button i {
        font-size: 32px;
    }

    .movie-title,
    .series-title {
        font-size: 1.1rem;
    }
}

/* ===== LARGE SCREENS - DESKTOP (1200px - 1399px) ===== */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container-fluid {
        max-width: 1140px;
    }

    .col-xl-3 {
        flex: 0 0 auto;
        width: 25%;
    }

    .play-button {
        width: 80px;
        height: 80px;
    }

    .play-button i {
        font-size: 28px;
    }
}

/* ===== MEDIUM SCREENS - LAPTOP (992px - 1199px) ===== */
@media (min-width: 992px) and (max-width: 1199px) {
    .container-fluid {
        max-width: 960px;
        padding-right: var(--spacing-lg);
        padding-left: var(--spacing-lg);
    }

    .col-lg-3 {
        flex: 0 0 auto;
        width: 25%;
    }

    .col-lg-4 {
        flex: 0 0 auto;
        width: 33.33333333%;
    }

    .play-button {
        width: 70px;
        height: 70px;
    }

    .play-button i {
        font-size: 24px;
    }

    .movie-title,
    .series-title {
        font-size: 0.95rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* ===== SMALL SCREENS - TABLET (768px - 991px) ===== */
@media (min-width: 768px) and (max-width: 991px) {
    .container-fluid {
        max-width: 720px;
        padding-right: var(--spacing-md);
        padding-left: var(--spacing-md);
    }

    .col-md-4 {
        flex: 0 0 auto;
        width: 33.33333333%;
    }

    .col-md-6 {
        flex: 0 0 auto;
        width: 50%;
    }

    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 2/3;
    }

    .play-button {
        width: 60px;
        height: 60px;
    }

    .play-button i {
        font-size: 20px;
    }

    .movie-title,
    .series-title {
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }

    /* Header adjustments */
    .h3 {
        font-size: 1.5rem;
    }

    /* Button adjustments */
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* ===== EXTRA SMALL SCREENS - MOBILE LARGE (576px - 767px) ===== */
@media (min-width: 576px) and (max-width: 767px) {
    .container-fluid {
        max-width: 540px;
        padding-right: var(--spacing-sm);
        padding-left: var(--spacing-sm);
    }

    .col-sm-6 {
        flex: 0 0 auto;
        width: 50%;
    }

    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 2/3;
    }

    .play-button {
        width: 50px;
        height: 50px;
    }

    .play-button i {
        font-size: 18px;
    }

    .movie-title,
    .series-title {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .badge {
        font-size: 0.6rem;
        padding: 0.1rem 0.25rem;
    }

    /* Header adjustments */
    .h3 {
        font-size: 1.3rem;
    }

    /* Button adjustments */
    .btn {
        padding: 0.35rem 0.7rem;
        font-size: 0.85rem;
    }

    /* Spacing adjustments */
    .mb-4 {
        margin-bottom: var(--spacing-lg) !important;
    }

    .p-3 {
        padding: var(--spacing-sm) !important;
    }
}

/* ===== MOBILE SMALL SCREENS (أقل من 576px) ===== */
@media (max-width: 575px) {
    .container-fluid {
        padding-right: var(--spacing-xs);
        padding-left: var(--spacing-xs);
    }

    /* Single column layout for very small screens */
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        flex: 0 0 auto;
        width: 100%;
        margin-bottom: var(--spacing-md);
    }

    /* Adjust card for mobile */
    .movie-card,
    .series-card {
        margin-bottom: var(--spacing-md);
    }

    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 16/9; /* Wider aspect ratio for mobile */
        max-height: 200px;
    }

    .play-button {
        width: 45px;
        height: 45px;
    }

    .play-button i {
        font-size: 16px;
    }

    .movie-title,
    .series-title {
        font-size: 0.8rem;
        line-height: 1.1;
    }

    .badge {
        font-size: 0.55rem;
        padding: 0.05rem 0.2rem;
    }

    /* Header adjustments */
    .h3 {
        font-size: 1.2rem;
    }

    /* Button adjustments */
    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Form adjustments */
    .form-control,
    .form-select {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }

    /* Modal adjustments */
    .modal-dialog {
        margin: var(--spacing-sm);
    }

    /* Alert adjustments */
    .alert {
        padding: var(--spacing-sm);
        font-size: 0.85rem;
    }

    /* Grid spacing */
    .g-4 {
        --bs-gutter-x: 0.5rem;
        --bs-gutter-y: 0.5rem;
    }
}

/* ===== iPhone SPECIFIC STYLES ===== */

/* iPhone SE, 5s, 5c, 5 (320px) */
@media only screen and (max-width: 320px) {
    .container-fluid {
        padding-right: 0.25rem;
        padding-left: 0.25rem;
    }

    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 16/9;
        max-height: 180px;
    }

    .play-button {
        width: 40px;
        height: 40px;
    }

    .play-button i {
        font-size: 14px;
    }

    .movie-title,
    .series-title {
        font-size: 0.75rem;
    }

    .h3 {
        font-size: 1.1rem;
    }
}

/* iPhone 12 mini, 13 mini (375px) */
@media only screen and (min-width: 321px) and (max-width: 375px) {
    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 3/2;
        max-height: 190px;
    }
}

/* iPhone 12, 12 Pro, 13, 13 Pro (390px) */
@media only screen and (min-width: 376px) and (max-width: 390px) {
    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 3/2;
        max-height: 200px;
    }
}

/* iPhone 12 Pro Max, 13 Pro Max (428px) */
@media only screen and (min-width: 391px) and (max-width: 428px) {
    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 3/2;
        max-height: 210px;
    }
}

/* ===== iPad SPECIFIC STYLES ===== */

/* iPad Mini (768px) */
@media only screen and (min-width: 768px) and (max-width: 768px) {
    .col-md-4 {
        width: 33.33333333%;
    }

    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 2/3;
    }
}

/* iPad (820px) */
@media only screen and (min-width: 769px) and (max-width: 820px) {
    .col-md-4 {
        width: 33.33333333%;
    }
}

/* iPad Pro 11" (834px) */
@media only screen and (min-width: 821px) and (max-width: 834px) {
    .col-md-3 {
        width: 25%;
    }
}

/* iPad Pro 12.9" (1024px) */
@media only screen and (min-width: 1024px) and (max-width: 1024px) {
    .col-lg-3 {
        width: 20%;
    }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .movie-poster-container,
    .series-poster-container {
        aspect-ratio: 16/9;
        max-height: 150px;
    }

    .play-button {
        width: 40px;
        height: 40px;
    }

    .play-button i {
        font-size: 16px;
    }

    .movie-title,
    .series-title {
        font-size: 0.8rem;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce animations on low-end devices */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* GPU acceleration for smooth animations */
.movie-card,
.series-card,
.play-button,
.movie-poster-container img,
.series-poster-container img {
    will-change: transform;
    transform: translateZ(0);
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus states */
.movie-poster-container:focus,
.series-poster-container:focus,
.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .badge {
        background: black !important;
        color: white !important;
        border: 1px solid white;
    }

    .play-button {
        border: 3px solid white;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .play-overlay,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }

    .movie-card,
    .series-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #ffffff;
    }

    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .card {
        background: #2d2d2d;
        color: #ffffff;
    }

    .badge {
        background: rgba(255,255,255,0.2) !important;
        color: white !important;
    }
}

/* ===== UTILITY CLASSES ===== */

/* Responsive text sizes */
.text-responsive {
    font-size: clamp(0.8rem, 2vw, 1.2rem);
}

/* Responsive spacing */
.spacing-responsive {
    padding: clamp(0.5rem, 2vw, 2rem);
}

/* Hide on specific devices */
.hide-mobile {
    display: block;
}

.hide-desktop {
    display: none;
}

@media (max-width: 767px) {
    .hide-mobile {
        display: none;
    }

    .hide-desktop {
        display: block;
    }
}

/* ===== LOADING STATES ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== TOUCH IMPROVEMENTS ===== */
@media (hover: none) and (pointer: coarse) {
    /* Touch devices */
    .movie-card:hover,
    .series-card:hover {
        transform: none;
    }

    .play-overlay {
        opacity: 0.8;
        background: rgba(0, 0, 0, 0.4);
    }

    .play-button {
        transform: scale(0.9);
    }

    /* Larger touch targets */
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* ===== FINAL RESPONSIVE GRID SYSTEM ===== */

/* Auto-fit grid for dynamic content */
.responsive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

@media (max-width: 575px) {
    .responsive-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .responsive-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .responsive-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

@media (min-width: 992px) {
    .responsive-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}
