<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class UserSubscriptionController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
        // Allow both web and api middleware groups
        $this->middleware('web')->only(['index', 'store', 'show', 'update', 'destroy', 'toggleStatus', 'extend', 'statistics']);
    }

    /**
     * Display a listing of user subscriptions.
     */
    public function index(Request $request): JsonResponse
    {
        $query = UserSubscription::with(['user:id,name,email', 'subscriptionPlan:id,name,price,currency']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by plan
        if ($request->filled('plan_id')) {
            $query->where('subscription_plan_id', $request->get('plan_id'));
        }

        // Filter by expiry
        if ($request->filled('expiry_filter')) {
            $filter = $request->get('expiry_filter');
            switch ($filter) {
                case 'expired':
                    $query->where('expires_at', '<=', now());
                    break;
                case 'expiring_soon':
                    $query->where('expires_at', '>', now())
                          ->where('expires_at', '<=', now()->addDays(7));
                    break;
                case 'active':
                    $query->where('expires_at', '>', now());
                    break;
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $subscriptions = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $subscriptions,
            'message' => 'User subscriptions retrieved successfully'
        ]);
    }

    /**
     * Store a newly created user subscription.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'starts_at' => 'nullable|date',
            'custom_duration_days' => 'nullable|integer|min:1',
            'amount_paid' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'allowed_applications' => 'nullable|array',
            'max_devices' => 'nullable|integer|min:1',
        ]);

        $plan = SubscriptionPlan::findOrFail($validated['subscription_plan_id']);
        $user = User::findOrFail($validated['user_id']);

        // Check if user already has an active subscription
        $existingActive = $user->subscriptions()
                              ->where('status', 'active')
                              ->where('expires_at', '>', now())
                              ->first();

        if ($existingActive) {
            return response()->json([
                'success' => false,
                'message' => 'User already has an active subscription'
            ], 422);
        }

        $startsAt = $validated['starts_at'] ? Carbon::parse($validated['starts_at']) : now();
        $durationDays = $validated['custom_duration_days'] ?? $plan->duration_days;
        $expiresAt = $startsAt->copy()->addDays($durationDays);

        $subscription = UserSubscription::create([
            'user_id' => $validated['user_id'],
            'subscription_plan_id' => $validated['subscription_plan_id'],
            'starts_at' => $startsAt,
            'expires_at' => $expiresAt,
            'status' => 'active',
            'allowed_applications' => $validated['allowed_applications'] ?? $plan->allowed_applications,
            'max_devices' => $validated['max_devices'] ?? $plan->max_devices,
            'amount_paid' => $validated['amount_paid'] ?? $plan->price,
            'payment_method' => $validated['payment_method'],
            'payment_reference' => $validated['payment_reference'],
            'notes' => $validated['notes'],
            'activated_at' => now(),
            'created_by' => Auth::guard('admin')->id(),
        ]);

        // Update user's legacy fields for backward compatibility
        $user->update([
            'subscription_expiry' => $expiresAt,
            'remaining_days' => $subscription->remaining_days,
            'is_active' => true,
            'application' => in_array('ALL', $subscription->allowed_applications ?? []) ? 'ALL' : 
                           (($subscription->allowed_applications[0] ?? 'SHAHID')),
        ]);

        $subscription->load(['user:id,name,email', 'subscriptionPlan:id,name,price,currency']);

        return response()->json([
            'success' => true,
            'data' => $subscription,
            'message' => 'User subscription created successfully'
        ], 201);
    }

    /**
     * Display the specified user subscription.
     */
    public function show(UserSubscription $userSubscription): JsonResponse
    {
        $userSubscription->load([
            'user:id,name,email,is_active',
            'subscriptionPlan:id,name,description,price,currency,duration_days',
            'creator:id,name',
            'updater:id,name'
        ]);

        return response()->json([
            'success' => true,
            'data' => $userSubscription,
            'message' => 'User subscription retrieved successfully'
        ]);
    }

    /**
     * Update the specified user subscription.
     */
    public function update(Request $request, UserSubscription $userSubscription): JsonResponse
    {
        $validated = $request->validate([
            'expires_at' => 'nullable|date|after:now',
            'status' => 'nullable|in:active,inactive,expired,cancelled',
            'allowed_applications' => 'nullable|array',
            'max_devices' => 'nullable|integer|min:1',
            'amount_paid' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $validated['updated_by'] = Auth::guard('admin')->id();

        $userSubscription->update($validated);

        // Update user's legacy fields for backward compatibility
        if (isset($validated['expires_at']) || isset($validated['status'])) {
            $userSubscription->user->update([
                'subscription_expiry' => $userSubscription->expires_at,
                'remaining_days' => $userSubscription->remaining_days,
                'is_active' => $userSubscription->isActive(),
            ]);
        }

        $userSubscription->load(['user:id,name,email', 'subscriptionPlan:id,name,price,currency']);

        return response()->json([
            'success' => true,
            'data' => $userSubscription,
            'message' => 'User subscription updated successfully'
        ]);
    }

    /**
     * Toggle subscription status.
     */
    public function toggleStatus(UserSubscription $userSubscription): JsonResponse
    {
        $newStatus = $userSubscription->status === 'active' ? 'inactive' : 'active';
        
        $userSubscription->update([
            'status' => $newStatus,
            'updated_by' => Auth::guard('admin')->id(),
            $newStatus === 'active' ? 'activated_at' : 'deactivated_at' => now(),
        ]);

        // Update user's legacy fields
        $userSubscription->user->update([
            'is_active' => $userSubscription->isActive(),
        ]);

        return response()->json([
            'success' => true,
            'data' => $userSubscription,
            'message' => 'Subscription status updated successfully'
        ]);
    }

    /**
     * Extend subscription.
     */
    public function extend(Request $request, UserSubscription $userSubscription): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'required|integer|min:1|max:3650', // Max 10 years
            'notes' => 'nullable|string',
        ]);

        $userSubscription->extend($validated['days']);
        
        if ($validated['notes']) {
            $currentNotes = $userSubscription->notes ?? '';
            $newNote = "\n[" . now()->format('Y-m-d H:i') . "] Extended by {$validated['days']} days: {$validated['notes']}";
            $userSubscription->update([
                'notes' => $currentNotes . $newNote,
                'updated_by' => Auth::guard('admin')->id(),
            ]);
        }

        // Update user's legacy fields
        $userSubscription->user->update([
            'subscription_expiry' => $userSubscription->expires_at,
            'remaining_days' => $userSubscription->remaining_days,
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'data' => $userSubscription,
            'message' => "Subscription extended by {$validated['days']} days successfully"
        ]);
    }

    /**
     * Get subscription statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_subscriptions' => UserSubscription::count(),
            'active_subscriptions' => UserSubscription::where('status', 'active')
                                                     ->where('expires_at', '>', now())
                                                     ->count(),
            'expired_subscriptions' => UserSubscription::where('expires_at', '<=', now())->count(),
            'expiring_soon' => UserSubscription::where('status', 'active')
                                              ->where('expires_at', '>', now())
                                              ->where('expires_at', '<=', now()->addDays(7))
                                              ->count(),
            'revenue_this_month' => UserSubscription::whereMonth('created_at', now()->month)
                                                   ->whereYear('created_at', now()->year)
                                                   ->sum('amount_paid'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Statistics retrieved successfully'
        ]);
    }

    /**
     * Cancel the specified user subscription.
     */
    public function destroy(UserSubscription $userSubscription): JsonResponse
    {
        // Soft delete or mark as cancelled instead of hard delete
        $userSubscription->update([
            'status' => 'cancelled',
            'deactivated_at' => now(),
            'updated_by' => Auth::guard('admin')->id(),
        ]);

        // Update user's legacy fields
        $userSubscription->user->update([
            'is_active' => false,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'User subscription cancelled successfully'
        ]);
    }
}
