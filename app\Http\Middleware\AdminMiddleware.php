<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required'
                ], 401);
            }
            
            return redirect()->route('admin.login');
        }

        $user = Auth::user();

        // Check if user is admin
        if (!$user->isAdmin()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin access required'
                ], 403);
            }
            abort(403, 'Admin access required');
        }

        // Check if user is active
        if (!$user->is_active) {
            Auth::logout();
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account deactivated'
                ], 403);
            }
            return redirect()->route('admin.login')->with('error', 'Your account has been deactivated.');
        }

        // Check if user is suspended
        if ($user->isSuspended()) {
            Auth::logout();
            $message = 'Your account has been suspended.';
            if ($user->suspended_reason) {
                $message .= ' Reason: ' . $user->suspended_reason;
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'suspended' => true
                ], 403);
            }
            return redirect()->route('admin.login')->with('error', $message);
        }

        // Update last activity
        $user->updateLastActivity();

        return $next($request);
    }
}
