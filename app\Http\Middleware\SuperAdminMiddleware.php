<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SuperAdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth('admin')->check()) {
            return redirect()->route('admin.login')->with('error', 'Please login to access this area.');
        }

        $user = auth('admin')->user();

        // Check if user is super admin OR has Super Admin role
        if (!$user->isSuperAdmin() && !$user->roles->contains('name', 'Super Admin')) {
            abort(403, 'Access denied. Super admin privileges required.');
        }

        // Check if user is suspended
        if ($user->isSuspended()) {
            auth('admin')->logout();
            return redirect()->route('admin.login')->with('error', 'Your account has been suspended.');
        }

        // Update last activity
        $user->updateLastActivity();

        return $next($request);
    }
}
