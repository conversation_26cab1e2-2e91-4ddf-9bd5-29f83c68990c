<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار سريع - FairPlay DRM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .btn.success { background: #4CAF50; }
        .btn.info { background: #2196F3; }
        .btn.warning { background: #ff9800; }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 14px;
        }
        
        .log {
            background: #000;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }

        .hls-url {
            background: #1a1a1a;
            border: 1px solid #4CAF50;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            word-break: break-all;
            margin: 10px 0;
            color: #4CAF50;
        }

        .copy-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        
        h1 { color: #ff6b35; }
        h3 { color: #fff; }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #1b5e20; color: #4caf50; }
        .status.error { background: #b71c1c; color: #f44336; }
        .status.warning { background: #e65100; color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍎 اختبار سريع - FairPlay DRM</h1>
        
        <div class="card">
            <h3>📝 بيانات الاختبار</h3>
            <div class="input-group">
                <input type="text" id="contentId" value="**************" placeholder="Asset ID (Episode ID)">
                <select id="contentIdSelect" onchange="selectContentId()" style="margin-left: 10px; padding: 8px; background: #333; color: #fff; border: 1px solid #555;">
                    <option value="">اختر Content ID محفوظ</option>
                    <option value="**************">************** (شغال مؤكد)</option>
                    <option value="9801006461">9801006461 (للاختبار)</option>
                    <option value="69217">69217 (Series ID)</option>
                    <option value="3010042528">3010042528 (للاختبار)</option>
                </select>
            </div>
            <div class="input-group">
                <input type="text" id="country" value="EG" placeholder="Country Code">
            </div>
            <div class="status success" style="background: #1b5e20; color: #4caf50;">
                ✅ تم إصلاح signature string - إزالة drmType من request
            </div>
            <div class="status info" style="background: #1565c0; color: #90caf9;">
                💡 Content ID: ************** (من الـ log الشغال)
            </div>

            <div style="margin-top: 15px; padding: 10px; background: #2e2e2e; border-radius: 5px;">
                <h4 style="color: #4caf50; margin: 0 0 10px 0;">🔍 استخراج Content ID من URL</h4>
                <input type="text" id="shahidUrl" placeholder="https://shahid.mbc.net/ar/episode/..." style="width: 70%; padding: 8px; background: #333; color: #fff; border: 1px solid #555;">
                <button onclick="extractContentId()" style="padding: 8px 15px; background: #ff9800; color: #fff; border: none; border-radius: 3px; margin-left: 10px;">استخراج ID</button>
            </div>
        </div>
        
        <div class="card">
            <h3>🚀 اختبارات سريعة</h3>
            <button class="btn" onclick="testAPI()">🔗 اختبار API</button>
            <button class="btn" onclick="simpleTest()" style="background: #9c27b0;">🧪 اختبار بسيط</button>
            <button class="btn success" onclick="getHLS()">📺 جلب HLS</button>
            <button class="btn info" onclick="getDRM()">🔐 جلب DRM</button>
            <button class="btn warning" onclick="completeSetup()">🎯 إعداد كامل</button>
            <button class="btn" onclick="showPlayerConfig()" style="background: #795548;">📱 Player Config</button>
            <button class="btn" onclick="compareDRM()" style="background: #e91e63;">⚖️ مقارنة DRM</button>
            <button class="btn" onclick="saveContentId()" style="background: #4caf50;">💾 حفظ ID</button>
            <button class="btn" onclick="extractKey()" style="background: #9c27b0;">🔑 استخراج Key</button>
            <button class="btn" onclick="clearLog()" style="background: #666;">🗑️ مسح</button>
        </div>
        
        <div class="card">
            <h3>� الـ Key المستخرج</h3>
            <div id="keyResult" style="background: #1a1a1a; padding: 15px; border-radius: 5px; margin: 10px 0; min-height: 60px; border: 2px dashed #555;">
                <div style="color: #888; text-align: center; padding: 20px;">
                    اضغط "🔑 استخراج Key" بعد الحصول على DRM URLs
                </div>
            </div>
        </div>

        <div class="card">
            <h3>�📊 النتائج</h3>
            <div class="log" id="log"></div>
        </div>
    </div>

    <script>
        const log = document.getElementById('log');
        let currentHlsUrl = null; // Store current HLS URL

        function addLog(type, message) {
            const time = new Date().toLocaleTimeString();
            log.textContent += `[${time}] ${type.toUpperCase()}: ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                addLog('success', 'تم نسخ الرابط إلى الحافظة ✅');
            }, function(err) {
                addLog('error', 'فشل في نسخ الرابط: ' + err);
            });
        }

        function showCopyableUrl(url) {
            const urlDiv = document.createElement('div');
            urlDiv.className = 'hls-url';
            urlDiv.innerHTML = `
                <strong>HLS URL:</strong>
                <button class="copy-btn" onclick="copyToClipboard('${url.replace(/'/g, "\\'")}')">📋 نسخ</button>
                <br><br>
                ${url}
            `;

            // Insert after log
            const logElement = document.getElementById('log');
            const parent = logElement.parentNode;

            // Remove previous URL display if exists
            const existing = parent.querySelector('.hls-url');
            if (existing) {
                existing.remove();
            }

            parent.insertBefore(urlDiv, logElement.nextSibling);
        }
        
        function clearLog() {
            log.textContent = '';
            addLog('info', 'تم مسح السجل');
        }

        function selectContentId() {
            const select = document.getElementById('contentIdSelect');
            const input = document.getElementById('contentId');
            if (select.value) {
                input.value = select.value;
                addLog('info', `تم اختيار Content ID: ${select.value}`);
            }
        }

        function saveContentId() {
            const contentId = document.getElementById('contentId').value.trim();
            if (!contentId) {
                addLog('error', 'يرجى إدخال Content ID أولاً');
                return;
            }

            // Check if already exists
            const select = document.getElementById('contentIdSelect');
            const options = Array.from(select.options);
            const exists = options.some(option => option.value === contentId);

            if (exists) {
                addLog('warning', 'Content ID موجود بالفعل في القائمة');
                return;
            }

            // Add to select
            const option = document.createElement('option');
            option.value = contentId;
            option.textContent = `${contentId} (محفوظ)`;
            select.appendChild(option);

            // Save to localStorage
            let savedIds = JSON.parse(localStorage.getItem('shahid_content_ids') || '[]');
            savedIds.push({
                id: contentId,
                name: `${contentId} (محفوظ)`,
                date: new Date().toISOString()
            });
            localStorage.setItem('shahid_content_ids', JSON.stringify(savedIds));

            addLog('success', `تم حفظ Content ID: ${contentId} ✅`);
        }

        function loadSavedContentIds() {
            const savedIds = JSON.parse(localStorage.getItem('shahid_content_ids') || '[]');
            const select = document.getElementById('contentIdSelect');

            savedIds.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = item.name;
                select.appendChild(option);
            });

            if (savedIds.length > 0) {
                addLog('info', `تم تحميل ${savedIds.length} Content IDs محفوظة`);
            }
        }

        function extractContentId() {
            const url = document.getElementById('shahidUrl').value.trim();
            if (!url) {
                addLog('error', 'يرجى إدخال URL أولاً');
                return;
            }

            addLog('info', 'محاولة استخراج Content ID من URL...');

            // Try different patterns
            const patterns = [
                /\/episode\/(\d+)/,           // /episode/123456
                /\/movie\/(\d+)/,             // /movie/123456
                /\/series\/(\d+)/,            // /series/123456
                /\/(\d{10,})/,                // Any long number
                /id[=:](\d+)/,                // id=123456 or id:123456
                /content[=:](\d+)/,           // content=123456
                /asset[=:](\d+)/              // asset=123456
            ];

            let contentId = null;
            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match) {
                    contentId = match[1];
                    break;
                }
            }

            if (contentId) {
                document.getElementById('contentId').value = contentId;
                addLog('success', `تم استخراج Content ID: ${contentId} ✅`);
                addLog('info', 'يمكنك الآن اختبار هذا الـ ID أو حفظه');
            } else {
                addLog('error', 'لم يتم العثور على Content ID في الـ URL');
                addLog('info', 'تأكد من أن الـ URL يحتوي على رقم الحلقة أو الفيلم');
            }
        }

        async function extractKey() {
            addLog('info', '🔑 بدء استخراج الـ Key...');

            const data = getData();

            try {
                const response = await fetch('/fairplay/api/extract-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    displayKeyResult(result.data);
                    addLog('success', '✅ تم استخراج الـ Key بنجاح!');
                } else {
                    addLog('error', `فشل استخراج الـ Key: ${result.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }

        function displayKeyResult(keyData) {
            const keyResult = document.getElementById('keyResult');

            let html = '<div style="color: #4caf50; font-weight: bold; margin-bottom: 15px;">🔑 تم استخراج الـ Key بنجاح!</div>';

            if (keyData.key) {
                const keyTypeColor = keyData.key_type === 'REAL_KEY' ? '#4caf50' :
                                   keyData.key_type === 'SAMPLE_KEY' ? '#ff9800' : '#f44336';
                const keyTypeIcon = keyData.key_type === 'REAL_KEY' ? '🔑' :
                                  keyData.key_type === 'SAMPLE_KEY' ? '🧪' : '❌';

                html += `
                    <div style="margin-bottom: 10px;">
                        <strong style="color: #ff9800;">🔐 الـ Key:</strong>
                        <span style="color: ${keyTypeColor}; margin-left: 10px;">${keyTypeIcon} ${keyData.key_type || 'UNKNOWN'}</span>
                        <div style="background: #333; padding: 10px; border-radius: 3px; margin-top: 5px; font-family: monospace; word-break: break-all; color: ${keyTypeColor};">
                            ${keyData.key}
                        </div>
                        <button onclick="copyToClipboard('${keyData.key}')" style="margin-top: 5px; padding: 5px 10px; background: #2196f3; color: white; border: none; border-radius: 3px; cursor: pointer;">📋 نسخ</button>
                        ${keyData.note ? `<div style="margin-top: 5px; padding: 8px; background: #2e2e2e; border-radius: 3px; font-size: 12px; color: #ccc;">${keyData.note}</div>` : ''}
                    </div>
                `;
            }

            if (keyData.kid) {
                html += `
                    <div style="margin-bottom: 10px;">
                        <strong style="color: #ff9800;">🆔 KID:</strong>
                        <div style="background: #333; padding: 10px; border-radius: 3px; margin-top: 5px; font-family: monospace; word-break: break-all; color: #4caf50;">
                            ${keyData.kid}
                        </div>
                        <button onclick="copyToClipboard('${keyData.kid}')" style="margin-top: 5px; padding: 5px 10px; background: #2196f3; color: white; border: none; border-radius: 3px; cursor: pointer;">📋 نسخ</button>
                    </div>
                `;
            }

            if (keyData.pssh) {
                html += `
                    <div style="margin-bottom: 10px;">
                        <strong style="color: #ff9800;">📦 PSSH:</strong>
                        <div style="background: #333; padding: 10px; border-radius: 3px; margin-top: 5px; font-family: monospace; word-break: break-all; color: #4caf50;">
                            ${keyData.pssh}
                        </div>
                        <button onclick="copyToClipboard('${keyData.pssh}')" style="margin-top: 5px; padding: 5px 10px; background: #2196f3; color: white; border: none; border-radius: 3px; cursor: pointer;">📋 نسخ</button>
                    </div>
                `;
            }

            html += `
                <div style="margin-top: 15px; padding: 10px; background: #2e2e2e; border-radius: 5px;">
                    <strong style="color: #4caf50;">📱 للاستخدام في التطبيق:</strong>
                    <ol style="margin: 10px 0; padding-left: 20px; color: #ccc;">
                        <li>انسخ الـ Key أعلاه</li>
                        <li>استخدمه مع الـ HLS URL في مشغل الفيديو</li>
                        <li>تأكد من دعم FairPlay DRM في المشغل</li>
                    </ol>
                </div>
            `;

            keyResult.innerHTML = html;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                addLog('success', '📋 تم نسخ النص إلى الحافظة');
            }).catch(() => {
                addLog('error', 'فشل في نسخ النص');
            });
        }

        // Load saved IDs on page load
        window.addEventListener('load', loadSavedContentIds);
        
        function getData() {
            return {
                content_id: document.getElementById('contentId').value,
                country: document.getElementById('country').value
            };
        }
        
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        }
        
        async function testAPI() {
            addLog('info', 'اختبار اتصال API...');

            try {
                const response = await fetch('/fairplay/api/health');
                const result = await response.json();

                if (result.success) {
                    addLog('success', 'API يعمل بشكل طبيعي ✅');
                    addLog('info', `FairPlay Support: ${result.health.fairplay_support}`);
                    addLog('info', `Device File: ${result.health.device_file_exists}`);
                } else {
                    addLog('error', 'API لا يعمل ❌');
                }
            } catch (error) {
                addLog('error', `خطأ في API: ${error.message}`);
            }
        }

        async function simpleTest() {
            addLog('info', 'اختبار بسيط...');

            const data = getData();

            try {
                const response = await fetch('/fairplay/api/simple-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    addLog('success', 'اختبار بسيط نجح ✅');
                    const testData = result.data;
                    addLog('info', `Content ID: ${testData.content_id}`);
                    addLog('info', `Country: ${testData.country}`);
                    addLog('info', `Has Token: ${testData.has_token ? 'Yes' : 'No'}`);
                    addLog('info', `Token Length: ${testData.token_length}`);

                    if (testData.has_token) {
                        addLog('success', '🔑 Token موجود - يمكن المتابعة');
                    } else {
                        addLog('error', '❌ Token مفقود - تحقق من الإعدادات');
                    }
                } else {
                    addLog('error', `فشل الاختبار البسيط: ${result.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        async function getHLS() {
            addLog('info', 'جلب رابط HLS...');
            
            const data = getData();
            addLog('info', `Content ID: ${data.content_id}`);
            
            try {
                const response = await fetch('/fairplay/api/stream-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    currentHlsUrl = result.data.hls_url; // Store for later use
                    addLog('success', 'تم جلب HLS بنجاح ✅');
                    addLog('info', '=== FULL HLS URL ===');
                    addLog('info', result.data.hls_url);
                    addLog('info', '=== END HLS URL ===');
                    addLog('info', `URL Length: ${result.data.hls_url.length} characters`);

                    // Show copyable URL
                    showCopyableUrl(result.data.hls_url);

                    // Parse URL to show domain
                    try {
                        const url = new URL(result.data.hls_url);
                        addLog('info', `Domain: ${url.hostname}`);
                        addLog('info', `Path: ${url.pathname}`);
                        if (url.search) {
                            addLog('info', `Query params: ${url.search.length} characters`);
                        }
                    } catch (e) {
                        addLog('warning', 'Could not parse URL');
                    }

                    if (result.data.playout_data) {
                        const playout = result.data.playout_data;
                        addLog('info', `Duration: ${playout.durationSeconds}s`);
                        addLog('info', `DRM: ${playout.drm ? 'Yes' : 'No'}`);
                        addLog('info', `HD: ${playout.hd ? 'Yes' : 'No'}`);
                        addLog('info', `4K Available: ${playout['4kResolution']?.available ? 'Yes' : 'No'}`);
                        addLog('info', `Provider: ${playout.provider || 'Unknown'}`);
                    }
                } else {
                    addLog('error', `فشل جلب HLS: ${result.error || result.data?.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        async function getDRM() {
            addLog('info', 'جلب معلومات DRM...');
            
            const data = getData();
            
            try {
                const response = await fetch('/fairplay/api/drm-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('success', 'تم جلب DRM بنجاح ✅');
                    const drm = result.data;
                    addLog('info', `Type: ${drm.drm_type}`);
                    addLog('info', `Response Code: ${drm.response_code || 'N/A'}`);
                    addLog('info', `Brand GUID: ${drm.brand_guid}`);

                    // Certificate URL
                    if (drm.certificate_url) {
                        addLog('success', '✅ Certificate URL Available');
                        addLog('info', `Certificate: ${drm.certificate_url}`);
                    } else {
                        addLog('error', '❌ Certificate URL Missing');
                    }

                    // License URL
                    if (drm.license_url) {
                        addLog('success', '✅ License URL Available');

                        // Check if it's FairPlay or Widevine
                        if (drm.license_url.includes('fairplay')) {
                            addLog('success', '🍎 FairPlay License URL');
                        } else if (drm.license_url.includes('widevine')) {
                            addLog('warning', '⚠️ Widevine License URL (converted to FairPlay)');
                        }

                        addLog('info', `License: ${drm.license_url.substring(0, 100)}...`);

                        // Show original if converted
                        if (drm.original_signature && drm.original_signature !== drm.license_url) {
                            addLog('info', `Original: ${drm.original_signature.substring(0, 100)}...`);
                        }

                        // Extract KID from license URL
                        try {
                            const url = new URL(drm.license_url);
                            const kid = url.searchParams.get('KID');
                            if (kid) {
                                addLog('info', `KID: ${kid}`);
                            }
                        } catch (e) {
                            addLog('warning', 'Could not parse license URL');
                        }
                    } else {
                        addLog('error', '❌ License URL Missing');
                    }

                    if (drm.expires) {
                        addLog('info', `Expires: ${drm.expires}`);
                    }

                    if (drm.current_date) {
                        addLog('info', `Server Date: ${drm.current_date}`);
                    }

                } else {
                    addLog('error', `فشل جلب DRM: ${result.error || result.data?.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        async function completeSetup() {
            addLog('info', 'إنشاء إعداد كامل...');
            
            const data = getData();
            
            try {
                const response = await fetch('/fairplay/api/complete-setup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    const setup = result.data;
                    currentHlsUrl = setup.hls_url; // Store HLS URL

                    addLog('success', 'إعداد كامل جاهز! 🎉');
                    addLog('success', `✅ HLS URL: Ready`);
                    addLog('success', `✅ DRM Info: Ready`);
                    addLog('success', `✅ Player Config: Ready`);

                    // Show full HLS URL
                    addLog('info', '=== COMPLETE SETUP HLS URL ===');
                    addLog('info', setup.hls_url);
                    addLog('info', '=== END HLS URL ===');

                    addLog('info', `Certificate: ${setup.drm_info.certificate_url}`);
                    addLog('info', `License: ${setup.drm_info.license_url}`);
                    addLog('success', '🍎 جاهز للاستخدام مع iOS!');

                    // Show copyable URL
                    showCopyableUrl(setup.hls_url);
                } else {
                    addLog('error', `فشل الإعداد: ${result.error || result.data?.error}`);
                    if (result.data && result.data.step) {
                        addLog('error', `فشل في: ${result.data.step}`);
                    }
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }
        
        async function compareDRM() {
            addLog('info', 'مقارنة طرق DRM...');

            const data = getData();

            try {
                const response = await fetch('/fairplay/api/compare-drm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    const comparison = result.data.comparison;
                    const fairplay = result.data.fairplay_method;
                    const series = result.data.series_method;

                    addLog('info', '=== مقارنة طرق DRM ===');
                    addLog('info', `FairPlay Method: ${comparison.fairplay_success ? '✅ نجح' : '❌ فشل'}`);
                    addLog('info', `Series Method: ${comparison.series_success ? '✅ نجح' : '❌ فشل'}`);
                    addLog('info', `Both Working: ${comparison.both_working ? '✅ نعم' : '❌ لا'}`);

                    if (fairplay.success) {
                        addLog('success', 'FairPlay Method: Certificate & License URLs available');
                    } else {
                        addLog('error', `FairPlay Method Error: ${fairplay.error}`);
                    }

                    if (series.success) {
                        addLog('success', 'Series Method: DRM data available');
                    } else {
                        addLog('error', `Series Method Error: ${series.error}`);
                    }

                    addLog('info', '=== نهاية المقارنة ===');

                } else {
                    addLog('error', `فشل المقارنة: ${result.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }

        async function showPlayerConfig() {
            addLog('info', 'إنشاء Player Config...');

            const data = getData();

            try {
                const response = await fetch('/fairplay/api/player-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': getCSRFToken()
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    addLog('success', 'Player Config جاهز! 📱');
                    addLog('info', '=== PLAYER CONFIG JSON ===');
                    addLog('info', JSON.stringify(result.data.config, null, 2));
                    addLog('info', '=== END PLAYER CONFIG ===');

                    // Show copyable config
                    const configDiv = document.createElement('div');
                    configDiv.className = 'hls-url';
                    configDiv.innerHTML = `
                        <strong>Player Config JSON:</strong>
                        <button class="copy-btn" onclick="copyToClipboard('${JSON.stringify(result.data.config).replace(/'/g, "\\'")}')">📋 نسخ</button>
                        <br><br>
                        <pre style="white-space: pre-wrap; font-size: 10px;">${JSON.stringify(result.data.config, null, 2)}</pre>
                    `;

                    // Insert after log
                    const logElement = document.getElementById('log');
                    const parent = logElement.parentNode;

                    // Remove previous config display if exists
                    const existing = parent.querySelector('.hls-url');
                    if (existing) {
                        existing.remove();
                    }

                    parent.insertBefore(configDiv, logElement.nextSibling);

                } else {
                    addLog('error', `فشل إنشاء Player Config: ${result.error}`);
                }
            } catch (error) {
                addLog('error', `خطأ: ${error.message}`);
            }
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', 'صفحة الاختبار السريع جاهزة');
            addLog('info', 'CSRF Token: ' + getCSRFToken().substring(0, 10) + '...');
            setTimeout(testAPI, 500);
        });
    </script>
</body>
</html>
