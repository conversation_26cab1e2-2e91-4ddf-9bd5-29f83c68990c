<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content', function (Blueprint $table) {
            $table->id();
            $table->string('content_id')->index();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('type')->default('movie'); // movie, series, episode
            $table->string('source')->default('shahid'); // shahid, netflix, etc.
            $table->string('poster_url')->nullable();
            $table->string('thumbnail_url')->nullable();
            $table->json('metadata')->nullable();
            $table->string('mpd_url')->nullable();
            $table->json('drm_info')->nullable();
            $table->integer('duration')->nullable(); // in minutes
            $table->string('quality')->nullable();
            $table->string('language')->nullable();
            $table->boolean('is_coming_soon')->default(false);
            $table->timestamp('release_date')->nullable();
            $table->integer('views_count')->default(0);
            $table->timestamps();

            // Indexes
            $table->unique(['content_id', 'source']);
            $table->index(['type', 'source']);
            $table->index('is_coming_soon');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content');
    }
};
