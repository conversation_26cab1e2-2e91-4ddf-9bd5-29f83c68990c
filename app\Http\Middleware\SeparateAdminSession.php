<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SeparateAdminSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Set different session configurations for admin vs user routes
        if ($request->is('admin/*')) {
            // Admin session configuration
            config([
                'session.cookie' => 'shahid_admin_session',
                'session.domain' => config('session.domain'),
                'session.path' => '/admin',
                'session.lifetime' => 120, // 2 hours for admin
            ]);
        } else {
            // User session configuration
            config([
                'session.cookie' => 'shahid_user_session',
                'session.domain' => config('session.domain'),
                'session.path' => '/',
                'session.lifetime' => 60, // 1 hour for users
            ]);
        }

        return $next($request);
    }
}
