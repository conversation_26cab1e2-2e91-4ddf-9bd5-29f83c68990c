@extends('layouts.app')

@section('title', 'Series - Shahid Play')

@section('styles')
<style>
.bg-gradient-dark {
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    transition: all 0.3s ease;
}

.series-poster-container:hover .bg-gradient-dark {
    background: linear-gradient(to top, rgba(255, 107, 107, 0.8), transparent);
}

.series-poster-container:hover .bg-gradient-dark h6 {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    transform: translateY(-2px);
}

.series-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border-radius: 10px;
    overflow: hidden;
    background: #fff;
    height: auto;
}

.series-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.series-card img {
    transition: transform 0.3s ease;
}

.series-card:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.1);
}

/* Live search indicator */
.search-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    font-size: 12px;
}

/* Search input with indicator */
.search-container {
    position: relative;
}

/* Highlight search matches */
.search-highlight {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
}

/* Play Overlay Styles for Series */
.series-poster-container {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
    z-index: 2;
}

.series-poster-container:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.8);
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.series-poster-container:hover .play-button {
    transform: scale(1);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
    animation: pulse 2s infinite;
}

.play-button i {
    color: white;
    font-size: 28px;
    margin-left: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes pulse {
    0% { box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4); }
    50% { box-shadow: 0 12px 35px rgba(255, 107, 107, 0.8); }
    100% { box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4); }
}

.series-poster-container:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 36, 0.1));
    z-index: 1;
    pointer-events: none;
}

.series-title {
    transition: all 0.3s ease;
    font-size: 1rem;
    line-height: 1.3;
}

.series-poster-container:hover .series-title {
    transform: translateY(-2px);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    font-weight: 700;
}

.badge {
    backdrop-filter: blur(10px);
    background: rgba(0,0,0,0.7) !important;
    transition: all 0.3s ease;
}

.series-poster-container:hover .badge {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
}

.series-card .card-body {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* ===== RESPONSIVE ADJUSTMENTS FOR SERIES ===== */
/* تعديل المقاسات فقط مع الحفاظ على التصميم */

/* Desktop Large (1400px+) */
@media (min-width: 1400px) {
    .col-lg-3 {
        flex: 0 0 auto;
        width: 20%; /* 5 أعمدة */
    }

    .play-button {
        width: 90px;
        height: 90px;
    }

    .play-button i {
        font-size: 32px;
    }
}

/* Laptop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .play-button {
        width: 70px;
        height: 70px;
    }

    .play-button i {
        font-size: 24px;
    }

    .series-title {
        font-size: 0.95rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .col-lg-3 {
        flex: 0 0 auto;
        width: 33.33333333%; /* 3 أعمدة */
    }

    .play-button {
        width: 60px;
        height: 60px;
    }

    .play-button i {
        font-size: 20px;
    }

    .series-title {
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .col-lg-3,
    .col-md-4 {
        flex: 0 0 auto;
        width: 50%; /* عمودين */
    }

    .play-button {
        width: 50px;
        height: 50px;
    }

    .play-button i {
        font-size: 18px;
    }

    .series-title {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .badge {
        font-size: 0.6rem;
        padding: 0.1rem 0.25rem;
    }

    .btn {
        padding: 0.35rem 0.7rem;
        font-size: 0.85rem;
    }

    .container-fluid {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
}

/* Mobile Small (أقل من 576px) */
@media (max-width: 575px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        flex: 0 0 auto;
        width: 100%; /* عمود واحد */
        margin-bottom: 1rem;
    }

    .play-button {
        width: 45px;
        height: 45px;
    }

    .play-button i {
        font-size: 16px;
    }

    .series-title {
        font-size: 0.8rem;
        line-height: 1.1;
    }

    .badge {
        font-size: 0.55rem;
        padding: 0.05rem 0.2rem;
    }

    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }

    .container-fluid {
        padding-right: 0.25rem;
        padding-left: 0.25rem;
    }

    .h3 {
        font-size: 1.2rem;
    }

    .form-control,
    .form-select {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
}

/* iPhone Specific */
@media only screen and (max-width: 320px) {
    .play-button {
        width: 40px;
        height: 40px;
    }

    .play-button i {
        font-size: 14px;
    }

    .series-title {
        font-size: 0.75rem;
    }

    .h3 {
        font-size: 1.1rem;
    }
}

/* Landscape على الموبايل */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        width: 25%; /* 4 أعمدة في landscape */
    }

    .play-button {
        width: 40px;
        height: 40px;
    }

    .play-button i {
        font-size: 16px;
    }
}

/* ===== HEADER RESPONSIVE IMPROVEMENTS ===== */

/* تحسين الـ header للشاشات المختلفة */
.page-header h1 {
    font-size: clamp(1.5rem, 4vw, 2rem);
    line-height: 1.2;
}

.page-header .btn {
    white-space: nowrap;
    min-width: auto;
}

/* Mobile header adjustments */
@media (max-width: 767px) {
    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .page-header p {
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .page-header .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Very small screens */
@media (max-width: 575px) {
    .page-header h1 {
        font-size: 1.3rem;
    }

    .page-header p {
        font-size: 0.85rem;
    }

    .page-header .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}

/* iPhone specific */
@media only screen and (max-width: 320px) {
    .page-header h1 {
        font-size: 1.2rem;
    }

    .page-header p {
        font-size: 0.8rem;
    }
}

/* Live search indicator */
.search-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    font-size: 12px;
}

/* Search input with indicator */
.search-container {
    position: relative;
}

/* Highlight search matches */
.search-highlight {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
}
</style>
@endsection

@section('content')
<div class="container-fluid" dir="ltr">
    <!-- Proxy Status: Active (check logs for details) -->

    <!-- Header -->
    <div class="row mb-4 page-header">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1 text-center text-md-start">
                    <h1 class="h3 mb-2 mb-md-0 fw-bold">
                        <i class="fas fa-tv me-2 text-primary"></i>
                        Shahid Series
                    </h1>
                    <p class="text-muted mb-0 small">Browse and explore a wide collection of series</p>
                    <!-- Proxy Status -->
                    <div class="mt-2">
                        <span id="proxyStatus" class="badge bg-secondary">
                            <i class="fas fa-spinner fa-spin me-1"></i>Checking proxy status...
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-2">
                            <label for="countrySelect" class="form-label">Country</label>
                            <select class="form-select" id="countrySelect">
                                <option value="EG">Egypt</option>
                                <option value="SA">Saudi Arabia</option>
                                <option value="AE">UAE</option>
                                <option value="KW">Kuwait</option>
                                <option value="QA">Qatar</option>
                                <option value="BH">Bahrain</option>
                                <option value="OM">Oman</option>
                                <option value="JO">Jordan</option>
                                <option value="LB">Lebanon</option>
                                <option value="IQ">Iraq</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="limitSelect" class="form-label">Results Limit</label>
                            <select class="form-select" id="limitSelect">
                                <option value="20">20</option>
                                <option value="50" selected>50</option>
                                <option value="100">100</option>
                                <option value="all">All Series</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">Search Series</label>
                            <div class="search-container">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search by title or ID...">
                                    <button class="btn btn-outline-primary" onclick="searchSeries()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="clearSearch()" title="Clear search">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadSeries()">
                                <i class="fas fa-sync-alt me-2"></i>
                                Load Series
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadSeries(false)">
                                <i class="fas fa-sync-alt me-2"></i>
                                Load
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-warning w-100" onclick="refreshCache()">
                                <i class="fas fa-cloud-download-alt me-2"></i>
                                تحديث المحتوى
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <div id="searchResultsInfo" class="row mb-3 d-none">
        <div class="col-12">
            <div class="alert alert-info d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="searchResultsText">Search results</span>
                </div>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearSearch()">
                    <i class="fas fa-times me-1"></i>
                    Clear Search
                </button>
            </div>
        </div>
    </div>

    <!-- Cache Status Info -->
    <div id="cacheStatusInfo" class="row mb-3 d-none">
        <div class="col-12">
            <div class="alert alert-success d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-database me-2"></i>
                    <span id="cacheStatusText">Cache Status</span>
                </div>
                <div>
                    <small class="text-muted" id="cacheDate">Last updated: -</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="text-center py-5 d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Loading series...</p>
    </div>

    <!-- Welcome Message -->
    <div id="welcomeMessage" class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-tv fa-4x text-primary"></i>
        </div>
        <h4 class="text-primary">Welcome to Shahid Series</h4>
        <p class="text-muted mb-4">Choose your country and click "Load Series" to browse available content.</p>
        <div class="d-flex justify-content-center gap-3">
            <button class="btn btn-primary" onclick="loadSeries()">
                <i class="fas fa-play me-2"></i>
                Load Series
            </button>
            <button class="btn btn-outline-primary" onclick="$('#searchInput').focus()">
                <i class="fas fa-search me-2"></i>
                Search Series
            </button>
        </div>
    </div>

    <!-- Series Grid -->
    <div id="seriesContainer" class="row g-4">
        <!-- Series will be loaded here -->
    </div>

    <!-- No Results -->
    <div id="noResults" class="text-center py-5 d-none">
        <div class="text-muted">
            <i class="fas fa-tv fa-3x mb-3"></i>
            <h5>No Series Found</h5>
            <p>No series were found. Please check your token settings and connection.</p>
            <button class="btn btn-success" onclick="loadSeries()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
    </div>
</div>

<!-- Token Modal -->
<div class="modal fade" id="tokenModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>
                    Setup Shahid Token
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="tokenForm">
                    <div class="mb-3">
                        <label for="shahidToken" class="form-label">Shahid Token</label>
                        <textarea
                            class="form-control"
                            id="shahidToken"
                            rows="3"
                            placeholder="Enter Shahid token here..."
                            required
                        ></textarea>
                        <div class="form-text">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                You can get the token from Shahid website
                            </small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveToken()">
                    <i class="fas fa-save me-2"></i>
                    Save Token
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
console.log('Script loaded');
let currentSeries = [];

$(document).ready(function() {
    console.log('Document ready - loading series...');

    // Test if jQuery is working
    if (typeof $ !== 'undefined') {
        console.log('jQuery is loaded');
    } else {
        console.error('jQuery is not loaded');
    }

    // Check proxy status
    checkProxyStatus();

    // Auto-load series from cache if available
    console.log('Series page loaded. Auto-loading from cache...');
    loadSeries(false); // Load from cache first

    // Force refresh styles
    setTimeout(function() {
        $('head').append('<style>.series-card img { object-fit: cover !important; }</style>');
    }, 100);
});

function loadSeries(forceRefresh = false) {
    console.log('loadSeries function called, forceRefresh:', forceRefresh);
    const country = $('#countrySelect').val();
    const limit = $('#limitSelect').val();
    const fetchAll = (limit === 'all');

    console.log('Country:', country, 'Limit:', limit, 'Fetch All:', fetchAll);

    showLoading();
    hideResults();
    hideSearchResults();
    hideCacheStatus();

    // Update loading message
    if (forceRefresh) {
        $('#loadingIndicator p').text('تحديث المحتوى من الخادم...');
    } else if (fetchAll) {
        $('#loadingIndicator p').text('Loading all series... This may take a while.');
    } else {
        $('#loadingIndicator p').text('Loading series...');
    }

    $.ajax({
        url: '/shahid/api/series',
        method: 'GET',
        data: {
            country: country,
            limit: fetchAll ? 999 : limit,
            offset: 0,
            refresh: forceRefresh
        },
        success: function(response) {
            hideLoading();

            if (response.success && response.data && response.data.length > 0) {
                currentSeries = response.data;
                displaySeries(response.data);
                showResults();

                // Show cache status
                showCacheStatus(response);

                // Show info about loaded series
                const cacheText = response.cached ? ' (من الذاكرة المؤقتة)' : ' (محدث من الخادم)';
                const infoText = fetchAll ?
                    `Loaded all ${response.data.length} series from ${country}${cacheText}` :
                    `Loaded ${response.data.length} series from ${country}${cacheText}`;
                // Only log to console, don't show alert for normal loading
                console.log('✅ Series loaded:', infoText);
            } else {
                // If no data and not force refresh, show welcome message instead of error
                if (!forceRefresh) {
                    showWelcome();
                    showAlert('info', 'لا توجد بيانات محفوظة. اضغط "تحديث المحتوى" لتحميل المسلسلات من الخادم.');
                } else {
                    showNoResults();
                }
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error loading series';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            // If error and not force refresh, show welcome with helpful message
            if (!forceRefresh) {
                showWelcome();
                showAlert('warning', 'لا توجد بيانات محفوظة. اضغط "تحديث المحتوى" لتحميل المسلسلات من الخادم.');
            } else {
                showAlert('danger', errorMessage);
                showNoResults();
            }
        }
    });
}

// Search functionality
function searchSeries() {
    const query = $('#searchInput').val().trim();

    if (!query) {
        showAlert('warning', 'Please enter a search term');
        return;
    }

    console.log('Searching in API for:', query);

    showLoading();
    hideResults();
    hideSearchResults();

    // Check if it's an ID search
    const isIdSearch = /^\d+$/.test(query);
    const searchType = isIdSearch ? 'ID' : 'title';

    $('#loadingIndicator p').text(`Searching for ${searchType}: "${query}" in Shahid database...`);

    $.ajax({
        url: '/api/shahid/search',
        method: 'GET',
        data: {
            query: query,
            limit: 50,
            type: 'SERIES'
        },
        success: function(response) {
            hideLoading();

            if (response.success && response.data && response.data.length > 0) {
                currentSeries = response.data;
                displaySeries(response.data);
                showResults();

                const isIdSearch = /^\d+$/.test(query);
                const searchType = isIdSearch ? 'ID' : 'title';
                const sortInfo = isIdSearch ? '' : ' (sorted by relevance)';
                showSearchResults(`${searchType}: "${query}"${sortInfo}`, response.data.length);

                // Only log search results, don't show alert
                console.log(`🔍 Found ${response.data.length} series for ${searchType}: "${query}"`);
            } else {
                currentSeries = []; // Clear current series
                showNoResults();

                const isIdSearch = /^\d+$/.test(query);
                const searchType = isIdSearch ? 'ID' : 'title';
                showAlert('warning', `No series found for ${searchType}: "${query}". Try a different search term.`);
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error searching series';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showAlert('danger', errorMessage);
            showNoResults();
        }
    });
}



// Show search results info
function showSearchResults(query, count) {
    $('#searchResultsText').text(`Found ${count} series for "${query}"`);
    $('#searchResultsInfo').removeClass('d-none');
}

// Hide search results info
function hideSearchResults() {
    $('#searchResultsInfo').addClass('d-none');
}

// Clear search and reload original content
function clearSearch() {
    $('#searchInput').val('');
    hideSearchResults();
    currentSeries = [];
    loadSeries(); // Reload original series
}

// Live search functionality
let searchTimeout;
$(document).ready(function() {
    // Handle Enter key in search input
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            searchSeries();
        }
    });

    // Live search as user types
    $('#searchInput').on('input', function() {
        const query = $(this).val().trim();

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        if (query === '') {
            // If search is empty, show all loaded series
            if (currentSeries && currentSeries.length > 0) {
                displaySeries(currentSeries);
                hideSearchResults();
            }
            return;
        }

        // If we have loaded series, filter them locally
        if (currentSeries && currentSeries.length > 0) {
            // Debounce the search
            searchTimeout = setTimeout(function() {
                filterLoadedSeries(query);
            }, 300);
        }
    });
});

// Calculate relevance score between query and title
function calculateRelevanceScore(query, title) {
    if (!query || !title) return 0;

    query = query.toLowerCase().trim();
    title = title.toLowerCase().trim();

    // Exact match gets highest score
    if (query === title) return 1.0;

    // Check if query is at the beginning of title
    if (title.indexOf(query) === 0) return 0.9;

    // Check if title contains the exact query
    if (title.indexOf(query) !== -1) return 0.8;

    // Calculate word-based similarity
    const queryWords = query.split(' ').filter(word => word.length >= 2);
    const titleWords = title.split(' ');

    let matchingWords = 0;
    const totalQueryWords = queryWords.length;

    queryWords.forEach(queryWord => {
        titleWords.forEach(titleWord => {
            if (queryWord === titleWord) {
                matchingWords++;
            } else if (queryWord.length >= 3 && titleWord.indexOf(queryWord) !== -1) {
                matchingWords += 0.7;
            }
        });
    });

    const wordScore = totalQueryWords > 0 ? matchingWords / totalQueryWords : 0;

    // Boost score if query length is significant portion of title
    const lengthRatio = query.length / title.length;
    if (lengthRatio > 0.5) {
        return Math.min(wordScore * 1.2, 1.0);
    }

    return Math.min(wordScore, 1.0);
}

// Filter already loaded series locally
function filterLoadedSeries(query) {
    if (!currentSeries || currentSeries.length === 0) {
        // No local data, search in API directly
        searchSeries();
        return;
    }

    const filteredSeries = currentSeries.filter(function(series) {
        const title = (series.title || '').toLowerCase();
        const id = (series.id || '').toString().toLowerCase();
        const description = (series.description || '').toLowerCase();
        const searchQuery = query.toLowerCase();

        // Calculate relevance score
        const relevanceScore = calculateRelevanceScore(searchQuery, title);

        // For ID search, exact match only
        if (/^\d+$/.test(query)) {
            return id === searchQuery;
        }

        // For text search, use relevance filtering
        return relevanceScore >= 0.3 || // Good relevance in title
               title.includes(searchQuery) || // Contains search term
               id.includes(searchQuery); // ID contains search term
    });

    if (filteredSeries.length > 0) {
        // Sort by relevance for text searches
        if (!/^\d+$/.test(query)) {
            filteredSeries.sort(function(a, b) {
                const scoreA = calculateRelevanceScore(query.toLowerCase(), (a.title || '').toLowerCase());
                const scoreB = calculateRelevanceScore(query.toLowerCase(), (b.title || '').toLowerCase());
                return scoreB - scoreA; // Sort by highest relevance first
            });
        }

        displaySeries(filteredSeries);
        showSearchResults(`"${query}" (Local Filter)`, filteredSeries.length);
    } else {
        // If no local results found, search in API for fresh results
        console.log('No local results found, searching in API...');
        // Only log to console
        console.log(`🔍 No local results for "${query}". Searching online...`);
        searchSeries();
    }
}

function displaySeries(seriesList) {
    const container = $('#seriesContainer');
    container.empty();

    // Store current series for local filtering
    if (seriesList && seriesList.length > 0) {
        currentSeries = seriesList;
    }

    seriesList.forEach(function(series) {
        const seriesCard = createSeriesCard(series);
        container.append(seriesCard);
    });

    // Force refresh styles after adding cards
    setTimeout(function() {
        $('.series-card img').each(function() {
            $(this).css({
                'object-fit': 'cover',
                'width': '100%',
                'height': 'auto'
            });
        });
    }, 100);
}

function createSeriesCard(series) {
    const title = series.title || 'Untitled';

    // Handle different poster formats from API
    let poster = '/images/no-poster.svg';

    if (series.poster_url) {
        poster = series.poster_url;
    } else if (series.image) {
        if (typeof series.image === 'string') {
            poster = series.image;
        } else if (series.image.posterImage) {
            poster = series.image.posterImage;
        } else if (series.image.thumbnailImage) {
            poster = series.image.thumbnailImage;
        }
    } else if (series.thumbnail) {
        poster = series.thumbnail;
    } else if (series.poster) {
        poster = series.poster;
    }

    // Ensure poster URL is properly formatted
    if (poster && poster !== '/images/no-poster.svg') {
        // If it's already a full URL, check for placeholders
        if (poster.startsWith('http')) {
            // Replace Shahid placeholders if they exist
            if (poster.includes('{height}') || poster.includes('{width}') || poster.includes('{croppingPoint}')) {
                poster = poster.replace('{height}', '600')
                              .replace('{width}', '400')
                              .replace('{croppingPoint}', 'mc');
            }
        }
        // If it's a UUID (media object), format it properly
        else if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(poster)) {
            poster = `https://shahid.mbc.net/mediaObject/${poster}?height=600&width=400&croppingPoint=mc&version=1`;
        }
        // If it's a path, prepend base URL
        else if (poster.startsWith('/')) {
            poster = `https://api2.shahid.net${poster}`;
        }
    }

    const seasonsCount = series.seasons_count ? `${series.seasons_count} Seasons` : '';
    const year = series.year || '';
    const seriesUrl = series.series_url || '';
    const type = series.type || '';
    const description = series.description || '';

    // Debug logging for poster
    console.log('Series card data:', {
        id: series.id,
        title: title,
        original_image: series.image,
        final_poster: poster,
        type: type
    });

    // Extract series ID from URL if not provided directly
    let seriesId = series.id;
    if (!seriesId && seriesUrl) {
        const urlMatch = seriesUrl.match(/\/series\/(\d+)/);
        if (urlMatch) {
            seriesId = urlMatch[1];
        }
    }

    return `
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100 border-0 shadow-sm series-card" data-series-id="${seriesId || ''}" data-series-url="${seriesUrl}">
                <div class="position-relative series-poster-container" onclick="viewSeries('${seriesId || ''}', '${seriesUrl}')">
                    <img src="${poster}" class="card-img-top" alt="${title}" style="width: 100%; height: 300px; object-fit: cover;"
                         onerror="this.src='/images/no-poster.svg'" loading="lazy">
                    <div class="position-absolute top-0 end-0 m-2">
                        ${year ? `<span class="badge bg-dark mb-1 d-block">${year}</span>` : ''}
                        ${type ? `<span class="badge bg-info mb-1 d-block">${type}</span>` : ''}
                        ${seasonsCount ? `<span class="badge bg-primary">${seasonsCount}</span>` : ''}
                    </div>
                    <div class="position-absolute bottom-0 start-0 end-0 bg-gradient-dark p-3">
                        <h6 class="text-white mb-0 fw-bold series-title">${title}</h6>
                        ${seriesId ? `<small class="text-white-50">ID: ${seriesId}</small>` : ''}
                    </div>
                    <!-- Play Button Overlay -->
                    <div class="play-overlay">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function viewSeries(seriesId, seriesUrl) {
    if (!seriesId && !seriesUrl) {
        console.warn('⚠️ Invalid series ID');
        return;
    }

    // Redirect to series details page immediately without alerts
    if (seriesId) {
        console.log(`🎬 Opening series: ${seriesId}`);
        window.location.href = `/shahid/series/${seriesId}`;
    } else {
        console.warn('⚠️ Series ID not available');
    }
}

function getSeriesDetails(seriesId) {
    if (!seriesId) {
        showAlert('warning', 'Invalid series ID');
        return;
    }

    showLoading();

    $.ajax({
        url: '/api/shahid/content-details',
        method: 'GET',
        data: {
            content_id: seriesId
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                showSeriesDetailsModal(response.data);
            } else {
                showAlert('danger', response.message || 'Failed to get series details');
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error getting details';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function showSeriesDetailsModal(data) {
    // Extract series information
    const seriesData = data.data || data;
    const productModel = seriesData.productModel || {};

    const title = productModel.title || 'Not Available';
    const description = productModel.description || productModel.shortDescription || 'No description available';
    const releaseDate = productModel.releaseDate || productModel.productionDate || 'Not specified';
    const seriesId = productModel.id || data.id || 'Not Available';

    // Extract poster image
    var posterImage = '';
    if (productModel.image && productModel.image.posterClean) {
        posterImage = productModel.image.posterClean;
    } else if (productModel.image && productModel.image.posterHero) {
        posterImage = productModel.image.posterHero;
    } else if (productModel.thumbnailImage) {
        posterImage = productModel.thumbnailImage;
    }

    // Extract cast information (if available)
    let cast = '';
    if (productModel.cast && productModel.cast.length > 0) {
        cast = productModel.cast.map(function(actor) { return actor.name || actor; }).join(', ');
    } else {
        cast = 'Not Available';
    }

    // Extract seasons information
    let seasonsCount = 'Not specified';
    if (productModel.seasons && productModel.seasons.length > 0) {
        seasonsCount = productModel.seasons.length + ' Seasons';
    } else if (productModel.seasonsCount) {
        seasonsCount = productModel.seasonsCount + ' Seasons';
    }

    // Format release date
    if (releaseDate && releaseDate !== 'Not specified') {
        try {
            const date = new Date(releaseDate);
            releaseDate = date.getFullYear();
        } catch (e) {
            // Keep original if parsing fails
        }
    }

    const modalHtml = `
        <div class="modal fade" id="seriesDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: none; border-radius: 15px;">
                    <div class="modal-header border-0" style="background: rgba(255,255,255,0.1); border-radius: 15px 15px 0 0;">
                        <h5 class="modal-title text-white fw-bold">
                            <i class="fas fa-tv me-2 text-primary"></i>Series Details
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="row">
                            ${posterImage ? `
                            <div class="col-md-4 mb-3">
                                <img src="${posterImage}" alt="${title}" class="img-fluid rounded shadow-lg" style="width: 100%; max-height: 400px; object-fit: cover;">
                            </div>
                            ` : ''}
                            <div class="${posterImage ? 'col-md-8' : 'col-12'}">
                                <div class="series-info">
                                    <h3 class="text-white mb-3 fw-bold">${title}</h3>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-warning text-dark me-2">
                                            <i class="fas fa-hashtag me-1"></i>Series ID
                                        </span>
                                        <span class="text-light">${seriesId}</span>
                                    </div>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-info me-2">
                                            <i class="fas fa-calendar me-1"></i>Release Year
                                        </span>
                                        <span class="text-light">${releaseDate}</span>
                                    </div>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-primary me-2">
                                            <i class="fas fa-tv me-1"></i>Seasons
                                        </span>
                                        <span class="text-light">${seasonsCount}</span>
                                    </div>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-success me-2">
                                            <i class="fas fa-users me-1"></i>Cast
                                        </span>
                                        <span class="text-light">${cast}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="story-section">
                                    <h5 class="text-warning mb-3">
                                        <i class="fas fa-book-open me-2"></i>Series Description
                                    </h5>
                                    <div class="story-content p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #ffc107;">
                                        <p class="text-light mb-0 lh-lg" style="font-size: 16px;">${description}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer border-0" style="background: rgba(255,255,255,0.05); border-radius: 0 0 15px 15px;">
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#seriesDetailsModal').remove();

    // Add new modal to body
    $('body').append(modalHtml);

    // Show modal
    $('#seriesDetailsModal').modal('show');
}

function showLoading() {
    $('#loadingIndicator').removeClass('d-none');
}

function hideLoading() {
    $('#loadingIndicator').addClass('d-none');
}

function showResults() {
    $('#seriesContainer').removeClass('d-none');
    $('#noResults').addClass('d-none');
}

function hideResults() {
    $('#seriesContainer').addClass('d-none');
}

function showNoResults() {
    $('#noResults').removeClass('d-none');
    $('#seriesContainer').addClass('d-none');
}

function showAlert(type, message) {
    // Simple console logging instead of intrusive alerts
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}] ${type.toUpperCase()}:`;

    switch(type) {
        case 'success':
            console.log(`✅ ${prefix}`, message);
            break;
        case 'danger':
        case 'error':
            console.error(`❌ ${prefix}`, message);
            break;
        case 'warning':
            console.warn(`⚠️ ${prefix}`, message);
            break;
        case 'info':
        default:
            console.info(`ℹ️ ${prefix}`, message);
            break;
    }

    // Only show critical errors to user
    if (type === 'danger' || type === 'error') {
        // Simple browser notification for errors only
        if (message.includes('Failed') || message.includes('Error') || message.includes('خطأ')) {
            showSimpleToast(type, message);
        }
    }
}

// Simple toast notification that doesn't interfere with navigation
function showSimpleToast(type, message) {
    // Create a simple toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' || type === 'danger' ? 'danger' : type} position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 300px;
        opacity: 0.9;
        transition: all 0.3s ease;
    `;
    toast.innerHTML = `
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        <small>${message}</small>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

function showTokenModal() {
    $('#tokenModal').modal('show');
}

function saveToken() {
    const token = $('#shahidToken').val().trim();

    if (!token) {
        showAlert('warning', 'Please enter the token');
        return;
    }

    $.ajax({
        url: '/api/shahid/auth/token',
        method: 'POST',
        data: {
            token: token
        },
        success: function(response) {
            if (response.success) {
                $('#tokenModal').modal('hide');
                showAlert('success', response.message);
                loadSeries(); // Reload series with new token
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            let errorMessage = 'Error saving token';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function showLoading() {
    $('#loadingIndicator').removeClass('d-none');
    $('#welcomeMessage').addClass('d-none');
}

function hideLoading() {
    $('#loadingIndicator').addClass('d-none');
}

function showResults() {
    $('#seriesContainer').removeClass('d-none');
    $('#welcomeMessage').addClass('d-none');
    $('#noResults').addClass('d-none');
}

function hideResults() {
    $('#seriesContainer').addClass('d-none');
}

function showNoResults() {
    $('#noResults').removeClass('d-none');
    $('#seriesContainer').addClass('d-none');
    $('#welcomeMessage').addClass('d-none');
}

function showWelcome() {
    $('#welcomeMessage').removeClass('d-none');
    $('#seriesContainer').addClass('d-none');
    $('#noResults').addClass('d-none');
    $('#loadingIndicator').addClass('d-none');
}

// Cache management functions
function refreshCache() {
    const country = $('#countrySelect').val();

    showLoading();
    $('#loadingIndicator p').text('تحديث المحتوى من الخادم...');

    $.ajax({
        url: '/shahid/api/cache/refresh',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            country: country
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                // Show success for cache refresh only
                console.log(`✅ تم تحديث المحتوى بنجاح! تم حفظ ${response.total_cached} مسلسل`);
                showSimpleToast('success', `تم تحديث ${response.total_cached} مسلسل`);

                // Reload series with fresh data
                loadSeries(false);
            } else {
                showAlert('danger', 'فشل في تحديث المحتوى: ' + (response.message || 'Unknown error'));
            }
        },
        error: function(xhr) {
            hideLoading();
            showAlert('danger', 'خطأ في تحديث المحتوى');
        }
    });
}

function showCacheStatus(response) {
    if (response.cached && response.cache_date) {
        const cacheDate = new Date(response.cache_date).toLocaleString('ar-EG');
        const stats = response.cache_stats;

        $('#cacheStatusText').html(`
            <strong>البيانات محفوظة مؤقتاً</strong> -
            ${stats.total_cached} مسلسل محفوظ
            (${stats.complete_cached} مكتمل - ${stats.cache_percentage}%)
        `);
        $('#cacheDate').text(`آخر تحديث: ${cacheDate}`);
        $('#cacheStatusInfo').removeClass('d-none');
        $('#cacheStatusInfo .alert').removeClass('alert-warning').addClass('alert-success');
    } else if (!response.cached) {
        $('#cacheStatusText').html('<strong>تم تحديث البيانات من الخادم</strong>');
        $('#cacheDate').text('تم التحديث الآن');
        $('#cacheStatusInfo').removeClass('d-none');
        $('#cacheStatusInfo .alert').removeClass('alert-success').addClass('alert-warning');
    }
}

function hideCacheStatus() {
    $('#cacheStatusInfo').addClass('d-none');
}

/**
 * Check proxy status
 */
function checkProxyStatus() {
    console.log('🔍 Checking proxy status...');
    $.ajax({
        url: '/api/proxy-status',
        method: 'GET',
        success: function(response) {
            console.log('✅ Proxy status response:', response);
            const statusElement = $('#proxyStatus');
            if (response.active) {
                statusElement.removeClass('bg-secondary bg-danger bg-warning')
                           .addClass('bg-success')
                           .html(`<i class="fas fa-shield-alt me-1"></i>Proxy Active: ${response.proxy_name}`);
                console.log('🟢 Proxy is active:', response.proxy_name);
            } else {
                statusElement.removeClass('bg-secondary bg-success bg-warning')
                           .addClass('bg-danger')
                           .html(`<i class="fas fa-exclamation-triangle me-1"></i>No Proxy Active`);
                console.log('🔴 No proxy active');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Proxy status check failed:', {xhr, status, error});
            $('#proxyStatus').removeClass('bg-secondary bg-success')
                           .addClass('bg-warning')
                           .html(`<i class="fas fa-question-circle me-1"></i>Proxy Status Unknown`);
        }
    });
}

</script>
@endsection
