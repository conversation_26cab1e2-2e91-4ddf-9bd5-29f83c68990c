@extends('layouts.app')

@section('title', 'Browser DRM Extractor')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);">
                <div class="card-header border-0 text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-browser me-2 text-warning"></i>
                        Browser DRM Key Extractor
                    </h4>
                    <p class="mb-0 text-muted">Extract DRM keys using browser's native Widevine CDM</p>
                </div>
                
                <div class="card-body p-4">
                    <!-- Input Section -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="mpdUrl" class="form-label text-white">MPD URL:</label>
                            <textarea 
                                id="mpdUrl" 
                                class="form-control bg-dark text-white border-secondary" 
                                rows="3" 
                                placeholder="Paste MPD URL here..."
                                style="font-size: 12px;"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="psshInput" class="form-label text-white">PSSH (Optional):</label>
                            <textarea 
                                id="psshInput" 
                                class="form-control bg-dark text-white border-secondary" 
                                rows="3" 
                                placeholder="Paste PSSH here (optional)..."
                                style="font-size: 12px;"></textarea>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" onclick="extractPSSHOnly()">
                                <i class="fas fa-download me-2"></i>Extract PSSH Only
                            </button>
                            <button class="btn btn-warning me-2" onclick="loadManifest()">
                                <i class="fas fa-play me-2"></i>Load Manifest
                            </button>
                            <button class="btn btn-success me-2" onclick="extractKeys()">
                                <i class="fas fa-key me-2"></i>Extract Keys
                            </button>
                            <button class="btn btn-info me-2" onclick="clearResults()">
                                <i class="fas fa-trash me-2"></i>Clear
                            </button>
                        </div>
                    </div>
                    
                    <!-- Video Player -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <video 
                                id="video" 
                                controls 
                                style="width: 100%; max-height: 400px; background: #000;"
                                class="rounded">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                    
                    <!-- Results Section -->
                    <div id="resultsSection" class="d-none">
                        <h5 class="text-white mb-3">
                            <i class="fas fa-key me-2 text-success"></i>Extraction Results
                        </h5>
                        
                        <div id="keysContainer"></div>
                    </div>
                    
                    <!-- Logs Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-white mb-2">
                                <i class="fas fa-terminal me-2"></i>Console Logs
                            </h6>
                            <div 
                                id="consoleLog" 
                                class="bg-dark text-light p-3 rounded" 
                                style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; border: 1px solid #444;">
                                Ready for DRM extraction...<br>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/shaka-player@4.3.0/dist/shaka-player.compiled.js"></script>
<script>
let player;
let extractedKeys = [];

// Initialize Shaka Player
function initPlayer() {
    const video = document.getElementById('video');
    player = new shaka.Player(video);
    
    // Configure player with proper Shahid DRM settings
    player.configure({
        drm: {
            servers: {
                'com.widevine.alpha': 'https://drm.shahid.net/widevine'
            },
            advanced: {
                'com.widevine.alpha': {
                    'videoRobustness': 'SW_SECURE_CRYPTO',
                    'audioRobustness': 'SW_SECURE_CRYPTO'
                }
            }
        },
        streaming: {
            bufferingGoal: 10,
            rebufferingGoal: 2
        }
    });
    
    // Listen for DRM events
    player.addEventListener('drmsessionupdate', onDrmSessionUpdate);
    player.addEventListener('keystatuschanged', onKeyStatusChanged);
    
    log('Shaka Player initialized');
}

// Load manifest
function loadManifest() {
    const mpdUrl = document.getElementById('mpdUrl').value.trim();

    if (!mpdUrl) {
        log('Error: Please enter MPD URL', 'error');
        return;
    }

    log('Loading manifest: ' + mpdUrl);

    // First, try to extract PSSH from MPD
    extractPSSHFromMPD(mpdUrl);

    player.load(mpdUrl).then(() => {
        log('Manifest loaded successfully', 'success');

        // Check if DRM is detected
        setTimeout(() => {
            checkDRMStatus();
        }, 2000);

    }).catch((error) => {
        log('Error loading manifest: ' + error.message, 'error');

        // Try to load without DRM to get basic info
        log('Attempting to load without DRM protection...');
        loadManifestWithoutDRM(mpdUrl);
    });
}

// Load manifest without DRM (for analysis)
function loadManifestWithoutDRM(mpdUrl) {
    // Create a new player instance without DRM
    const tempPlayer = new shaka.Player();
    tempPlayer.configure({
        drm: {
            servers: {}
        }
    });

    tempPlayer.load(mpdUrl).then(() => {
        log('Manifest loaded without DRM - analyzing structure...', 'success');

        const tracks = tempPlayer.getVariantTracks();
        log(`Found ${tracks.length} tracks`, 'info');

        tracks.forEach((track, index) => {
            log(`Track ${index + 1}: ${track.video ? 'Video' : 'Audio'} - ${track.bandwidth} bps`, 'info');
        });

        tempPlayer.destroy();
    }).catch((error) => {
        log('Failed to load manifest even without DRM: ' + error.message, 'error');
    });
}

// Extract PSSH from MPD
function extractPSSHFromMPD(mpdUrl) {
    log('Fetching MPD to extract PSSH...');

    fetch(mpdUrl)
        .then(response => response.text())
        .then(mpdContent => {
            log('MPD content fetched, analyzing...', 'success');

            // Look for ContentProtection elements
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(mpdContent, 'text/xml');

            const contentProtections = xmlDoc.getElementsByTagName('ContentProtection');

            for (let i = 0; i < contentProtections.length; i++) {
                const cp = contentProtections[i];
                const schemeIdUri = cp.getAttribute('schemeIdUri');

                if (schemeIdUri && schemeIdUri.includes('widevine')) {
                    log('Found Widevine ContentProtection', 'success');

                    // Look for PSSH
                    const psshElements = cp.getElementsByTagName('cenc:pssh');
                    if (psshElements.length > 0) {
                        const pssh = psshElements[0].textContent.trim();
                        log('PSSH extracted from MPD: ' + pssh.substring(0, 50) + '...', 'success');

                        // Auto-fill PSSH field
                        document.getElementById('psshInput').value = pssh;

                        // Try to extract KID from PSSH
                        extractKIDFromPSSH(pssh);
                    }
                }
            }
        })
        .catch(error => {
            log('Error fetching MPD: ' + error.message, 'error');
        });
}

// Extract KID from PSSH
function extractKIDFromPSSH(pssh) {
    try {
        log('Extracting KID from PSSH...');

        // Decode base64 PSSH
        const psshData = atob(pssh);
        const psshBytes = new Uint8Array(psshData.length);

        for (let i = 0; i < psshData.length; i++) {
            psshBytes[i] = psshData.charCodeAt(i);
        }

        // Look for 16-byte patterns that might be KIDs
        for (let i = 32; i < psshBytes.length - 16; i++) {
            const potentialKid = psshBytes.slice(i, i + 16);

            // Check if this looks like a valid KID
            const isAllZeros = potentialKid.every(byte => byte === 0);
            const isAllFF = potentialKid.every(byte => byte === 0xFF);

            if (!isAllZeros && !isAllFF) {
                const kidHex = Array.from(potentialKid)
                    .map(byte => byte.toString(16).padStart(2, '0'))
                    .join('');

                log('KID extracted: ' + kidHex, 'success');
                return kidHex;
            }
        }

        log('No KID found in PSSH', 'warning');
        return null;

    } catch (error) {
        log('Error extracting KID: ' + error.message, 'error');
        return null;
    }
}

// Check DRM status
function checkDRMStatus() {
    const video = document.getElementById('video');
    const mediaKeys = video.mediaKeys;

    if (mediaKeys) {
        log('DRM detected: ' + mediaKeys.keySystem, 'success');
        log('Media keys are available for key extraction', 'success');
    } else {
        log('No DRM protection detected in this manifest', 'warning');
        log('This might be a non-DRM manifest or DRM setup failed', 'info');
    }
}

// Extract keys
function extractKeys() {
    log('Starting key extraction...');
    
    // Get media keys
    const video = document.getElementById('video');
    const mediaKeys = video.mediaKeys;
    
    if (!mediaKeys) {
        log('No media keys available. Try loading a DRM-protected manifest first.', 'warning');
        return;
    }
    
    log('Media keys detected: ' + mediaKeys.keySystem);
    
    // This is where we would intercept the license response
    // For now, we'll show what we can detect
    showDetectedInfo();
}

// Show detected DRM information
function showDetectedInfo() {
    const video = document.getElementById('video');
    const mediaKeys = video.mediaKeys;
    
    let info = {
        keySystem: mediaKeys ? mediaKeys.keySystem : 'None',
        videoTracks: [],
        audioTracks: []
    };
    
    // Get track information
    if (player) {
        const tracks = player.getVariantTracks();
        tracks.forEach(track => {
            if (track.video) {
                info.videoTracks.push({
                    id: track.id,
                    bandwidth: track.bandwidth,
                    width: track.video.width,
                    height: track.video.height,
                    codecs: track.video.codecs
                });
            }
            if (track.audio) {
                info.audioTracks.push({
                    id: track.id,
                    bandwidth: track.bandwidth,
                    codecs: track.audio.codecs,
                    language: track.language
                });
            }
        });
    }
    
    displayResults(info);
    log('DRM information extracted', 'success');
}

// Display results
function displayResults(info) {
    const container = document.getElementById('keysContainer');
    const pssh = document.getElementById('psshInput').value.trim();

    let html = '';

    // PSSH Section
    if (pssh) {
        html += `
            <div class="alert alert-success border-0 mb-3" style="background: rgba(40, 167, 69, 0.2);">
                <h6 class="text-success mb-2">
                    <i class="fas fa-shield-alt me-2"></i>PSSH Extracted
                </h6>
                <div class="bg-dark p-3 rounded mt-2" style="border: 1px solid #444;">
                    <code class="text-light" style="font-size: 11px; word-break: break-all; line-height: 1.4;">${pssh}</code>
                </div>
                <button class="btn btn-outline-success btn-sm mt-2" onclick="copyToClipboard('${pssh}', 'PSSH')">
                    <i class="fas fa-copy me-1"></i>Copy PSSH
                </button>
            </div>
        `;
    }

    // DRM System Info
    html += `
        <div class="alert alert-info border-0 mb-3" style="background: rgba(23, 162, 184, 0.2);">
            <h6 class="text-info mb-2">
                <i class="fas fa-info-circle me-2"></i>DRM System Information
            </h6>
            <p class="mb-1"><strong>Key System:</strong> ${info.keySystem}</p>
            <p class="mb-1"><strong>Video Tracks:</strong> ${info.videoTracks.length}</p>
            <p class="mb-0"><strong>Audio Tracks:</strong> ${info.audioTracks.length}</p>
        </div>
    `;

    // Track Details
    if (info.videoTracks.length > 0 || info.audioTracks.length > 0) {
        html += `
            <div class="bg-dark p-3 rounded mb-3">
                <h6 class="text-white mb-2">Track Information:</h6>
                <pre class="text-light mb-0" style="font-size: 12px;">${JSON.stringify({
                    videoTracks: info.videoTracks,
                    audioTracks: info.audioTracks
                }, null, 2)}</pre>
            </div>
        `;
    }

    // Instructions
    html += `
        <div class="alert alert-warning border-0" style="background: rgba(255, 193, 7, 0.2);">
            <h6 class="text-warning mb-2">
                <i class="fas fa-lightbulb me-2"></i>Next Steps
            </h6>
            <ul class="mb-0 text-muted">
                <li>Copy the PSSH above</li>
                <li>Use it with external tools like yt-dlp or CDM tools</li>
                <li>Or use the main DRM extractor for automatic key extraction</li>
            </ul>
        </div>
    `;

    container.innerHTML = html;
    document.getElementById('resultsSection').classList.remove('d-none');
}

// Clear results
function clearResults() {
    document.getElementById('mpdUrl').value = '';
    document.getElementById('psshInput').value = '';
    document.getElementById('keysContainer').innerHTML = '';
    document.getElementById('resultsSection').classList.add('d-none');
    document.getElementById('consoleLog').innerHTML = 'Ready for DRM extraction...<br>';
    
    if (player) {
        player.unload();
    }
    
    extractedKeys = [];
}

// Event handlers
function onDrmSessionUpdate(event) {
    log('DRM session update: ' + event.type);
}

function onKeyStatusChanged(event) {
    log('Key status changed: ' + JSON.stringify(event));
}

// Logging function
function log(message, type = 'info') {
    const logContainer = document.getElementById('consoleLog');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = type === 'error' ? 'text-danger' : 
                      type === 'success' ? 'text-success' : 
                      type === 'warning' ? 'text-warning' : 'text-light';
    
    logContainer.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span><br>`;
    logContainer.scrollTop = logContainer.scrollHeight;
}

// Extract PSSH only (without loading video)
function extractPSSHOnly() {
    const mpdUrl = document.getElementById('mpdUrl').value.trim();

    if (!mpdUrl) {
        log('Error: Please enter MPD URL', 'error');
        return;
    }

    log('Extracting PSSH from MPD without loading video...');
    extractPSSHFromMPD(mpdUrl);
}

// Copy to clipboard function
function copyToClipboard(text, label) {
    if (!text) {
        log(`No ${label} to copy`, 'warning');
        return;
    }

    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(() => {
            log(`${label} copied to clipboard`, 'success');
        }).catch(err => {
            log(`Clipboard API failed: ${err.message}`, 'error');
            fallbackCopyToClipboard(text, label);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text, label);
    }
}

function fallbackCopyToClipboard(text, label) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // Select and copy the text
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            log(`${label} copied to clipboard (fallback method)`, 'success');
        } else {
            throw new Error('execCommand failed');
        }
    } catch (err) {
        log(`Fallback copy failed: ${err.message}`, 'error');

        // Last resort: show the text for manual copy
        log(`Please copy this ${label} manually: ${text.substring(0, 100)}...`, 'warning');

        // Try to show in a prompt
        if (window.prompt) {
            window.prompt(`Please copy this ${label}:`, text);
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initPlayer();
});
</script>
@endpush
