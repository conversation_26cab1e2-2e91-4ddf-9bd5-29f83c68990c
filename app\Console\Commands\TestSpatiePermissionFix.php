<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Admin;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class TestSpatiePermissionFix extends Command
{
    protected $signature = 'test:spatie-permission-fix';
    protected $description = 'Test if Spatie Permission guard fixes are working';

    public function handle()
    {
        $this->info('=== Testing Spatie Permission Guard Fixes ===');
        $this->newLine();

        // Reset cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Test 1: Check guard configuration
        $this->info('1. Testing Guard Configuration:');
        $this->line('   User model guard_name: ' . (new User())->guard_name);
        $this->line('   Admin model guard_name: ' . (new Admin())->guard_name);
        $this->newLine();

        // Test 2: Check role existence
        $this->info('2. Testing Role Existence:');
        $webRoles = Role::where('guard_name', 'web')->count();
        $adminRoles = Role::where('guard_name', 'admin')->count();
        $this->line("   Web guard roles: {$webRoles}");
        $this->line("   Admin guard roles: {$adminRoles}");
        $this->newLine();

        // Test 3: Check permission existence
        $this->info('3. Testing Permission Existence:');
        $webPermissions = Permission::where('guard_name', 'web')->count();
        $adminPermissions = Permission::where('guard_name', 'admin')->count();
        $this->line("   Web guard permissions: {$webPermissions}");
        $this->line("   Admin guard permissions: {$adminPermissions}");
        $this->newLine();

        // Test 4: Test admin role assignment
        $this->info('4. Testing Admin Role Assignment:');
        try {
            $admin = Admin::first();
            if (!$admin) {
                $this->line('   No admin found. Creating test admin...');
                $admin = Admin::create([
                    'name' => 'Test Admin',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'is_active' => true,
                ]);
            }

            $adminRole = Role::where('guard_name', 'admin')->first();
            if ($adminRole) {
                $admin->assignRole($adminRole);
                $this->line("   ✓ Successfully assigned role '{$adminRole->name}' to admin");
                $this->line('   ✓ Admin has role: ' . ($admin->hasRole($adminRole) ? 'YES' : 'NO'));
                
                $permission = $adminRole->permissions->first();
                if ($permission) {
                    $this->line("   ✓ Admin has permission '{$permission->name}': " . ($admin->hasPermissionTo($permission) ? 'YES' : 'NO'));
                }
            } else {
                $this->error('   ✗ No admin guard roles found');
            }
        } catch (\Exception $e) {
            $this->error('   ✗ Error: ' . $e->getMessage());
        }
        $this->newLine();

        // Test 5: Test policy authorization
        $this->info('5. Testing Policy Authorization:');
        try {
            $admin = Admin::first();
            $role = Role::where('guard_name', 'admin')->first();
            
            if ($admin && $role) {
                // Test if admin can manage permissions for the role
                $canManage = $admin->can('managePermissions', $role);
                $this->line("   ✓ Admin can manage permissions: " . ($canManage ? 'YES' : 'NO'));
            }
        } catch (\Exception $e) {
            $this->error('   ✗ Policy error: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        
        if ($adminRoles > 0 && $adminPermissions > 0) {
            $this->info('✓ Spatie Permission setup appears to be working correctly!');
        } else {
            $this->error('✗ Issues detected with Spatie Permission setup');
        }

        return 0;
    }
}
