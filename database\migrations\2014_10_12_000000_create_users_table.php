<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('api_code')->unique();
            $table->string('access_token')->nullable();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('application')->default('ALL');
            $table->timestamp('subscription_expiry')->nullable();
            $table->integer('remaining_days')->default(0);
            $table->boolean('is_active')->default(false);
            $table->json('user_info')->nullable();
            $table->timestamp('login_time')->nullable();
            $table->boolean('remember_me')->default(false);
            $table->string('device_fingerprint')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
