<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; media-src 'self' blob: data: https: http:; connect-src 'self' https: http: ws: wss: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:; style-src 'self' 'unsafe-inline' https: http:; img-src 'self' data: blob: https: http:; font-src 'self' data: https: http:;">
    <title>{{ isset($playerData['title']) ? $playerData['title'] : 'Shahid Player' }}</title>
    <link rel="icon" type="image/png" href="{{ asset('images/shahid-logo.png') }}">
    <style>
        html,body,#player{height:100% !important;overflow:hidden !important}
        body{margin:0 auto;background-color:#000}
        .jw-aspect.jw-reset[style*=padding-top]{padding-top:unset !important}

        /* Fix for blob URL issues */
        video {
            object-fit: contain;
            width: 100%;
            height: 100%;
        }

        /* Ensure player container allows blob content */
        #player, .jw-media, .jw-video {
            position: relative;
            overflow: hidden;
        }

        /* ❌ Hide all title displays */
        .jw-title,
        .jw-title-primary,
        .jw-title-secondary,
        .jw-media-title,
        .custom-episode-title {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }
    </style>
</head>
<body>
    <div id="player" data-skin-url="{{ asset('player/tod_skin.css') }}"></div>
    <button id="playStreamButton" style="display: none;"></button>
    
    <!-- Smart Proxy System - All-in-one solution -->
    <script src="{{ asset('player/smart-proxy.js') }}"></script>

    <!-- Player Configuration and Fix Scripts -->
    <script src="{{ asset('player/player-config.js') }}"></script>
    <script src="{{ asset('player/csp-fix.js') }}"></script>
    <script src="{{ asset('player/drm-fix.js') }}"></script>

    <!-- JWPlayer Scripts -->
    <script src="{{ asset('player/js/jwplayer.js') }}"></script>
    <script>
        // Set JWPlayer base path immediately after loading
        if (typeof jwplayer !== 'undefined') {
            try {
                // Force the correct base path
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.base = '{{ url("/player/js") }}/';

                // Override any existing base configuration
                if (jwplayer.utils && jwplayer.utils.repo) {
                    jwplayer.utils.repo = '{{ url("/player/js") }}/';
                }

                console.log('JWPlayer base path set to:', jwplayer.defaults.base);

                // Set JWPlayer license key - CRITICAL for avoiding 102630 error
                jwplayer.key = 'Z8lq0BAJBEu//qi4oQ7e5kmmCB4pOlIsjYLVL95r9jE=';

                // Disable analytics and external connections that cause CORS issues
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.analytics = false;
                jwplayer.defaults.advertising = false;
                jwplayer.defaults.related = false;
                jwplayer.defaults.sharing = false;

                console.log('🔑 JWPlayer license key and settings configured');
            } catch(e) {
                console.warn('Could not set JWPlayer base path:', e);
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <script src="{{ asset('player/js/provider.shaka.js') }}"></script>
    <script>
        // Fix Shaka Player configuration before loading main player
        if (typeof shaka !== 'undefined') {
            // Install polyfills to patch browser incompatibilities
            shaka.polyfill.installAll();

            // Check for browser support
            if (shaka.Player.isBrowserSupported()) {
                console.log('✅ Shaka Player is supported in this browser');
            } else {
                console.error('❌ Shaka Player is not supported in this browser');
            }

            // Configure Shaka for blob URLs
            shaka.net.NetworkingEngine.registerScheme('blob', function(uri, request, requestType, progressUpdated) {
                return new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest();
                    xhr.open(request.method || 'GET', uri, true);
                    xhr.responseType = 'arraybuffer';

                    xhr.onload = function() {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            resolve({
                                uri: uri,
                                data: xhr.response,
                                headers: {}
                            });
                        } else {
                            reject(new Error('Network error: ' + xhr.status));
                        }
                    };

                    xhr.onerror = function() {
                        reject(new Error('Network error'));
                    };

                    xhr.send(request.body || null);
                });
            });

            console.log('✅ Shaka Player configured for blob URLs');
        }
    </script>
    <script>
        // ✅ Define global variables for player_original.js
        window.playerTitle = '{{ $playerData['title'] ?? 'Shahid Player' }}';
        console.log('🔍 Global title set:', window.playerTitle);
    </script>
    <script src="{{ asset('player/player_original.js') }}"></script>
    <script>
        console.log('🎬 Player page loaded');
        console.log('🔍 JWPlayer available:', typeof jwplayer !== 'undefined');
        console.log('🔍 Shaka available:', typeof shaka !== 'undefined');
        // Get URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                title: params.get('title') || '',
                mpd: params.get('mpd') || '',
                hls: params.get('hls') || '',
                keyId: params.get('keyId') || '',
                key: params.get('key') || '',
                type: params.get('type') || 'dash',
                token: params.get('token') || '',
                licenseUrl: params.get('licenseUrl') || ''
            };
        }

        // Initialize player when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, initializing player...');

            // Check if JWPlayer is available
            if (typeof jwplayer === 'undefined') {
                console.error('❌ JWPlayer not available!');
                document.getElementById('player').innerHTML =
                    '<div style="color: red; text-align: center; padding: 20px; font-size: 18px;">❌ JWPlayer failed to load<br><small>Please refresh the page</small></div>';
                return;
            }
            console.log('🚀 DOM Content Loaded - Starting player initialization');

            // Check if required libraries are loaded
            if (typeof jwplayer === 'undefined') {
                console.error('❌ JWPlayer not loaded!');
                document.getElementById('player').innerHTML =
                    '<div style="color: red; text-align: center; padding: 20px;">❌ JWPlayer failed to load</div>';
                return;
            }

            if (typeof shaka === 'undefined') {
                console.warn('⚠️ Shaka Player not loaded - DASH may not work');
            }

            console.log('✅ Required libraries loaded successfully');
            const params = getUrlParams();

            // Support both URL params and Laravel blade data
            let streamType = params.type || '{{ $playerData['stream_type'] ?? 'dash' }}';
            let streamUrl = params.mpd || params.hls || '{{ $playerData['mpd_url'] ?? $playerData['manifest_url'] ?? '' }}';
            let keyId = params.keyId || '{{ $playerData['kid'] ?? '' }}';
            let key = params.key || '{{ $playerData['drm_key'] ?? '' }}';
            let token = params.token || '';
            let licenseUrl = params.licenseUrl || '';
            let title = params.title || '{{ $playerData['title'] ?? 'Shahid Player' }}';

            // ✅ Debug title variable
            console.log('🔍 Title debugging:');
            console.log('- params.title:', params.title);
            console.log('- playerData.title:', '{{ $playerData['title'] ?? 'NOT_SET' }}');
            console.log('- final_title:', title);

            // Handle keys array from Laravel (for episodes)
            @if(isset($playerData['keys']) && is_array($playerData['keys']) && count($playerData['keys']) > 0)
                if (!keyId && !key) {
                    keyId = '{{ $playerData['keys'][0]['kid'] ?? '' }}';
                    key = '{{ $playerData['keys'][0]['key'] ?? '' }}';
                    console.log('🔑 Using keys from Laravel data:', { keyId, key: key ? key.substring(0, 10) + '...' : 'None' });
                }
            @endif

            // Handle keys from URL params (JSON format)
            if (params.keys) {
                try {
                    const keysArray = JSON.parse(params.keys);
                    if (keysArray && keysArray.length > 0) {
                        keyId = keysArray[0].kid || keyId;
                        key = keysArray[0].key || key;
                        console.log('🔑 Using keys from URL params:', { keyId, key: key ? key.substring(0, 10) + '...' : 'None' });
                    }
                } catch (e) {
                    console.warn('⚠️ Failed to parse keys from URL params:', e);
                }
            }

            // Update page title if provided
            if (title) {
                document.title = title + ' - Shahid Player';
            }

            console.log('🎬 Initializing player with:', { title, streamType, streamUrl, keyId, key: key ? 'Present' : 'None', token, licenseUrl });
            console.log('🔍 Raw URL params:', window.location.search);
            console.log('🔍 Parsed params:', params);
            console.log('🔍 Laravel data:', {!! json_encode($playerData ?? []) !!});

            // Add debugging information
            console.log('🔍 Debug Info:');
            console.log('- Stream Type:', streamType);
            console.log('- Stream URL:', streamUrl);
            console.log('- Key ID:', keyId);
            console.log('- Key:', key ? key.substring(0, 10) + '...' : 'None');
            console.log('- Token:', token ? 'Present' : 'None');
            console.log('- License URL:', licenseUrl || 'None');

            if (streamUrl) {
                console.log('🚀 Using Smart Proxy System');
                console.log('🔍 Stream URL received:', streamUrl);
                console.log('🔍 Stream Type:', streamType);

                // The Smart Proxy will automatically handle URL transformation
                // No need for manual proxy URL creation

                // Validate data before proceeding
                if (window.DRMFix) {
                    if (!window.DRMFix.validateManifestUrl(streamUrl)) {
                        console.error('❌ Invalid manifest URL format');
                        document.getElementById('player').innerHTML =
                            '<div style="color: red; text-align: center; padding: 20px; font-size: 18px;">Invalid manifest URL format</div>';
                        return;
                    }

                    if (streamType === 'dash' && keyId && key) {
                        const keyData = `${keyId}:${key}`;
                        if (!window.DRMFix.validateClearKeys(keyData)) {
                            console.error('❌ Invalid DRM key format');
                            document.getElementById('player').innerHTML =
                                '<div style="color: red; text-align: center; padding: 20px; font-size: 18px;">Invalid DRM key format</div>';
                            return;
                        }
                    }
                }

                // Set up the button with stream data
                const playButton = document.getElementById('playStreamButton');
                if (playButton) {
                    if (streamType === 'hls') {
                        // HLS handling - Smart Proxy will handle URL transformation automatically
                        playButton.dataset.hlsUrl = streamUrl; // Use original URL
                        playButton.dataset.streamType = 'hls';
                        playButton.dataset.keyData = '';
                        playButton.dataset.token = token || '';
                        playButton.dataset.title = title || document.title || 'Shahid Content';

                        console.log('📋 HLS stream configured with Smart Proxy support');

                        if (token) {
                            window.shahidToken = token;
                            console.log('✅ Set global Shahid token for HLS authentication');
                        }
                    } else {
                        // DASH handling - Smart Proxy will handle URL transformation automatically
                        playButton.dataset.mpdUrl = streamUrl; // Use original URL, Smart Proxy will transform it
                        playButton.dataset.streamType = 'dash';
                        playButton.dataset.keyData = keyId && key ? `${keyId}:${key}` : '';
                        playButton.dataset.licenseUrl = licenseUrl || '';
                        playButton.dataset.title = title || document.title || 'Shahid Content';

                        console.log('📋 DASH stream configured with Smart Proxy support');
                    }

                    // Auto-start playback
                    setTimeout(() => {
                        console.log('🎬 Auto-clicking play button');
                        console.log('🔍 Play button dataset:', playButton.dataset);
                        console.log('🔍 Play button exists:', !!playButton);

                        if (playButton) {
                            playButton.click();
                            console.log('✅ Play button clicked successfully');
                        } else {
                            console.error('❌ Play button not found!');
                        }

                        // Unmute audio after a delay and add error handling
                        setTimeout(() => {
                            try {
                                const player = jwplayer('player');
                                if (player) {
                                    // Add error event listener
                                    player.on('error', function(event) {
                                        console.error('❌ JWPlayer Error:', event);

                                        // Handle specific DRM errors
                                        if (event.code === 241403) {
                                            console.error('DRM Error: Problem providing access to protected content');
                                            // Try to reload with different configuration
                                            setTimeout(() => {
                                                console.log('🔄 Attempting to reload player...');
                                                playButton.click();
                                            }, 3000);
                                        }
                                    });

                                    player.setMute(false);
                                    player.setVolume(100);
                                    console.log('Audio unmuted');
                                }

                                const video = document.querySelector('video');
                                if (video) {
                                    video.muted = false;
                                    video.volume = 1.0;

                                    // Add click handler for play/pause
                                    video.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        const player = jwplayer('player');
                                        if (player) {
                                            const state = player.getState();
                                            if (state === 'playing') {
                                                player.pause();
                                                console.log('Video clicked - paused');
                                            } else if (state === 'paused' || state === 'idle') {
                                                player.play();
                                                console.log('Video clicked - playing');
                                            }
                                        }
                                    });

                                    console.log('Video click handler added');
                                }
                            } catch (e) {
                                console.log('Error unmuting:', e);
                            }
                        }, 2000);
                    }, 500);
                }
            } else {
                console.error('❌ No stream URL provided');
                console.log('🔍 Available data:', {
                    params,
                    playerData: {!! json_encode($playerData ?? []) !!}
                });
                document.getElementById('player').innerHTML =
                    '<div style="color: white; text-align: center; padding: 20px; font-size: 18px;">❌ No stream URL provided<br><small>Check console for details</small></div>';
            }

            // Add fallback manual play button if auto-play fails
            setTimeout(() => {
                const playerElement = document.getElementById('player');
                const video = document.querySelector('#player video');

                if (playerElement && !video) {
                    console.warn('⚠️ No video element found after 3 seconds, adding manual play button');
                    playerElement.innerHTML = `
                        <div style="color: white; text-align: center; padding: 20px; font-size: 18px;">
                            <div style="margin-bottom: 20px;">🎬 Player Loading...</div>
                            <button onclick="location.reload()" style="background: #e50914; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                                🔄 Reload Player
                            </button>
                        </div>
                    `;
                }
            }, 3000);
        });
    </script>
</body>
</html>
