<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Shahid Play',
                'type' => 'string',
                'description' => 'Site name'
            ],
            [
                'key' => 'site_description',
                'value' => 'Premium streaming platform',
                'type' => 'string',
                'description' => 'Site description'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'description' => 'Contact email'
            ],

            // Maintenance Settings
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'description' => 'Maintenance mode status'
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'We are currently performing scheduled maintenance. Please check back soon.',
                'type' => 'string',
                'description' => 'Maintenance mode message'
            ],

            // User Settings
            [
                'key' => 'allow_registration',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Allow user registration'
            ],
            [
                'key' => 'max_users',
                'value' => '1000',
                'type' => 'integer',
                'description' => 'Maximum number of users'
            ],
            [
                'key' => 'session_timeout',
                'value' => '120',
                'type' => 'integer',
                'description' => 'Session timeout in minutes'
            ],

            // API Settings
            [
                'key' => 'decryption_api_url',
                'value' => 'http://127.0.0.1:5000',
                'type' => 'string',
                'description' => 'Decryption API URL for DRM-protected content'
            ],
            [
                'key' => 'enable_caching',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable caching to improve performance'
            ],
            [
                'key' => 'enable_logging',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable logging of events and errors'
            ],
            [
                'key' => 'auto_retry',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Automatic retry on failed requests'
            ],
            [
                'key' => 'max_retry_attempts',
                'value' => '3',
                'type' => 'integer',
                'description' => 'Maximum number of retry attempts'
            ],
            [
                'key' => 'cache_duration',
                'value' => '3600',
                'type' => 'integer',
                'description' => 'Cache duration in seconds (default: 1 hour)'
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                [
                    'value' => $setting['value'],
                    'type' => $setting['type'],
                    'description' => $setting['description']
                ]
            );
        }
    }
}
