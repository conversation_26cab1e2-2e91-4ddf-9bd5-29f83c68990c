<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

/**
 * Shahid Base API Service
 * Contains common functionality for authentication, token management, and shared utilities
 */
abstract class ShahidBaseAPI
{
    protected $token;
    protected $baseUrl = 'https://api3.shahid.net';
    protected $tokenFile = 'shahid_token.txt';
    protected $proxies = null;
    protected $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    
    // API Endpoints
    protected $endpoints = [
        'movies' => '/api/v2/movies',
        'series' => '/api/v2/series',
        'channels' => '/api/v2/channels', 
        'search' => '/api/v2/search',
        'content' => '/api/v2/content',
        'stream' => '/api/v2/stream',
        'validate' => '/api/v2/validate'
    ];

    // Headers for API requests
    protected $defaultHeaders = [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'Accept-Language' => 'ar,en;q=0.9',
        'Accept-Encoding' => 'gzip, deflate, br',
        'Connection' => 'keep-alive',
        'Upgrade-Insecure-Requests' => '1'
    ];

    public function __construct($token = null)
    {
        $this->token = $token;

        // Load token from file if not provided
        if (!$this->token) {
            $this->token = $this->loadTokenFromFile();
            Log::info('Token after loading from file', ['token_length' => $this->token ? strlen($this->token) : 0]);
        } else {
            Log::info('Token provided in constructor', ['token_length' => strlen($this->token)]);
        }

        // Set up proxies if configured
        $this->setupProxies();
    }

    /**
     * Load token from storage
     */
    protected function loadTokenFromFile()
    {
        try {
            if (Storage::exists($this->tokenFile)) {
                $token = trim(Storage::get($this->tokenFile));
                Log::info('Shahid token loaded from file');
                return $token;
            } else {
                Log::warning('Token file not found: ' . $this->tokenFile);
            }
        } catch (\Exception $e) {
            Log::error('Error loading token from file: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Save token to storage
     */
    public function saveToken($token)
    {
        try {
            $this->token = $token;
            Storage::put($this->tokenFile, $token);
            Log::info('Token saved successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Error saving token: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Setup proxies configuration
     */
    protected function setupProxies()
    {
        // Add proxy configuration if needed
        // This can be extended based on requirements
    }

    /**
     * Get headers for API requests
     */
    protected function getHeaders($includeAuth = false)
    {
        $headers = $this->defaultHeaders;

        // Add Shahid-specific headers
        $headers['browser_name'] = 'CHROME';
        $headers['browser_version'] = '122.0.0.0';
        $headers['shahid_os'] = 'WINDOWS';
        $headers['language'] = 'EN';
        $headers['origin'] = 'https://shahid.mbc.net';
        $headers['referer'] = 'https://shahid.mbc.net/';

        if ($includeAuth && $this->token) {
            // Use 'token' header instead of Authorization Bearer (Shahid-specific)
            $headers['token'] = $this->token;
        }

        return $headers;
    }

    /**
     * Make HTTP request with error handling
     */
    protected function makeRequest($method, $url, $options = [])
    {
        try {
            $headers = $this->getHeaders($options['auth'] ?? false);
            $httpClient = Http::withHeaders($headers)->timeout(30);

            // Add proxy if configured
            if ($this->proxies) {
                $httpClient = $httpClient->withOptions([
                    'proxy' => $this->proxies
                ]);
            }

            switch (strtoupper($method)) {
                case 'GET':
                    $response = $httpClient->get($url, $options['data'] ?? []);
                    break;
                case 'POST':
                    $response = $httpClient->post($url, $options['data'] ?? []);
                    break;
                case 'PUT':
                    $response = $httpClient->put($url, $options['data'] ?? []);
                    break;
                case 'DELETE':
                    $response = $httpClient->delete($url);
                    break;
                default:
                    throw new \Exception('Unsupported HTTP method: ' . $method);
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                $responseBody = $response->body();
                $responseData = $response->json();

                // Handle specific Shahid API errors
                $errorMessage = 'HTTP ' . $response->status();

                if (isset($responseData['faults']) && is_array($responseData['faults'])) {
                    $fault = $responseData['faults'][0];
                    if (isset($fault['code']) && $fault['code'] == 5004) {
                        $errorMessage = 'This content requires Shahid VIP subscription';
                    } else {
                        $errorMessage = $fault['userMessage'] ?? $fault['internalMessage'] ?? $errorMessage;
                    }
                }

                Log::error("API request failed: {$method} {$url}", [
                    'status' => $response->status(),
                    'body' => $responseBody
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'status_code' => $response->status(),
                    'requires_vip' => isset($responseData['faults'][0]['code']) && $responseData['faults'][0]['code'] == 5004
                ];
            }
        } catch (\Exception $e) {
            Log::error("API request exception: {$method} {$url}", [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check if token is valid
     */
    public function hasValidToken()
    {
        return !empty($this->token);
    }

    /**
     * Validate token with Shahid API
     */
    public function validateToken()
    {
        if (!$this->hasValidToken()) {
            return false;
        }
        
        $response = $this->makeRequest('get', $this->baseUrl . $this->endpoints['validate'], ['auth' => true]);
        return $response !== null;
    }

    /**
     * Get token
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Extract content ID from URL
     */
    public function extractContentId($input)
    {
        // If it's already a numeric ID, return it
        if (is_numeric($input)) {
            return $input;
        }
        
        // Extract from various URL formats
        $patterns = [
            '/\/movie\/(\d+)/',
            '/\/series\/(\d+)/',
            '/\/episode\/(\d+)/',
            '/\/season\/(\d+)/',
            '/\/content\/(\d+)/',
            '/(\d{11,})/', // Long numeric IDs
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }

    /**
     * Get content details by ID
     */
    public function getContentDetails($contentId)
    {
        $url = $this->baseUrl . $this->endpoints['content'] . '/' . $contentId;
        return $this->makeRequest('get', $url);
    }

    /**
     * Get streaming information for content
     */
    public function getStreamingInfo($contentId)
    {
        if (!$this->hasValidToken()) {
            Log::warning('No valid token for streaming info');
            return null;
        }
        
        $url = $this->baseUrl . $this->endpoints['stream'] . '/' . $contentId;
        return $this->makeRequest('get', $url, ['auth' => true]);
    }

    /**
     * Search content by query
     */
    public function searchContent($query, $limit = 20, $type = null)
    {
        $params = [
            'q' => $query,
            'limit' => $limit
        ];
        
        if ($type) {
            $params['type'] = $type;
        }
        
        $url = $this->baseUrl . $this->endpoints['search'] . '?' . http_build_query($params);
        return $this->makeRequest('get', $url);
    }

    /**
     * Get live channels (requires token)
     */
    public function getLiveChannels()
    {
        if (!$this->hasValidToken()) {
            Log::warning('Token required for live channels');
            return null;
        }
        
        $url = $this->baseUrl . $this->endpoints['channels'];
        return $this->makeRequest('get', $url, ['auth' => true]);
    }

    /**
     * Log API response for debugging
     */
    protected function logResponse($method, $url, $response, $context = [])
    {
        Log::info("API Response: {$method} {$url}", array_merge([
            'response_size' => is_array($response) ? count($response) : (is_string($response) ? strlen($response) : 'unknown'),
            'has_data' => !empty($response)
        ], $context));
    }

    /**
     * Handle pagination for large datasets
     */
    protected function handlePagination($baseUrl, $limit, $offset, $fetchAll, $options = [])
    {
        $allResults = [];
        $currentOffset = $offset;
        
        do {
            $params = array_merge([
                'limit' => $limit,
                'offset' => $currentOffset
            ], $options['params'] ?? []);
            
            $url = $baseUrl . '?' . http_build_query($params);
            $response = $this->makeRequest('get', $url, $options);
            
            if (!$response || !isset($response['data'])) {
                break;
            }
            
            $results = $response['data'];
            $allResults = array_merge($allResults, $results);
            
            // If we got fewer results than requested, we've reached the end
            if (count($results) < $limit) {
                break;
            }
            
            $currentOffset += $limit;
            
            // Safety check to prevent infinite loops
            if (count($allResults) > 10000) {
                Log::warning('Pagination safety limit reached');
                break;
            }
            
        } while ($fetchAll);
        
        return $allResults;
    }

    /**
     * Process image URL - replace placeholders and optimize
     */
    protected function processImageUrl($imageUrl)
    {
        if (!$imageUrl) {
            return null;
        }

        // Handle different image URL formats
        if (is_array($imageUrl)) {
            // If it's an array, try to get the URL from common keys
            $url = $imageUrl['url'] ?? $imageUrl['src'] ?? $imageUrl['href'] ?? null;
            if (!$url) {
                return null;
            }
            $imageUrl = $url;
        }

        // Ensure it's a string
        if (!is_string($imageUrl)) {
            return null;
        }

        // Replace common placeholders
        $processedUrl = str_replace([
            '{width}', '{height}', '{croppingPoint}',
            '{WIDTH}', '{HEIGHT}', '{CROPPINGPOINT}'
        ], [
            '400', '600', '',
            '400', '600', ''
        ], $imageUrl);

        // Ensure HTTPS
        if (strpos($processedUrl, 'http://') === 0) {
            $processedUrl = str_replace('http://', 'https://', $processedUrl);
        }

        // Add protocol if missing
        if (strpos($processedUrl, '//') === 0) {
            $processedUrl = 'https:' . $processedUrl;
        }

        return $processedUrl;
    }
}
