<?php

namespace App\Services;

use App\Models\SeriesCache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SeriesCacheService
{
    protected $seriesAPI;

    public function __construct(ShahidSeriesAPI $seriesAPI)
    {
        $this->seriesAPI = $seriesAPI;
    }

    /**
     * Get series with caching - main method
     */
    public function getSeries($country = 'EG', $limit = 50, $offset = 0, $forceRefresh = false)
    {
        try {
            Log::info("📦 Getting series with cache - Country: {$country}, Limit: {$limit}, Offset: {$offset}, Force: " . ($forceRefresh ? 'Yes' : 'No'));

            // If force refresh, clear cache first
            if ($forceRefresh) {
                $this->clearCountryCache($country);
                Log::info("🗑️ Cache cleared for country: {$country}");
            }

            // Check if we have cached data
            $cachedSeries = $this->getCachedSeries($country);
            
            if (!empty($cachedSeries) && !$forceRefresh) {
                Log::info("⚡ Using cached series data - Total: " . count($cachedSeries));

                // Apply pagination to cached data
                // If limit is high (200+), return all cached data
                if ($limit >= 200 || $limit == 999) {
                    $paginatedSeries = $cachedSeries;
                    Log::info("📋 Returning all cached series: " . count($cachedSeries));
                } else {
                    $paginatedSeries = array_slice($cachedSeries, $offset, $limit);
                    Log::info("📄 Returning paginated series: " . count($paginatedSeries) . " of " . count($cachedSeries));
                }

                return [
                    'success' => true,
                    'series' => $paginatedSeries,
                    'total' => count($cachedSeries),
                    'cached' => true,
                    'cache_date' => $this->getLatestCacheDate($country),
                ];
            }

            // No cache or force refresh - fetch from API
            Log::info("🌐 Fetching fresh series data from API");
            $apiResult = $this->seriesAPI->getSeries($country, 200, 0, true); // fetchAll = true to get all series

            if (!isset($apiResult['series']) || empty($apiResult['series'])) {
                return [
                    'success' => false,
                    'error' => 'No series data available',
                    'cached' => false,
                ];
            }

            // Cache the fresh data
            $this->cacheSeriesData($country, $apiResult['series']);
            
            // Apply pagination
            $paginatedSeries = array_slice($apiResult['series'], $offset, $limit);

            return [
                'success' => true,
                'series' => $paginatedSeries,
                'total' => count($apiResult['series']),
                'cached' => false,
                'cache_date' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error in getSeries: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'cached' => false,
            ];
        }
    }

    /**
     * Get series details with caching
     */
    public function getSeriesDetails($seriesId, $country = 'EG', $forceRefresh = false)
    {
        try {
            Log::info("📺 Getting series details with cache - ID: {$seriesId}, Country: {$country}");

            // Check cache first
            $cachedSeries = SeriesCache::getCachedSeries($country, $seriesId);
            
            if ($cachedSeries && $cachedSeries->is_complete && !$cachedSeries->needsRefresh() && !$forceRefresh) {
                Log::info("⚡ Using cached series details");
                return [
                    'success' => true,
                    'series_data' => $cachedSeries->seasons_data,
                    'series_info' => [
                        'title' => $cachedSeries->title,
                        'description' => $cachedSeries->description,
                        'poster_url' => $cachedSeries->poster_url,
                        'metadata' => $cachedSeries->metadata,
                        'total_seasons' => $cachedSeries->total_seasons,
                        'total_episodes' => $cachedSeries->total_episodes,
                    ],
                    'cached' => true,
                    'cache_date' => $cachedSeries->last_updated->toISOString(),
                ];
            }

            // Fetch fresh data
            Log::info("🌐 Fetching fresh series details from API");
            $seasonsResult = $this->seriesAPI->getSeriesSeasons($seriesId);
            
            if (!$seasonsResult['success']) {
                return $seasonsResult;
            }

            // Get complete data for all seasons
            $completeData = $this->getCompleteSeriesData($seriesId, $seasonsResult['data']);
            
            // Cache the complete data
            $this->cacheCompleteSeriesData($country, $seriesId, $completeData);

            return [
                'success' => true,
                'series_data' => $completeData['seasons_data'],
                'series_info' => $completeData['series_info'],
                'cached' => false,
                'cache_date' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error in getSeriesDetails: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'cached' => false,
            ];
        }
    }

    /**
     * Get cached series for a country
     */
    protected function getCachedSeries($country)
    {
        $cached = SeriesCache::getCachedSeriesByCountry($country);
        
        return $cached->map(function ($series) {
            return [
                'id' => $series->series_id,
                'title' => $series->title,
                'description' => $series->description,
                'poster_url' => $series->poster_url,
                'metadata' => $series->metadata,
                'total_seasons' => $series->total_seasons,
                'total_episodes' => $series->total_episodes,
                'cache_date' => $series->last_updated->toISOString(),
            ];
        })->toArray();
    }

    /**
     * Cache series data
     */
    protected function cacheSeriesData($country, $seriesArray)
    {
        Log::info("💾 Caching " . count($seriesArray) . " series for country: {$country}");
        
        foreach ($seriesArray as $series) {
            SeriesCache::cacheSeries($country, $series['id'], [
                'title' => $series['title'] ?? 'Unknown Series',
                'description' => $series['description'] ?? '',
                'poster_url' => $series['poster_url'] ?? null,
                'metadata' => [
                    'year' => $series['year'] ?? null,
                    'genres' => $series['genres'] ?? [],
                    'cast' => $series['cast'] ?? [],
                    'country' => $series['country'] ?? $country,
                ],
                'seasons_data' => [],
                'total_seasons' => 0,
                'total_episodes' => 0,
                'is_complete' => false,
            ]);
        }
        
        Log::info("✅ Series cached successfully");
    }

    /**
     * Get complete series data (all seasons and episodes)
     */
    protected function getCompleteSeriesData($seriesId, $seasons)
    {
        $completeSeasons = [];
        $totalEpisodes = 0;
        $seriesInfo = null;

        foreach ($seasons as $season) {
            // Get episodes for this season
            $episodesResult = $this->seriesAPI->getSeasonEpisodes($season['id']);
            
            if ($episodesResult['success']) {
                $season['episodes'] = $episodesResult['data'];
                $totalEpisodes += count($episodesResult['data']);
                
                // Extract series info from first season
                if (!$seriesInfo && isset($season['series_details'])) {
                    $seriesInfo = $season['series_details'];
                }
            } else {
                $season['episodes'] = [];
            }
            
            $completeSeasons[] = $season;
        }

        return [
            'seasons_data' => $completeSeasons,
            'series_info' => [
                'title' => $seriesInfo['title'] ?? 'Unknown Series',
                'description' => $seriesInfo['description'] ?? '',
                'poster_url' => $seriesInfo['poster_url'] ?? null,
                'metadata' => $seriesInfo,
                'total_seasons' => count($completeSeasons),
                'total_episodes' => $totalEpisodes,
            ],
            'total_seasons' => count($completeSeasons),
            'total_episodes' => $totalEpisodes,
        ];
    }

    /**
     * Cache complete series data
     */
    protected function cacheCompleteSeriesData($country, $seriesId, $completeData)
    {
        SeriesCache::cacheSeries($country, $seriesId, [
            'title' => $completeData['series_info']['title'],
            'description' => $completeData['series_info']['description'],
            'poster_url' => $completeData['series_info']['poster_url'],
            'metadata' => $completeData['series_info']['metadata'],
            'seasons_data' => $completeData['seasons_data'],
            'total_seasons' => $completeData['total_seasons'],
            'total_episodes' => $completeData['total_episodes'],
            'is_complete' => true,
        ]);
        
        Log::info("💾 Complete series data cached for: {$seriesId}");
    }

    /**
     * Clear cache for a country
     */
    public function clearCountryCache($country)
    {
        return SeriesCache::clearCountryCache($country);
    }

    /**
     * Get latest cache date for a country
     */
    protected function getLatestCacheDate($country)
    {
        $latest = SeriesCache::where('country', $country)
                            ->orderBy('last_updated', 'desc')
                            ->first();
        
        return $latest ? $latest->last_updated->toISOString() : null;
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats($country)
    {
        $total = SeriesCache::where('country', $country)->count();
        $complete = SeriesCache::where('country', $country)->where('is_complete', true)->count();
        $latest = $this->getLatestCacheDate($country);
        
        return [
            'total_cached' => $total,
            'complete_cached' => $complete,
            'latest_update' => $latest,
            'cache_percentage' => $total > 0 ? round(($complete / $total) * 100, 2) : 0,
        ];
    }
}
