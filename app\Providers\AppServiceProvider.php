<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register SeriesCacheService
        $this->app->singleton(\App\Services\SeriesCacheService::class, function ($app) {
            return new \App\Services\SeriesCacheService(
                $app->make(\App\Services\ShahidSeriesAPI::class)
            );
        });

        // Register MoviesCacheService
        $this->app->singleton(\App\Services\MoviesCacheService::class, function ($app) {
            return new \App\Services\MoviesCacheService(
                $app->make(\App\Services\ShahidMoviesAPI::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
