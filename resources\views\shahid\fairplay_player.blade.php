<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; media-src 'self' blob: data: https: http:; connect-src 'self' https: http: ws: wss: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:; style-src 'self' 'unsafe-inline' https: http:; img-src 'self' data: blob: https: http:; font-src 'self' data: https: http:;">
    <title>FairPlay Test Player</title>
    <link rel="icon" type="image/png" href="{{ asset('images/shahid-logo.png') }}">
    <style>
        html,body,#player{height:100% !important;overflow:hidden !important}
        body{margin:0 auto;background-color:#000}
        .jw-aspect.jw-reset[style*=padding-top]{padding-top:unset !important}
        .jw-title,.jw-title-primary,.jw-title-secondary,.jw-media-title{display:none!important}
    </style>
</head>
<body>
    <div id="player" data-skin-url="{{ asset('player/tod_skin.css') }}"></div>
    <button id="playStreamButton" style="display:none;"></button>

    <!-- Keep the page minimal for FairPlay; avoid helper scripts that transform URLs or blob video -->

    <!-- Local JWPlayer bundle (avoids CDN issues) -->
    <script src="{{ asset('player/js/jwplayer.js') }}"></script>
    <script>
        if (typeof jwplayer !== 'undefined') {
            try {
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.base = '{{ url('/player/js') }}/';
                if (jwplayer.utils && jwplayer.utils.repo) {
                    jwplayer.utils.repo = '{{ url('/player/js') }}/';
                }
                jwplayer.key = 'Z8lq0BAJBEu//qi4oQ7e5kmmCB4pOlIsjYLVL95r9jE=';
                jwplayer.defaults.analytics = false;
                jwplayer.defaults.advertising = false;
                jwplayer.defaults.related = false;
                jwplayer.defaults.sharing = false;
            } catch(e) { console.warn('JWPlayer init warning', e); }
        }
    </script>

    <!-- Shaka Player (for FairPlay EME) -->
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <!-- Main FairPlay logic -->
    <script src="{{ asset('player/fairplay_player.js') }}"></script>
    <script>
        function getParams() {
            const p = new URLSearchParams(window.location.search);
            return {
                title: p.get('title') || 'FairPlay Test',
                hls: p.get('hls') || '',
                keyId: p.get('keyId') || p.get('kid') || '',
                licenseUrl: p.get('licenseUrl') || '',
                certificateUrl: p.get('certificateUrl') || p.get('cert') || '',
                iv: p.get('iv') || '',
                brandGuid: p.get('brandGuid') || '',
                token: p.get('token') || ''
            };
        }

        document.addEventListener('DOMContentLoaded', function() {
            const params = getParams();
            document.title = params.title + ' - FairPlay Test Player';

            if (!params.hls) {
                document.getElementById('player').innerHTML = '<div style="color:#fff;text-align:center;padding:20px;">❌ Missing HLS URL</div>';
                console.error('Missing hls param');
                return;
            }

            // Configure HLS through the smart proxy; JW/HLS.js will request via /video-proxy
            const playBtn = document.getElementById('playStreamButton');
            playBtn.dataset.streamType = 'hls';
            playBtn.dataset.hlsUrl = params.hls; // smart-proxy/player_original will convert to /video-proxy/manifest
            playBtn.dataset.title = params.title;
            playBtn.dataset.keyData = '';
            playBtn.dataset.licenseUrl = params.licenseUrl || '';
            playBtn.dataset.certificateUrl = params.certificateUrl || '';

            if (params.token) {
                window.shahidToken = params.token;
            }

            // Auto start
            setTimeout(() => {
                try { playBtn.click(); } catch(e) { console.warn('Auto play failed', e); }
            }, 400);
        });
    </script>
</body>
</html>

