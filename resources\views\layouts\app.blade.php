<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Shahid Play')</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        

        .content-area {
            padding: 30px;
            margin-left: 250px; /* Sidebar width */
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 25px;
            font-weight: 500;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }


        
        /* ===== RESPONSIVE LAYOUT ADJUSTMENTS ===== */
        /* تعديل المقاسات فقط مع الحفاظ على التصميم */

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .content-area {
                margin-left: 250px;
                padding: 25px;
            }

            .card {
                border-radius: 10px;
            }

            .btn-primary {
                padding: 8px 20px;
                font-size: 0.9rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .content-area {
                margin-left: 0;
                padding: 15px 10px;
                padding-top: 80px;
            }

            .card {
                border-radius: 8px;
                margin-bottom: 1rem;
            }

            .btn-primary {
                padding: 6px 15px;
                font-size: 0.85rem;
            }

            .navbar-brand {
                font-size: 1.3rem;
            }
        }

        /* Mobile Small (أقل من 576px) */
        @media (max-width: 575px) {
            .content-area {
                margin-left: 0;
                padding: 10px 5px;
                padding-top: 70px;
            }

            .card {
                border-radius: 6px;
                margin-bottom: 0.75rem;
            }

            .btn-primary {
                padding: 5px 12px;
                font-size: 0.8rem;
            }

            .navbar-brand {
                font-size: 1.2rem;
            }

            .alert {
                padding: 10px 15px;
                font-size: 0.85rem;
            }
        }

        /* iPhone Specific */
        @media only screen and (max-width: 320px) {
            .content-area {
                padding: 8px 3px;
                padding-top: 65px;
            }

            .navbar-brand {
                font-size: 1.1rem;
            }

            .btn-primary {
                padding: 4px 10px;
                font-size: 0.75rem;
            }
        }

        /* Landscape على الموبايل */
        @media screen and (orientation: landscape) and (max-height: 500px) {
            .content-area {
                padding-top: 60px;
            }

            .navbar {
                min-height: 50px;
            }
        }

        /* ===== NAVBAR REFRESH BUTTON STYLING ===== */
        .navbar .refresh-btn {
            background: rgba(255, 255, 255, 0.2) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
            padding: 0.375rem 0.75rem !important;
            font-size: 0.875rem !important;
        }

        .navbar .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            transform: translateY(-1px) !important;
            color: white !important;
        }

        .navbar .refresh-btn:active {
            transform: translateY(0) !important;
            background: rgba(255, 255, 255, 0.15) !important;
        }

        .navbar .refresh-btn:focus {
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.25) !important;
            color: white !important;
        }

        /* Loading animation for navbar refresh button */
        .navbar .refresh-btn.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .navbar .refresh-btn.loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments for navbar refresh button */
        @media (max-width: 575px) {
            .navbar .refresh-btn {
                padding: 0.25rem 0.5rem !important;
                font-size: 0.8rem !important;
            }
        }

        @media only screen and (max-width: 320px) {
            .navbar .refresh-btn {
                padding: 0.2rem 0.4rem !important;
                font-size: 0.75rem !important;
            }
        }

        /* ===== SWEETALERT2 CUSTOM STYLING ===== */

        /* Custom SweetAlert2 theme to match site design */
        .swal2-popup {
            border-radius: 15px !important;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            margin-top: 20px !important;
        }

        /* Position alerts at top */
        .swal2-container.swal2-top {
            padding-top: 20px !important;
        }

        /* ===== TOAST STYLING ===== */

        /* Clean minimal toast styling - like your image */
        .swal2-toast {
            border-radius: 4px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
            border: 1px solid #e0e0e0 !important;
            backdrop-filter: none !important;
            max-width: 350px !important;
            min-width: 300px !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            padding: 12px 16px !important;
            background: #ffffff !important;
        }

        .swal2-toast .swal2-title {
            font-size: 0.9rem !important;
            font-weight: 400 !important;
            margin: 0 !important;
            color: #333333 !important;
            line-height: 1.4 !important;
        }

        .swal2-toast .swal2-icon {
            margin: 0 10px 0 0 !important;
            width: 18px !important;
            height: 18px !important;
            min-width: 18px !important;
            border: none !important;
            background: none !important;
        }

        /* Fix icon display issues */
        .swal2-toast .swal2-icon .swal2-icon-content {
            font-size: 14px !important;
            font-weight: bold !important;
        }

        .swal2-toast .swal2-icon::before {
            display: none !important;
        }

        .swal2-toast .swal2-timer-progress-bar {
            height: 2px !important;
            background: rgba(0, 0, 0, 0.1) !important;
        }

        .swal2-toast .swal2-content {
            font-size: 0.85rem !important;
            margin: 2px 0 0 0 !important;
            color: #666666 !important;
        }

        /* Success toast - Minimal green like your image */
        .swal2-toast.swal2-icon-success {
            background: #ffffff !important;
            border: 1px solid #d4edda !important;
            border-left: 3px solid #28a745 !important;
        }

        .swal2-toast.swal2-icon-success .swal2-icon {
            color: #28a745 !important;
        }

        .swal2-toast.swal2-icon-success .swal2-icon .swal2-icon-content::before {
            content: "✓" !important;
            font-family: inherit !important;
        }

        /* Error toast - Minimal red */
        .swal2-toast.swal2-icon-error {
            background: #ffffff !important;
            border: 1px solid #f8d7da !important;
            border-left: 3px solid #dc3545 !important;
        }

        .swal2-toast.swal2-icon-error .swal2-icon {
            color: #dc3545 !important;
        }

        .swal2-toast.swal2-icon-error .swal2-icon .swal2-icon-content::before {
            content: "✕" !important;
            font-family: inherit !important;
        }

        /* Warning toast - Minimal yellow */
        .swal2-toast.swal2-icon-warning {
            background: #ffffff !important;
            border: 1px solid #fff3cd !important;
            border-left: 3px solid #ffc107 !important;
        }

        .swal2-toast.swal2-icon-warning .swal2-icon {
            color: #ffc107 !important;
        }

        .swal2-toast.swal2-icon-warning .swal2-icon .swal2-icon-content::before {
            content: "⚠" !important;
            font-family: inherit !important;
        }

        /* Info toast - Minimal blue */
        .swal2-toast.swal2-icon-info {
            background: #ffffff !important;
            border: 1px solid #d1ecf1 !important;
            border-left: 3px solid #17a2b8 !important;
        }

        .swal2-toast.swal2-icon-info .swal2-icon {
            color: #17a2b8 !important;
        }

        .swal2-toast.swal2-icon-info .swal2-icon .swal2-icon-content::before {
            content: "ℹ" !important;
            font-family: inherit !important;
        }

        .swal2-title {
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }

        .swal2-content {
            color: #5a6c7d !important;
            font-size: 1rem !important;
        }

        .swal2-confirm {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 8px !important;
            font-weight: 500 !important;
            padding: 0.75rem 1.5rem !important;
            transition: all 0.3s ease !important;
        }

        .swal2-confirm:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
        }

        .swal2-cancel {
            background: #6c757d !important;
            border: none !important;
            border-radius: 8px !important;
            font-weight: 500 !important;
            padding: 0.75rem 1.5rem !important;
            transition: all 0.3s ease !important;
        }

        .swal2-cancel:hover {
            background: #5a6268 !important;
            transform: translateY(-2px) !important;
        }

        .swal2-icon {
            border: none !important;
        }

        .swal2-icon.swal2-success {
            color: #28a745 !important;
        }

        .swal2-icon.swal2-error {
            color: #dc3545 !important;
        }

        .swal2-icon.swal2-warning {
            color: #ffc107 !important;
        }

        .swal2-icon.swal2-info {
            color: #17a2b8 !important;
        }

        .swal2-timer-progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        /* Confirmation dialogs styling */
        .swal2-popup-confirm {
            border-radius: 15px !important;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2) !important;
        }

        /* Responsive adjustments for minimal Toast */
        @media (max-width: 768px) {
            .swal2-toast {
                max-width: 320px !important;
                min-width: 280px !important;
                margin: 10px !important;
                padding: 10px 14px !important;
            }

            .swal2-toast .swal2-title {
                font-size: 0.85rem !important;
            }

            .swal2-toast .swal2-content {
                font-size: 0.8rem !important;
            }

            .swal2-toast .swal2-icon {
                width: 16px !important;
                height: 16px !important;
                min-width: 16px !important;
            }
        }

        @media (max-width: 576px) {
            .swal2-toast {
                max-width: 300px !important;
                min-width: 260px !important;
                margin: 8px !important;
                padding: 10px 12px !important;
            }

            .swal2-toast .swal2-title {
                font-size: 0.8rem !important;
            }

            .swal2-toast .swal2-content {
                font-size: 0.75rem !important;
            }

            .swal2-toast .swal2-icon {
                width: 16px !important;
                height: 16px !important;
                min-width: 16px !important;
                margin: 0 8px 0 0 !important;
            }
        }
    </style>
    
    @yield('styles')
    @stack('styles')
</head>
<body>
    @auth
    <!-- Include Sidebar Component -->
    @include('components.sidebar')
    @endauth

    <!-- Mobile Navbar for small screens -->
    @auth
    <nav class="navbar navbar-expand-md navbar-dark d-md-none" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('dashboard') }}">
                <i class="fas fa-play-circle me-2"></i>
                Shahid Play
            </a>
            <div class="d-flex align-items-center">
                <!-- Refresh button for movies/series/channels pages -->
                @if(request()->routeIs('shahid.movies'))
                    <button class="btn refresh-btn btn-sm me-2" onclick="loadMovies()" title="Refresh Movies">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                @elseif(request()->routeIs('shahid.series'))
                    <button class="btn refresh-btn btn-sm me-2" onclick="loadSeries()" title="Refresh Series">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                @elseif(request()->routeIs('shahid.channels'))
                    <button class="btn refresh-btn btn-sm me-2" onclick="loadChannels()" title="Refresh Channels">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                @endif
                <!-- Menu toggle button -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mobileNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
            </div>
            <div class="collapse navbar-collapse" id="mobileNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('shahid.movies') ? 'active' : '' }}" href="{{ route('shahid.movies') }}">
                            <i class="fas fa-video me-2"></i>Movies
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('shahid.series') ? 'active' : '' }}" href="{{ route('shahid.series') }}">
                            <i class="fas fa-tv me-2"></i>Series
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('shahid.channels') ? 'active' : '' }}" href="{{ route('shahid.channels') }}">
                            <i class="fas fa-broadcast-tower me-2"></i>Live Channels
                        </a>
                    </li>

                    <li class="nav-item">
                        <form action="{{ route('logout') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="nav-link border-0 bg-transparent">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    @endauth

    <!-- Main Content -->
    <div class="content-area">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('info'))
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ session('info') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @yield('content')
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // CSRF Token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // ===== SWEETALERT2 UNIFIED SYSTEM =====

        // ===== COMPACT TOAST SYSTEM SETUP =====
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            showCloseButton: false,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer);
                toast.addEventListener('mouseleave', Swal.resumeTimer);
            }
        });

        // Success messages with custom icon
        window.showSuccess = function(title, message = '') {
            Toast.fire({
                iconHtml: '<i class="fas fa-check-circle" style="color: #28a745;"></i>',
                title: title || 'Success!',
                text: message
            });
        };

        // Error messages with custom icon
        window.showError = function(title, message = '') {
            Toast.fire({
                iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>',
                title: title || 'Error!',
                text: message,
                timer: 4000
            });
        };

        // Warning messages with custom icon
        window.showWarning = function(title, message = '') {
            Toast.fire({
                iconHtml: '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>',
                title: title || 'Warning!',
                text: message,
                timer: 3500
            });
        };

        // Info messages with custom icon
        window.showInfo = function(title, message = '') {
            Toast.fire({
                iconHtml: '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>',
                title: title || 'Information',
                text: message
            });
        };

        // Confirmation dialogs - Keep in center for important actions
        window.showConfirm = function(title, message, callback) {
            Swal.fire({
                icon: 'question',
                title: title || 'Are you sure?',
                text: message,
                position: 'center',
                showCancelButton: true,
                confirmButtonText: 'Yes',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                reverseButtons: true,
                backdrop: 'rgba(0, 0, 0, 0.5)',
                allowOutsideClick: false,
                focusCancel: true,
                customClass: {
                    popup: 'swal2-popup-confirm'
                }
            }).then((result) => {
                if (result.isConfirmed && callback) {
                    callback();
                }
            });
        };

        // Loading messages
        window.showLoading = function(title = 'Loading...') {
            Swal.fire({
                title: title,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        };

        // Close loading
        window.closeLoading = function() {
            Swal.close();
        };

        // Custom styled alerts
        window.showCustomAlert = function(options) {
            const defaultOptions = {
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#6c757d',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp'
                }
            };

            Swal.fire({...defaultOptions, ...options});
        };

        // Handle Laravel validation errors
        window.showValidationErrors = function(errors) {
            let errorList = '<ul style="text-align: left; margin: 0;">';

            if (typeof errors === 'object') {
                Object.keys(errors).forEach(function(key) {
                    if (Array.isArray(errors[key])) {
                        errors[key].forEach(function(error) {
                            errorList += '<li>' + error + '</li>';
                        });
                    } else {
                        errorList += '<li>' + errors[key] + '</li>';
                    }
                });
            } else {
                errorList += '<li>' + errors + '</li>';
            }

            errorList += '</ul>';

            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                html: errorList,
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc3545',
                customClass: {
                    htmlContainer: 'text-start'
                }
            });
        };

        // ===== COPY TO CLIPBOARD FUNCTION =====
        window.copyToClipboard = function(text, button, successMessage = 'Copied to clipboard!') {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(function() {
                    // Change button icon temporarily if button provided
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            const originalClass = icon.className;
                            icon.className = 'fas fa-check text-success';

                            setTimeout(() => {
                                icon.className = originalClass;
                            }, 2000);
                        }
                    }

                    Toast.fire({
                        icon: 'success',
                        title: successMessage
                    });
                }).catch(function(err) {
                    console.error('Failed to copy: ', err);
                    fallbackCopyTextToClipboard(text, button, successMessage);
                });
            } else {
                fallbackCopyTextToClipboard(text, button, successMessage);
            }
        };

        // Fallback copy function for older browsers
        function fallbackCopyTextToClipboard(text, button, successMessage) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            const originalClass = icon.className;
                            icon.className = 'fas fa-check text-success';

                            setTimeout(() => {
                                icon.className = originalClass;
                            }, 2000);
                        }
                    }

                    Toast.fire({
                        icon: 'success',
                        title: successMessage
                    });
                } else {
                    Toast.fire({
                        icon: 'error',
                        title: 'Failed to copy'
                    });
                }
            } catch (err) {
                Toast.fire({
                    icon: 'error',
                    title: 'Copy not supported by browser'
                });
            }

            document.body.removeChild(textArea);
        }

        // Auto-show Laravel session messages
        @if(session('success'))
            showSuccess('Success', '{{ session('success') }}');
        @endif

        @if(session('error'))
            showError('Error', '{{ session('error') }}');
        @endif

        @if(session('warning'))
            showWarning('Warning', '{{ session('warning') }}');
        @endif

        @if(session('info'))
            showInfo('Information', '{{ session('info') }}');
        @endif

        @if($errors->any())
            showValidationErrors(@json($errors->all()));
        @endif
    </script>

    <!-- Custom Alert System - Load after Toast is defined -->
    <script src="{{ asset('js/alerts.js') }}"></script>
    
    @yield('scripts')
    @stack('scripts')
</body>
</html>
