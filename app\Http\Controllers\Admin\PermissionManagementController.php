<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Permission;

class PermissionManagementController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
    }

    /**
     * Display a listing of permissions.
     */
    public function index(Request $request): JsonResponse
    {
        // $this->authorize('viewAny', Permission::class);

        $query = Permission::with(['roles', 'users'])
                          ->withCount(['roles', 'users']);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('guard_name', 'like', "%{$search}%");
            });
        }

        // Filter by guard
        if ($request->has('guard')) {
            $query->where('guard_name', $request->get('guard'));
        }

        // Filter by category (based on permission name patterns)
        if ($request->has('category')) {
            $category = $request->get('category');
            switch ($category) {
                case 'user_management':
                    $query->where('name', 'like', '%user%');
                    break;
                case 'admin_management':
                    $query->where('name', 'like', '%admin%');
                    break;
                case 'role_management':
                    $query->where('name', 'like', '%role%');
                    break;
                case 'permission_management':
                    $query->where('name', 'like', '%permission%');
                    break;
                case 'subscription_management':
                    $query->where('name', 'like', '%subscription%');
                    break;
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $permissions = $query->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $permissions,
            'message' => 'Permissions retrieved successfully'
        ]);
    }

    /**
     * Get all permissions for role management (no pagination).
     */
    public function getAllPermissions(): JsonResponse
    {
        $permissions = Permission::orderBy('name', 'asc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'data' => $permissions
            ],
            'message' => 'All permissions retrieved successfully'
        ]);
    }

    /**
     * Store a newly created permission.
     */
    public function store(Request $request): JsonResponse
    {
        $this->authorize('create', Permission::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
            'guard_name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
        ]);

        $validated['guard_name'] = $validated['guard_name'] ?? 'web';

        $permission = Permission::create($validated);

        // Log activity
        AdminActivityLog::logActivity([
            'admin_id' => Auth::guard('admin')->id(),
            'action' => 'created',
            'target_type' => 'Permission',
            'target_id' => $permission->id,
            'target_name' => $permission->name,
            'description' => "Created permission: {$permission->name}",
            'new_values' => $permission->only(['name', 'guard_name']),
        ]);

        return response()->json([
            'success' => true,
            'data' => $permission,
            'message' => 'Permission created successfully'
        ], 201);
    }

    /**
     * Display the specified permission.
     */
    public function show(Permission $permission): JsonResponse
    {
        $this->authorize('view', $permission);

        $permission->load(['roles', 'users']);
        $permission->loadCount(['roles', 'users']);

        return response()->json([
            'success' => true,
            'data' => $permission,
            'message' => 'Permission retrieved successfully'
        ]);
    }

    /**
     * Update the specified permission.
     */
    public function update(Request $request, Permission $permission): JsonResponse
    {
        $this->authorize('update', $permission);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'guard_name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
        ]);

        // Store old values for logging
        $oldValues = $permission->only(['name', 'guard_name']);

        $permission->update($validated);

        // Log activity
        AdminActivityLog::logActivity([
            'admin_id' => Auth::guard('admin')->id(),
            'action' => 'updated',
            'target_type' => 'Permission',
            'target_id' => $permission->id,
            'target_name' => $permission->name,
            'description' => "Updated permission: {$permission->name}",
            'old_values' => $oldValues,
            'new_values' => $permission->only(['name', 'guard_name']),
        ]);

        return response()->json([
            'success' => true,
            'data' => $permission,
            'message' => 'Permission updated successfully'
        ]);
    }

    /**
     * Remove the specified permission.
     */
    public function destroy(Permission $permission): JsonResponse
    {
        $this->authorize('delete', $permission);

        // Log activity before deletion
        AdminActivityLog::logActivity([
            'admin_id' => Auth::guard('admin')->id(),
            'action' => 'deleted',
            'target_type' => 'Permission',
            'target_id' => $permission->id,
            'target_name' => $permission->name,
            'description' => "Deleted permission: {$permission->name}",
            'old_values' => $permission->only(['name', 'guard_name']),
        ]);

        $permission->delete();

        return response()->json([
            'success' => true,
            'message' => 'Permission deleted successfully'
        ]);
    }

    /**
     * Get permission categories.
     */
    public function categories(): JsonResponse
    {
        $this->authorize('viewAny', Permission::class);

        $categories = [
            'user_management' => [
                'name' => 'User Management',
                'permissions' => Permission::where('name', 'like', '%user%')->pluck('name'),
            ],
            'admin_management' => [
                'name' => 'Admin Management',
                'permissions' => Permission::where('name', 'like', '%admin%')->pluck('name'),
            ],
            'role_management' => [
                'name' => 'Role Management',
                'permissions' => Permission::where('name', 'like', '%role%')->pluck('name'),
            ],
            'permission_management' => [
                'name' => 'Permission Management',
                'permissions' => Permission::where('name', 'like', '%permission%')->pluck('name'),
            ],
            'subscription_management' => [
                'name' => 'Subscription Management',
                'permissions' => Permission::where('name', 'like', '%subscription%')->pluck('name'),
            ],
            'system' => [
                'name' => 'System',
                'permissions' => Permission::whereNotIn('name', function ($query) {
                    $query->select('name')
                          ->from('permissions')
                          ->where('name', 'like', '%user%')
                          ->orWhere('name', 'like', '%admin%')
                          ->orWhere('name', 'like', '%role%')
                          ->orWhere('name', 'like', '%permission%')
                          ->orWhere('name', 'like', '%subscription%');
                })->pluck('name'),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $categories,
            'message' => 'Permission categories retrieved successfully'
        ]);
    }

    /**
     * Get permission statistics.
     */
    public function statistics(): JsonResponse
    {
        // $this->authorize('viewAny', Permission::class);

        $stats = [
            'total_permissions' => Permission::count(),
            'permissions_with_roles' => Permission::has('roles')->count(),
            'permissions_with_users' => Permission::has('users')->count(),
            'unused_permissions' => Permission::doesntHave('roles')->doesntHave('users')->count(),
            'system_permissions' => Permission::whereIn('name', [
                'manage admins',
                'manage roles',
                'manage permissions',
                'super admin access'
            ])->count(),
            'most_used_permissions' => Permission::withCount(['roles', 'users'])
                                                ->orderByRaw('(roles_count + users_count) desc')
                                                ->limit(10)
                                                ->get(['name', 'roles_count', 'users_count']),
            'permissions_by_category' => [
                'user_management' => Permission::where('name', 'like', '%user%')->count(),
                'admin_management' => Permission::where('name', 'like', '%admin%')->count(),
                'role_management' => Permission::where('name', 'like', '%role%')->count(),
                'permission_management' => Permission::where('name', 'like', '%permission%')->count(),
                'subscription_management' => Permission::where('name', 'like', '%subscription%')->count(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Permission statistics retrieved successfully'
        ]);
    }

    /**
     * Bulk create permissions.
     */
    public function bulkCreate(Request $request): JsonResponse
    {
        $this->authorize('create', Permission::class);

        $validated = $request->validate([
            'permissions' => 'required|array|min:1',
            'permissions.*.name' => 'required|string|max:255|unique:permissions,name',
            'permissions.*.guard_name' => 'nullable|string|max:255',
            'permissions.*.description' => 'nullable|string|max:500',
        ]);

        $createdPermissions = [];

        foreach ($validated['permissions'] as $permissionData) {
            $permissionData['guard_name'] = $permissionData['guard_name'] ?? 'web';
            
            $permission = Permission::create($permissionData);
            $createdPermissions[] = $permission;

            // Log activity
            AdminActivityLog::logActivity([
                'admin_id' => Auth::guard('admin')->id(),
                'action' => 'created',
                'target_type' => 'Permission',
                'target_id' => $permission->id,
                'target_name' => $permission->name,
                'description' => "Created permission: {$permission->name} (bulk create)",
                'new_values' => $permission->only(['name', 'guard_name']),
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $createdPermissions,
            'message' => count($createdPermissions) . ' permissions created successfully'
        ], 201);
    }
}
