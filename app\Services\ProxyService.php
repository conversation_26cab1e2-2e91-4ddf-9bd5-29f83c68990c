<?php

namespace App\Services;

use App\Models\ProxySettings;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ProxyService
{
    private $activeProxy = null;
    private $cacheKey = 'active_proxy_settings';

    public function __construct()
    {
        $this->loadActiveProxy();
    }

    /**
     * Load active proxy from cache or database
     */
    private function loadActiveProxy()
    {
        // Try to get from cache first
        $this->activeProxy = Cache::get($this->cacheKey);
        
        if (!$this->activeProxy) {
            // Load from database
            $proxy = ProxySettings::getActiveProxy();
            if ($proxy) {
                $this->activeProxy = $proxy;
                // Cache for 5 minutes
                Cache::put($this->cacheKey, $proxy, now()->addMinutes(5));
            }
        }
    }

    /**
     * Check if proxy is enabled
     */
    public function isProxyEnabled()
    {
        return $this->activeProxy !== null && $this->activeProxy->is_active;
    }

    /**
     * Get active proxy settings
     */
    public function getActiveProxy()
    {
        return $this->activeProxy;
    }

    /**
     * Get HTTP client with proxy configuration
     */
    public function getHttpClient($additionalOptions = [])
    {
        $options = [];

        if ($this->isProxyEnabled()) {
            $proxyUrl = $this->activeProxy->getProxyUrl();
            $options['proxy'] = $proxyUrl;
            
            Log::info("🌐 Using proxy for HTTP request: {$this->activeProxy->name}");
        }

        // Merge additional options
        $options = array_merge($options, $additionalOptions);

        return Http::withOptions($options);
    }

    /**
     * Make HTTP GET request with proxy
     */
    public function get($url, $headers = [], $additionalOptions = [])
    {
        $client = $this->getHttpClient($additionalOptions);
        
        if (!empty($headers)) {
            $client = $client->withHeaders($headers);
        }

        return $client->get($url);
    }

    /**
     * Make HTTP POST request with proxy
     */
    public function post($url, $data = [], $headers = [], $additionalOptions = [])
    {
        $client = $this->getHttpClient($additionalOptions);
        
        if (!empty($headers)) {
            $client = $client->withHeaders($headers);
        }

        return $client->post($url, $data);
    }

    /**
     * Refresh active proxy cache
     */
    public function refreshActiveProxy()
    {
        Cache::forget($this->cacheKey);
        $this->loadActiveProxy();
        
        Log::info("🔄 Proxy cache refreshed");
        
        return $this->activeProxy;
    }

    /**
     * Test current proxy connection
     */
    public function testCurrentProxy()
    {
        if (!$this->isProxyEnabled()) {
            return [
                'success' => false,
                'message' => 'No proxy is currently active'
            ];
        }

        try {
            $startTime = microtime(true);
            
            $response = $this->get('https://shahid.mbc.net', [], [
                'timeout' => 10,
                'connect_timeout' => 5
            ]);

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'Proxy connection successful',
                    'response_time' => $responseTime,
                    'proxy_name' => $this->activeProxy->name
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "HTTP {$response->status()}"
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get proxy status for display
     */
    public function getProxyStatus()
    {
        if (!$this->isProxyEnabled()) {
            return [
                'enabled' => false,
                'status' => 'No proxy active',
                'message' => 'Using direct connection'
            ];
        }

        $proxy = $this->activeProxy;
        $isHealthy = $proxy->isHealthy();

        return [
            'enabled' => true,
            'status' => $isHealthy ? 'Healthy' : 'Unknown',
            'proxy_name' => $proxy->name,
            'proxy_address' => "{$proxy->host}:{$proxy->port}",
            'proxy_type' => strtoupper($proxy->type),
            'last_tested' => $proxy->last_tested_at ? $proxy->last_tested_at->diffForHumans() : 'Never',
            'response_time' => isset($proxy->test_results['response_time']) ? $proxy->test_results['response_time'] . 'ms' : 'Unknown'
        ];
    }

    /**
     * Apply proxy to existing HTTP client instance
     */
    public function applyProxyToClient($httpClient)
    {
        if ($this->isProxyEnabled()) {
            $proxyUrl = $this->activeProxy->getProxyUrl();
            return $httpClient->withOptions(['proxy' => $proxyUrl]);
        }

        return $httpClient;
    }

    /**
     * Get proxy configuration for external use
     */
    public function getProxyConfig()
    {
        if (!$this->isProxyEnabled()) {
            return null;
        }

        return [
            'host' => $this->activeProxy->host,
            'port' => $this->activeProxy->port,
            'username' => $this->activeProxy->username,
            'password' => $this->activeProxy->password,
            'type' => $this->activeProxy->type,
            'url' => $this->activeProxy->getProxyUrl()
        ];
    }

    /**
     * Log proxy usage
     */
    public function logProxyUsage($url, $success = true, $responseTime = null)
    {
        if ($this->isProxyEnabled()) {
            $logData = [
                'proxy_name' => $this->activeProxy->name,
                'url' => parse_url($url, PHP_URL_HOST),
                'success' => $success
            ];

            if ($responseTime) {
                $logData['response_time'] = $responseTime;
            }

            Log::info("🌐 Proxy request", $logData);
        }
    }

    /**
     * Disable proxy temporarily
     */
    public function disableProxy()
    {
        Cache::forget($this->cacheKey);
        $this->activeProxy = null;
        
        Log::info("🔴 Proxy temporarily disabled");
    }

    /**
     * Enable proxy
     */
    public function enableProxy()
    {
        $this->refreshActiveProxy();
        
        if ($this->isProxyEnabled()) {
            Log::info("🟢 Proxy enabled: {$this->activeProxy->name}");
        } else {
            Log::warning("⚠️ No active proxy found to enable");
        }
    }
}
