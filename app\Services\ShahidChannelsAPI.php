<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\ProxyService;

/**
 * Shahid Channels API Service
 * Handles all live channels related operations
 */
class Shahid<PERSON>hannelsAPI extends ShahidBaseAP<PERSON>
{
    protected $proxyService;

    // Blocked channels configuration
    private $blockedChannelPatterns = [
        'saudi sports',  // Saudi Sports Company
        'ssc extra',     // SSC Extra channels
        'ssc1',          // SSC1 HD
        'ssc2',          // SSC2 HD
        'ssc3',          // SSC3 HD
        'ssc4',          // SSC4 HD
        'ssc5',          // SSC5 HD
        // يمكن إضافة المزيد من القنوات المحظورة هنا
    ];

    public function __construct($token = null, ProxyService $proxyService = null)
    {
        parent::__construct($token);
        $this->proxyService = $proxyService ?: app(ProxyService::class);
    }

    /**
     * Override makeRequest to use ProxyService
     */
    protected function makeRequest($method, $url, $options = [])
    {
        try {
            $headers = $this->getHeaders($options['auth'] ?? false);

            // Use ProxyService for HTTP client
            $httpClient = $this->proxyService->getHttpClient([
                'timeout' => 30
            ])->withHeaders($headers);

            switch (strtoupper($method)) {
                case 'GET':
                    $response = $httpClient->get($url, $options['data'] ?? []);
                    break;
                case 'POST':
                    $response = $httpClient->post($url, $options['data'] ?? []);
                    break;
                case 'PUT':
                    $response = $httpClient->put($url, $options['data'] ?? []);
                    break;
                case 'DELETE':
                    $response = $httpClient->delete($url);
                    break;
                default:
                    throw new \Exception('Unsupported HTTP method: ' . $method);
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                $responseBody = $response->body();
                $responseData = $response->json();

                // Handle specific Shahid API errors
                $errorMessage = 'HTTP ' . $response->status();

                if (isset($responseData['faults']) && is_array($responseData['faults'])) {
                    $fault = $responseData['faults'][0];
                    if (isset($fault['code']) && $fault['code'] == 5004) {
                        $errorMessage = 'This content requires Shahid VIP subscription';
                    } else {
                        $errorMessage = $fault['userMessage'] ?? $fault['internalMessage'] ?? $errorMessage;
                    }
                } elseif (isset($responseData['message'])) {
                    $errorMessage = $responseData['message'];
                }

                Log::warning('API request failed', [
                    'url' => $url,
                    'status' => $response->status(),
                    'error' => $errorMessage,
                    'response_body' => $responseBody
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'status_code' => $response->status(),
                    'response_data' => $responseData
                ];
            }

        } catch (\Exception $e) {
            Log::error('HTTP request exception', [
                'url' => $url,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get live channels from Shahid (requires token)
     */
    public function getLiveChannels()
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found for live channels'];
        }

        try {
            Log::info("Fetching Shahid live channels");

            $allChannels = [];
            $countries = ['EG', 'SA']; // Egypt and Saudi Arabia

            foreach ($countries as $country) {
                Log::info("Fetching channels for country: {$country}");
                $page = 0;

                while (true) {
                    try {
                        $params = [
                            'filter' => json_encode([
                                'pageNumber' => $page,
                                'pageSize' => 24,
                                'productType' => 'LIVESTREAM',
                                'productSubType' => 'LIVE_CHANNEL'
                            ]),
                            'country' => $country
                        ];

                        $headers = [
                            'authority' => 'api2.shahid.net',
                            'accept' => 'application/json',
                            'accept-language' => 'en',
                            'content-type' => 'application/json',
                            'language' => 'en',
                            'origin' => 'https://shahid.mbc.net',
                            'referer' => 'https://shahid.mbc.net/',
                            'token' => $this->token,
                            'user-agent' => $this->userAgent,
                            'browser_name' => 'CHROME',
                            'browser_version' => '122.0.0.0',
                            'shahid_os' => 'WINDOWS'
                        ];

                        $response = Http::withHeaders($headers)
                            ->timeout(15)
                            ->get('https://api2.shahid.net/proxy/v2.1/product/filter', $params);

                        if ($response->successful()) {
                            $data = $response->json();

                            if (!isset($data['productList']) || empty($data['productList']['products'])) {
                                break;
                            }

                            $pageChannels = $data['productList']['products'];

                            foreach ($pageChannels as $channel) {
                                // Skip blocked channels
                                if ($this->isChannelBlocked($channel)) {
                                    continue;
                                }

                                $channel['country'] = $country;
                                $channel['country_name'] = $this->getCountryName($country);

                                // Process channel logo/image URLs using the original method
                                $this->processChannelImagesOriginal($channel);

                                $allChannels[] = $channel;
                            }

                            $page++;

                            // Limit to prevent infinite loops
                            if ($page > 10) {
                                break;
                            }
                        } else {
                            Log::warning("Failed to fetch channels page {$page} for {$country}: " . $response->status());
                            break;
                        }

                    } catch (\Exception $e) {
                        Log::error("Error fetching channels page {$page} for {$country}: " . $e->getMessage());
                        break;
                    }
                }

                Log::info("$country: Total channels found: " . count($allChannels));

                // Add small delay to avoid rate limiting
                usleep(100000); // 0.1 seconds
            }

            // Remove duplicates based on channel ID
            $uniqueChannels = [];
            $seenIds = [];

            foreach ($allChannels as $channel) {
                $channelId = $channel['id'] ?? null;
                if ($channelId && !in_array($channelId, $seenIds)) {
                    $uniqueChannels[] = $channel;
                    $seenIds[] = $channelId;
                }
            }

            // Sort channels by source (country), category, and then by title
            usort($uniqueChannels, function($a, $b) {
                // First sort by country
                $countryA = $a['country'] ?? '';
                $countryB = $b['country'] ?? '';

                if ($countryA !== $countryB) {
                    return strcmp($countryA, $countryB);
                }

                // Second, sort by category/genre if available
                $categoryA = $a['genre'] ?? $a['category'] ?? $a['productSubType'] ?? '';
                $categoryB = $b['genre'] ?? $b['category'] ?? $b['productSubType'] ?? '';

                if ($categoryA !== $categoryB) {
                    return strcmp($categoryA, $categoryB);
                }

                // Third, prioritize certain channel types (news, sports, etc.)
                $priorityA = $this->getChannelPriority($a);
                $priorityB = $this->getChannelPriority($b);

                if ($priorityA !== $priorityB) {
                    return $priorityA - $priorityB; // Lower number = higher priority
                }

                // Finally, sort by title alphabetically
                $titleA = $a['title'] ?? '';
                $titleB = $b['title'] ?? '';

                return strcmp($titleA, $titleB);
            });

            Log::info("Total channels collected: " . count($allChannels));
            Log::info("Unique channels after deduplication: " . count($uniqueChannels));
            Log::info("Channels sorted by source (country) and title");

            return [
                'channels' => $uniqueChannels,
                'total' => count($uniqueChannels)
            ];

        } catch (\Exception $e) {
            Log::error("Error fetching live channels: " . $e->getMessage());
            return ['error' => 'Failed to fetch live channels: ' . $e->getMessage()];
        }
    }

    /**
     * Get cached live channels
     */
    public function getCachedLiveChannels($forceRefresh = false)
    {
        $cacheKey = 'shahid_live_channels';
        $cacheDuration = 3600; // 1 hour

        if ($forceRefresh) {
            Cache::forget($cacheKey);
        }

        return Cache::remember($cacheKey, $cacheDuration, function () {
            return $this->getLiveChannels();
        });
    }

    /**
     * Search channels by name
     */
    public function searchChannels($query, $channels = null)
    {
        if ($channels === null) {
            $result = $this->getCachedLiveChannels();
            if (isset($result['error'])) {
                return $result;
            }
            $channels = $result['channels'] ?? [];
        }

        $filteredChannels = array_filter($channels, function($channel) use ($query) {
            $name = strtolower($channel['name'] ?? '');
            $title = strtolower($channel['title'] ?? '');
            $searchQuery = strtolower($query);
            
            return strpos($name, $searchQuery) !== false || 
                   strpos($title, $searchQuery) !== false;
        });

        return [
            'channels' => array_values($filteredChannels),
            'total' => count($filteredChannels),
            'query' => $query
        ];
    }

    /**
     * Get channel by ID
     */
    public function getChannelById($channelId)
    {
        $result = $this->getCachedLiveChannels();
        
        if (isset($result['error'])) {
            return $result;
        }

        $channels = $result['channels'] ?? [];
        
        foreach ($channels as $channel) {
            if (($channel['id'] ?? null) == $channelId) {
                return ['channel' => $channel];
            }
        }

        return ['error' => 'Channel not found'];
    }

    /**
     * Get channels by country
     */
    public function getChannelsByCountry($countryCode)
    {
        $result = $this->getCachedLiveChannels();
        
        if (isset($result['error'])) {
            return $result;
        }

        $channels = $result['channels'] ?? [];
        
        $filteredChannels = array_filter($channels, function($channel) use ($countryCode) {
            return ($channel['country'] ?? '') === strtoupper($countryCode);
        });

        return [
            'channels' => array_values($filteredChannels),
            'total' => count($filteredChannels),
            'country' => $countryCode
        ];
    }

    /**
     * Process channel images and logos using original Flask method
     */
    private function processChannelImagesOriginal(&$channel)
    {
        try {
            // Initialize image URLs
            $channel['landscape_url'] = null;
            $channel['hero_url'] = null;
            $channel['poster_url'] = null;
            $channel['thumbnail_url'] = null;
            $channel['logo_url'] = null;

            // First, check for logoTitleImage directly in channel (this is the main logo)
            if (!empty($channel['logoTitleImage'])) {
                $baseUrl = $channel['logoTitleImage'];
                Log::info("Original URL: " . $baseUrl);

                // Create clean URL with proper parameters
                if (strpos($baseUrl, 'mediaObject/') !== false) {
                    // Extract mediaObject ID
                    preg_match('/mediaObject\/([^?]+)/', $baseUrl, $matches);
                    if (!empty($matches[1])) {
                        $mediaId = $matches[1];
                        $optimizedUrl = "https://shahid.mbc.net/mediaObject/{$mediaId}?width=300&version=1&type=avif&q=80";
                    } else {
                        $optimizedUrl = explode('?', $baseUrl)[0] . '?width=300&version=1&type=avif&q=80';
                    }
                } else {
                    $optimizedUrl = explode('?', $baseUrl)[0] . '?width=300&version=1&type=avif&q=80';
                }

                // Clean the URL
                $optimizedUrl = $this->cleanPosterUrl($optimizedUrl);
                $channel['logo_url'] = $optimizedUrl;
                Log::info("Fixed logoTitleImage for {$channel['title']}: " . $optimizedUrl);
            }

            // If no logoTitleImage, try images from the image object
            if (empty($channel['logo_url']) && !empty($channel['image'])) {
                $imageData = $channel['image'];

                // Try different image types in order of preference
                $imageTypes = [
                    ['logoImage', 'logo_url'],
                    ['thumbnailImage', 'thumbnail_url'],
                    ['posterClean', 'poster_url'],
                    ['posterImage', 'poster_url'],
                    ['landscape', 'landscape_url'],
                    ['hero', 'hero_url']
                ];

                foreach ($imageTypes as [$imgType, $urlKey]) {
                    if (!empty($imageData[$imgType])) {
                        $baseUrl = $imageData[$imgType];

                        if (strpos($baseUrl, 'mediaObject/') !== false) {
                            preg_match('/mediaObject\/([^?]+)/', $baseUrl, $matches);
                            if (!empty($matches[1])) {
                                $mediaId = $matches[1];
                                $optimizedUrl = "https://shahid.mbc.net/mediaObject/{$mediaId}?width=300&version=1&type=avif&q=80";
                            } else {
                                $optimizedUrl = explode('?', $baseUrl)[0] . '?width=300&version=1&type=avif&q=80';
                            }
                        } else {
                            $optimizedUrl = explode('?', $baseUrl)[0] . '?width=300&version=1&type=avif&q=80';
                        }

                        $optimizedUrl = $this->cleanPosterUrl($optimizedUrl);
                        $channel[$urlKey] = $optimizedUrl;

                        // Set logo_url to first available image if not already set
                        if (empty($channel['logo_url'])) {
                            $channel['logo_url'] = $optimizedUrl;
                        }
                    }
                }
            }

            // Final check
            if (empty($channel['logo_url'])) {
                Log::warning("No logo found for {$channel['title']}");
            } else {
                Log::info("Final logo for {$channel['title']}: " . substr($channel['logo_url'], 0, 100) . "...");
            }

        } catch (\Exception $e) {
            Log::error("Error processing images for channel {$channel['title']}: " . $e->getMessage());
        }
    }

    /**
     * Clean poster URL (from original Flask code)
     */
    private function cleanPosterUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // Remove unwanted parameters but keep essential ones
        $parsedUrl = parse_url($url);
        if (!$parsedUrl) {
            return $url;
        }

        $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path'];

        // Keep only essential parameters
        $allowedParams = ['width', 'height', 'version', 'type', 'q'];
        $queryParams = [];

        if (!empty($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $params);
            foreach ($params as $key => $value) {
                if (in_array($key, $allowedParams)) {
                    $queryParams[$key] = $value;
                }
            }
        }

        if (!empty($queryParams)) {
            $baseUrl .= '?' . http_build_query($queryParams);
        }

        return $baseUrl;
    }

    /**
     * Process channel images and logos (legacy method)
     */
    private function processChannelImages($channel)
    {
        // Initialize logo_url
        $channel['logo_url'] = null;

        // Check for logoTitleImage (main logo)
        if (!empty($channel['logoTitleImage'])) {
            $channel['logo_url'] = $this->processImageUrl($channel['logoTitleImage']);
        }

        // Check for image object
        if (!empty($channel['image'])) {
            $image = $channel['image'];

            // Try different image types
            $imageTypes = ['posterClean', 'posterImage', 'posterHero', 'landscape'];

            foreach ($imageTypes as $type) {
                if (!empty($image[$type])) {
                    if (!$channel['logo_url']) {
                        $channel['logo_url'] = $this->processImageUrl($image[$type]);
                    }
                    $channel['image'][$type] = $this->processImageUrl($image[$type]);
                }
            }
        }

        // Check for thumbnailImage
        if (!empty($channel['thumbnailImage'])) {
            if (!$channel['logo_url']) {
                $channel['logo_url'] = $this->processImageUrl($channel['thumbnailImage']);
            }
            $channel['thumbnailImage'] = $this->processImageUrl($channel['thumbnailImage']);
        }

        return $channel;
    }

    /**
     * Check if channel should be blocked/filtered out
     */
    private function isChannelBlocked($channel)
    {
        $title = strtolower($channel['title'] ?? '');
        $description = strtolower($channel['description'] ?? '');

        // Check if channel title or description contains blocked patterns
        foreach ($this->blockedChannelPatterns as $pattern) {
            if (str_contains($title, $pattern) || str_contains($description, $pattern)) {
                Log::info("🚫 Blocking channel: {$channel['title']} (matched pattern: {$pattern})");
                return true;
            }
        }

        return false;
    }

    /**
     * Add a channel pattern to the blocked list
     */
    public function addBlockedPattern($pattern)
    {
        $pattern = strtolower(trim($pattern));
        if (!in_array($pattern, $this->blockedChannelPatterns)) {
            $this->blockedChannelPatterns[] = $pattern;
            Log::info("🚫 Added blocked pattern: {$pattern}");
        }
    }

    /**
     * Remove a channel pattern from the blocked list
     */
    public function removeBlockedPattern($pattern)
    {
        $pattern = strtolower(trim($pattern));
        $key = array_search($pattern, $this->blockedChannelPatterns);
        if ($key !== false) {
            unset($this->blockedChannelPatterns[$key]);
            $this->blockedChannelPatterns = array_values($this->blockedChannelPatterns); // Re-index
            Log::info("✅ Removed blocked pattern: {$pattern}");
        }
    }

    /**
     * Get current blocked patterns
     */
    public function getBlockedPatterns()
    {
        return $this->blockedChannelPatterns;
    }

    /**
     * Get channel priority for sorting (lower number = higher priority)
     */
    private function getChannelPriority($channel)
    {
        $title = strtolower($channel['title'] ?? '');
        $category = strtolower($channel['genre'] ?? $channel['category'] ?? $channel['productSubType'] ?? '');

        // News channels get highest priority
        if (str_contains($title, 'news') || str_contains($title, 'أخبار') ||
            str_contains($category, 'news') || str_contains($title, 'إخبار')) {
            return 1;
        }

        // Sports channels
        if (str_contains($title, 'sport') || str_contains($title, 'رياض') ||
            str_contains($category, 'sport') || str_contains($title, 'كرة')) {
            return 2;
        }

        // Entertainment/Drama channels
        if (str_contains($title, 'drama') || str_contains($title, 'مسلسل') ||
            str_contains($category, 'drama') || str_contains($title, 'ترفيه')) {
            return 3;
        }

        // Movies channels
        if (str_contains($title, 'movie') || str_contains($title, 'cinema') ||
            str_contains($title, 'أفلام') || str_contains($category, 'movie')) {
            return 4;
        }

        // Kids channels
        if (str_contains($title, 'kids') || str_contains($title, 'children') ||
            str_contains($title, 'أطفال') || str_contains($category, 'kids')) {
            return 5;
        }

        // Music channels
        if (str_contains($title, 'music') || str_contains($title, 'موسيق') ||
            str_contains($category, 'music')) {
            return 6;
        }

        // Documentary channels
        if (str_contains($title, 'documentary') || str_contains($title, 'وثائق') ||
            str_contains($category, 'documentary')) {
            return 7;
        }

        // Religious channels
        if (str_contains($title, 'quran') || str_contains($title, 'قرآن') ||
            str_contains($title, 'islamic') || str_contains($title, 'دين')) {
            return 8;
        }

        // General/Other channels
        return 9;
    }

    /**
     * Get country name from code
     */
    private function getCountryName($countryCode)
    {
        $countries = [
            'EG' => 'Egypt',
            'SA' => 'Saudi Arabia',
            'AE' => 'UAE',
            'KW' => 'Kuwait',
            'QA' => 'Qatar',
            'BH' => 'Bahrain',
            'OM' => 'Oman',
            'JO' => 'Jordan',
            'LB' => 'Lebanon',
            'IQ' => 'Iraq'
        ];

        return $countries[$countryCode] ?? $countryCode;
    }

    /**
     * Clear channels cache
     */
    public function clearCache()
    {
        Cache::forget('shahid_live_channels');
        Log::info("Shahid channels cache cleared");
    }




    /**
     * Get channel details by ID
     */
    public function getChannelDetails($channelId)
    {
        try {
            Log::info("Getting channel details for ID: {$channelId}");

            $response = Http::withHeaders($this->getHeaders())
                ->get("{$this->baseUrl}/v1_1/channels/{$channelId}");

            if ($response->successful()) {
                return $response->json();
            }

            Log::error("Failed to get channel details: " . $response->body());
            return ['error' => 'Failed to get channel details'];

        } catch (\Exception $e) {
            Log::error("Channel details error: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get channel stream URL
     */
    public function getChannelStreamUrl($channelId)
    {
        try {
            Log::info("Getting stream URL for channel ID: {$channelId}");

            // Try different endpoints for stream data
            $endpoints = [
                "/v1_1/channels/{$channelId}/stream",
                "/v1_1/channels/{$channelId}/player",
                "/v1_1/live/{$channelId}/stream",
                "/v1_1/channels/{$channelId}/playout"
            ];

            foreach ($endpoints as $endpoint) {
                $response = Http::withHeaders($this->getHeaders())
                    ->get($this->baseUrl . $endpoint);

                if ($response->successful()) {
                    $data = $response->json();

                    // Check if we got stream data
                    if (isset($data['stream']) || isset($data['url']) ||
                        isset($data['manifest']) || isset($data['playout'])) {
                        Log::info("Stream data found using endpoint: {$endpoint}");
                        return $data;
                    }
                }
            }

            Log::error("No stream data found for channel {$channelId}");
            return ['error' => 'No stream data found'];

        } catch (\Exception $e) {
            Log::error("Stream URL error: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get the current token
     */
    public function getToken()
    {
        return $this->token;
    }
}
