@extends('layouts.app')

@section('title', 'Cache Management - Video Proxy')

@section('styles')
<style>
.cache-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.cache-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.btn-clear-cache {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-clear-cache:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-inactive {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-1">🗄️ Video Cache Management</h2>
            <p class="text-muted mb-0">Manage video proxy cache for optimal streaming performance</p>
        </div>
    </div>

    <!-- Cache Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="cache-card">
                <div class="d-flex align-items-center">
                    <span class="status-indicator status-active"></span>
                    <div>
                        <h6 class="mb-0">Cache Status</h6>
                        <small id="cacheStatus">Active</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="cache-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <h6 class="mb-0">Cache Driver</h6>
                <small id="cacheDriver">Loading...</small>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="cache-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <h6 class="mb-0">Segments Cached</h6>
                <small id="segmentsCached">Loading...</small>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="cache-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <h6 class="mb-0">Cache Size</h6>
                <small id="cacheSize">Loading...</small>
            </div>
        </div>
    </div>

    <!-- Cache Settings -->
    <div class="cache-stats">
        <h5 class="mb-3">📊 Cache Settings</h5>
        <div class="row">
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Manifest Cache:</strong></td>
                        <td><span id="manifestCache">30 seconds</span></td>
                    </tr>
                    <tr>
                        <td><strong>Init Segments:</strong></td>
                        <td><span id="initCache">1 hour</span></td>
                    </tr>
                    <tr>
                        <td><strong>Video Segments:</strong></td>
                        <td><span id="segmentCache">5 minutes</span></td>
                    </tr>
                    <tr>
                        <td><strong>Subtitles:</strong></td>
                        <td><span id="subtitleCache">30 minutes</span></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Cache Benefits:</h6>
                <ul class="list-unstyled">
                    <li>✅ Faster video loading</li>
                    <li>✅ Reduced bandwidth usage</li>
                    <li>✅ Better streaming quality</li>
                    <li>✅ Less server load</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Cache Actions -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🧹 Cache Actions</h5>
                </div>
                <div class="card-body">
                    <p>Clear cache to free up space or force refresh of cached content.</p>
                    
                    <button class="btn btn-clear-cache me-2" onclick="clearCache(false)">
                        <i class="fas fa-broom me-2"></i>Clear Video Cache
                    </button>
                    
                    <button class="btn btn-clear-cache" onclick="clearCache(true)">
                        <i class="fas fa-trash me-2"></i>Clear All Cache
                    </button>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Note:</strong> Clearing cache will temporarily slow down video loading until cache rebuilds.
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📈 Performance Tips</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Clear cache weekly for optimal performance
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-info me-2"></i>
                            Cache automatically expires based on content type
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-server text-success me-2"></i>
                            Monitor cache size to prevent disk space issues
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            Higher cache hit rate = better performance
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Log -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📋 Recent Cache Activity</h5>
                </div>
                <div class="card-body">
                    <div id="cacheLog">
                        <p class="text-muted">Loading cache activity...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    loadCacheStats();
    
    // Refresh stats every 30 seconds
    setInterval(loadCacheStats, 30000);
});

function loadCacheStats() {
    $.ajax({
        url: '/video-proxy/cache/stats',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                $('#cacheDriver').text(response.cache_driver || 'Unknown');
                
                // Update cache settings display
                if (response.settings) {
                    $('#manifestCache').text(response.settings.manifest + ' seconds');
                    $('#initCache').text(Math.floor(response.settings.init / 60) + ' minutes');
                    $('#segmentCache').text(Math.floor(response.settings.segment / 60) + ' minutes');
                    $('#subtitleCache').text(Math.floor(response.settings.subtitle / 60) + ' minutes');
                }
            }
        },
        error: function() {
            $('#cacheDriver').text('Error loading stats');
        }
    });
}

function clearCache(force = false) {
    const action = force ? 'all cache' : 'video cache';
    
    if (!confirm(`Are you sure you want to clear ${action}? This may temporarily slow down video loading.`)) {
        return;
    }
    
    const btn = event.target.closest('button');
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Clearing...';
    
    $.ajax({
        url: '/video-proxy/cache/clear',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            force: force
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                loadCacheStats(); // Refresh stats
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Failed to clear cache');
        },
        complete: function() {
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
