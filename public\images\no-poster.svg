<svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34495e;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="600" fill="url(#grad1)"/>
  <circle cx="200" cy="250" r="60" fill="#7f8c8d" opacity="0.3"/>
  <rect x="150" y="320" width="100" height="8" fill="#7f8c8d" opacity="0.3" rx="4"/>
  <rect x="130" y="340" width="140" height="6" fill="#7f8c8d" opacity="0.2" rx="3"/>
  <rect x="160" y="360" width="80" height="6" fill="#7f8c8d" opacity="0.2" rx="3"/>
  <text x="200" y="450" font-family="Arial, sans-serif" font-size="16" fill="#95a5a6" text-anchor="middle">لا توجد صورة</text>
  <text x="200" y="480" font-family="Arial, sans-serif" font-size="14" fill="#7f8c8d" text-anchor="middle">No Image Available</text>
</svg>
