<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'admin_id',
        'action',
        'target_type',
        'target_id',
        'target_name',
        'old_values',
        'new_values',
        'description',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Admin who performed the action.
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get the target model.
     */
    public function target()
    {
        if ($this->target_type && $this->target_id) {
            $modelClass = "App\\Models\\{$this->target_type}";
            if (class_exists($modelClass)) {
                return $modelClass::find($this->target_id);
            }
        }
        return null;
    }

    /**
     * Get formatted action.
     */
    public function getFormattedActionAttribute(): string
    {
        $actions = [
            'created' => 'Created',
            'updated' => 'Updated',
            'deleted' => 'Deleted',
            'suspended' => 'Suspended',
            'unsuspended' => 'Unsuspended',
            'activated' => 'Activated',
            'deactivated' => 'Deactivated',
            'role_assigned' => 'Assigned Role',
            'role_removed' => 'Removed Role',
            'permission_granted' => 'Granted Permission',
            'permission_revoked' => 'Revoked Permission',
            'password_reset' => 'Reset Password',
            'login' => 'Logged In',
            'logout' => 'Logged Out',
        ];

        return $actions[$this->action] ?? ucfirst($this->action);
    }

    /**
     * Get action color for UI.
     */
    public function getActionColorAttribute(): string
    {
        $colors = [
            'created' => 'success',
            'updated' => 'info',
            'deleted' => 'danger',
            'suspended' => 'warning',
            'unsuspended' => 'success',
            'activated' => 'success',
            'deactivated' => 'warning',
            'role_assigned' => 'primary',
            'role_removed' => 'secondary',
            'permission_granted' => 'primary',
            'permission_revoked' => 'secondary',
            'password_reset' => 'warning',
            'login' => 'info',
            'logout' => 'secondary',
        ];

        return $colors[$this->action] ?? 'secondary';
    }

    /**
     * Get action icon for UI.
     */
    public function getActionIconAttribute(): string
    {
        $icons = [
            'created' => 'fas fa-plus-circle',
            'updated' => 'fas fa-edit',
            'deleted' => 'fas fa-trash',
            'suspended' => 'fas fa-ban',
            'unsuspended' => 'fas fa-check-circle',
            'activated' => 'fas fa-toggle-on',
            'deactivated' => 'fas fa-toggle-off',
            'role_assigned' => 'fas fa-user-tag',
            'role_removed' => 'fas fa-user-minus',
            'permission_granted' => 'fas fa-key',
            'permission_revoked' => 'fas fa-lock',
            'password_reset' => 'fas fa-unlock-alt',
            'login' => 'fas fa-sign-in-alt',
            'logout' => 'fas fa-sign-out-alt',
        ];

        return $icons[$this->action] ?? 'fas fa-info-circle';
    }

    /**
     * Scope for recent activities.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for specific admin.
     */
    public function scopeByAdmin($query, $adminId)
    {
        return $query->where('admin_id', $adminId);
    }

    /**
     * Scope for specific action.
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for specific target type.
     */
    public function scopeByTargetType($query, $targetType)
    {
        return $query->where('target_type', $targetType);
    }

    /**
     * Log admin activity.
     */
    public static function logActivity(array $data): self
    {
        return self::create(array_merge($data, [
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]));
    }

    /**
     * Log user creation.
     */
    public static function logUserCreated(User $admin, User $user): self
    {
        return self::logActivity([
            'admin_id' => $admin->id,
            'action' => 'created',
            'target_type' => 'User',
            'target_id' => $user->id,
            'target_name' => $user->name,
            'description' => "Created user: {$user->name} ({$user->email})",
            'new_values' => $user->only(['name', 'email', 'user_type', 'is_admin']),
        ]);
    }

    /**
     * Log user update.
     */
    public static function logUserUpdated(User $admin, User $user, array $oldValues): self
    {
        return self::logActivity([
            'admin_id' => $admin->id,
            'action' => 'updated',
            'target_type' => 'User',
            'target_id' => $user->id,
            'target_name' => $user->name,
            'description' => "Updated user: {$user->name}",
            'old_values' => $oldValues,
            'new_values' => $user->only(['name', 'email', 'user_type', 'is_admin', 'is_active']),
        ]);
    }

    /**
     * Log role assignment.
     */
    public static function logRoleAssigned(User $admin, User $user, string $roleName): self
    {
        return self::logActivity([
            'admin_id' => $admin->id,
            'action' => 'role_assigned',
            'target_type' => 'User',
            'target_id' => $user->id,
            'target_name' => $user->name,
            'description' => "Assigned role '{$roleName}' to user: {$user->name}",
            'new_values' => ['role' => $roleName],
        ]);
    }

    /**
     * Log permission granted.
     */
    public static function logPermissionGranted(User $admin, User $user, string $permissionName): self
    {
        return self::logActivity([
            'admin_id' => $admin->id,
            'action' => 'permission_granted',
            'target_type' => 'User',
            'target_id' => $user->id,
            'target_name' => $user->name,
            'description' => "Granted permission '{$permissionName}' to user: {$user->name}",
            'new_values' => ['permission' => $permissionName],
        ]);
    }
}
