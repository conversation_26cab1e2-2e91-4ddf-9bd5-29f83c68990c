# 🚀 Quick Deployment Guide - Shahid Admin Panel

## ⚡ 5-Minute Setup

### 1. Upload Files
```bash
# Upload all files to your server (except .git, node_modules, .env)
# Make sure you're in the Laravel root directory
```

### 2. Run Deployment Script
```bash
# Make script executable and run
chmod +x deploy.sh
./deploy.sh
```

### 3. Configure Environment
```bash
# Edit .env file
nano .env

# Update these essential settings:
APP_URL=https://your-domain.com
DB_HOST=localhost
DB_DATABASE=your_database_name
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
ADMIN_EMAIL=<EMAIL>
```

### 4. Point Domain to Public Directory
Your web server should point to the `public` directory, not the root.

**Example for cPanel:**
- Document Root: `/public_html/shahid-laravel/public`

**Example for VPS:**
- Nginx/Apache virtual host should point to `/path/to/shahid-laravel/public`

### 5. Test Installation
Visit: `https://your-domain.com/admin`

Default login:
- Email: `<EMAIL>`
- Password: `password`

**⚠️ Change default password immediately!**

---

## 🔧 Common Hosting Providers

### cPanel/Shared Hosting
1. Upload files to `public_html/shahid-laravel/`
2. Move contents of `public` folder to `public_html/`
3. Edit `public_html/index.php`:
   ```php
   require __DIR__.'/shahid-laravel/vendor/autoload.php';
   $app = require_once __DIR__.'/shahid-laravel/bootstrap/app.php';
   ```

### VPS/Dedicated Server
1. Upload to `/var/www/shahid-laravel/`
2. Configure Nginx/Apache to point to `/var/www/shahid-laravel/public`
3. Run deployment script

### Cloud Hosting (AWS, DigitalOcean, etc.)
1. Use git clone or file upload
2. Run deployment script
3. Configure load balancer/reverse proxy

---

## 🛠️ Essential Commands

```bash
# Generate app key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Create admin user
php artisan make:admin

# Clear and cache config
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
chmod -R 775 storage bootstrap/cache
```

---

## 🔐 Security Checklist

- [ ] Change default admin password
- [ ] Update APP_KEY in .env
- [ ] Set APP_DEBUG=false
- [ ] Configure proper file permissions
- [ ] Enable SSL certificate
- [ ] Review firewall settings

---

## 📞 Need Help?

1. **Check logs:** `storage/logs/laravel.log`
2. **Test health:** `https://your-domain.com/health.php`
3. **Review full guide:** `DEPLOYMENT_GUIDE.md`

---

**🎉 Your Shahid Admin Panel is ready!**
