/**
 * Template URL Handler for DASH Streaming
 * Handles $Number$ and other template variables in DASH manifests
 */

console.log('🔧 Template Handler loaded');

// Override Shaka Player's network engine to handle template URLs
if (typeof shaka !== 'undefined') {
    // Store original request function
    const originalRequest = shaka.net.NetworkingEngine.prototype.request;
    
    shaka.net.NetworkingEngine.prototype.request = function(type, request, opt_context) {
        // Check if this is a template URL request
        if (request.uris && request.uris.length > 0) {
            const uri = request.uris[0];
            
            // Handle template URLs that contain $Number$
            if (uri.includes('/video-proxy/template/') && uri.includes('$Number$')) {
                console.log('🔄 Processing template URL request:', uri);
                
                // Extract segment number from context or generate it
                let segmentNumber = 0;
                
                // Try to extract number from context
                if (opt_context && opt_context.segment) {
                    segmentNumber = opt_context.segment.startTime || 0;
                } else if (opt_context && opt_context.time !== undefined) {
                    segmentNumber = Math.floor(opt_context.time);
                } else {
                    // Generate based on current time or sequence
                    segmentNumber = Date.now() % 10000;
                }
                
                // Replace $Number$ with actual number
                const actualUri = uri.replace('$Number$', segmentNumber);
                console.log('📝 Template URL resolved:', actualUri);
                
                // Update the request URI
                request.uris = [actualUri];
            }
        }
        
        // Call original request function
        return originalRequest.call(this, type, request, opt_context);
    };
    
    console.log('✅ Shaka Player template handling configured');
}

// Alternative approach: Intercept fetch requests
const originalFetch = window.fetch;
window.fetch = function(input, init) {
    let url = input;
    
    if (typeof input === 'object' && input.url) {
        url = input.url;
    }
    
    // Handle template URLs in fetch requests
    if (typeof url === 'string' && url.includes('/video-proxy/template/') && url.includes('$Number$')) {
        console.log('🔄 Intercepting template fetch:', url);
        
        // Generate a segment number (this is a simplified approach)
        const segmentNumber = Math.floor(Date.now() / 1000) % 10000;
        const actualUrl = url.replace('$Number$', segmentNumber);
        
        console.log('📝 Template fetch resolved:', actualUrl);
        
        if (typeof input === 'object') {
            input.url = actualUrl;
        } else {
            input = actualUrl;
        }
    }
    
    return originalFetch.call(this, input, init);
};

// Helper function to resolve template URLs manually
window.TemplateHandler = {
    resolveTemplateUrl: function(templateUrl, segmentNumber) {
        if (!templateUrl || typeof templateUrl !== 'string') {
            return templateUrl;
        }
        
        // Replace common template variables
        let resolvedUrl = templateUrl
            .replace(/\$Number\$/g, segmentNumber)
            .replace(/\$Time\$/g, Date.now())
            .replace(/\$Bandwidth\$/g, '1000000'); // Default bandwidth
        
        console.log('🔧 Template resolved:', {
            template: templateUrl,
            resolved: resolvedUrl,
            segmentNumber: segmentNumber
        });
        
        return resolvedUrl;
    },
    
    // Extract segment number from various sources
    extractSegmentNumber: function(context) {
        if (!context) return 0;
        
        if (context.segment && context.segment.startTime !== undefined) {
            return Math.floor(context.segment.startTime);
        }
        
        if (context.time !== undefined) {
            return Math.floor(context.time);
        }
        
        if (context.sequenceNumber !== undefined) {
            return context.sequenceNumber;
        }
        
        // Fallback to timestamp-based number
        return Math.floor(Date.now() / 1000) % 10000;
    }
};

console.log('✅ Template Handler ready');
