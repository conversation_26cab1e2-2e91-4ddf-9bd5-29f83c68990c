<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\IOSPlayerController;

class UniversalPlayerController extends Controller
{
    /**
     * Universal player endpoint that detects device and chooses appropriate DRM
     */
    public function playContent(Request $request, $contentId)
    {
        try {
            $userAgent = $request->header('User-Agent', '');
            $isIOS = $this->isIOSDevice($userAgent);
            $isAndroid = $this->isAndroidDevice($userAgent);
            $isWindows = $this->isWindowsDevice($userAgent);
            
            Log::info('🎬 Universal Player Request', [
                'content_id' => $contentId,
                'user_agent' => $userAgent,
                'is_ios' => $isIOS,
                'is_android' => $isAndroid,
                'is_windows' => $isWindows,
                'ip' => $request->ip()
            ]);

            if ($isIOS) {
                $result = $this->handleIOSPlayback($request, $contentId);
                return response()->json($result);
            } else {
                return $this->handleAndroidWindowsPlayback($request, $contentId);
            }

        } catch (\Exception $e) {
            Log::error('Universal Player Error', [
                'content_id' => $contentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to process playback request',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle iOS playback (HLS + FairPlay)
     */
    private function handleIOSPlayback(Request $request, $contentId)
    {
        Log::info('🍎 iOS Device Detected - Using FairPlay', ['content_id' => $contentId]);

        try {
            // Call iOS controller directly instead of HTTP request
            $iosController = new \App\Http\Controllers\IOSPlayerController();
            $response = $iosController->extractFairPlayData($request, $contentId);

            // If it's a JSON response, get the data
            if ($response instanceof \Illuminate\Http\JsonResponse) {
                return $response->getData(true);
            }

            return $response;

        } catch (\Exception $e) {
            Log::error('iOS FairPlay Extraction Failed', [
                'content_id' => $contentId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'platform' => 'ios',
                'error' => 'Failed to extract FairPlay data: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle Android/Windows playback (MPD + Widevine) - using existing system
     */
    private function handleAndroidWindowsPlayback(Request $request, $contentId)
    {
        $platform = $this->isAndroidDevice($request->header('User-Agent', '')) ? 'android' : 'windows';
        Log::info("🤖 {$platform} Device Detected - Using Widevine", ['content_id' => $contentId]);

        try {
            // Use direct API call to extract Widevine data (same as existing system)
            $extractResponse = \Illuminate\Support\Facades\Http::post(url('/api/shahid/extract_movie_drm'), [
                'movie_id' => $contentId,
                'movie_title' => "Shahid Asset {$contentId}"
            ]);

            if (!$extractResponse->successful()) {
                throw new \Exception('Failed to connect to extraction API');
            }

            $extractData = $extractResponse->json();

            if (!$extractData['success']) {
                throw new \Exception('Failed to extract Widevine data: ' . ($extractData['error'] ?? 'Unknown error'));
            }

            // Get the extracted key and MPD data
            $data = $extractData['data'];
            $drmKey = $data['key'] ?? $data['drm_key'] ?? '';
            $kid = $data['kid'] ?? '';
            $mpdUrl = $data['stream_url'] ?? $data['mpd_url'] ?? $data['manifest_url'] ?? '';

            // Generate player URL using existing Widevine player
            $playerParams = [
                'mpd' => $mpdUrl,
                'key' => $drmKey,
                'keyId' => $kid,
                'title' => "Shahid Asset {$contentId}",
                'type' => 'dash'
            ];

            // Use existing movie player route
            $playerUrl = url("/shahid/play/{$contentId}") . '?' . http_build_query($playerParams);

            Log::info("🤖 {$platform} Widevine URL Generated", [
                'content_id' => $contentId,
                'player_url' => $playerUrl,
                'has_mpd' => !empty($mpdUrl),
                'has_key' => !empty($drmKey)
            ]);

            return response()->json([
                'success' => true,
                'platform' => $platform,
                'drm_type' => 'widevine',
                'player_url' => $playerUrl,
                'data' => [
                    'mpd_url' => $mpdUrl,
                    'drm_key' => $drmKey,
                    'kid' => $kid,
                    'license_url' => $extractData['license_url'] ?? ''
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("{$platform} Widevine Extraction Failed", [
                'content_id' => $contentId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'platform' => $platform,
                'error' => 'Failed to extract Widevine data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Device detection methods
     */
    private function isIOSDevice($userAgent)
    {
        return preg_match('/iPhone|iPad|iPod/i', $userAgent);
    }

    private function isAndroidDevice($userAgent)
    {
        return preg_match('/Android/i', $userAgent);
    }

    private function isWindowsDevice($userAgent)
    {
        return preg_match('/Windows/i', $userAgent);
    }

    /**
     * API endpoint to get device-appropriate player data
     */
    public function getPlayerData(Request $request, $contentId)
    {
        return $this->playContent($request, $contentId);
    }
}