@extends('admin.layouts.app')

@section('title', 'User Subscriptions')
@section('page-title', 'User Subscriptions')

@section('breadcrumb')
<li class="breadcrumb-item">
    <a href="{{ route('admin.user-subscriptions.index') }}" style="color: #667eea; text-decoration: none;">Subscriptions</a>
</li>
<li class="breadcrumb-item active">Management</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Manage user subscriptions, extend durations, and track subscription status.</p>
    </div>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
        <i class="fas fa-plus me-2"></i>Create New Subscription
    </button>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Active Subscriptions</div>
                    <div class="h4 mb-0" id="activeCount">-</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Expiring Soon</div>
                    <div class="h4 mb-0" id="expiringSoonCount">-</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Expired</div>
                    <div class="h4 mb-0" id="expiredCount">-</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Monthly Revenue</div>
                    <div class="h4 mb-0" id="monthlyRevenue">-</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <input type="text" class="form-control" id="searchInput" placeholder="Search users...">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="expired">Expired</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="planFilter">
                    <option value="">All Plans</option>
                    <!-- Plans will be loaded dynamically -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="expiryFilter">
                    <option value="">All Expiry</option>
                    <option value="expired">Expired</option>
                    <option value="expiring_soon">Expiring Soon</option>
                    <option value="active">Active</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-secondary" id="refreshBtn" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-outline-warning" id="clearFilters" title="Clear Filters">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="btn btn-outline-secondary" id="exportBtn" title="Export">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscriptions Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table id="subscriptions-table" class="table table-hover">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Plan</th>
                        <th>Status</th>
                        <th>Start Date</th>
                        <th>Expiry Date</th>
                        <th>Remaining</th>
                        <th>Amount</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="subscriptionsTableBody">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Subscriptions pagination">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination will be loaded here -->
            </ul>
        </nav>
    </div>
</div>

<!-- Create Subscription Modal -->
<div class="modal fade" id="createSubscriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createSubscriptionForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userId" class="form-label">User *</label>
                                <select class="form-select" id="userId" name="user_id" required>
                                    <option value="">Select User</option>
                                    <!-- Users will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="planId" class="form-label">Subscription Plan *</label>
                                <select class="form-select" id="planId" name="subscription_plan_id" required>
                                    <option value="">Select Plan</option>
                                    <!-- Plans will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="startsAt" class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" id="startsAt" name="starts_at">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customDuration" class="form-label">Custom Duration (Days)</label>
                                <input type="number" class="form-control" id="customDuration" name="custom_duration_days" 
                                       placeholder="Leave blank to use plan default">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="amountPaid" class="form-label">Amount Paid</label>
                                <input type="number" step="0.01" class="form-control" id="amountPaid" name="amount_paid">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="paymentMethod" class="form-label">Payment Method</label>
                                <input type="text" class="form-control" id="paymentMethod" name="payment_method">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="paymentReference" class="form-label">Payment Reference</label>
                                <input type="text" class="form-control" id="paymentReference" name="payment_reference">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subscriptionNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="subscriptionNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Extend Subscription Modal -->
<div class="modal fade" id="extendSubscriptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Extend Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="extendSubscriptionForm">
                <div class="modal-body">
                    <input type="hidden" id="extendSubscriptionId" name="subscription_id">
                    <div class="mb-3">
                        <label for="extendDays" class="form-label">Extend by (Days) *</label>
                        <input type="number" class="form-control" id="extendDays" name="days" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="extendNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="extendNotes" name="notes" rows="3" 
                                  placeholder="Reason for extension..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Extend Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentPage = 1;
let currentFilters = {};

$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    loadSubscriptions();
    loadStatistics();
    loadUsers();
    loadPlans();
    
    // Search and filter events
    $('#searchInput').on('input', debounce(function() {
        currentPage = 1;
        loadSubscriptions();
    }, 500));
    
    $('#statusFilter, #planFilter, #expiryFilter').on('change', function() {
        currentPage = 1;
        loadSubscriptions();
    });

    // Clear filters button
    $('#clearFilters').on('click', function() {
        $('#searchInput').val('');
        $('#statusFilter').val('');
        $('#planFilter').val('');
        $('#expiryFilter').val('');
        currentPage = 1;
        loadSubscriptions();
    });
    
    $('#refreshBtn').on('click', function() {
        loadSubscriptions();
        loadStatistics();
    });
    
    // Form submissions
    $('#createSubscriptionForm').on('submit', function(e) {
        e.preventDefault();
        createSubscription();
    });
    
    $('#extendSubscriptionForm').on('submit', function(e) {
        e.preventDefault();
        extendSubscription();
    });
});

function loadSubscriptions() {
    const filters = {
        page: currentPage
    };

    // Only add non-empty filters
    const search = $('#searchInput').val();
    if (search && search.trim() !== '') {
        filters.search = search.trim();
    }

    const status = $('#statusFilter').val();
    if (status && status !== '') {
        filters.status = status;
    }

    const planId = $('#planFilter').val();
    if (planId && planId !== '') {
        filters.plan_id = planId;
    }

    const expiryFilter = $('#expiryFilter').val();
    if (expiryFilter && expiryFilter !== '') {
        filters.expiry_filter = expiryFilter;
    }

    currentFilters = filters;

    // Show loading state
    $('#subscriptionsTableBody').html(`
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="loading-spinner me-2"></div>
                Loading subscriptions...
            </td>
        </tr>
    `);

    $.get('/admin/ajax/user-subscriptions', filters)
        .done(function(response) {
            if (response.success) {
                renderSubscriptionsTable(response.data.data);
                renderPagination(response.data);
                updateResultsInfo(response.data);
            }
        })
        .fail(function(xhr) {
            const errorMessage = xhr.responseJSON?.message || 'Failed to load subscriptions';
            $('#subscriptionsTableBody').html(`
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                            <h5 class="text-danger">Error Loading Data</h5>
                            <p class="text-muted">${errorMessage}</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="loadSubscriptions()">
                                <i class="fas fa-refresh me-2"></i>Try Again
                            </button>
                        </div>
                    </td>
                </tr>
            `);
        });
}

function loadStatistics() {
    $.get('/admin/ajax/user-subscriptions-statistics')
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#activeCount').text(data.active_subscriptions.toLocaleString());
                $('#expiringSoonCount').text(data.expiring_soon.toLocaleString());
                $('#expiredCount').text(data.expired_subscriptions.toLocaleString());
                $('#monthlyRevenue').text('$' + data.revenue_this_month.toLocaleString());
            }
        })
        .fail(function() {
            console.error('Failed to load statistics');
        });
}

function loadUsers() {
    $.get('/admin/ajax/users', { per_page: 1000 })
        .done(function(response) {
            if (response.success) {
                let options = '<option value="">Select User</option>';
                response.data.data.forEach(user => {
                    options += `<option value="${user.id}">${user.name} (${user.email})</option>`;
                });
                $('#userId').html(options);
            }
        });
}

function loadPlans() {
    $.get('/admin/ajax/subscription-plans', { per_page: 1000 })
        .done(function(response) {
            if (response.success) {
                let planOptions = '<option value="">Select Plan</option>';
                let filterOptions = '<option value="">All Plans</option>';
                
                response.data.data.forEach(plan => {
                    planOptions += `<option value="${plan.id}">${plan.name} (${plan.currency} ${plan.price})</option>`;
                    filterOptions += `<option value="${plan.id}">${plan.name}</option>`;
                });
                
                $('#planId').html(planOptions);
                $('#planFilter').html(filterOptions);
            }
        });
}

function renderSubscriptionsTable(subscriptions) {
    let html = '';
    
    if (subscriptions.length === 0) {
        html = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h5>No subscriptions found</h5>
                        <p class="text-muted">No user subscriptions match your current filters.</p>
                        <button class="btn btn-primary btn-sm" onclick="$('#createSubscriptionModal').modal('show')">
                            <i class="fas fa-plus me-2"></i>Create First Subscription
                        </button>
                    </div>
                </td>
            </tr>
        `;
    } else {
        subscriptions.forEach(subscription => {
            const user = subscription.user;
            const plan = subscription.subscription_plan;
            
            let statusBadge = '';
            switch(subscription.status) {
                case 'active':
                    statusBadge = '<span class="badge bg-success">Active</span>';
                    break;
                case 'inactive':
                    statusBadge = '<span class="badge bg-secondary">Inactive</span>';
                    break;
                case 'expired':
                    statusBadge = '<span class="badge bg-danger">Expired</span>';
                    break;
                case 'cancelled':
                    statusBadge = '<span class="badge bg-dark">Cancelled</span>';
                    break;
            }
            
            const remainingDays = subscription.remaining_days || 0;
            let remainingBadge = '';
            if (remainingDays > 30) {
                remainingBadge = `<span class="badge bg-success">${remainingDays} days</span>`;
            } else if (remainingDays > 7) {
                remainingBadge = `<span class="badge bg-warning">${remainingDays} days</span>`;
            } else if (remainingDays > 0) {
                remainingBadge = `<span class="badge bg-danger">${remainingDays} days</span>`;
            } else {
                remainingBadge = '<span class="badge bg-secondary">Expired</span>';
            }
            
            html += `
                <tr>
                    <td>
                        <div class="fw-bold">${user.name}</div>
                        <div class="text-muted small">${user.email}</div>
                    </td>
                    <td>
                        <div class="fw-bold">${plan.name}</div>
                        <div class="text-muted small">${plan.duration_text}</div>
                    </td>
                    <td>${statusBadge}</td>
                    <td>${new Date(subscription.starts_at).toLocaleDateString()}</td>
                    <td>${new Date(subscription.expires_at).toLocaleDateString()}</td>
                    <td>${remainingBadge}</td>
                    <td>
                        ${subscription.amount_paid ? 
                            `<div class="fw-bold">${plan.currency} ${subscription.amount_paid}</div>` : 
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewSubscription(${subscription.id})" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="extendSubscriptionModal(${subscription.id})" title="Extend">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="btn btn-outline-${subscription.status === 'active' ? 'warning' : 'success'}" 
                                    onclick="toggleSubscriptionStatus(${subscription.id})" 
                                    title="${subscription.status === 'active' ? 'Deactivate' : 'Activate'}">
                                <i class="fas fa-${subscription.status === 'active' ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="cancelSubscription(${subscription.id})" title="Cancel">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#subscriptionsTableBody').html(html);
}

function renderPagination(data) {
    let html = '';
    
    if (data.last_page > 1) {
        // Previous button
        html += `<li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.current_page - 1})">Previous</a>
                 </li>`;
        
        // Page numbers
        for (let i = 1; i <= data.last_page; i++) {
            if (i === data.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else if (i === 1 || i === data.last_page || (i >= data.current_page - 2 && i <= data.current_page + 2)) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
            } else if (i === data.current_page - 3 || i === data.current_page + 3) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        // Next button
        html += `<li class="page-item ${data.current_page === data.last_page ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.current_page + 1})">Next</a>
                 </li>`;
    }
    
    $('#pagination').html(html);
}

function changePage(page) {
    currentPage = page;
    loadSubscriptions();
}

function createSubscription() {
    const formData = new FormData($('#createSubscriptionForm')[0]);
    
    $.ajax({
        url: '/admin/ajax/user-subscriptions',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#createSubscriptionModal').modal('hide');
                $('#createSubscriptionForm')[0].reset();
                loadSubscriptions();
                loadStatistics();
                showAlert('success', 'Subscription created successfully!');
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors || {};
            const message = xhr.responseJSON?.message || 'Failed to create subscription';
            if (Object.keys(errors).length > 0) {
                showFormErrors('createSubscriptionForm', errors);
            } else {
                showAlert('error', message);
            }
        }
    });
}

function extendSubscriptionModal(subscriptionId) {
    $('#extendSubscriptionId').val(subscriptionId);
    $('#extendSubscriptionModal').modal('show');
}

function extendSubscription() {
    const subscriptionId = $('#extendSubscriptionId').val();
    const formData = new FormData($('#extendSubscriptionForm')[0]);
    
    $.ajax({
        url: `/admin/ajax/user-subscriptions/${subscriptionId}/extend`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#extendSubscriptionModal').modal('hide');
                $('#extendSubscriptionForm')[0].reset();
                loadSubscriptions();
                loadStatistics();
                showAlert('success', response.message);
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors || {};
            const message = xhr.responseJSON?.message || 'Failed to extend subscription';
            if (Object.keys(errors).length > 0) {
                showFormErrors('extendSubscriptionForm', errors);
            } else {
                showAlert('error', message);
            }
        }
    });
}

function toggleSubscriptionStatus(subscriptionId) {
    if (confirm('Are you sure you want to change this subscription\'s status?')) {
        $.post(`/admin/ajax/user-subscriptions/${subscriptionId}/toggle-status`)
            .done(function(response) {
                if (response.success) {
                    loadSubscriptions();
                    loadStatistics();
                    showAlert('success', response.message);
                }
            })
            .fail(function() {
                showAlert('error', 'Failed to update subscription status');
            });
    }
}

function cancelSubscription(subscriptionId) {
    if (confirm('Are you sure you want to cancel this subscription?')) {
        $.ajax({
            url: `/admin/ajax/user-subscriptions/${subscriptionId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    loadSubscriptions();
                    loadStatistics();
                    showAlert('success', response.message);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to cancel subscription';
                showAlert('error', message);
            }
        });
    }
}

function viewSubscription(subscriptionId) {
    // Implement view subscription details
    window.open(`/admin/user-subscriptions/${subscriptionId}`, '_blank');
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.main-content').prepend(alert);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

function showFormErrors(formId, errors) {
    // Clear previous errors
    $(`#${formId} .is-invalid`).removeClass('is-invalid');
    $(`#${formId} .invalid-feedback`).remove();
    
    // Show new errors
    Object.keys(errors).forEach(field => {
        const input = $(`#${formId} [name="${field}"]`);
        input.addClass('is-invalid');
        input.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
    });
}

function updateResultsInfo(paginationData) {
    const from = paginationData.from || 0;
    const to = paginationData.to || 0;
    const total = paginationData.total || 0;

    let infoText = '';
    if (total > 0) {
        infoText = `Showing ${from} to ${to} of ${total} subscriptions`;
    } else {
        infoText = 'No subscriptions found';
    }

    // Add or update results info
    let resultsInfo = $('#results-info');
    if (resultsInfo.length === 0) {
        $('#subscriptions-table').before(`<div id="results-info" class="text-muted small mb-2">${infoText}</div>`);
    } else {
        resultsInfo.text(infoText);
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
@endsection

@push('styles')
<style>
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.btn-group-sm > .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 4px;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
    border-radius: 8px 8px 0 0 !important;
}

.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
    border-radius: 0 0 12px 12px;
}

.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #495057;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.text-muted {
    color: #6c757d !important;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm > .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-flex.justify-content-between .btn {
        align-self: stretch;
    }

    .row.mb-4 .col-xl-3 {
        margin-bottom: 1rem;
    }

    .card-body .row .col-md-3,
    .card-body .row .col-md-6 {
        margin-bottom: 1rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state i {
        font-size: 2rem;
    }
}
</style>
@endpush
