<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ShahidChannelsAPI;
use App\Services\ProxyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Shahid Channels API Controller
 * Handles all live channels related API endpoints
 */
class ShahidChannelsController extends Controller
{
    protected $channelsAPI;
    protected $proxyService;

    public function __construct(ShahidChannelsAPI $channelsAPI, ProxyService $proxyService)
    {
        $this->channelsAPI = $channelsAPI;
        $this->proxyService = $proxyService;
    }

    /**
     * Get all live channels
     * GET /api/shahid/channels
     */
    public function index(Request $request)
    {
        try {
            $forceRefresh = $request->boolean('refresh', false);
            $result = $this->channelsAPI->getCachedLiveChannels($forceRefresh);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['channels'] ?? [],
                'total_channels' => $result['total'] ?? 0,
                'cached' => !$forceRefresh
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels index: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching live channels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search channels by name
     * GET /api/shahid/channels/search?q=query
     */
    public function search(Request $request)
    {
        try {
            $query = $request->get('q', '');
            
            if (empty($query)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search query is required'
                ], 400);
            }

            $result = $this->channelsAPI->searchChannels($query);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['channels'] ?? [],
                'total_results' => $result['total'] ?? 0,
                'query' => $result['query'] ?? $query
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels search: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error searching channels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get channel by ID
     * GET /api/shahid/channels/{id}
     */
    public function show($id)
    {
        try {
            $result = $this->channelsAPI->getChannelById($id);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $result['channel'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels show: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching channel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get channels by country
     * GET /api/shahid/channels/country/{code}
     */
    public function byCountry($countryCode)
    {
        try {
            $result = $this->channelsAPI->getChannelsByCountry($countryCode);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['channels'] ?? [],
                'total_channels' => $result['total'] ?? 0,
                'country' => $result['country'] ?? $countryCode
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels byCountry: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching channels by country: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh channels cache
     * POST /api/shahid/channels/refresh
     */
    public function refresh()
    {
        try {
            $this->channelsAPI->clearCache();
            $result = $this->channelsAPI->getLiveChannels();

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Channels cache refreshed successfully',
                'data' => $result['channels'] ?? [],
                'total_channels' => $result['total'] ?? 0
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels refresh: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error refreshing channels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extract channel stream data (MPD/HLS with or without DRM)
     * GET /api/shahid/channels/{channelId}/extract
     */
    public function extractChannelData($channelId)
    {
        try {
            Log::info("Starting channel extraction for ID: {$channelId}");

            // Get channel info first
            $channelInfo = $this->getChannelInfo($channelId);
            if (!$channelInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Channel not found or unavailable'
                ]);
            }

            // Get stream data
            $streamData = $this->getChannelStreamData($channelId);
            if (!$streamData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to get stream data'
                ]);
            }

            // Extract and analyze stream
            $extractedData = $this->analyzeStreamData($streamData, $channelInfo);

            return response()->json([
                'success' => true,
                'data' => $extractedData,
                'message' => 'Channel data extracted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("Channel extraction error: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Extraction failed: ' . $e->getMessage()
            ]);
        }
    }



    /**
     * Get channel stream data using Shahid API
     */
    private function getChannelStreamData($channelId)
    {
        try {
            $streamResult = $this->extractChannelStreamData($channelId, 'EG'); // Use Egypt as default

            if (!$streamResult || !$streamResult['success']) {
                return null;
            }

            return $streamResult;
        } catch (\Exception $e) {
            Log::error("Error getting channel stream data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Cache channel stream for dynamic proxy (Flask-style)
     * POST /api/shahid/channels/cache-stream
     */
    public function cacheChannelStream(Request $request)
    {
        try {
            $channelId = $request->input('channel_id');
            $streamUrl = $request->input('stream_url');
            $token = $request->input('token');

            if (!$channelId || !$streamUrl) {
                return response()->json([
                    'success' => false,
                    'error' => 'Channel ID and stream URL are required'
                ], 400);
            }

            // Initialize session cache if not exists
            if (!session()->has('shahid_channel_cache')) {
                session(['shahid_channel_cache' => []]);
            }

            // Cache the stream data (shorter expiry for live channels)
            $cacheData = [
                'stream_url' => $streamUrl,
                'token' => $token,
                'cached_at' => now()->toISOString(),
                'expires_at' => now()->addMinutes(45)->toISOString(), // Shorter expiry for live streams
                'refresh_count' => 0
            ];

            $cache = session('shahid_channel_cache');
            $cache[$channelId] = $cacheData;
            session(['shahid_channel_cache' => $cache]);

            // Generate proxy URL based on stream type
            if (strpos($streamUrl, '.m3u8') !== false) {
                // HLS - use dynamic proxy (Flask-style)
                $proxyUrl = "/shahid/channel/{$channelId}/playlist.m3u8";
            } else {
                // DASH/MPD - use VideoProxyController (same as movies/series)
                $proxyUrl = "/video-proxy/manifest?url=" . urlencode($streamUrl);
            }

            $streamType = strpos($streamUrl, '.m3u8') !== false ? 'hls' : 'dash';
            $proxyMethod = $streamType === 'hls' ? 'Dynamic Proxy' : 'VideoProxyController';

            Log::info("📦 Cached channel stream", [
                'channel_id' => $channelId,
                'proxy_url' => $proxyUrl,
                'stream_type' => $streamType,
                'proxy_method' => $proxyMethod,
                'session_id' => session()->getId(),
                'cache_size' => count($cache)
            ]);

            return response()->json([
                'success' => true,
                'proxy_url' => $proxyUrl,
                'cached' => true,
                'channel_id' => $channelId
            ]);

        } catch (\Exception $e) {
            Log::error('Cache channel stream error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Cache failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available countries
     * GET /api/shahid/channels/countries
     */
    public function countries()
    {
        try {
            $countries = [
                ['code' => 'EG', 'name' => 'Egypt'],
                ['code' => 'SA', 'name' => 'Saudi Arabia'],
                ['code' => 'AE', 'name' => 'UAE'],
                ['code' => 'KW', 'name' => 'Kuwait'],
                ['code' => 'QA', 'name' => 'Qatar'],
                ['code' => 'BH', 'name' => 'Bahrain'],
                ['code' => 'OM', 'name' => 'Oman'],
                ['code' => 'JO', 'name' => 'Jordan'],
                ['code' => 'LB', 'name' => 'Lebanon'],
                ['code' => 'IQ', 'name' => 'Iraq']
            ];

            return response()->json([
                'success' => true,
                'data' => $countries
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels countries: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching countries: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get channels statistics
     * GET /api/shahid/channels/stats
     */
    public function stats()
    {
        try {
            $result = $this->channelsAPI->getCachedLiveChannels();

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            $channels = $result['channels'] ?? [];
            $stats = [
                'total_channels' => count($channels),
                'by_country' => []
            ];

            // Count channels by country
            foreach ($channels as $channel) {
                $country = $channel['country'] ?? 'Unknown';
                $countryName = $channel['country_name'] ?? $country;
                
                if (!isset($stats['by_country'][$country])) {
                    $stats['by_country'][$country] = [
                        'code' => $country,
                        'name' => $countryName,
                        'count' => 0
                    ];
                }
                $stats['by_country'][$country]['count']++;
            }

            // Convert to array
            $stats['by_country'] = array_values($stats['by_country']);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error("Error in channels stats: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching channels statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get channel information
     */
    private function getChannelInfo($channelId)
    {
        try {
            // First try to get from cached channels
            $result = $this->channelsAPI->getChannelById($channelId);

            if (isset($result['channel'])) {
                return $result['channel'];
            }

            // If not found in cache, try direct API call
            $response = $this->channelsAPI->getChannelDetails($channelId);

            if (isset($response['error'])) {
                Log::warning("Channel {$channelId} not found in cache or API");
                // Return a basic structure for testing
                return [
                    'id' => $channelId,
                    'title' => "Channel {$channelId}",
                    'description' => 'Test channel for extraction'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            Log::error("Failed to get channel info: " . $e->getMessage());
            // Return a basic structure for testing
            return [
                'id' => $channelId,
                'title' => "Channel {$channelId}",
                'description' => 'Test channel for extraction'
            ];
        }
    }

    /**
     * Analyze stream data to determine encryption status and stream info
     */
    private function analyzeStreamData($streamData, $channelInfo)
    {
        try {
            // Extract stream URL
            $streamUrl = $this->extractStreamUrl($streamData);
            if (!$streamUrl) {
                throw new \Exception('No stream URL found in stream data');
            }

            // Determine stream type
            $streamType = strpos($streamUrl, '.m3u8') !== false ? 'hls' : 'dash';

            // Initialize result
            $result = [
                'channel_id' => $channelInfo['id'] ?? null,
                'title' => $channelInfo['title'] ?? 'Unknown Channel',
                'stream_url' => $streamUrl,
                'stream_type' => $streamType,
                'is_encrypted' => false,
                'drm_info' => null,
                'quality_info' => [],
                'token' => $this->channelsAPI->getToken()
            ];

            // Analyze manifest to check for encryption
            if ($streamType === 'dash') {
                $manifestAnalysis = $this->analyzeDashManifest($streamUrl);
                if ($manifestAnalysis) {
                    $result['is_encrypted'] = $manifestAnalysis['is_encrypted'];
                    $result['quality_info'] = $manifestAnalysis['quality_info'];

                    if ($manifestAnalysis['is_encrypted']) {
                        $result['drm_info'] = [
                            'drm_type' => $manifestAnalysis['drm_type'],
                            'pssh' => $manifestAnalysis['pssh'] ?? null,
                            'kid' => $manifestAnalysis['kid'] ?? null
                        ];
                    }
                }
            } else {
                $manifestAnalysis = $this->analyzeHlsManifest($streamUrl);
                if ($manifestAnalysis) {
                    $result['is_encrypted'] = $manifestAnalysis['is_encrypted'];
                    $result['quality_info'] = $manifestAnalysis['quality_info'];

                    if ($manifestAnalysis['is_encrypted']) {
                        $result['drm_info'] = [
                            'drm_type' => $manifestAnalysis['drm_type'],
                            'encryption_method' => $manifestAnalysis['encryption_method'] ?? null,
                            'key_url' => $manifestAnalysis['key_url'] ?? null
                        ];
                    }
                }
            }

            Log::info("Stream analysis complete for channel {$channelInfo['id']}", [
                'stream_type' => $streamType,
                'is_encrypted' => $result['is_encrypted'],
                'has_quality_info' => !empty($result['quality_info'])
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error("Stream analysis error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract stream URL from response
     */
    private function extractStreamUrl($streamData)
    {
        // Try different possible keys
        $possibleKeys = ['stream', 'url', 'manifest', 'stream_url', 'manifest_url', 'playout_url'];

        foreach ($possibleKeys as $key) {
            if (isset($streamData[$key]) && !empty($streamData[$key])) {
                return $streamData[$key];
            }
        }

        // Check nested structures
        if (isset($streamData['data']['stream'])) {
            return $streamData['data']['stream'];
        }

        if (isset($streamData['player']['stream'])) {
            return $streamData['player']['stream'];
        }

        if (isset($streamData['playout']['url'])) {
            return $streamData['playout']['url'];
        }

        return null;
    }

    /**
     * Analyze DASH manifest
     */
    private function analyzeDashManifest($manifestUrl)
    {
        try {
            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ])->get($manifestUrl);

            if (!$response->successful()) {
                return null;
            }

            $manifest = $response->body();
            $result = [
                'is_encrypted' => false,
                'quality_info' => [],
                'audio_tracks' => []
            ];

            // Check for encryption
            if (strpos($manifest, 'ContentProtection') !== false) {
                $result['is_encrypted'] = true;
                $result['drm_type'] = 'widevine';

                // Extract PSSH
                if (preg_match('/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/', $manifest, $matches)) {
                    $result['pssh'] = trim($matches[1]);
                }

                // Extract KID
                if (preg_match('/cenc:default_KID="([^"]+)"/', $manifest, $matches)) {
                    $result['kid'] = str_replace('-', '', $matches[1]);
                }
            }

            // Extract quality information
            if (preg_match_all('/<Representation[^>]+width="(\d+)"[^>]+height="(\d+)"[^>]+bandwidth="(\d+)"/', $manifest, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $result['quality_info'][] = [
                        'width' => (int)$match[1],
                        'height' => (int)$match[2],
                        'bandwidth' => (int)$match[3]
                    ];
                }
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Failed to analyze DASH manifest: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Analyze HLS manifest
     */
    private function analyzeHlsManifest($manifestUrl)
    {
        try {
            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ])->get($manifestUrl);

            if (!$response->successful()) {
                return null;
            }

            $manifest = $response->body();
            $result = [
                'is_encrypted' => false,
                'quality_info' => []
            ];

            // Check for encryption
            if (strpos($manifest, '#EXT-X-KEY') !== false) {
                $result['is_encrypted'] = true;
                $result['drm_type'] = 'aes128';

                // Extract key info
                if (preg_match('/#EXT-X-KEY:METHOD=([^,]+),URI="([^"]+)"/', $manifest, $matches)) {
                    $result['encryption_method'] = $matches[1];
                    $result['key_url'] = $matches[2];
                }
            }

            // Extract quality information
            if (preg_match_all('/#EXT-X-STREAM-INF:.*RESOLUTION=(\d+)x(\d+).*BANDWIDTH=(\d+)/', $manifest, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $result['quality_info'][] = [
                        'width' => (int)$match[1],
                        'height' => (int)$match[2],
                        'bandwidth' => (int)$match[3]
                    ];
                }
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Failed to analyze HLS manifest: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract DRM information
     */
    private function extractDrmInfo($streamData, $streamUrl)
    {
        $drmInfo = [];

        // Check for license URL in stream data
        if (isset($streamData['license_url'])) {
            $drmInfo['license_url'] = $streamData['license_url'];
        }

        // Check for DRM headers
        if (isset($streamData['drm_headers'])) {
            $drmInfo['headers'] = $streamData['drm_headers'];
        }

        // Try to extract keys if available
        if (isset($streamData['keys']) && is_array($streamData['keys'])) {
            foreach ($streamData['keys'] as $key) {
                if (isset($key['kid']) && isset($key['key'])) {
                    $drmInfo['kid'] = $key['kid'];
                    $drmInfo['key'] = $key['key'];
                    $drmInfo['formatted_key'] = $key['kid'] . ':' . $key['key'];
                    break;
                }
            }
        }

        return $drmInfo;
    }

    /**
     * Extract DRM data for a channel (like the movie DRM extractor)
     */
    public function extractChannelDrm(Request $request)
    {
        $request->validate([
            'channel_id' => 'required|string',
            'channel_title' => 'string',
            'country' => 'string'
        ]);

        try {
            $channelId = $request->get('channel_id');
            $channelTitle = $request->get('channel_title', 'Unknown Channel');
            $country = $request->get('country', 'SA');

            // 🎯 Log channel click with details
            Log::info("🎬 CHANNEL CLICKED: {$channelTitle} (ID: {$channelId})", [
                'channel_id' => $channelId,
                'channel_title' => $channelTitle,
                'country' => $country,
                'timestamp' => now()->toDateTimeString(),
                'user_ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Initialize DRM service
            $drm = new \App\Services\ShahidDRM();

            // Extract channel stream using the channels API
            $streamResult = $this->extractChannelStreamData($channelId, $country);

            if (!$streamResult || !$streamResult['success']) {
                $statusCode = 400;
                $message = $streamResult['error'] ?? 'Failed to extract channel stream';

                // If it requires VIP, use a different status code
                if (isset($streamResult['requires_vip']) && $streamResult['requires_vip']) {
                    $statusCode = 402; // Payment Required
                    $message = 'This channel requires Shahid VIP subscription. Please upgrade your account to access premium content.';
                }

                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'requires_vip' => $streamResult['requires_vip'] ?? false,
                    'error_type' => $streamResult['requires_vip'] ? 'subscription_required' : 'extraction_failed'
                ], $statusCode);
            }

            // Get MPD URL from streams
            $mpdUrl = null;
            foreach ($streamResult['streams'] as $stream) {
                if ($stream['type'] === 'dash') {
                    $mpdUrl = $stream['url'];
                    break;
                }
            }

            if (!$mpdUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'No DASH stream found for this channel'
                ], 400);
            }

            // Get DRM info with PSSH and KID
            $drmInfo = $streamResult['drm_info'] ?? [];
            $pssh = $drmInfo['pssh'] ?? null;
            $kid = $drmInfo['kid'] ?? null;
            $licenseUrl = $drmInfo['license_url'] ?? null;

            Log::info("Channel DRM Info from stream result:", [
                'pssh' => $pssh ? 'Present (' . strlen($pssh) . ' chars)' : 'Not found',
                'kid' => $kid ?? 'Not found',
                'license_url' => $licenseUrl ?? 'Not found'
            ]);

            // Try to extract PSSH from MPD if not available
            if (!$pssh) {
                Log::info("Extracting PSSH from MPD: {$mpdUrl}");
                $pssh = $drm->extractPSSH($mpdUrl);
                Log::info("PSSH extraction result:", ['pssh' => $pssh ? 'Success' : 'Failed']);
            }

            // Try to get license URL for the channel
            if (!$licenseUrl && $channelId) {
                Log::info("Attempting to get license URL for channel: {$channelId}");
                $licenseUrl = $drm->getLicenseUrl($channelId, $country);
                Log::info("License URL extraction result:", ['license_url' => $licenseUrl ? 'Success' : 'Failed']);
            }

            // Try to extract key if PSSH is available
            $key = null;
            $keyExtractionError = null;

            if ($pssh && $drm->hasDeviceFile()) {
                $keyResult = $drm->extractKey($pssh, $licenseUrl);

                if ($keyResult['success']) {
                    // Extract the first content key if available
                    $keys = $keyResult['keys'] ?? [];
                    foreach ($keys as $keyInfo) {
                        if ($keyInfo['type'] === 'CONTENT') {
                            // Get the key if available
                            if (isset($keyInfo['key']) && $keyInfo['key']) {
                                $key = $keyInfo['key'];
                            }
                            // Always get the KID from the extraction result
                            if (isset($keyInfo['kid'])) {
                                $kid = $keyInfo['kid'];
                            }
                            break;
                        }
                    }

                    // If no key was extracted, note the reason
                    if (!$key && !empty($keys)) {
                        $keyExtractionError = 'KID extracted but key requires license server response';
                    }
                } else {
                    $keyExtractionError = $keyResult['error'] ?? 'Key extraction failed';
                }
            }

            // Build formatted key using the correct KID (after key extraction)
            $formattedKey = null;
            if ($kid && $key) {
                $formattedKey = $kid . ':' . $key;
            } elseif ($kid) {
                $formattedKey = $kid . ':KEY_NEEDED';
            }

            // Detect actual stream type from URL
            $actualStreamType = 'dash'; // default
            if (strpos($mpdUrl, '.m3u8') !== false) {
                $actualStreamType = 'hls';
            } elseif (strpos($mpdUrl, '.mpd') !== false) {
                $actualStreamType = 'dash';
            }

            $responseData = [
                'channel_id' => $channelId,
                'channel_title' => $channelTitle,
                'stream_url' => $mpdUrl,
                'stream_type' => $actualStreamType, // Use detected type
                'is_encrypted' => !empty($pssh), // Channel is encrypted if PSSH is present
                'drm_type' => !empty($pssh) ? 'widevine' : null,
                'pssh' => $pssh,
                'kid' => $kid,
                'key' => $key,
                'formatted_key' => $formattedKey,
                'license_url' => $licenseUrl,
                'drm_supported' => $drm->hasDeviceFile()
            ];

            if ($keyExtractionError) {
                $responseData['key_extraction_error'] = $keyExtractionError;
            }

            Log::info("Final response data for channel {$channelId}:", [
                'pssh_present' => !empty($pssh),
                'kid_present' => !empty($kid),
                'key_present' => !empty($key),
                'formatted_key_present' => !empty($formattedKey),
                'pssh_length' => $pssh ? strlen($pssh) : 0,
                'kid_length' => $kid ? strlen($kid) : 0
            ]);

            return response()->json([
                'success' => true,
                'data' => $responseData,
                'message' => 'Channel stream information extracted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error extracting channel DRM: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Dynamic HLS proxy for channels - Main Playlist
     * GET /shahid/channel/{channelId}/playlist.m3u8
     */
    public function dynamicChannelPlaylist($channelId)
    {
        try {
            Log::info("🎬 Dynamic channel playlist request for: {$channelId}");

            // Debug session information
            Log::info("🔍 Session debug", [
                'session_id' => session()->getId(),
                'has_cache' => session()->has('shahid_channel_cache'),
                'all_session_keys' => array_keys(session()->all())
            ]);

            // Check session cache
            if (!session()->has('shahid_channel_cache')) {
                Log::error("❌ No shahid_channel_cache in session");
                return response('No cached stream found', 404);
            }

            $cache = session('shahid_channel_cache');
            Log::info("📦 Cache contents", ['cache_keys' => array_keys($cache)]);

            $cacheData = $cache[$channelId] ?? null;

            if (!$cacheData) {
                Log::error("❌ Channel {$channelId} not found in cache", [
                    'available_channels' => array_keys($cache),
                    'requested_channel' => $channelId
                ]);
                return response('Channel not cached', 404);
            }

            // Check if cache is expired or stream URL needs refresh
            $cachedAt = new \DateTime($cacheData['cached_at']);
            $now = new \DateTime();
            $minutesSinceCached = $now->diff($cachedAt)->i + ($now->diff($cachedAt)->h * 60);

            // If cached for more than 10 minutes, try to refresh the URL (more frequent for live channels)
            if ($minutesSinceCached > 10) {
                Log::info("🔄 Cache is old ({$minutesSinceCached} minutes), attempting to refresh URL for channel: {$channelId}");

                $refreshedData = $this->refreshChannelStreamUrl($channelId, $cacheData);
                if ($refreshedData) {
                    $cacheData = $refreshedData;
                    // Update cache with fresh data
                    $cache[$channelId] = $cacheData;
                    session(['shahid_channel_cache' => $cache]);
                    Log::info("✅ Channel stream URL refreshed successfully");
                } else {
                    Log::warning("⚠️ Failed to refresh URL, using cached version");
                }
            }

            $streamUrl = $cacheData['stream_url'];
            $token = $cacheData['token'] ?? null;

            // Prepare headers for the request
            $headers = [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept' => '*/*',
                'Accept-Language' => 'en-US,en;q=0.9',
                'Origin' => 'https://shahid.mbc.net',
                'Referer' => 'https://shahid.mbc.net/'
            ];

            if ($token) {
                $headers['Authorization'] = "Bearer {$token}";
                $headers['token'] = $token;
            }

            // Fetch original playlist with retry logic
            $maxRetries = 3;
            $response = null;

            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                Log::info("🔄 Fetching playlist (attempt {$attempt}/{$maxRetries})");

                $response = $this->proxyService->getHttpClient(['timeout' => 30])
                    ->withHeaders($headers)
                    ->get($streamUrl);

                if ($response->successful()) {
                    $content = $response->body();

                    // Rewrite URLs to use dynamic proxy
                    $rewrittenContent = $this->rewriteHlsUrlsForDynamicProxy($content, $channelId);

                    Log::info("✅ Playlist fetched successfully on attempt {$attempt}");
                    return response($rewrittenContent)
                        ->header('Content-Type', 'application/x-mpegURL')
                        ->header('Access-Control-Allow-Origin', '*')
                        ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                    ->header('Pragma', 'no-cache')
                    ->header('Expires', '0');

                } else if ($response->status() === 403 || $response->status() === 401) {
                    // Authentication/authorization error - try to refresh URL
                    Log::warning("⚠️ Auth error ({$response->status()}) on attempt {$attempt}, trying to refresh URL");

                    if ($attempt < $maxRetries) {
                        $refreshedData = $this->refreshChannelStreamUrl($channelId, $cacheData);
                        if ($refreshedData) {
                            $streamUrl = $refreshedData['stream_url'];
                            $cacheData = $refreshedData;
                            // Update session cache
                            $cache[$channelId] = $refreshedData;
                            session(['shahid_channel_cache' => $cache]);
                            Log::info("🔄 URL refreshed, retrying with new URL");
                            continue;
                        }
                    }
                } else {
                    Log::warning("⚠️ Request failed with status {$response->status()} on attempt {$attempt}");
                    if ($attempt < $maxRetries) {
                        sleep(1); // Wait 1 second before retry
                        continue;
                    }
                }
            }

            Log::error("❌ Failed to fetch playlist after {$maxRetries} attempts: {$response->status()}");
            return response('Playlist fetch failed after retries', 502);

        } catch (\Exception $e) {
            Log::error("Dynamic channel playlist error: " . $e->getMessage());
            return response('Proxy error', 500);
        }
    }

    /**
     * Dynamic HLS proxy for channels - Segments & Sub-playlists
     * GET /shahid/channel/{channelId}/{segment}
     */
    public function dynamicChannelSegment($channelId, $segment)
    {
        try {
            Log::info("🎥 Dynamic channel segment request: {$channelId}/{$segment}");

            // Check session cache
            if (!session()->has('shahid_channel_cache')) {
                return response('No cached stream found', 404);
            }

            $cache = session('shahid_channel_cache');
            $cacheData = $cache[$channelId] ?? null;

            if (!$cacheData) {
                Log::error("❌ Channel {$channelId} not found in cache");
                return response('Channel not cached', 404);
            }

            // Check if cache needs refresh for segments too
            $cachedAt = new \DateTime($cacheData['cached_at']);
            $now = new \DateTime();
            $minutesSinceCached = $now->diff($cachedAt)->i + ($now->diff($cachedAt)->h * 60);

            if ($minutesSinceCached > 10) {
                Log::info("🔄 Segment request: Cache is old ({$minutesSinceCached} minutes), attempting refresh");
                $refreshedData = $this->refreshChannelStreamUrl($channelId, $cacheData);
                if ($refreshedData) {
                    $cacheData = $refreshedData;
                    $cache[$channelId] = $cacheData;
                    session(['shahid_channel_cache' => $cache]);
                }
            }

            $streamUrl = $cacheData['stream_url'];
            $token = $cacheData['token'] ?? null;

            // Handle different types of segment URLs
            if (str_starts_with($segment, 'http')) {
                // Absolute URL - use as is
                $segmentUrl = $segment;
            } elseif (str_starts_with($segment, 'mpd_template/')) {
                // MPD template URL handling
                $templatePart = substr($segment, 13); // Remove 'mpd_template/'
                $decodedTemplate = urldecode($templatePart);

                // Extract base URL from query parameter
                $baseUrl = request()->get('base', dirname($streamUrl));

                // Handle template variables - restore original DASH template format
                $decodedTemplate = str_replace(['__NUMBER__', '__TIME__', '__REPID__'], ['$Number$', '$Time$', '$RepresentationID$'], $decodedTemplate);

                // For template URLs, we need to handle them as regular segments
                // The actual template resolution should happen on the client side
                $segmentUrl = rtrim($baseUrl, '/') . '/' . ltrim($decodedTemplate, '/');

                Log::info("🎬 MPD template URL: {$decodedTemplate} → {$segmentUrl}");
            } else {
                // Regular relative URL - build from base stream URL
                $decodedSegment = urldecode($segment);

                if (str_starts_with($decodedSegment, 'http')) {
                    // Decoded segment is absolute URL
                    $segmentUrl = $decodedSegment;
                } else {
                    // Build from base stream URL - handle relative paths properly
                    $parsedStreamUrl = parse_url($streamUrl);
                    $baseUrl = $parsedStreamUrl['scheme'] . '://' . $parsedStreamUrl['host'];
                    $basePath = dirname($parsedStreamUrl['path'] ?? '');

                    // 🎯 Enhanced logging for debugging relative path resolution
                    Log::info("🔍 RELATIVE PATH DEBUG", [
                        'original_segment' => $segment,
                        'decoded_segment' => $decodedSegment,
                        'stream_url' => $streamUrl,
                        'parsed_stream_url' => $parsedStreamUrl,
                        'base_url' => $baseUrl,
                        'base_path' => $basePath,
                        'contains_dotdot' => str_contains($decodedSegment, '../')
                    ]);

                    // Handle relative paths that go up directories (../)
                    if (str_contains($decodedSegment, '../')) {
                        // Resolve relative path
                        $resolvedPath = $this->resolveRelativePath($basePath, $decodedSegment);
                        $segmentUrl = $baseUrl . $resolvedPath;

                        Log::info("🔄 RESOLVED RELATIVE PATH", [
                            'base_path' => $basePath,
                            'relative_segment' => $decodedSegment,
                            'resolved_path' => $resolvedPath,
                            'final_url' => $segmentUrl
                        ]);
                    } else {
                        // Simple relative path - build from base directory
                        // Check if this looks like a resolved segment (e.g., video/468p750kbps/segment.ts)
                        if (preg_match('/^(video|audio)\/\d+p?\d*kbps\//', $decodedSegment)) {
                            // This is a resolved segment path - build from base directory, not base URL root
                            $segmentUrl = $baseUrl . rtrim($basePath, '/') . '/' . ltrim($decodedSegment, '/');

                            Log::info("🎯 RESOLVED SEGMENT PATH", [
                                'base_url' => $baseUrl,
                                'base_path' => $basePath,
                                'segment' => $decodedSegment,
                                'final_url' => $segmentUrl
                            ]);
                        } else {
                            // Regular relative path - build from base directory
                            $segmentUrl = $baseUrl . rtrim($basePath, '/') . '/' . ltrim($decodedSegment, '/');

                            Log::info("🔗 SIMPLE RELATIVE PATH", [
                                'base_path' => $basePath,
                                'segment' => $decodedSegment,
                                'final_url' => $segmentUrl
                            ]);
                        }
                    }
                }
            }

            Log::info("🔗 Segment URL built", [
                'channel_id' => $channelId,
                'original_segment' => $segment,
                'decoded_segment' => $decodedSegment ?? 'N/A',
                'base_stream_url' => $streamUrl,
                'base_url_dirname' => dirname($streamUrl),
                'final_segment_url' => $segmentUrl,
                'url_length' => strlen($segmentUrl)
            ]);

            // Prepare headers
            $headers = [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept' => '*/*',
                'Accept-Language' => 'en-US,en;q=0.9',
                'Origin' => 'https://shahid.mbc.net',
                'Referer' => 'https://shahid.mbc.net/'
            ];

            if ($token) {
                $headers['Authorization'] = "Bearer {$token}";
                $headers['token'] = $token;
            }

            // Fetch the segment with retry logic
            $maxRetries = 2;
            $response = null;

            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                Log::info("🔄 Fetching segment (attempt {$attempt}/{$maxRetries}): {$segment}");

                $response = $this->proxyService->getHttpClient(['timeout' => 15])
                    ->withHeaders($headers)
                    ->get($segmentUrl);

                if ($response->successful()) {
                    break;
                } else {
                    Log::warning("⚠️ Segment fetch failed (attempt {$attempt}): {$response->status()}");
                    if ($attempt < $maxRetries) {
                        sleep(1);
                    }
                }
            }

            if ($response->successful()) {
                $content = $response->body();

                // Determine content type and caching based on segment type
                if (strpos($segment, '.m3u8') !== false) {
                    // HLS playlist
                    $content = $this->rewriteHlsUrlsForDynamicProxy($content, $channelId);
                    $contentType = 'application/x-mpegURL';
                    $cacheControl = 'no-cache, no-store, must-revalidate';
                } elseif (strpos($segment, '.mpd') !== false) {
                    // DASH manifest
                    $content = $this->rewriteMpdUrlsForDynamicProxy($content, $channelId, $segmentUrl);
                    $contentType = 'application/dash+xml';
                    $cacheControl = 'no-cache, no-store, must-revalidate';
                } elseif (strpos($segment, '.m4s') !== false || strpos($segment, '.mp4') !== false) {
                    // DASH video segments
                    $contentType = $response->header('Content-Type', 'video/mp4');
                    $cacheControl = 'public, max-age=30';
                } elseif (strpos($segment, '.ts') !== false) {
                    // HLS video segments
                    $contentType = $response->header('Content-Type', 'video/mp2t');
                    $cacheControl = 'public, max-age=30';
                } else {
                    // Default handling
                    $contentType = $response->header('Content-Type', 'application/octet-stream');
                    $cacheControl = 'public, max-age=30';
                }

                return response($content)
                    ->header('Content-Type', $contentType)
                    ->header('Access-Control-Allow-Origin', '*')
                    ->header('Cache-Control', $cacheControl)
                    ->header('Pragma', 'no-cache')
                    ->header('Expires', '0');
            } else {
                Log::error("❌ Failed to fetch segment after {$maxRetries} attempts", [
                    'channel_id' => $channelId,
                    'segment' => $segment,
                    'final_url' => $segmentUrl,
                    'status_code' => $response->status(),
                    'response_body' => substr($response->body(), 0, 200)
                ]);
                return response('Segment fetch failed: ' . $response->status(), 502);
            }

        } catch (\Exception $e) {
            Log::error("Dynamic channel segment error: " . $e->getMessage());
            return response('Proxy error', 500);
        }
    }

    /**
     * Rewrite HLS manifest URLs for dynamic proxy
     */
    private function rewriteHlsUrlsForDynamicProxy($content, $channelId)
    {
        $lines = explode("\n", $content);
        $rewrittenLines = [];

        foreach ($lines as $line) {
            $originalLine = $line;
            $line = trim($line);

            if ($line && !str_starts_with($line, '#')) {
                // This is a URL line
                if (!str_starts_with($line, 'http')) {
                    // Relative URL - rewrite to use dynamic proxy
                    // Handle query parameters properly
                    $urlParts = parse_url($line);
                    $path = $urlParts['path'] ?? $line;
                    $query = isset($urlParts['query']) ? '?' . $urlParts['query'] : '';

                    // 🎯 Enhanced URL encoding for relative paths
                    if (str_contains($path, '../')) {
                        // For paths with ../, we need to resolve them first
                        // Don't just encode them - resolve the relative path
                        $resolvedPath = $this->resolveRelativePathForRewrite($path);
                        $encodedPath = str_replace(['%2F'], ['/'], urlencode($resolvedPath));

                        Log::info("🔄 RELATIVE PATH WITH DOTDOT RESOLVED", [
                            'original' => $path,
                            'resolved' => $resolvedPath,
                            'encoded' => $encodedPath
                        ]);
                    } else {
                        // For simple relative paths, minimal encoding
                        $encodedPath = str_replace(['%2F'], ['/'], urlencode($path));
                    }

                    $dynamicUrl = "/shahid/channel/{$channelId}/{$encodedPath}{$query}";
                    Log::info("🔄 Dynamic rewrite: {$line} → {$dynamicUrl}");
                    $line = $dynamicUrl;
                } else {
                    // Absolute URL - also proxy it for consistency
                    $encodedUrl = urlencode($line);
                    $dynamicUrl = "/shahid/channel/{$channelId}/proxy?url={$encodedUrl}";
                    Log::info("🔄 Absolute URL proxy: {$line} → {$dynamicUrl}");
                    $line = $dynamicUrl;
                }
            }

            $rewrittenLines[] = $line;
        }

        return implode("\n", $rewrittenLines);
    }

    /**
     * Resolve relative path with .. navigation
     */
    private function resolveRelativePath($basePath, $relativePath)
    {
        // Split paths into components
        $baseComponents = array_filter(explode('/', $basePath), 'strlen');
        $relativeComponents = array_filter(explode('/', $relativePath), 'strlen');

        $resolvedComponents = $baseComponents;

        foreach ($relativeComponents as $component) {
            if ($component === '..') {
                // Go up one directory
                if (!empty($resolvedComponents)) {
                    array_pop($resolvedComponents);
                }
            } elseif ($component !== '.') {
                // Add component to path
                $resolvedComponents[] = $component;
            }
        }

        return '/' . implode('/', $resolvedComponents);
    }

    /**
     * Resolve relative path for URL rewriting (simpler version)
     */
    private function resolveRelativePathForRewrite($relativePath)
    {
        // For paths like ../../../video/468p750kbps/segment.ts
        // We want to extract just the final meaningful part: video/468p750kbps/segment.ts

        $components = explode('/', $relativePath);
        $resolvedComponents = [];

        foreach ($components as $component) {
            if ($component === '..') {
                // Skip the .. components - we don't need them in the rewritten URL
                continue;
            } elseif ($component !== '.' && !empty($component)) {
                // Add meaningful components
                $resolvedComponents[] = $component;
            }
        }

        $resolved = implode('/', $resolvedComponents);

        Log::info("🔧 PATH RESOLUTION", [
            'original' => $relativePath,
            'components' => $components,
            'resolved_components' => $resolvedComponents,
            'final' => $resolved
        ]);

        return $resolved;
    }

    /**
     * Dynamic proxy for absolute URLs
     * GET /shahid/channel/{channelId}/proxy?url={encodedUrl}
     */
    public function dynamicChannelAbsoluteProxy($channelId, Request $request)
    {
        try {
            $encodedUrl = $request->query('url');
            if (!$encodedUrl) {
                return response('Missing URL parameter', 400);
            }

            $absoluteUrl = urldecode($encodedUrl);
            Log::info("🔗 Absolute URL proxy request: {$absoluteUrl}");

            // Check session cache for token
            if (!session()->has('shahid_channel_cache')) {
                return response('No cached stream found', 404);
            }

            $cache = session('shahid_channel_cache');
            $cacheData = $cache[$channelId] ?? null;

            if (!$cacheData) {
                return response('Channel not cached', 404);
            }

            $token = $cacheData['token'] ?? null;

            // Prepare headers
            $headers = [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept' => '*/*',
                'Accept-Language' => 'en-US,en;q=0.9',
                'Origin' => 'https://shahid.mbc.net',
                'Referer' => 'https://shahid.mbc.net/'
            ];

            if ($token) {
                $headers['Authorization'] = "Bearer {$token}";
                $headers['token'] = $token;
            }

            // Fetch the absolute URL
            $response = $this->proxyService->getHttpClient(['timeout' => 15])
                ->withHeaders($headers)
                ->get($absoluteUrl);

            if ($response->successful()) {
                $content = $response->body();
                $contentType = $response->header('Content-Type', 'application/octet-stream');

                return response($content)
                    ->header('Content-Type', $contentType)
                    ->header('Access-Control-Allow-Origin', '*')
                    ->header('Cache-Control', 'public, max-age=300');
            } else {
                Log::error("❌ Failed to fetch absolute URL: {$response->status()}");
                return response('Absolute URL fetch failed', 502);
            }

        } catch (\Exception $e) {
            Log::error("Absolute URL proxy error: " . $e->getMessage());
            return response('Proxy error', 500);
        }
    }

    /**
     * Refresh channel stream URL when it expires
     */
    private function refreshChannelStreamUrl($channelId, $oldCacheData)
    {
        try {
            Log::info("🔄 Attempting to refresh stream URL for channel: {$channelId}");

            // Initialize Shahid API
            $shahidAPI = new \App\Services\ShahidAPI();

            // Get fresh channel data from Shahid API
            $channelData = $shahidAPI->getChannelDetails($channelId);

            if (!$channelData || !isset($channelData['streams']) || empty($channelData['streams'])) {
                Log::error("❌ Failed to get fresh channel data for: {$channelId}");
                return null;
            }

            // Extract the first available stream
            $stream = $channelData['streams'][0];
            $newStreamUrl = $stream['url'] ?? null;

            if (!$newStreamUrl) {
                Log::error("❌ No stream URL in fresh channel data for: {$channelId}");
                return null;
            }

            // Create new cache data with fresh URL
            $newCacheData = [
                'stream_url' => $newStreamUrl,
                'token' => $oldCacheData['token'], // Keep the same token
                'cached_at' => now()->toISOString(),
                'expires_at' => now()->addHours(2)->toISOString(),
                'refresh_count' => ($oldCacheData['refresh_count'] ?? 0) + 1
            ];

            Log::info("✅ Successfully refreshed stream URL", [
                'channel_id' => $channelId,
                'old_url_preview' => substr($oldCacheData['stream_url'], 0, 100) . '...',
                'new_url_preview' => substr($newStreamUrl, 0, 100) . '...',
                'refresh_count' => $newCacheData['refresh_count']
            ]);

            return $newCacheData;

        } catch (\Exception $e) {
            Log::error("❌ Error refreshing channel stream URL: " . $e->getMessage(), [
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Extract channel stream data using Shahid API
     */
    private function extractChannelStreamData($channelId, $country = 'SA')
    {
        try {
            // Get channel playout URL using the same method as Shahid_Live.py
            $playoutUrl = "https://api2.shahid.net/proxy/v2.1/playout/new/url/{$channelId}?country={$country}";

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->channelsAPI->getToken(),
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'browser_name' => 'CHROME',
                'browser_version' => '*********',
                'shahid_os' => 'WINDOWS'
            ];

            $response = \Illuminate\Support\Facades\Http::withHeaders($headers)
                ->timeout(15)
                ->get($playoutUrl);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'error' => 'Failed to get channel playout data',
                    'requires_vip' => $response->status() === 402
                ];
            }

            $data = $response->json();

            if (!isset($data['playout'])) {
                return [
                    'success' => false,
                    'error' => 'No playout data found',
                    'requires_vip' => false
                ];
            }

            $playout = $data['playout'];
            $mpdUrl = $playout['url'] ?? null;

            if (!$mpdUrl) {
                return [
                    'success' => false,
                    'error' => 'No stream URL found',
                    'requires_vip' => false
                ];
            }

            // Clean and add filter for better quality
            // Remove existing manifestfilter parameters to avoid duplication
            $mpdUrl = preg_replace('/[&?]aws\.manifestfilter=[^&]*/', '', $mpdUrl);
            $mpdUrl = preg_replace('/[&?]video_height=[^&]*/', '', $mpdUrl);
            $mpdUrl = preg_replace('/[&?]video_codec=[^&]*/', '', $mpdUrl);

            // Add clean filter
            if (strpos($mpdUrl, '?') !== false) {
                $mpdUrl .= '&aws.manifestfilter=video_height:144-1080;video_codec:H264';
            } else {
                $mpdUrl .= '?aws.manifestfilter=video_height:144-1080;video_codec:H264';
            }

            // 🎯 Log the stream URL for this channel
            Log::info("📺 CHANNEL STREAM URL EXTRACTED", [
                'channel_id' => $channelId,
                'country' => $country,
                'stream_url' => $mpdUrl,
                'is_drm_protected' => $playout['drm'] ?? false,
                'timestamp' => now()->toDateTimeString()
            ]);

            $streams = [
                [
                    'type' => 'dash',
                    'url' => $mpdUrl,
                    'quality' => 'adaptive',
                    'drm_protected' => $playout['drm'] ?? false
                ]
            ];

            // Extract DRM info if available
            $drmInfo = [];
            if ($playout['drm'] ?? false) {
                $drmInfo['license_url'] = $playout['license_url'] ?? null;

                // Try to extract PSSH from MPD
                try {
                    // Use ProxyService for HTTP client
                    $mpdResponse = $this->proxyService->getHttpClient([
                        'timeout' => 30
                    ])->withHeaders([
                        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    ])->get($mpdUrl);

                    if ($mpdResponse->successful()) {
                        $manifest = $mpdResponse->body();

                        // Extract PSSH
                        if (preg_match('/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/', $manifest, $matches)) {
                            $drmInfo['pssh'] = trim($matches[1]);
                        }

                        // Extract KID
                        if (preg_match('/cenc:default_KID="([^"]+)"/', $manifest, $matches)) {
                            $drmInfo['kid'] = str_replace('-', '', $matches[1]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to extract PSSH from MPD: " . $e->getMessage());
                }
            }

            return [
                'success' => true,
                'streams' => $streams,
                'drm_info' => $drmInfo
            ];

        } catch (\Exception $e) {
            Log::error('Channel stream extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Handle dynamic MPD manifest requests
     */
    public function dynamicChannelMpdManifest($channelId)
    {
        try {
            Log::info("🎬 Dynamic MPD manifest request for channel: {$channelId}");

            // Check session cache
            if (!session()->has('shahid_channel_cache')) {
                Log::error("❌ No shahid_channel_cache in session");
                return response('No cached stream found', 404);
            }

            $cache = session('shahid_channel_cache');
            $cacheData = $cache[$channelId] ?? null;

            if (!$cacheData) {
                Log::error("❌ Channel {$channelId} not found in cache");
                return response('Channel not cached', 404);
            }

            // Check if cache needs refresh
            $cachedAt = new \DateTime($cacheData['cached_at']);
            $now = new \DateTime();
            $minutesSinceCached = $now->diff($cachedAt)->i + ($now->diff($cachedAt)->h * 60);

            if ($minutesSinceCached > 10) {
                Log::info("🔄 MPD request: Cache is old ({$minutesSinceCached} minutes), attempting refresh");
                $refreshedData = $this->refreshChannelStreamUrl($channelId, $cacheData);
                if ($refreshedData) {
                    $cacheData = $refreshedData;
                    $cache[$channelId] = $cacheData;
                    session(['shahid_channel_cache' => $cache]);
                }
            }

            $streamUrl = $cacheData['stream_url'];
            $token = $cacheData['token'] ?? null;

            // Check if this is an MPD stream
            if (!str_contains($streamUrl, '.mpd')) {
                Log::warning("⚠️ Stream URL is not MPD format, but proceeding: {$streamUrl}");
                // Don't return error, let it proceed in case it's a valid DASH stream
            }

            Log::info("📡 Fetching MPD manifest from: {$streamUrl}");

            // Prepare headers
            $headers = [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept' => 'application/dash+xml,video/vnd.mpeg.dash.mpd,application/xml,*/*',
                'Referer' => 'https://shahid.mbc.net/',
                'Origin' => 'https://shahid.mbc.net'
            ];

            if ($token) {
                $headers['Authorization'] = "Bearer {$token}";
            }

            // Fetch the MPD manifest
            $response = $this->proxyService->getHttpClient(['timeout' => 30])
                ->withHeaders($headers)
                ->get($streamUrl);

            if ($response->successful()) {
                $content = $response->body();

                // Rewrite URLs in MPD manifest for dynamic proxy
                $rewrittenContent = $this->rewriteMpdUrlsForDynamicProxy($content, $channelId, $streamUrl);

                Log::info("✅ MPD manifest fetched and rewritten successfully");

                return response($rewrittenContent)
                    ->header('Content-Type', 'application/dash+xml')
                    ->header('Access-Control-Allow-Origin', '*')
                    ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                    ->header('Pragma', 'no-cache')
                    ->header('Expires', '0');
            } else {
                Log::error("❌ Failed to fetch MPD manifest: {$response->status()}");
                return response('Failed to fetch manifest', $response->status());
            }

        } catch (\Exception $e) {
            Log::error("Dynamic MPD manifest error: " . $e->getMessage());
            return response('Manifest fetch failed', 500);
        }
    }

    /**
     * Rewrite MPD manifest URLs for dynamic proxy
     */
    private function rewriteMpdUrlsForDynamicProxy($content, $channelId, $baseUrl)
    {
        try {
            Log::info("🔄 Rewriting MPD URLs for dynamic proxy");

            // Parse the base URL to get the directory
            $baseDir = dirname($baseUrl);

            // Rewrite initialization URLs
            $content = preg_replace_callback(
                '/initialization="([^"]+)"/',
                function ($matches) use ($channelId, $baseDir) {
                    $url = $matches[1];
                    if (!str_starts_with($url, 'http')) {
                        $fullUrl = rtrim($baseDir, '/') . '/' . ltrim($url, '/');
                        $proxyUrl = "/channel/{$channelId}/" . urlencode($fullUrl);
                        Log::info("🔄 MPD init rewrite: {$url} → {$proxyUrl}");
                        return 'initialization="' . $proxyUrl . '"';
                    }
                    return $matches[0];
                },
                $content
            );

            // Rewrite media URLs
            $content = preg_replace_callback(
                '/media="([^"]+)"/',
                function ($matches) use ($channelId, $baseDir) {
                    $url = $matches[1];
                    if (!str_starts_with($url, 'http')) {
                        // Handle template URLs like "segment_$Number$.m4s"
                        if (str_contains($url, '$')) {
                            // For template URLs, we need special handling
                            $templateUrl = str_replace(['$Number$', '$Time$', '$RepresentationID$'], ['__NUMBER__', '__TIME__', '__REPID__'], $url);
                            $proxyUrl = "/channel/{$channelId}/mpd_template/" . urlencode($templateUrl) . "?base=" . urlencode($baseDir);
                        } else {
                            $fullUrl = rtrim($baseDir, '/') . '/' . ltrim($url, '/');
                            $proxyUrl = "/channel/{$channelId}/" . urlencode($fullUrl);
                        }
                        Log::info("🔄 MPD media rewrite: {$url} → {$proxyUrl}");
                        return 'media="' . $proxyUrl . '"';
                    }
                    return $matches[0];
                },
                $content
            );

            // Rewrite BaseURL elements
            $content = preg_replace_callback(
                '/<BaseURL>([^<]+)<\/BaseURL>/',
                function ($matches) use ($channelId, $baseDir) {
                    $url = $matches[1];
                    if (!str_starts_with($url, 'http')) {
                        $fullUrl = rtrim($baseDir, '/') . '/' . ltrim($url, '/');
                        $proxyUrl = "/channel/{$channelId}/" . urlencode($fullUrl);
                        Log::info("🔄 MPD BaseURL rewrite: {$url} → {$proxyUrl}");
                        return '<BaseURL>' . $proxyUrl . '</BaseURL>';
                    }
                    return $matches[0];
                },
                $content
            );

            return $content;

        } catch (\Exception $e) {
            Log::error("❌ Error rewriting MPD URLs: " . $e->getMessage());
            return $content; // Return original content if rewriting fails
        }
    }

    /**
     * Refresh a specific channel manually
     */
    public function refreshChannel($channelId)
    {
        try {
            Log::info("🔄 Manual refresh requested for channel: {$channelId}");

            // Dispatch refresh job
            \App\Jobs\RefreshChannelStreamsJob::dispatch($channelId);

            return response()->json([
                'success' => true,
                'message' => "Channel {$channelId} refresh job dispatched"
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Manual channel refresh failed: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Refresh failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh all active channels manually
     */
    public function refreshAllChannels()
    {
        try {
            Log::info("🔄 Manual refresh requested for all channels");

            // Dispatch refresh job for all channels
            \App\Jobs\RefreshChannelStreamsJob::dispatch();

            return response()->json([
                'success' => true,
                'message' => "All channels refresh job dispatched"
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Manual all channels refresh failed: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Refresh failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
