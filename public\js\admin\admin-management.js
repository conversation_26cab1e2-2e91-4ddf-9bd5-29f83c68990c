// Admin Management JavaScript
class AdminManagement {
    constructor() {
        this.currentPage = 1;
        this.perPage = 15;
        this.filters = {};
        this.init();
    }

    init() {
        this.loadStatistics();
        this.loadAdmins();
        this.setupEventListeners();
        this.loadRoles();
        this.loadDepartments();
    }

    setupEventListeners() {
        // Search and filters
        document.getElementById('search-input').addEventListener('input', 
            this.debounce(() => this.applyFilters(), 500));
        
        document.getElementById('apply-filters').addEventListener('click', () => this.applyFilters());
        document.getElementById('clear-filters').addEventListener('click', () => this.clearFilters());

        // Form submissions
        const createForm = document.getElementById('create-admin-form');
        if (createForm) {
            createForm.addEventListener('submit', (e) => this.handleCreateAdmin(e));
        }

        const suspendForm = document.getElementById('suspend-admin-form');
        if (suspendForm) {
            suspendForm.addEventListener('submit', (e) => this.handleSuspendAdmin(e));
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async loadStatistics() {
        try {
            const response = await fetch('/admin/admin-management/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.updateStatistics(data.data);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateStatistics(stats) {
        document.getElementById('total-admins').textContent = stats.total_admins || 0;
        document.getElementById('active-admins').textContent = stats.active_admins || 0;
        document.getElementById('suspended-admins').textContent = stats.suspended_admins || 0;
        document.getElementById('super-admins').textContent = stats.super_admins || 0;
    }

    async loadAdmins(page = 1) {
        try {
            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage,
                ...this.filters
            });

            const response = await fetch(`/admin/admin-management/api?${params}`);
            console.log('Response status:', response.status);
            const data = await response.json();
            console.log('Response data:', data);
            
            if (data.success) {
                // Handle both paginated and non-paginated responses
                const admins = data.data.data || data.data;
                this.renderAdmins(admins);

                // Only render pagination if data has pagination info
                if (data.data.links) {
                    this.renderPagination(data.data);
                }
                this.currentPage = page;
            }
        } catch (error) {
            console.error('Error loading admins:', error);
            this.showAlert('Error loading admins', 'danger');
        }
    }

    renderAdmins(admins) {
        console.log('renderAdmins called with:', admins);
        const tbody = document.getElementById('admins-tbody');

        if (!tbody) {
            console.error('admins-tbody element not found');
            return;
        }

        tbody.innerHTML = '';

        if (!admins || admins.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No admins found</td></tr>';
            return;
        }

        admins.forEach(admin => {
            const row = this.createAdminRow(admin);
            tbody.appendChild(row);
        });
    }

    createAdminRow(admin) {
        const row = document.createElement('tr');
        
        // Status badge
        let statusBadge = '';
        if (admin.suspended_at) {
            statusBadge = '<span class="badge bg-warning status-badge">Suspended</span>';
        } else if (admin.is_active) {
            statusBadge = '<span class="badge bg-success status-badge">Active</span>';
        } else {
            statusBadge = '<span class="badge bg-secondary status-badge">Inactive</span>';
        }

        // Super admin badge
        const superAdminBadge = admin.is_super_admin ? 
            '<span class="badge bg-danger status-badge ms-1">Super Admin</span>' : '';

        // Roles badges - handle case where roles might not exist
        const rolesBadges = admin.roles ? admin.roles.map(role =>
            `<span class="badge bg-primary role-badge">${role.name}</span>`
        ).join('') : '';

        // Last activity
        const lastActivity = admin.last_login_at ?
            new Date(admin.last_login_at).toLocaleString() : 'Never';

        row.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <img src="${admin.avatar_url || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(admin.name.charAt(0)) + '&background=667eea&color=fff&size=40'}"
                         class="admin-avatar me-3" alt="${admin.name}">
                    <div>
                        <div class="fw-bold">${admin.name}</div>
                        <small class="text-muted">${admin.username || 'N/A'}</small>
                    </div>
                </div>
            </td>
            <td>${admin.email}</td>
            <td>${admin.department || 'N/A'}</td>
            <td>${rolesBadges}</td>
            <td>${statusBadge}${superAdminBadge}</td>
            <td>
                <small class="last-activity">${lastActivity}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="adminManagement.viewAdmin(${admin.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <a href="/admin/admin-management/${admin.id}/edit" class="btn btn-outline-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>
                    ${admin.suspended_at ?
                        `<button class="btn btn-outline-success" onclick="adminManagement.unsuspendAdmin(${admin.id})" title="Unsuspend">
                            <i class="fas fa-check"></i>
                        </button>` :
                        `<button class="btn btn-outline-danger" onclick="adminManagement.suspendAdmin(${admin.id})" title="Suspend">
                            <i class="fas fa-ban"></i>
                        </button>`
                    }
                    <button class="btn btn-outline-danger" onclick="adminManagement.deleteAdmin(${admin.id})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    renderPagination(paginationData) {
        const container = document.getElementById('pagination-container');
        if (!paginationData.links || paginationData.links.length <= 3) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<ul class="pagination justify-content-center">';
        
        paginationData.links.forEach(link => {
            const isActive = link.active ? 'active' : '';
            const isDisabled = !link.url ? 'disabled' : '';
            const page = this.extractPageFromUrl(link.url);
            
            paginationHTML += `
                <li class="page-item ${isActive} ${isDisabled}">
                    <a class="page-link" href="#" onclick="adminManagement.loadAdmins(${page}); return false;">
                        ${link.label}
                    </a>
                </li>
            `;
        });
        
        paginationHTML += '</ul>';
        container.innerHTML = paginationHTML;
    }

    extractPageFromUrl(url) {
        if (!url) return 1;
        const match = url.match(/page=(\d+)/);
        return match ? parseInt(match[1]) : 1;
    }

    applyFilters() {
        this.filters = {
            search: document.getElementById('search-input').value,
            status: document.getElementById('status-filter').value,
            role: document.getElementById('role-filter').value,
            department: document.getElementById('department-filter').value,
        };

        // Remove empty filters
        Object.keys(this.filters).forEach(key => {
            if (!this.filters[key]) {
                delete this.filters[key];
            }
        });

        this.loadAdmins(1);
    }

    clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('status-filter').value = '';
        document.getElementById('role-filter').value = '';
        document.getElementById('department-filter').value = '';
        
        this.filters = {};
        this.loadAdmins(1);
    }

    async loadRoles() {
        try {
            const response = await fetch('/admin/admin-management/api/roles');
            const data = await response.json();

            if (data.success) {
                const roleFilter = document.getElementById('role-filter');
                const createRoleSelect = document.getElementById('admin-roles');

                // Clear existing options
                if (roleFilter) roleFilter.innerHTML = '<option value="">All Roles</option>';
                if (createRoleSelect) createRoleSelect.innerHTML = '';

                data.data.forEach(role => {
                    if (roleFilter) {
                        const option = document.createElement('option');
                        option.value = role.name;
                        option.textContent = role.name;
                        roleFilter.appendChild(option);
                    }

                    if (createRoleSelect) {
                        const option = document.createElement('option');
                        option.value = role.name;
                        option.textContent = role.name;
                        createRoleSelect.appendChild(option);
                    }
                });
            }
        } catch (error) {
            console.error('Error loading roles:', error);
        }
    }

    async loadDepartments() {
        // This would typically come from an API, but for now we'll use static data
        const departments = ['IT', 'Customer Service', 'Sales', 'Marketing', 'HR', 'Finance'];
        const departmentFilter = document.getElementById('department-filter');
        
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept;
            option.textContent = dept;
            departmentFilter.appendChild(option);
        });
    }

    async handleCreateAdmin(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        console.log('Form data:', data);

        // Handle checkboxes
        data.is_super_admin = formData.has('is_super_admin');
        data.is_active = formData.has('is_active');

        // Handle multiple roles
        const roles = formData.getAll('roles[]');
        data.roles = roles;

        try {
            console.log('Sending data:', data);
            const response = await fetch('/admin/admin-management/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            console.log('Response status:', response.status);
            const result = await response.json();
            console.log('Response result:', result);
            
            if (result.success) {
                this.showAlert('Admin created successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createAdminModal')).hide();
                e.target.reset();
                this.loadAdmins();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error creating admin', 'danger');
            }
        } catch (error) {
            console.error('Error creating admin:', error);
            this.showAlert('Error creating admin', 'danger');
        }
    }

    async viewAdmin(adminId) {
        try {
            const response = await fetch(`/admin/admin-management/api/${adminId}`);
            const data = await response.json();
            
            if (data.success) {
                this.showAdminDetails(data.data);
            }
        } catch (error) {
            console.error('Error loading admin details:', error);
            this.showAlert('Error loading admin details', 'danger');
        }
    }

    showAdminDetails(admin) {
        const modal = new bootstrap.Modal(document.getElementById('adminDetailsModal'));
        const content = document.getElementById('admin-details-content');
        
        // Render admin details
        content.innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <img src="${admin.avatar_url}" class="rounded-circle mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    <h5>${admin.name}</h5>
                    <p class="text-muted">${admin.position || 'N/A'}</p>
                </div>
                <div class="col-md-8">
                    <h6>Contact Information</h6>
                    <p><strong>Email:</strong> ${admin.email}</p>
                    <p><strong>Phone:</strong> ${admin.phone || 'N/A'}</p>
                    <p><strong>Department:</strong> ${admin.department || 'N/A'}</p>
                    
                    <h6 class="mt-4">Roles & Permissions</h6>
                    <p><strong>Roles:</strong> ${admin.roles.map(r => r.name).join(', ') || 'None'}</p>
                    <p><strong>Is Super Admin:</strong> ${admin.is_super_admin ? 'Yes' : 'No'}</p>
                    <p><strong>Can Manage Admins:</strong> ${admin.can_manage_admins ? 'Yes' : 'No'}</p>
                    
                    <h6 class="mt-4">Activity</h6>
                    <p><strong>Last Activity:</strong> ${admin.last_activity_at ? new Date(admin.last_activity_at).toLocaleString() : 'Never'}</p>
                    <p><strong>Created:</strong> ${new Date(admin.created_at).toLocaleString()}</p>
                    
                    ${admin.bio ? `<h6 class="mt-4">Bio</h6><p>${admin.bio}</p>` : ''}
                </div>
            </div>
        `;
        
        modal.show();
    }

    async suspendAdmin(adminId) {
        this.currentAdminId = adminId;
        const modal = new bootstrap.Modal(document.getElementById('suspendAdminModal'));
        modal.show();
    }

    async handleSuspendAdmin(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        try {
            const response = await fetch(`/admin/admin-management/api/${this.currentAdminId}/suspend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Admin suspended successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('suspendAdminModal')).hide();
                e.target.reset();
                this.loadAdmins();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error suspending admin', 'danger');
            }
        } catch (error) {
            console.error('Error suspending admin:', error);
            this.showAlert('Error suspending admin', 'danger');
        }
    }

    async unsuspendAdmin(adminId) {
        if (!confirm('Are you sure you want to unsuspend this admin?')) return;

        try {
            const response = await fetch(`/admin/admin-management/api/${adminId}/unsuspend`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Admin unsuspended successfully!', 'success');
                this.loadAdmins();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error unsuspending admin', 'danger');
            }
        } catch (error) {
            console.error('Error unsuspending admin:', error);
            this.showAlert('Error unsuspending admin', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    async deleteAdmin(adminId) {
        if (!confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/admin/admin-management/api/${adminId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Admin deleted successfully!', 'success');
                this.loadAdmins();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error deleting admin', 'danger');
            }
        } catch (error) {
            console.error('Error deleting admin:', error);
            this.showAlert('Error deleting admin', 'danger');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.adminManagement = new AdminManagement();
});
