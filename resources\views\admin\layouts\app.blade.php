<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src 'self' data: https: http:; connect-src 'self' https:;">
    <title>@yield('title', 'Admin Panel') - Shahid Play</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome - Multiple CDN sources for reliability -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css';">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet" media="none" onload="if(media!='all')media='all'">
    <!-- Fallback for icons if FontAwesome fails -->
    <style>
        .fas, .fa {
            font-family: 'Font Awesome 6 Free', 'Font Awesome 5 Free', sans-serif;
            font-weight: 900;
        }
        /* Fallback icons using Unicode */
        .fa-crown::before { content: '👑'; font-family: sans-serif; }
        .fa-tachometer-alt::before { content: '📊'; font-family: sans-serif; }
        .fa-users::before { content: '👥'; font-family: sans-serif; }
        .fa-tags::before { content: '🏷️'; font-family: sans-serif; }
        .fa-credit-card::before { content: '💳'; font-family: sans-serif; }
        .fa-external-link-alt::before { content: '🔗'; font-family: sans-serif; }
        .fa-sign-out-alt::before { content: '🚪'; font-family: sans-serif; }
        .fa-check-circle::before { content: '✅'; font-family: sans-serif; }
        .fa-exclamation-circle::before { content: '⚠️'; font-family: sans-serif; }
        .fa-info-circle::before { content: 'ℹ️'; font-family: sans-serif; }
        .fa-plus::before { content: '➕'; font-family: sans-serif; }
        .fa-edit::before { content: '✏️'; font-family: sans-serif; }
        .fa-trash::before { content: '🗑️'; font-family: sans-serif; }
        .fa-eye::before { content: '👁️'; font-family: sans-serif; }
        .fa-search::before { content: '🔍'; font-family: sans-serif; }
    </style>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Local Icon Fallbacks -->
    <link href="{{ asset('css/icons.css') }}" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 250px !important;
            height: 100vh !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            z-index: 1000 !important;
            overflow-y: auto !important;
            display: block !important;
        }

        .content-area {
            padding: 20px;
            padding-top: 37px; /* Custom spacing for fixed header */
            margin-left: 250px; /* Sidebar width */
            min-height: 100vh;
            background: #f8f9fa;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 25px;
            font-weight: 500;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        @media (max-width: 767px) {
            .content-area {
                margin-left: 0;
                padding: 15px;
                padding-top: 37px; /* Custom spacing for mobile header */
            }
        }
    </style>
    
    @yield('styles')
    @stack('styles')
</head>
<body>
    @auth('admin')
    <!-- Include Admin Sidebar Component -->
    @include('admin.components.sidebar')

    <!-- Include Admin Header Component -->
    @include('admin.components.header')
    @endauth

    <!-- Mobile Navbar for small screens -->
    @auth('admin')
    <nav class="navbar navbar-expand-md navbar-dark d-md-none" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('admin.dashboard') }}">
                <i class="fas fa-crown me-2"></i>
                Admin Panel
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="mobileNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" href="{{ route('admin.users.index') }}">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.subscription-plans.*') ? 'active' : '' }}" href="{{ route('admin.subscription-plans.index') }}">
                            <i class="fas fa-tags me-2"></i>Plans
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.user-subscriptions.*') ? 'active' : '' }}" href="{{ route('admin.user-subscriptions.index') }}">
                            <i class="fas fa-credit-card me-2"></i>Subscriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard') }}">
                            <i class="fas fa-external-link-alt me-2"></i>User Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <form action="{{ route('admin.logout') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="nav-link border-0 bg-transparent">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    @endauth

    <!-- Main Content -->
    <div class="content-area">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('info'))
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ session('info') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @yield('content')
    </div>
    
    <!-- jQuery with multiple fallbacks -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js'"></script>
    <script>
        // jQuery fallback check
        window.jQuery || document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"><\/script>');
    </script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF Token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
    
    @yield('scripts')
    @stack('scripts')
</body>
</html>
