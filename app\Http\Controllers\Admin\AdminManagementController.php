<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Spatie\Permission\Models\Role;

class AdminManagementController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
    }

    /**
     * Display a listing of admin users.
     */
    public function index(Request $request): JsonResponse
    {
        // $this->authorize('viewAny', Admin::class);

        try {
            $admins = Admin::with(['roles'])->get();
        } catch (\Exception $e) {
            \Log::error('AdminManagement index error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading admins: ' . $e->getMessage()
            ], 500);
        }

        return response()->json([
            'success' => true,
            'data' => $admins,
            'message' => 'Admin users retrieved successfully'
        ]);
    }

    /**
     * Store a newly created admin user.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:admins',
                'password' => 'required|string|min:8|confirmed',
                'username' => 'nullable|string|max:255|unique:admins',
                'department' => 'nullable|string|max:255',
                'is_super_admin' => 'boolean',
                'is_active' => 'boolean',
                'roles' => 'array',
                'roles.*' => 'string|exists:roles,name',
            ]);

            $validated['password'] = Hash::make($validated['password']);
            $validated['is_active'] = $validated['is_active'] ?? true;
            $validated['is_super_admin'] = $validated['is_super_admin'] ?? false;

            $admin = Admin::create($validated);

            // Assign roles
            if (!empty($validated['roles'])) {
                \Log::info('Assigning roles: ' . json_encode($validated['roles']));
                foreach ($validated['roles'] as $roleName) {
                    $role = \Spatie\Permission\Models\Role::where('name', $roleName)
                        ->where('guard_name', 'admin')
                        ->first();
                    if ($role) {
                        \Log::info('Assigning role: ' . $role->name . ' to admin: ' . $admin->id);
                        $admin->assignRole($role);

                        // Automatically set is_super_admin if Super Admin role is assigned
                        if ($roleName === 'Super Admin') {
                            $admin->update(['is_super_admin' => true]);
                            \Log::info("Set is_super_admin to true for admin: {$admin->email}");
                        }
                    } else {
                        \Log::warning('Role not found: ' . $roleName . ' for admin guard');
                    }
                }
            }

            // Load relationships for response
            $admin->load(['roles']);

            return response()->json([
                'success' => true,
                'data' => $admin,
                'message' => 'Admin user created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('AdminManagement store error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error creating admin: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified admin user.
     */
    public function show(Admin $admin): JsonResponse
    {
        // $this->authorize('view', $admin);

        $admin->load([
            'roles',
            'permissions',
            'suspendedBy',
            'createdByAdmin',
            'activityLogs' => function ($query) {
                $query->latest()->limit(20);
            }
        ]);

        // Note: Temporarily disabled user counts due to table structure mismatch
        // $admin->loadCount(['createdUsers', 'suspendedUsers']);

        return response()->json([
            'success' => true,
            'data' => $admin,
            'message' => 'Admin user retrieved successfully'
        ]);
    }

    /**
     * Update the specified admin user.
     */
    public function update(Request $request, Admin $admin): JsonResponse
    {
        // $this->authorize('update', $admin);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->ignore($admin->id)
            ],
            'password' => 'nullable|string|min:8|confirmed',
            'department' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'is_super_admin' => 'boolean',
            'can_manage_admins' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Store old values for logging
        $oldValues = $admin->only(['name', 'email', 'department', 'position', 'is_super_admin', 'can_manage_admins', 'is_active']);

        // Only super admins can modify super admin status
        if (isset($validated['is_super_admin']) && $validated['is_super_admin'] !== $admin->is_super_admin) {
            if (!Auth::guard('admin')->user()->isSuperAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only super admins can modify super admin status'
                ], 403);
            }
        }

        // Handle password update
        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
            $validated['password_changed_at'] = now();
            $validated['force_password_change'] = false;
        } else {
            unset($validated['password']);
        }

        $admin->update($validated);

        // Log activity
        // AdminActivityLog::logUserUpdated(Auth::user(), $admin, $oldValues);

        return response()->json([
            'success' => true,
            'data' => $admin,
            'message' => 'Admin user updated successfully'
        ]);
    }

    /**
     * Suspend the specified admin user.
     */
    public function suspend(Request $request, Admin $admin): JsonResponse
    {
        // $this->authorize('suspend', $admin);



        if ($admin->isSuspended()) {
            return response()->json([
                'success' => false,
                'message' => 'Admin is already suspended'
            ], 422);
        }

        $validated = $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $admin->suspend($validated['reason'], Auth::guard('admin')->user());

        return response()->json([
            'success' => true,
            'message' => 'Admin user suspended successfully'
        ]);
    }

    /**
     * Unsuspend the specified admin user.
     */
    public function unsuspend(Admin $admin): JsonResponse
    {
        $this->authorize('suspend', $admin);



        if (!$admin->isSuspended()) {
            return response()->json([
                'success' => false,
                'message' => 'Admin is not suspended'
            ], 422);
        }

        $admin->unsuspend(Auth::guard('admin')->user());

        return response()->json([
            'success' => true,
            'message' => 'Admin user unsuspended successfully'
        ]);
    }

    /**
     * Get admin statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_admins' => Admin::count(),
                'active_admins' => Admin::where('is_active', true)->count(),
                'suspended_admins' => Admin::where('is_active', false)->count(),
                'super_admins' => Admin::where('is_super_admin', true)->count(),
            ];
        } catch (\Exception $e) {
            \Log::error('AdminManagement statistics error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading statistics: ' . $e->getMessage()
            ], 500);
        }

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Admin statistics retrieved successfully'
        ]);
    }

    /**
     * Get available roles for admin assignment.
     */
    public function getRoles(): JsonResponse
    {
        try {
            // Get only web guard roles since Admin model uses web guard
            $roles = \Spatie\Permission\Models\Role::where('guard_name', 'web')
                ->get(['id', 'name', 'guard_name']);

            return response()->json([
                'success' => true,
                'data' => $roles,
                'message' => 'Roles retrieved successfully'
            ]);
        } catch (\Exception $e) {
            \Log::error('AdminManagement getRoles error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading roles: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign roles to admin user.
     */
    public function assignRoles(Request $request, Admin $admin): JsonResponse
    {
        // $this->authorize('manageRoles', $admin);

        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,name',
        ]);

        $assignedRoles = [];
        $failedRoles = [];

        foreach ($validated['roles'] as $roleName) {
            $role = Role::where('name', $roleName)->where('guard_name', 'admin')->first();

            if ($role && $this->canAssignRole($role)) {
                if (!$admin->hasRole($role)) {
                    $admin->assignRole($role);
                    $assignedRoles[] = $roleName;
                }
            } else {
                $failedRoles[] = $roleName;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'assigned' => $assignedRoles,
                'failed' => $failedRoles,
            ],
            'message' => 'Roles assignment completed'
        ]);
    }

    /**
     * Revoke roles from admin user.
     */
    public function revokeRoles(Request $request, Admin $admin): JsonResponse
    {
        // $this->authorize('manageRoles', $admin);

        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,name',
        ]);

        $revokedRoles = [];
        $failedRoles = [];

        foreach ($validated['roles'] as $roleName) {
            $role = Role::where('name', $roleName)->first();

            if ($role && $this->canRevokeRole($role)) {
                if ($admin->hasRole($role)) {
                    $admin->removeRole($role);
                    $revokedRoles[] = $roleName;
                }
            } else {
                $failedRoles[] = $roleName;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'revoked' => $revokedRoles,
                'failed' => $failedRoles,
            ],
            'message' => 'Roles revocation completed'
        ]);
    }

    /**
     * Sync roles for admin user.
     */
    public function syncRoles(Request $request, Admin $admin): JsonResponse
    {
        // $this->authorize('manageRoles', $admin);

        $validated = $request->validate([
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
        ]);

        $allowedRoles = [];

        // Filter roles based on user's ability to assign them
        foreach ($validated['roles'] ?? [] as $roleName) {
            $role = Role::where('name', $roleName)->first();
            if ($role && $this->canAssignRole($role)) {
                $allowedRoles[] = $role;
            }
        }

        // Store old roles for logging
        $oldRoles = $admin->roles->pluck('name')->toArray();

        $admin->syncRoles($allowedRoles);

        // Check if Super Admin role is assigned and update is_super_admin field
        $hasSupeAdminRole = collect($allowedRoles)->contains(function ($role) {
            return $role->name === 'Super Admin';
        });

        if ($hasSupeAdminRole && !$admin->is_super_admin) {
            $admin->update(['is_super_admin' => true]);
            \Log::info("Set is_super_admin to true for admin: {$admin->email}");
        } elseif (!$hasSupeAdminRole && $admin->is_super_admin) {
            // Remove super admin status if Super Admin role is removed
            $admin->update(['is_super_admin' => false]);
            \Log::info("Set is_super_admin to false for admin: {$admin->email}");
        }

        $admin->load(['roles']);

        return response()->json([
            'success' => true,
            'data' => $admin,
            'message' => 'Admin roles synced successfully'
        ]);
    }

    /**
     * Check if current user can assign a role.
     */
    private function canAssignRole(Role $role): bool
    {
        // return Auth::user()->can('assign', $role);
        return true; // Temporary for testing
    }

    /**
     * Check if current user can revoke a role.
     */
    private function canRevokeRole(Role $role): bool
    {
        // return Auth::user()->can('revoke', $role);
        return true; // Temporary for testing
    }

    /**
     * Check if current user can assign a permission.
     */
    private function canAssignPermission(string $permissionName): bool
    {
        $permission = \Spatie\Permission\Models\Permission::where('name', $permissionName)->first();
        // return $permission && Auth::user()->can('assign', $permission);
        return true; // Temporary for testing
    }

    /**
     * Delete an admin user.
     */
    public function destroy(Admin $admin): JsonResponse
    {
        try {
            // Prevent deleting yourself
            if (auth('admin')->id() === $admin->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot delete yourself'
                ], 403);
            }

            // Prevent deleting super admin unless you are super admin
            if ($admin->is_super_admin && !auth('admin')->user()->is_super_admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only super admins can delete super admin users'
                ], 403);
            }

            $adminName = $admin->name;
            $admin->delete();

            return response()->json([
                'success' => true,
                'message' => "Admin user '{$adminName}' deleted successfully"
            ]);

        } catch (\Exception $e) {
            \Log::error('AdminManagement destroy error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting admin: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update admin password
     */
    public function updatePassword(Request $request, Admin $admin)
    {
        try {
            $validated = $request->validate([
                'password' => 'required|string|min:8|confirmed',
                'force_password_change' => 'boolean'
            ]);

            // Check if current user can update this admin
            $currentAdmin = auth('admin')->user();
            if (!$currentAdmin->is_super_admin && $admin->is_super_admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only super admins can update super admin passwords'
                ], 403);
            }

            // Update password
            $admin->update([
                'password' => Hash::make($validated['password']),
                'password_changed_at' => now(),
                'force_password_change' => $validated['force_password_change'] ?? false
            ]);

            // Log activity
            AdminActivityLog::logActivity([
                'admin_id' => $currentAdmin->id,
                'action' => 'password_updated',
                'target_type' => 'Admin',
                'target_id' => $admin->id,
                'target_name' => $admin->name,
                'description' => "Updated password for admin: {$admin->name}"
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Password updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error updating admin password: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the password'
            ], 500);
        }
    }
}
