<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ترخيص شاهد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            direction: rtl;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 14px;
        }
        
        .log {
            background: #000;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        
        .response-box {
            background: #1e1e1e;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .json-viewer {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار ترخيص شاهد DRM</h1>
        
        <div class="test-section">
            <h3>📝 بيانات الاختبار</h3>
            
            <div class="input-group">
                <label>Asset ID:</label>
                <input type="text" id="assetId" value="4992343679206767" placeholder="أدخل Asset ID">
            </div>
            
            <div class="input-group">
                <label>Country Code:</label>
                <input type="text" id="country" value="EG" placeholder="مثال: EG, SA, AE">
            </div>
            
            <div class="input-group">
                <label>Authorization Token:</label>
                <input type="text" id="authToken" value="3ce470dbc332bb0c023a1670099f6b41e54528627bb15340bf6ad1180f9604cb" placeholder="Authorization header">
            </div>
            
            <button class="btn" onclick="testDRMLicense()">🔐 اختبار الترخيص</button>
            <button class="btn" onclick="testCertificate()">📜 اختبار الشهادة</button>
            <button class="btn" onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div class="log" id="log"></div>
        </div>
        
        <div class="test-section">
            <h3>📨 آخر استجابة</h3>
            <div class="response-box">
                <div class="json-viewer" id="response"></div>
            </div>
        </div>
    </div>

    <script>
        const logDiv = document.getElementById('log');
        const responseDiv = document.getElementById('response');
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console[type === 'error' ? 'error' : type === 'warn' ? 'warn' : 'log'](message);
        }
        
        function clearLog() {
            logDiv.textContent = '';
            responseDiv.textContent = '';
        }
        
        function displayResponse(data, title = 'Response') {
            responseDiv.innerHTML = `
                <h4>${title}</h4>
                <pre style="color: #4CAF50;">${JSON.stringify(data, null, 2)}</pre>
            `;
        }
        
        function getShahidHeaders() {
            const authToken = document.getElementById('authToken').value;
            
            return {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en',
                'Authorization': authToken,
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Host': 'api3.shahid.net',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'Referer': 'https://shahid.mbc.net/'
            };
        }
        
        async function testDRMLicense() {
            addLog('info', 'بدء اختبار ترخيص DRM...');
            
            const assetId = document.getElementById('assetId').value;
            const country = document.getElementById('country').value;
            
            if (!assetId) {
                addLog('error', 'يرجى إدخال Asset ID');
                return;
            }
            
            // Build the URL exactly like in the screenshot
            const url = `https://api3.shahid.net/proxy/v2.1/playout/new/drm?request=%7B%22assetId%22:${assetId}%7D&country=${country}&ts=${Date.now()}`;
            
            addLog('info', `URL: ${url}`);
            
            try {
                const headers = getShahidHeaders();
                addLog('info', 'Headers: ' + JSON.stringify(headers, null, 2));
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });
                
                addLog('info', `HTTP Status: ${response.status} ${response.statusText}`);
                
                // Log response headers
                const responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });
                addLog('info', 'Response Headers: ' + JSON.stringify(responseHeaders, null, 2));
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addLog('success', 'تم الحصول على الترخيص بنجاح!');
                addLog('info', 'Response Data: ' + JSON.stringify(data, null, 2));
                
                displayResponse(data, 'DRM License Response');
                
                // Extract important info
                if (data.fairplay) {
                    addLog('success', `FairPlay Certificate URL: ${data.fairplay}`);
                }
                if (data.signature) {
                    addLog('success', `License URL: ${data.signature}`);
                }
                if (data.kid) {
                    addLog('success', `Key ID: ${data.kid}`);
                }
                
            } catch (error) {
                addLog('error', `فشل في الحصول على الترخيص: ${error.message}`);
                console.error('Full error:', error);
            }
        }
        
        async function testCertificate() {
            addLog('info', 'بدء اختبار شهادة FairPlay...');
            
            const certificateUrl = 'https://shahid-ga.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4';
            
            addLog('info', `Certificate URL: ${certificateUrl}`);
            
            try {
                const response = await fetch(certificateUrl, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                        'Accept': 'application/octet-stream',
                        'Origin': 'https://shahid.mbc.net',
                        'Referer': 'https://shahid.mbc.net/'
                    }
                });
                
                addLog('info', `Certificate HTTP Status: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const arrayBuffer = await response.arrayBuffer();
                const certificateSize = arrayBuffer.byteLength;
                
                addLog('success', `تم الحصول على الشهادة بنجاح! الحجم: ${certificateSize} bytes`);
                
                // Convert to base64 for display
                const uint8Array = new Uint8Array(arrayBuffer);
                const base64 = btoa(String.fromCharCode.apply(null, uint8Array));
                
                displayResponse({
                    size: certificateSize,
                    type: 'FairPlay Certificate',
                    base64Preview: base64.substring(0, 100) + '...'
                }, 'FairPlay Certificate');
                
            } catch (error) {
                addLog('error', `فشل في الحصول على الشهادة: ${error.message}`);
                console.error('Certificate error:', error);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', 'صفحة اختبار ترخيص شاهد جاهزة');
            addLog('info', 'تأكد من إدخال البيانات الصحيحة قبل الاختبار');
        });
    </script>
</body>
</html>
