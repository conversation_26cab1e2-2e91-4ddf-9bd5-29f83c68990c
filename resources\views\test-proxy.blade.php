<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام Video Proxy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 اختبار نظام Video Proxy</h1>
        
        <div class="test-section info">
            <h3>📋 معلومات النظام</h3>
            <p><strong>الخادم:</strong> {{ request()->getHost() }}</p>
            <p><strong>البروتوكول:</strong> {{ request()->getScheme() }}</p>
            <p><strong>URL الأساسي:</strong> {{ url('/') }}</p>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار Proxy Routes</h3>
            <button onclick="testManifestProxy()">اختبار Manifest Proxy</button>
            <button onclick="testSegmentProxy()">اختبار Segment Proxy</button>
            <button onclick="testCORS()">اختبار CORS</button>
            <div id="proxy-results" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🎥 اختبار تشغيل فيديو تجريبي</h3>
            <button onclick="testVideoPlayback()">تشغيل فيديو تجريبي</button>
            <div id="video-container" style="margin-top: 10px;"></div>
            <div id="video-results" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 سجل الأحداث</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="main-log" class="log"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('main-log');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('main-log').innerHTML = '';
            document.getElementById('proxy-results').innerHTML = '';
            document.getElementById('video-results').innerHTML = '';
        }

        async function testManifestProxy() {
            log('🔄 اختبار Manifest Proxy...');
            
            try {
                const testUrl = 'https://mbcvod-enc.edgenextcdn.net/test.mpd';
                const proxyUrl = `/video-proxy/manifest?url=${encodeURIComponent(testUrl)}`;
                
                const response = await fetch(proxyUrl, { method: 'HEAD' });
                
                if (response.ok) {
                    log('✅ Manifest Proxy يعمل بشكل صحيح', 'success');
                    document.getElementById('proxy-results').innerHTML += 
                        `<div style="color: green">✅ Manifest Proxy: OK (${response.status})</div>`;
                } else {
                    log(`❌ Manifest Proxy فشل: ${response.status}`, 'error');
                    document.getElementById('proxy-results').innerHTML += 
                        `<div style="color: red">❌ Manifest Proxy: Failed (${response.status})</div>`;
                }
            } catch (error) {
                log(`❌ خطأ في Manifest Proxy: ${error.message}`, 'error');
                document.getElementById('proxy-results').innerHTML += 
                    `<div style="color: red">❌ Manifest Proxy: Error - ${error.message}</div>`;
            }
        }

        async function testSegmentProxy() {
            log('🔄 اختبار Segment Proxy...');
            
            try {
                const testUrl = 'https://mbcvod-enc.edgenextcdn.net/test.mp4';
                const encodedUrl = btoa(testUrl);
                const proxyUrl = `/video-proxy/segment/${encodedUrl}`;
                
                const response = await fetch(proxyUrl, { method: 'HEAD' });
                
                if (response.ok || response.status === 404) {
                    log('✅ Segment Proxy يعمل بشكل صحيح', 'success');
                    document.getElementById('proxy-results').innerHTML += 
                        `<div style="color: green">✅ Segment Proxy: OK (${response.status})</div>`;
                } else {
                    log(`❌ Segment Proxy فشل: ${response.status}`, 'error');
                    document.getElementById('proxy-results').innerHTML += 
                        `<div style="color: red">❌ Segment Proxy: Failed (${response.status})</div>`;
                }
            } catch (error) {
                log(`❌ خطأ في Segment Proxy: ${error.message}`, 'error');
                document.getElementById('proxy-results').innerHTML += 
                    `<div style="color: red">❌ Segment Proxy: Error - ${error.message}</div>`;
            }
        }

        async function testCORS() {
            log('🔄 اختبار CORS...');
            
            try {
                const response = await fetch('/video-proxy/manifest', {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = response.headers.get('Access-Control-Allow-Origin');
                
                if (corsHeaders) {
                    log('✅ CORS يعمل بشكل صحيح', 'success');
                    document.getElementById('proxy-results').innerHTML += 
                        `<div style="color: green">✅ CORS: OK - ${corsHeaders}</div>`;
                } else {
                    log('❌ CORS غير مُعد بشكل صحيح', 'error');
                    document.getElementById('proxy-results').innerHTML += 
                        `<div style="color: red">❌ CORS: Not configured</div>`;
                }
            } catch (error) {
                log(`❌ خطأ في CORS: ${error.message}`, 'error');
                document.getElementById('proxy-results').innerHTML += 
                    `<div style="color: red">❌ CORS: Error - ${error.message}</div>`;
            }
        }

        function testVideoPlayback() {
            log('🔄 اختبار تشغيل الفيديو...');
            
            const videoContainer = document.getElementById('video-container');
            videoContainer.innerHTML = `
                <video width="400" height="200" controls>
                    <source src="data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0AAACrgYF//+q3EXpvebZSLeWLNgg2SPu73gyNjQgLSBjb3JlIDE1MiByMjg1NCBlOWE1OTAzIC0gSC4yNjQvTVBFRy00IEFWQyBjb2RlYyAtIENvcHlsZWZ0IDIwMDMtMjAxNyAtIGh0dHA6Ly93d3cudmlkZW9sYW4ub3JnL3gyNjQuaHRtbCAtIG9wdGlvbnM6IGNhYmFjPTEgcmVmPTMgZGVibG9jaz0xOjA6MCBhbmFseXNlPTB4MzoweDExMyBtZT1oZXggc3VibWU9NyBwc3k9MSBwc3lfcmQ9MS4wMDowLjAwIG1peGVkX3JlZj0xIG1lX3JhbmdlPTE2IGNocm9tYV9tZT0xIHRyZWxsaXM9MSA4eDhkY3Q9MSBjcW09MCBkZWFkem9uZT0yMSwxMSBmYXN0X3Bza2lwPTEgY2hyb21hX3FwX29mZnNldD0tMiB0aHJlYWRzPTMgbG9va2FoZWFkX3RocmVhZHM9MSBzbGljZWRfdGhyZWFkcz0wIG5yPTAgZGVjaW1hdGU9MSBpbnRlcmxhY2VkPTAgYmx1cmF5X2NvbXBhdD0wIGNvbnN0cmFpbmVkX2ludHJhPTAgYmZyYW1lcz0zIGJfcHlyYW1pZD0yIGJfYWRhcHQ9MSBiX2JpYXM9MCBkaXJlY3Q9MSB3ZWlnaHRiPTEgb3Blbl9nb3A9MCB3ZWlnaHRwPTIga2V5aW50PTI1MCBrZXlpbnRfbWluPTEwIHNjZW5lY3V0PTQwIGludHJhX3JlZnJlc2g9MCByY19sb29rYWhlYWQ9NDAgcmM9Y3JmIG1idHJlZT0xIGNyZj0yMy4wIHFjb21wPTAuNjAgcXBtaW49MCBxcG1heD02OSBxcHN0ZXA9NCBpcF9yYXRpbz0xLjQwIGFxPTE6MS4wMACAAAAAD2WIhAA3//728P4FNjuY0EA==" type="video/mp4">
                    متصفحك لا يدعم تشغيل الفيديو.
                </video>
            `;
            
            log('✅ تم إنشاء مشغل فيديو تجريبي', 'success');
            document.getElementById('video-results').innerHTML = 
                '<div style="color: green">✅ تم إنشاء مشغل فيديو تجريبي بنجاح</div>';
        }

        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.onload = function() {
            log('🚀 تم تحميل صفحة اختبار Video Proxy');
            log('📋 يمكنك الآن اختبار المكونات المختلفة');
        };
    </script>
</body>
</html>
