<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SeriesCache extends Model
{
    use HasFactory;

    protected $table = 'series_cache';

    protected $fillable = [
        'country',
        'series_id',
        'title',
        'description',
        'poster_url',
        'metadata',
        'seasons_data',
        'total_seasons',
        'total_episodes',
        'last_updated',
        'is_complete',
    ];

    protected $casts = [
        'metadata' => 'array',
        'seasons_data' => 'array',
        'total_seasons' => 'integer',
        'total_episodes' => 'integer',
        'last_updated' => 'datetime',
        'is_complete' => 'boolean',
    ];

    /**
     * Get cached series by country and series_id
     */
    public static function getCachedSeries($country, $seriesId)
    {
        return self::where('country', $country)
                   ->where('series_id', $seriesId)
                   ->first();
    }

    /**
     * Cache series data
     */
    public static function cacheSeries($country, $seriesId, $data)
    {
        return self::updateOrCreate(
            [
                'country' => $country,
                'series_id' => $seriesId,
            ],
            [
                'title' => $data['title'] ?? 'Unknown Series',
                'description' => $data['description'] ?? '',
                'poster_url' => $data['poster_url'] ?? null,
                'metadata' => $data['metadata'] ?? [],
                'seasons_data' => $data['seasons_data'] ?? [],
                'total_seasons' => $data['total_seasons'] ?? 0,
                'total_episodes' => $data['total_episodes'] ?? 0,
                'last_updated' => now(),
                'is_complete' => $data['is_complete'] ?? false,
            ]
        );
    }

    /**
     * Get all cached series for a country
     */
    public static function getCachedSeriesByCountry($country)
    {
        return self::where('country', $country)
                   ->orderBy('title')
                   ->get();
    }

    /**
     * Clear cache for a country
     */
    public static function clearCountryCache($country)
    {
        return self::where('country', $country)->delete();
    }

    /**
     * Check if series needs refresh (older than 24 hours)
     */
    public function needsRefresh()
    {
        return $this->last_updated < now()->subHours(24);
    }
}
