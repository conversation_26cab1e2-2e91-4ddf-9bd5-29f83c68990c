<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\ProxyService;

/**
 * Shahid Movies API Service
 * Handles all movie-related functionality
 */
class ShahidMoviesAPI extends ShahidBaseAP<PERSON>
{
    protected $proxyService;

    public function __construct(ProxyService $proxyService = null)
    {
        parent::__construct();
        $this->proxyService = $proxyService ?: app(ProxyService::class);
    }

    /**
     * Override makeRequest to use ProxyService
     */
    protected function makeRequest($method, $url, $options = [])
    {
        try {
            $headers = $this->getHeaders($options['auth'] ?? false);

            // Use ProxyService for HTTP client
            $httpClient = $this->proxyService->getHttpClient([
                'timeout' => 30
            ])->withHeaders($headers);

            switch (strtoupper($method)) {
                case 'GET':
                    $response = $httpClient->get($url, $options['data'] ?? []);
                    break;
                case 'POST':
                    $response = $httpClient->post($url, $options['data'] ?? []);
                    break;
                case 'PUT':
                    $response = $httpClient->put($url, $options['data'] ?? []);
                    break;
                case 'DELETE':
                    $response = $httpClient->delete($url);
                    break;
                default:
                    throw new \Exception('Unsupported HTTP method: ' . $method);
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                $responseBody = $response->body();
                $responseData = $response->json();

                // Handle specific Shahid API errors
                $errorMessage = 'HTTP ' . $response->status();

                if (isset($responseData['faults']) && is_array($responseData['faults'])) {
                    $fault = $responseData['faults'][0];
                    if (isset($fault['code']) && $fault['code'] == 5004) {
                        $errorMessage = 'This content requires Shahid VIP subscription';
                    } else {
                        $errorMessage = $fault['userMessage'] ?? $fault['internalMessage'] ?? $errorMessage;
                    }
                } elseif (isset($responseData['message'])) {
                    $errorMessage = $responseData['message'];
                }

                Log::warning('API request failed', [
                    'url' => $url,
                    'status' => $response->status(),
                    'error' => $errorMessage,
                    'response_body' => $responseBody
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'status_code' => $response->status(),
                    'response_data' => $responseData
                ];
            }

        } catch (\Exception $e) {
            Log::error('HTTP request exception', [
                'url' => $url,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get movies from Shahid (no token required for browsing)
     */
    public function getMovies($country = 'EG', $limit = 50, $offset = 0, $fetchAll = false)
    {
        try {
            // Increase execution time limit
            set_time_limit(120);

            Log::info("Fetching movies for country: {$country}");

            $headers = [
                'accept' => 'application/json, text/plain, */*',
                'accept-language' => 'en',
                'browser_name' => 'CHROME',
                'browser_version' => '131.0.0.0',
                'cache-control' => 'no-cache',
                'language' => 'EN',
                'mparticleid' => '-8068094556643926163',
                'origin' => 'https://shahid.mbc.net',
                'os_version' => 'NT 10.0',
                'pragma' => 'no-cache',
                'priority' => 'u=1, i',
                'profile' => '{"id":"5fd56d50-ada0-11ef-8f21-014b67b685e0","ageRestriction":false,"master":true}',
                'profile-key' => '{"ageRestriction":false,"isAdult":true}',
                'referer' => 'https://shahid.mbc.net/',
                'sec-ch-ua' => '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Windows"',
                'sec-fetch-dest' => 'empty',
                'sec-fetch-mode' => 'cors',
                'sec-fetch-site' => 'cross-site',
                'sec-gpc' => '1',
                'shahid_os' => 'WINDOWS',
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            ];

            // Get movies page carousels
            $moviesPageUrl = "https://api3.shahid.net/proxy/v2.1/editorial/page?request=%7B%22pageAlias%22:%22movies%22,%22profileFolder%22:%22WW%22%7D&country={$country}";

            // Use ProxyService for HTTP client
            $httpClient = $this->proxyService->getHttpClient([
                'timeout' => 60,
                'retry' => [3, 1000]
            ])->withHeaders($headers);

            $response = $httpClient->get($moviesPageUrl);

            if (!$response->successful()) {
                Log::error("Failed to get movies page for {$country}: " . $response->status());
                return ['error' => 'Failed to fetch movies page'];
            }

            $pageData = $response->json();
            $moviesLinks = [];

            // Extract carousel links
            $carousels = $pageData['carousels'] ?? [];
            if (empty($carousels)) {
                Log::warning("No carousels found for {$country}");
                return ['movies' => [], 'total' => 0];
            }

            // Limit carousels to process (to avoid timeout)
            if ($fetchAll) {
                Log::info("Fetching ALL movies - processing all carousels");
                $carouselsToProcess = $carousels; // Process all carousels
            } else {
                $maxCarousels = 5; // Process only first 5 carousels for faster response
                $carouselsToProcess = array_slice($carousels, 0, $maxCarousels);
                Log::info("Fetching limited movies - processing {$maxCarousels} carousels");
            }

            foreach ($carouselsToProcess as $carousel) {
                try {
                    $encoded = str_replace('/', '%2F', $carousel['id']);
                    $carouselUrl = "https://api3.shahid.net/proxy/v2.1/editorial/carousel?request=%7B%22displayedItems%22:0,%22id%22:%22{$encoded}%22,%22itemsRequestedStatic%22:true,%22pageNumber%22:0,%22pageSize%22:999,%22totalItems%22:999%7D&country={$country}";
                    $moviesLinks[] = $carouselUrl;
                } catch (\Exception $e) {
                    Log::error("Error processing carousel for {$country}: " . $e->getMessage());
                    continue;
                }
            }

            Log::info("Found " . count($moviesLinks) . " movie carousels for {$country}");

            $allMovies = [];

            // Process each carousel
            foreach ($moviesLinks as $url) {
                try {
                    Log::info("Processing movies carousel URL: {$url}");
                    $carouselResponse = $httpClient->get($url);

                    if (!$carouselResponse->successful()) {
                        if ($carouselResponse->status() === 404) {
                            Log::info("Movies carousel not found (404), skipping: {$url}");
                        } else {
                            Log::warning("Failed to get carousel: " . $carouselResponse->status());
                        }
                        continue;
                    }

                    $items = $carouselResponse->json()['editorialItems'] ?? [];
                    Log::info("Found " . count($items) . " items in carousel");

                    foreach ($items as $item) {
                        try {
                            $movieData = $item['item'] ?? [];

                            // Extract movie details
                            $title = $movieData['title'] ?? 'Unknown Title';

                            // Get movie URL
                            $movieUrl = "";
                            foreach ($movieData['productUrls'] ?? [] as $productUrl) {
                                if (strpos($productUrl['url'] ?? '', '/en/player/') !== false) {
                                    $movieUrl = $productUrl['url'];
                                    break;
                                }
                            }

                            if (empty($movieUrl)) {
                                continue;
                            }

                            // Extract poster image
                            $posterUrl = $this->extractPosterUrl($movieData);

                            // Extract year and duration
                            $year = null;
                            $duration = null;

                            if (isset($movieData['releaseDate'])) {
                                $year = substr($movieData['releaseDate'], 0, 4);
                            }

                            if (isset($movieData['runtime'])) {
                                $duration = $movieData['runtime'];
                            } elseif (isset($movieData['duration'])) {
                                $duration = $movieData['duration'];
                            }

                            $movieInfo = [
                                'id' => $movieData['id'] ?? null,
                                'title' => $title,
                                'poster_url' => $posterUrl,
                                'movie_url' => $movieUrl,
                                'year' => $year,
                                'duration' => $duration,
                                'country' => $country
                            ];

                            $allMovies[] = $movieInfo;

                        } catch (\Exception $e) {
                            Log::warning("Error processing movie item: " . $e->getMessage());
                            continue;
                        }
                    }

                } catch (\Exception $e) {
                    Log::warning("Error processing carousel: " . $e->getMessage());
                    continue;
                }
            }

            // Remove duplicates based on movie URL
            $uniqueMovies = [];
            $seenUrls = [];

            foreach ($allMovies as $movie) {
                if (!in_array($movie['movie_url'], $seenUrls)) {
                    $uniqueMovies[] = $movie;
                    $seenUrls[] = $movie['movie_url'];
                }
            }

            // Apply limit and offset only if not fetching all
            $total = count($uniqueMovies);

            if ($fetchAll) {
                $movies = $uniqueMovies; // Return all movies when fetchAll is true
                Log::info("{$country}: Total {$total} unique movies found, returning ALL {$total} movies");
            } else {
                $movies = array_slice($uniqueMovies, $offset, $limit);
                Log::info("{$country}: Total {$total} unique movies found, returning " . count($movies));
            }

            return [
                'movies' => $movies,
                'total' => $total,
                'country' => $country
            ];

        } catch (\Exception $e) {
            Log::error("Error in getMovies: " . $e->getMessage());
            return ['error' => 'Failed to fetch movies: ' . $e->getMessage()];
        }
    }

    /**
     * Get movie playout URL
     */
    public function getMoviePlayoutUrl($movieId, $country = 'EG')
    {
        Log::info('=== MOVIE PLAYOUT DEBUG ===', [
            'movie_id' => $movieId,
            'has_token' => !empty($this->token),
            'token_length' => $this->token ? strlen($this->token) : 0
        ]);

        if (!$this->hasValidToken()) {
            Log::error('No valid token found for movie playout');
            return ['error' => 'No valid token found'];
        }

        $url = $this->baseUrl . "/proxy/v2.1/playout/new/url/{$movieId}?outputParameter=vmap&country={$country}";
        $result = $this->makeRequest('GET', $url, ['auth' => true]);

        if ($result['success']) {
            return $result['data'];
        } else {
            return ['error' => $result['error'] ?? 'Failed to get movie playout'];
        }
    }

    /**
     * Extract poster URL from movie data
     */
    private function extractPosterUrl($movieData)
    {
        $posterUrl = null;

        if (isset($movieData['image']) && $movieData['image']) {
            $imageData = $movieData['image'];

            // Try different image types in order of preference
            $imageTypes = ['posterImage', 'posterClean', 'posterHero', 'thumbnailImage', 'heroSliderImage', 'landscapeClean'];
            foreach ($imageTypes as $imageType) {
                if (isset($imageData[$imageType]) && $imageData[$imageType]) {
                    $posterUrl = $imageData[$imageType];
                    break;
                }
            }
        }

        // Fallback: try direct posterImage field
        if (!$posterUrl && isset($movieData['posterImage']) && $movieData['posterImage']) {
            $posterUrl = $movieData['posterImage'];
        }

        // Fallback: try thumbnailImage field
        if (!$posterUrl && isset($movieData['thumbnailImage']) && $movieData['thumbnailImage']) {
            $posterUrl = $movieData['thumbnailImage'];
        }

        // Clean and optimize poster URL
        if ($posterUrl) {
            // Replace template variables with actual values
            if (strpos($posterUrl, '{height}') !== false || strpos($posterUrl, '{width}') !== false) {
                $posterUrl = str_replace(['{height}', '{width}', '{croppingPoint}'], ['600', '400', 'center'], $posterUrl);
            }

            // Clean URL by removing unwanted parameters
            $posterUrl = $this->cleanPosterUrl($posterUrl);

            // Additional optimization for mediaObject URLs
            if (strpos($posterUrl, 'mediaObject/') !== false && strpos($posterUrl, '?') === false) {
                $posterUrl .= "?width=400&height=600";
            }
        }

        return $posterUrl;
    }

    /**
     * Clean poster URL
     */
    private function cleanPosterUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // Remove unwanted parameters but keep essential ones
        $parsedUrl = parse_url($url);
        if (!$parsedUrl) {
            return $url;
        }

        $cleanUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }

        // Add clean parameters for image optimization
        if (strpos($url, 'mediaObject/') !== false) {
            $cleanUrl .= '?width=400&height=600';
        }

        return $cleanUrl;
    }

    /**
     * Get movie details
     */
    public function getMovieDetails($movieId)
    {
        try {
            Log::info("🎬 Getting movie details for: {$movieId}");

            if (!$this->hasValidToken()) {
                return ['success' => false, 'error' => 'No valid token found'];
            }

            // Use api3.shahid.net like the series method
            $url = "https://api3.shahid.net/proxy/v2.1/product/id";
            $params = [
                'request' => '{"id":"' . $movieId . '"}',
                'country' => 'EG'
            ];

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid' => 'web'
            ];

            Log::info("=== GETTING MOVIE DETAILS ===", [
                'movie_id' => $movieId,
                'url' => $url
            ]);

            $httpClient = $this->proxyService->getHttpClient(['timeout' => 30])->withHeaders($headers);
            $response = $httpClient->get($url, $params);

            if (!$response->successful()) {
                Log::error("Movie details API failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return ['success' => false, 'error' => 'API request failed'];
            }

            $data = $response->json();

            if (empty($data)) {
                Log::error("Empty response from movie details API");
                return ['success' => false, 'error' => 'Empty response'];
            }

            // Extract movie information
            $movieInfo = [
                'title' => $data['title'] ?? 'Unknown Movie',
                'description' => $data['description'] ?? '',
                'poster_url' => $this->extractPosterUrl($data),
                'duration' => $data['duration'] ?? null,
                'year' => $data['year'] ?? null,
                'metadata' => [
                    'genres' => $data['genres'] ?? [],
                    'cast' => $data['cast'] ?? [],
                    'director' => $data['director'] ?? null,
                    'rating' => $data['rating'] ?? null,
                ]
            ];

            Log::info("✅ Movie details retrieved successfully", [
                'title' => $movieInfo['title'],
                'duration' => $movieInfo['duration']
            ]);

            return [
                'success' => true,
                'data' => $data,
                'movie_info' => $movieInfo
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error getting movie details: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get movie stream data (MPD URL and PSSH)
     */
    public function getMovieStreamData($movieId)
    {
        try {
            Log::info("🎬 Getting movie stream data for: {$movieId}");

            if (!$this->hasValidToken()) {
                return ['success' => false, 'error' => 'No valid token found'];
            }

            // Use the stream API endpoint
            $url = "https://api2.shahid.net/proxy/v2.1/product/link";
            $params = [
                'request' => json_encode([
                    'id' => $movieId,
                    'type' => 'movie',
                    'drm' => true
                ]),
                'country' => 'EG'
            ];

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid' => 'web'
            ];

            Log::info("=== GETTING MOVIE STREAM DATA ===", [
                'movie_id' => $movieId,
                'url' => $url
            ]);

            $httpClient = $this->proxyService->getHttpClient(['timeout' => 30])->withHeaders($headers);
            $response = $httpClient->get($url, $params);

            if (!$response->successful()) {
                Log::error("Movie stream API failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return ['success' => false, 'error' => 'Stream API request failed'];
            }

            $data = $response->json();

            if (empty($data)) {
                Log::error("Empty response from movie stream API");
                return ['success' => false, 'error' => 'Empty stream response'];
            }

            // Extract stream URL and PSSH
            $streamUrl = null;
            $pssh = null;

            // Look for stream data in various possible locations
            if (isset($data['url'])) {
                $streamUrl = $data['url'];
            } elseif (isset($data['link'])) {
                $streamUrl = $data['link'];
            } elseif (isset($data['stream_url'])) {
                $streamUrl = $data['stream_url'];
            }

            // Look for PSSH
            if (isset($data['pssh'])) {
                $pssh = $data['pssh'];
            } elseif (isset($data['drm']['pssh'])) {
                $pssh = $data['drm']['pssh'];
            }

            if (!$streamUrl) {
                Log::error("No stream URL found in response", ['data' => $data]);
                return ['success' => false, 'error' => 'No stream URL found'];
            }

            Log::info("✅ Movie stream data retrieved successfully");
            Log::info("Stream URL: " . substr($streamUrl, 0, 100) . "...");
            if ($pssh) {
                Log::info("PSSH: " . substr($pssh, 0, 50) . "...");
            } else {
                Log::info("No PSSH in response, will extract from MPD");
            }

            return [
                'success' => true,
                'stream_url' => $streamUrl,
                'pssh' => $pssh,
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error getting movie stream data: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
