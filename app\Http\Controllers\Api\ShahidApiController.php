<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ShahidAPI;
use App\Services\ShahidMoviesAPI;
use App\Services\ShahidSeriesAPI;
use App\Services\ShahidChannelsAPI;
use App\Services\ShahidExtractor;
use App\Services\ShahidDRM;

class ShahidApiController extends Controller
{
    private $moviesAPI;
    private $seriesAPI;
    private $channelsAPI;

    public function __construct()
    {
        // No authentication required for public browsing
        $this->moviesAPI = new ShahidMoviesAPI();
        $this->seriesAPI = new ShahidSeriesAPI();
        $this->channelsAPI = new ShahidChannelsAPI();
    }

    /**
     * Get movies from Shahid
     */
    public function getMovies(Request $request)
    {
        try {
            $country = $request->get('country', 'EG');
            $limit = $request->get('limit', 50);
            $offset = $request->get('offset', 0);
            $fetchAll = $request->get('fetch_all', false);

            // Convert string 'true'/'false' to boolean
            if (is_string($fetchAll)) {
                $fetchAll = filter_var($fetchAll, FILTER_VALIDATE_BOOLEAN);
            }

            $result = $this->moviesAPI->getMovies($country, $limit, $offset, $fetchAll);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['movies'] ?? [],
                'total_movies' => $result['total'] ?? 0,
                'country' => $country,
                'limit' => $limit,
                'offset' => $offset
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching movies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get series from Shahid
     */
    public function getSeries(Request $request)
    {
        try {
            $country = $request->get('country', 'EG');
            $limit = $request->get('limit', 50);
            $offset = $request->get('offset', 0);
            $fetchAll = $request->get('fetch_all', false);

            // Convert string 'true'/'false' to boolean
            if (is_string($fetchAll)) {
                $fetchAll = filter_var($fetchAll, FILTER_VALIDATE_BOOLEAN);
            }

            $result = $this->seriesAPI->getSeries($country, $limit, $offset, $fetchAll);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['series'] ?? [],
                'total_series' => $result['total'] ?? 0,
                'country' => $country,
                'limit' => $limit,
                'offset' => $offset,
                'fetch_all' => $fetchAll
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching series: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search content in Shahid
     */
    public function searchContent(Request $request)
    {
        try {
            $query = $request->get('query', '');
            $limit = $request->get('limit', 20);
            $type = $request->get('type', null); // 'SERIES', 'MOVIE', etc.

            if (empty($query)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search query is required'
                ], 400);
            }

            $result = $this->seriesAPI->searchContent($query, $limit, $type);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['data'] ?? [],
                'total_results' => count($result['data'] ?? []),
                'query' => $query,
                'type' => $type
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get live channels from Shahid
     * @deprecated Use ShahidChannelsController instead
     */
    public function getLiveChannels(Request $request)
    {
        try {
            $forceRefresh = $request->boolean('refresh', false);
            $result = $this->channelsAPI->getCachedLiveChannels($forceRefresh);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result['channels'] ?? [],
                'total_channels' => $result['total'] ?? 0,
                'cached' => !$forceRefresh
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching live channels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get content details
     */
    public function getContentDetails(Request $request)
    {
        $request->validate([
            'content_id' => 'required|string'
        ]);

        try {
            $contentId = $request->get('content_id');
            $result = $this->seriesAPI->getContentDetails($contentId);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting content details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extract DRM data for a movie
     */
    public function extractMovieDrm(Request $request)
    {
        $request->validate([
            'movie_id' => 'required|string',
            'movie_title' => 'string'
        ]);

        try {
            $movieId = $request->get('movie_id');
            $movieTitle = $request->get('movie_title', 'Unknown Movie');

            // Initialize services
            $extractor = new ShahidExtractor($this->moviesAPI);
            $drm = new ShahidDRM();

            // Extract movie stream
            $streamResult = $extractor->extractMovieStream($movieId);

            if (!$streamResult || !$streamResult['success']) {
                $statusCode = 400;
                $message = $streamResult['error'] ?? 'Failed to extract movie stream';

                // If it requires VIP, use a different status code
                if (isset($streamResult['requires_vip']) && $streamResult['requires_vip']) {
                    $statusCode = 402; // Payment Required
                    $message = 'This movie requires Shahid VIP subscription. Please upgrade your account to access premium content.';
                }

                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'requires_vip' => $streamResult['requires_vip'] ?? false
                ], $statusCode);
            }

            // Get MPD URL from streams
            $mpdUrl = null;
            foreach ($streamResult['streams'] as $stream) {
                if ($stream['type'] === 'dash') {
                    $mpdUrl = $stream['url'];
                    break;
                }
            }

            if (!$mpdUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'No DASH stream found for this movie'
                ], 400);
            }

            // Get DRM info with PSSH and KID
            $drmInfo = $streamResult['drm_info'] ?? [];
            $pssh = $drmInfo['pssh'] ?? null;
            $kid = $drmInfo['kid'] ?? null;
            $licenseUrl = $drmInfo['license_url'] ?? null;

            // Try to extract key if PSSH is available
            $key = null;
            $keyExtractionError = null;

            if ($pssh && $drm->hasDeviceFile()) {
                $keyResult = $drm->extractKey($pssh, $licenseUrl);

                if ($keyResult['success']) {
                    // Extract the first content key if available
                    $keys = $keyResult['keys'] ?? [];
                    foreach ($keys as $keyInfo) {
                        if ($keyInfo['type'] === 'CONTENT' && $keyInfo['key']) {
                            $key = $keyInfo['key'];
                            // Use the KID from the key extraction result (from Python API)
                            // This is the correct KID format that matches the key
                            if (isset($keyInfo['kid'])) {
                                $kid = $keyInfo['kid'];
                            }
                            break;
                        }
                    }
                } else {
                    $keyExtractionError = $keyResult['error'] ?? 'Key extraction failed';
                }
            }

            // Build formatted key using the correct KID (after key extraction)
            $formattedKey = null;
            if ($kid && $key) {
                $formattedKey = $kid . ':' . $key;
            } elseif ($kid) {
                $formattedKey = $kid . ':KEY_NEEDED';
            }

            $responseData = [
                'movie_id' => $movieId,
                'movie_title' => $movieTitle,
                'stream_url' => $mpdUrl,
                'stream_type' => 'dash',
                'pssh' => $pssh,
                'kid' => $kid,
                'key' => $key,
                'formatted_key' => $formattedKey,
                'license_url' => $licenseUrl,
                'drm_supported' => $drm->hasDeviceFile()
            ];

            if ($keyExtractionError) {
                $responseData['key_extraction_error'] = $keyExtractionError;
            }

            return response()->json([
                'success' => true,
                'data' => $responseData,
                'message' => 'Stream information extracted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error extracting movie DRM: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extract DRM data for an episode
     */
    public function extractEpisodeDrm(Request $request)
    {
        $request->validate([
            'episode_id' => 'required|string',
            'country' => 'string'
        ]);

        try {
            $episodeId = $request->get('episode_id');
            $country = $request->get('country', 'SA');

            // Initialize services
            $extractor = new ShahidExtractor($this->seriesAPI);
            $drm = new ShahidDRM();

            // Extract episode stream
            $streamResult = $extractor->extractEpisodeStream($episodeId);

            if (!$streamResult || !$streamResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $streamResult['error'] ?? 'Failed to extract episode stream'
                ], 400);
            }

            // Get MPD URL from streams
            $mpdUrl = null;
            foreach ($streamResult['streams'] as $stream) {
                if ($stream['type'] === 'dash') {
                    $mpdUrl = $stream['url'];
                    break;
                }
            }

            if (!$mpdUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'No DASH stream found for this episode'
                ], 400);
            }

            // Extract PSSH from MPD
            $pssh = $drm->extractPSSH($mpdUrl);

            // For now, return basic info (KID and Key would need additional DRM processing)
            return response()->json([
                'success' => true,
                'data' => [
                    'episode_id' => $episodeId,
                    'stream_url' => $mpdUrl,
                    'stream_type' => 'dash',
                    'pssh' => $pssh,
                    'kid' => null, // Would need DRM key extraction
                    'key' => null, // Would need DRM key extraction
                    'formatted_key' => null,
                    'license_url' => $streamResult['drm_info']['license_url'] ?? null
                ],
                'message' => 'Stream information extracted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error extracting episode DRM: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save Shahid token
     */
    public function saveToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        try {
            $token = trim($request->get('token'));

            // Use ShahidSeriesAPI service to save token
            $saved = $this->seriesAPI->saveToken($token);

            if ($saved) {
                return response()->json([
                    'success' => true,
                    'message' => 'Token saved successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save token'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving token: ' . $e->getMessage()
            ], 500);
        }
    }

}
