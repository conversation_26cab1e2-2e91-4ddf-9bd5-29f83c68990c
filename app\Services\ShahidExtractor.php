<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Shahid Extractor Service - Laravel Port of Python shahid_extractor.py
 * This service handles content extraction from Shahid service
 */
class ShahidExtractor
{
    private $api;
    private $drm;

    public function __construct($api = null, ShahidDRM $drm = null)
    {
        // Accept any API class that extends ShahidBaseAPI or the old ShahidAPI
        if ($api === null) {
            $this->api = new ShahidAPI();
        } else {
            $this->api = $api;
        }
        $this->drm = $drm ?: new ShahidDRM();
    }

    /**
     * Extract stream information from content ID or URL
     */
    public function extractStreamInfo($contentIdOrUrl)
    {
        try {
            // Extract content ID if URL is provided
            $contentId = $this->api->extractContentId($contentIdOrUrl);
            
            // Get content details first
            $contentDetails = $this->api->getContentDetails($contentId);
            
            if (isset($contentDetails['error'])) {
                return [
                    'success' => false,
                    'error' => $contentDetails['error']
                ];
            }

            $contentType = $contentDetails['type'] ?? 'unknown';

            switch ($contentType) {
                case 'movie':
                    return $this->extractMovieStream($contentId);
                case 'series':
                    return $this->extractSeriesInfo($contentId);
                case 'episode':
                    return $this->extractEpisodeStream($contentId);
                default:
                    return [
                        'success' => false,
                        'error' => 'Unknown content type: ' . $contentType
                    ];
            }

        } catch (\Exception $e) {
            Log::error('Stream extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract movie stream information
     */
    public function extractMovieStream($movieId)
    {
        try {
            // Get playout data from API
            $playoutData = $this->api->getMoviePlayoutUrl($movieId);

            if (isset($playoutData['error'])) {
                return [
                    'success' => false,
                    'error' => $playoutData['error'],
                    'requires_vip' => $playoutData['requires_vip'] ?? false
                ];
            }

            // Extract stream URLs from playout data
            $streams = $this->parsePlayoutData($playoutData);

            // Get DRM info and extract additional data
            $drmInfo = $this->drm->getDRMInfo($movieId);

            // Extract PSSH and KID from MPD if available
            $mpdUrl = null;
            foreach ($streams as $stream) {
                if ($stream['type'] === 'dash') {
                    $mpdUrl = $stream['url'];
                    break;
                }
            }

            if ($mpdUrl) {
                $pssh = $this->drm->extractPSSH($mpdUrl);
                if ($pssh) {
                    $drmInfo['pssh'] = $pssh;
                    $drmInfo['kid'] = $this->drm->extractKIDFromPSSH($pssh);
                }
            }

            return [
                'success' => true,
                'content_type' => 'movie',
                'content_id' => $movieId,
                'streams' => $streams,
                'drm_info' => $drmInfo
            ];

        } catch (\Exception $e) {
            Log::error('Movie stream extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract series information
     */
    public function extractSeriesInfo($seriesId)
    {
        try {
            $contentDetails = $this->api->getContentDetails($seriesId);
            
            if (isset($contentDetails['error'])) {
                return [
                    'success' => false,
                    'error' => $contentDetails['error']
                ];
            }

            // Get episodes list
            $episodes = $contentDetails['episodes'] ?? [];
            
            return [
                'success' => true,
                'content_type' => 'series',
                'content_id' => $seriesId,
                'title' => $contentDetails['title'] ?? 'Unknown Series',
                'description' => $contentDetails['description'] ?? '',
                'episodes' => $episodes,
                'total_episodes' => count($episodes)
            ];

        } catch (\Exception $e) {
            Log::error('Series extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract episode stream information
     */
    public function extractEpisodeStream($episodeId)
    {
        try {
            // Get playout data for episode
            $playoutData = $this->api->getEpisodePlayoutUrl($episodeId);

            if (isset($playoutData['error'])) {
                return [
                    'success' => false,
                    'error' => $playoutData['error']
                ];
            }

            // Extract stream URLs from playout data
            $streams = $this->parsePlayoutData($playoutData);

            return [
                'success' => true,
                'content_type' => 'episode',
                'content_id' => $episodeId,
                'streams' => $streams,
                'drm_info' => $this->drm->getDRMInfo($episodeId)
            ];

        } catch (\Exception $e) {
            Log::error('Episode stream extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse stream data from API response
     */
    private function parseStreamData($streamingInfo)
    {
        $streams = [];

        try {
            // Extract MPD URL
            if (isset($streamingInfo['mpd_url'])) {
                $streams[] = [
                    'type' => 'dash',
                    'url' => $streamingInfo['mpd_url'],
                    'quality' => 'adaptive',
                    'drm_protected' => true
                ];
            }

            // Extract HLS URL
            if (isset($streamingInfo['hls_url'])) {
                $streams[] = [
                    'type' => 'hls',
                    'url' => $streamingInfo['hls_url'],
                    'quality' => 'adaptive',
                    'drm_protected' => false
                ];
            }

            // Extract direct URLs if available
            if (isset($streamingInfo['direct_urls'])) {
                foreach ($streamingInfo['direct_urls'] as $quality => $url) {
                    $streams[] = [
                        'type' => 'direct',
                        'url' => $url,
                        'quality' => $quality,
                        'drm_protected' => false
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error('Stream data parsing error: ' . $e->getMessage());
        }

        return $streams;
    }

    /**
     * Parse playout data from Shahid API response
     */
    private function parsePlayoutData($playoutData)
    {
        $streams = [];

        try {
            // Extract MPD URL from playout data
            if (isset($playoutData['playout']['url'])) {
                $mpdUrl = $playoutData['playout']['url'];

                $streams[] = [
                    'type' => 'dash',
                    'url' => $mpdUrl,
                    'quality' => 'adaptive',
                    'drm_protected' => $playoutData['playout']['drm'] ?? true
                ];
            }

            // Check for alternative URLs
            if (isset($playoutData['alternatives'])) {
                foreach ($playoutData['alternatives'] as $alt) {
                    if (isset($alt['url'])) {
                        $type = 'dash';
                        if (strpos($alt['url'], '.m3u8') !== false) {
                            $type = 'hls';
                        }

                        $streams[] = [
                            'type' => $type,
                            'url' => $alt['url'],
                            'quality' => $alt['quality'] ?? 'adaptive',
                            'drm_protected' => $alt['drm'] ?? false
                        ];
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Playout data parsing error: ' . $e->getMessage());
        }

        return $streams;
    }

    /**
     * Extract content ID from various URL formats
     */
    public function extractContentId($input)
    {
        return $this->api->extractContentId($input);
    }

    /**
     * Get supported stream formats
     */
    public function getSupportedFormats()
    {
        return [
            'dash' => 'MPEG-DASH (with DRM)',
            'hls' => 'HTTP Live Streaming',
            'direct' => 'Direct MP4 URLs'
        ];
    }

    /**
     * Check if content is DRM protected
     */
    public function isDRMProtected($contentId)
    {
        try {
            $streamingInfo = $this->api->getStreamingInfo($contentId);
            return isset($streamingInfo['drm_info']) && !empty($streamingInfo['drm_info']);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get content metadata
     */
    public function getContentMetadata($contentId)
    {
        try {
            $details = $this->api->getContentDetails($contentId);
            
            if (isset($details['error'])) {
                return null;
            }

            return [
                'title' => $details['title'] ?? 'Unknown',
                'description' => $details['description'] ?? '',
                'duration' => $details['duration'] ?? 0,
                'poster' => $details['poster_url'] ?? '',
                'thumbnail' => $details['thumbnail_url'] ?? '',
                'genre' => $details['genre'] ?? [],
                'cast' => $details['cast'] ?? [],
                'release_date' => $details['release_date'] ?? '',
                'rating' => $details['rating'] ?? 0
            ];

        } catch (\Exception $e) {
            Log::error('Metadata extraction error: ' . $e->getMessage());
            return null;
        }
    }
}
