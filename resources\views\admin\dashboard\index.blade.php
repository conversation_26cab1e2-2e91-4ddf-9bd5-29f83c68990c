@extends('admin.layouts.app')

@section('title', 'Admin Dashboard')
@section('page-title', 'Dashboard')

@section('breadcrumb')
<li class="breadcrumb-item active">Dashboard</li>
@endsection

@section('content')
<!-- Welcome Message -->
<div class="alert alert-info border-0 mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
    <div class="d-flex align-items-center">
        <i class="fas fa-crown fa-2x me-3"></i>
        <div>
            <h5 class="mb-1">Welcome back, {{ Auth::guard('admin')->user()->name ?? 'Admin' }}!</h5>
            <p class="mb-0 opacity-75">Here's what's happening with your Shahid Play platform today.</p>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="small opacity-75">Total Users</div>
                        <div class="h3 mb-0" id="totalUsers">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-credit-card fa-2x opacity-75"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="small opacity-75">Active Subscriptions</div>
                        <div class="h3 mb-0" id="activeSubscriptions">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="small opacity-75">Expiring Soon</div>
                        <div class="h3 mb-0" id="expiringSoon">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="small opacity-75">Revenue This Month</div>
                        <div class="h3 mb-0" id="monthlyRevenue">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <!-- Subscription Trends Chart -->
    <div class="col-xl-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" style="color: #333; font-weight: 600;">
                    <i class="fas fa-chart-line me-2" style="color: #667eea;"></i>
                    Subscription Trends (Last 30 Days)
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" data-period="30">30 Days</button>
                    <button type="button" class="btn btn-outline-primary" data-period="90">90 Days</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="subscriptionTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Top Plans -->
    <div class="col-xl-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0" style="color: #333; font-weight: 600;">
                    <i class="fas fa-trophy me-2" style="color: #ffc107;"></i>
                    Top Subscription Plans
                </h5>
            </div>
            <div class="card-body">
                <div id="topPlans">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activities and Alerts -->
<div class="row mb-4">
    <!-- Recent Activities -->
    <div class="col-xl-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0" style="color: #333; font-weight: 600;">
                    <i class="fas fa-clock me-2" style="color: #17a2b8;"></i>
                    Recent Activities
                </h5>
            </div>
            <div class="card-body">
                <div id="recentActivities">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Expiring Subscriptions Alert -->
    <div class="col-xl-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0" style="color: #333; font-weight: 600;">
                    <i class="fas fa-bell me-2" style="color: #dc3545;"></i>
                    Expiring Subscriptions
                </h5>
            </div>
            <div class="card-body">
                <div id="expiringSubscriptions">
                    <div class="text-center py-4">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Chart -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0" style="color: #333; font-weight: 600;">
                    <i class="fas fa-chart-bar me-2" style="color: #28a745;"></i>
                    Revenue Trends (Last 12 Months)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="revenueTrendsChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0" style="color: #333; font-weight: 600;">
                    <i class="fas fa-bolt me-2" style="color: #ffc107;"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-user-plus fa-2x mb-2 d-block"></i>
                            <div>Add New User</div>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.subscription-plans.index') }}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                            <div>Create Plan</div>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.user-subscriptions.index') }}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-credit-card fa-2x mb-2 d-block"></i>
                            <div>Manage Subscriptions</div>
                        </a>
                    </div>
                </div>

                @if(auth('admin')->user()->isSuperAdmin())
                <div class="row mt-3">
                    <div class="col-md-4 mb-3">
                        <a href="{{ route('admin.cache.management') }}" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-broom fa-2x mb-2 d-block"></i>
                            <div>Cache Management</div>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-danger w-100 py-3" onclick="quickClearCache()">
                            <i class="fas fa-trash-alt fa-2x mb-2 d-block"></i>
                            <div>Quick Clear All Cache</div>
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-primary w-100 py-3" onclick="quickClearAppCache()">
                            <i class="fas fa-sync fa-2x mb-2 d-block"></i>
                            <div>Clear App Cache</div>
                        </button>
                    </div>
                </div>
                @endif

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.reports.index') }}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-line fa-2x mb-2 d-block"></i>
                            <div>View Reports</div>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary w-100 py-3">
                            <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                            <div>System Settings</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Load dashboard data
    loadDashboardStatistics();
    loadSubscriptionTrends();
    loadRevenueTrends();
    loadTopPlans();
    loadRecentActivities();
    loadExpiringSubscriptions();

    // Auto-refresh every 5 minutes
    setInterval(function() {
        loadDashboardStatistics();
        loadRecentActivities();
        loadExpiringSubscriptions();
    }, 300000);
});

function loadDashboardStatistics() {
    $.get('/admin/api/dashboard/statistics')
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#totalUsers').text(data.users.total.toLocaleString());
                $('#activeSubscriptions').text(data.subscriptions.active.toLocaleString());
                $('#expiringSoon').text(data.subscriptions.expiring_soon.toLocaleString());
                $('#monthlyRevenue').text('$' + data.revenue.this_month.toLocaleString());
            }
        })
        .fail(function() {
            console.error('Failed to load dashboard statistics');
        });
}

function loadSubscriptionTrends() {
    $.get('/admin/api/dashboard/subscription-trends')
        .done(function(response) {
            if (response.success) {
                renderSubscriptionTrendsChart(response.data);
            }
        })
        .fail(function() {
            console.error('Failed to load subscription trends');
        });
}

function loadRevenueTrends() {
    $.get('/admin/api/dashboard/revenue-trends')
        .done(function(response) {
            if (response.success) {
                renderRevenueTrendsChart(response.data);
            }
        })
        .fail(function() {
            console.error('Failed to load revenue trends');
        });
}

function loadTopPlans() {
    $.get('/admin/api/dashboard/top-plans')
        .done(function(response) {
            if (response.success) {
                renderTopPlans(response.data);
            }
        })
        .fail(function() {
            $('#topPlans').html('<div class="text-danger">Failed to load top plans</div>');
        });
}

function loadRecentActivities() {
    $.get('/admin/api/dashboard/recent-activities')
        .done(function(response) {
            if (response.success) {
                renderRecentActivities(response.data);
            }
        })
        .fail(function() {
            $('#recentActivities').html('<div class="text-danger">Failed to load recent activities</div>');
        });
}

function loadExpiringSubscriptions() {
    $.get('/admin/api/dashboard/expiring-subscriptions')
        .done(function(response) {
            if (response.success) {
                renderExpiringSubscriptions(response.data);
            }
        })
        .fail(function() {
            $('#expiringSubscriptions').html('<div class="text-danger">Failed to load expiring subscriptions</div>');
        });
}

function renderSubscriptionTrendsChart(data) {
    const ctx = document.getElementById('subscriptionTrendsChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => new Date(item.date).toLocaleDateString()),
            datasets: [{
                label: 'New Subscriptions',
                data: data.map(item => item.count),
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function renderRevenueTrendsChart(data) {
    const ctx = document.getElementById('revenueTrendsChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => new Date(item.month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' })),
            datasets: [{
                label: 'Revenue ($)',
                data: data.map(item => item.revenue),
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgb(102, 126, 234)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

function renderTopPlans(data) {
    let html = '';
    if (data.length === 0) {
        html = '<div class="text-muted">No subscription plans found</div>';
    } else {
        data.forEach((plan, index) => {
            html += `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-bold">${plan.name}</div>
                        <div class="text-muted small">${plan.price}</div>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">${plan.subscribers}</div>
                        <div class="text-muted small">subscribers</div>
                    </div>
                </div>
                ${index < data.length - 1 ? '<hr>' : ''}
            `;
        });
    }
    $('#topPlans').html(html);
}

function renderRecentActivities(data) {
    let html = '';
    if (data.length === 0) {
        html = '<div class="text-muted">No recent activities</div>';
    } else {
        data.forEach((activity, index) => {
            const icon = activity.type === 'subscription_created' ? 'fa-credit-card text-success' : 'fa-user text-primary';
            html += `
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="fas ${icon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="small">${activity.message}</div>
                        <div class="text-muted small">${new Date(activity.created_at).toLocaleString()}</div>
                    </div>
                </div>
                ${index < data.length - 1 ? '<hr>' : ''}
            `;
        });
    }
    $('#recentActivities').html(html);
}

function renderExpiringSubscriptions(data) {
    let html = '';
    if (data.length === 0) {
        html = '<div class="text-success"><i class="fas fa-check-circle me-2"></i>No subscriptions expiring soon</div>';
    } else {
        data.forEach((subscription, index) => {
            const urgency = subscription.remaining_days <= 3 ? 'text-danger' : 'text-warning';
            html += `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-bold">${subscription.user_name}</div>
                        <div class="text-muted small">${subscription.plan_name}</div>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold ${urgency}">${subscription.remaining_days} days</div>
                        <div class="text-muted small">remaining</div>
                    </div>
                </div>
                ${index < data.length - 1 ? '<hr>' : ''}
            `;
        });
    }
    $('#expiringSubscriptions').html(html);
}

// Quick cache clearing functions
function quickClearCache() {
    if (!confirm('Are you sure you want to clear ALL cache? This will clear application, database, files, series, and movies cache.')) {
        return;
    }

    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin fa-2x mb-2 d-block"></i><div>Clearing...</div>';

    $.ajax({
        url: '{{ route("admin.cache.clear") }}',
        method: 'POST',
        data: { type: 'all' },
        success: function(response) {
            if (response.success) {
                showDashboardAlert('success', '✅ All cache cleared successfully!');
                // Refresh page after 2 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showDashboardAlert('danger', '❌ Failed to clear cache: ' + response.message);
            }
        },
        error: function(xhr) {
            showDashboardAlert('danger', '❌ Error clearing cache: ' + (xhr.responseJSON?.message || 'Unknown error'));
        },
        complete: function() {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    });
}

function quickClearAppCache() {
    if (!confirm('Are you sure you want to clear application cache? This will clear Laravel cache, config, routes, and views.')) {
        return;
    }

    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin fa-2x mb-2 d-block"></i><div>Clearing...</div>';

    $.ajax({
        url: '{{ route("admin.cache.clear") }}',
        method: 'POST',
        data: { type: 'application' },
        success: function(response) {
            if (response.success) {
                showDashboardAlert('success', '✅ Application cache cleared successfully!');
            } else {
                showDashboardAlert('danger', '❌ Failed to clear application cache: ' + response.message);
            }
        },
        error: function(xhr) {
            showDashboardAlert('danger', '❌ Error clearing application cache: ' + (xhr.responseJSON?.message || 'Unknown error'));
        },
        complete: function() {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    });
}

function showDashboardAlert(type, message) {
    // Remove existing alerts
    $('.dashboard-alert').remove();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show dashboard-alert" role="alert" style="position: fixed; top: 80px; right: 20px; z-index: 1060; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.dashboard-alert').fadeOut();
    }, 5000);
}
</script>

@if(app()->environment(['local', 'development']) && auth('admin')->user() && auth('admin')->user()->isSuperAdmin())
<!-- Development Tools (Super Admin Only) -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <div class="card border-warning shadow-lg" style="max-width: 300px;">
        <div class="card-header bg-warning text-dark py-2">
            <h6 class="mb-0">
                <i class="fas fa-tools me-2"></i>Development Tools
            </h6>
        </div>
        </div>
    </div>
</div>
@endif

@endsection
