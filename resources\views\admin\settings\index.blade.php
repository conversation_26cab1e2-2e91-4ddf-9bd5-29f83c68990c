@extends('admin.layouts.app')

@section('title', 'Settings')
@section('page-title', 'System Settings')

@section('breadcrumb')
<li class="breadcrumb-item">
    <a href="{{ route('admin.settings.index') }}" style="color: #667eea; text-decoration: none;">Settings</a>
</li>
<li class="breadcrumb-item active">System</li>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">
                    <i class="fas fa-cog me-2 text-primary"></i>Application Settings
                </h4>
                <p class="text-muted mb-0">Manage your application configuration</p>
            </div>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetSettings()">
                    <i class="fas fa-undo me-2"></i>Reset to Default
                </button>
                <button type="button" class="btn btn-warning" onclick="toggleMaintenance()">
                    <i class="fas fa-tools me-2"></i>
                    <span id="maintenanceToggleText">
                        {{ ($settings['maintenance_mode'] ?? false) ? 'Disable' : 'Enable' }} Maintenance
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Alerts -->
@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<!-- Settings Form -->
<form id="settingsForm" method="POST" action="{{ route('admin.settings.update') }}">
    @csrf
    @method('PUT')

    <div class="row">
        <!-- General Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>General Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="site_name" class="form-label fw-semibold">Site Name</label>
                        <input type="text" class="form-control" id="site_name" name="site_name"
                               value="{{ $settings['site_name'] ?? 'Shahid Play' }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="site_description" class="form-label fw-semibold">Site Description</label>
                        <textarea class="form-control" id="site_description" name="site_description"
                                  rows="3">{{ $settings['site_description'] ?? '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="contact_email" class="form-label fw-semibold">Contact Email</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email"
                               value="{{ $settings['contact_email'] ?? '<EMAIL>' }}" required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Maintenance Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input type="hidden" name="maintenance_mode" value="0">
                            <input class="form-check-input" type="checkbox" id="maintenance_mode"
                                   name="maintenance_mode" value="1" {{ ($settings['maintenance_mode'] ?? false) ? 'checked' : '' }}>
                            <label class="form-check-label fw-semibold" for="maintenance_mode">
                                Enable Maintenance Mode
                            </label>
                        </div>
                        <small class="text-muted">When enabled, users will see a maintenance page</small>
                    </div>

                    <div class="mb-3">
                        <label for="maintenance_message" class="form-label fw-semibold">Maintenance Message</label>
                        <textarea class="form-control" id="maintenance_message" name="maintenance_message"
                                  rows="4" placeholder="Enter the message to display during maintenance">{{ $settings['maintenance_message'] ?? 'We are currently performing scheduled maintenance. Please check back soon.' }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>User Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input type="hidden" name="allow_registration" value="0">
                            <input class="form-check-input" type="checkbox" id="allow_registration"
                                   name="allow_registration" value="1" {{ ($settings['allow_registration'] ?? true) ? 'checked' : '' }}>
                            <label class="form-check-label fw-semibold" for="allow_registration">
                                Allow User Registration
                            </label>
                        </div>
                        <small class="text-muted">Allow new users to register on the platform</small>
                    </div>

                    <div class="mb-3">
                        <label for="max_users" class="form-label fw-semibold">Maximum Users</label>
                        <input type="number" class="form-control" id="max_users" name="max_users"
                               value="{{ $settings['max_users'] ?? 1000 }}" min="1" max="100000">
                        <small class="text-muted">Maximum number of users allowed on the platform</small>
                    </div>

                    <div class="mb-3">
                        <label for="session_timeout" class="form-label fw-semibold">Session Timeout (minutes)</label>
                        <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                               value="{{ $settings['session_timeout'] ?? 120 }}" min="5" max="1440" required>
                        <small class="text-muted">User session timeout in minutes (5-1440)</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-code me-2"></i>Decryption API Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="decryption_api_url" class="form-label fw-semibold">Decryption API URL</label>
                        <input type="url" class="form-control" id="decryption_api_url" name="decryption_api_url"
                               value="{{ $settings['decryption_api_url'] ?? 'http://127.0.0.1:5000' }}" required>
                        <small class="text-muted">URL for the decryption API service used to extract DRM keys</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input type="hidden" name="enable_caching" value="0">
                            <input class="form-check-input" type="checkbox" id="enable_caching"
                                   name="enable_caching" value="1" {{ ($settings['enable_caching'] ?? true) ? 'checked' : '' }}>
                            <label class="form-check-label fw-semibold" for="enable_caching">
                                Enable API Caching
                            </label>
                        </div>
                        <small class="text-muted">Cache API responses to improve performance</small>
                    </div>

                    <div class="mb-3">
                        <label for="cache_duration" class="form-label fw-semibold">Cache Duration (seconds)</label>
                        <input type="number" class="form-control" id="cache_duration" name="cache_duration"
                               value="{{ $settings['cache_duration'] ?? 3600 }}" min="60" max="86400" required>
                        <small class="text-muted">How long to cache API responses (60-86400 seconds)</small>
                    </div>

                    <div class="mb-3">
                        <label for="max_retry_attempts" class="form-label fw-semibold">Max Retry Attempts</label>
                        <input type="number" class="form-control" id="max_retry_attempts" name="max_retry_attempts"
                               value="{{ $settings['max_retry_attempts'] ?? 3 }}" min="1" max="10" required>
                        <small class="text-muted">Number of retry attempts for failed API requests</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>System Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h4 mb-0">
                                    <i class="fas fa-circle {{ ($settings['maintenance_mode'] ?? false) ? 'text-warning' : 'text-success' }}"></i>
                                </div>
                                <small class="text-muted">
                                    {{ ($settings['maintenance_mode'] ?? false) ? 'Maintenance' : 'Online' }}
                                </small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-primary">
                                {{ \App\Models\User::count() }}
                            </div>
                            <small class="text-muted">Total Users</small>
                        </div>
                    </div>

                    <hr>

                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-muted small">PHP</div>
                            <div class="fw-semibold">{{ PHP_VERSION }}</div>
                        </div>
                        <div class="col-4">
                            <div class="text-muted small">Laravel</div>
                            <div class="fw-semibold">{{ app()->version() }}</div>
                        </div>
                        <div class="col-4">
                            <div class="text-muted small">Environment</div>
                            <div class="fw-semibold text-{{ app()->environment() === 'production' ? 'success' : 'warning' }}">
                                {{ ucfirst(app()->environment()) }}
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- API Status Check -->
                    <div class="text-center">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="testApiConnection()">
                            <i class="fas fa-plug me-2"></i>Test API Connection
                        </button>
                        <div id="apiStatus" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg px-5" id="saveBtn">
                        <i class="fas fa-save me-2"></i>
                        <span id="saveBtnText">Save Settings</span>
                        <span id="saveSpinner" class="spinner-border spinner-border-sm ms-2 d-none"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.card {
    border: none;
    border-radius: 12px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border: none;
    font-weight: 600;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.alert {
    border-radius: 10px;
    border: none;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Add validation for required fields
    function validateForm() {
        let isValid = true;
        const errors = [];

        // Validate session timeout
        const sessionTimeout = parseInt($('#session_timeout').val());
        if (!sessionTimeout || sessionTimeout < 5 || sessionTimeout > 1440) {
            errors.push('Session timeout must be between 5 and 1440 minutes');
            isValid = false;
        }

        // Validate cache duration
        const cacheDuration = parseInt($('#cache_duration').val());
        if (!cacheDuration || cacheDuration < 60 || cacheDuration > 86400) {
            errors.push('Cache duration must be between 60 and 86400 seconds');
            isValid = false;
        }

        // Show errors if any
        if (!isValid) {
            showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    // Handle form submission
    $('#settingsForm').on('submit', function(e) {
        e.preventDefault();

        // Validate form first
        if (!validateForm()) {
            return;
        }

        const form = $(this);
        const saveBtn = $('#saveBtn');
        const saveBtnText = $('#saveBtnText');
        const saveSpinner = $('#saveSpinner');

        // Show loading state
        saveBtn.prop('disabled', true);
        saveBtnText.text('Saving...');
        saveSpinner.removeClass('d-none');

        // Handle checkboxes properly
        const formData = new FormData(form[0]);

        // Ensure checkbox values are sent as 1 or 0
        const checkboxes = ['maintenance_mode', 'allow_registration', 'enable_caching'];
        checkboxes.forEach(function(name) {
            const checkbox = $(`input[name="${name}"][type="checkbox"]`);
            if (checkbox.length) {
                // Remove the hidden input value first
                formData.delete(name);
                // Set the correct value based on checkbox state
                formData.set(name, checkbox.is(':checked') ? '1' : '0');
            }
        });

        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);

                    // Update maintenance toggle text
                    const maintenanceMode = $('#maintenance_mode').is(':checked');
                    $('#maintenanceToggleText').text(maintenanceMode ? 'Disable Maintenance' : 'Enable Maintenance');
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while saving settings';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showAlert('danger', errorMessage);
            },
            complete: function() {
                // Reset button state
                saveBtn.prop('disabled', false);
                saveBtnText.text('Save Settings');
                saveSpinner.addClass('d-none');
            }
        });
    });
});

function toggleMaintenance() {
    $.ajax({
        url: '{{ route("admin.settings.toggle-maintenance") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);

                // Update checkbox and button text
                $('#maintenance_mode').prop('checked', response.maintenance_mode);
                $('#maintenanceToggleText').text(response.maintenance_mode ? 'Disable Maintenance' : 'Enable Maintenance');

                // Reload page after 2 seconds to update status
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error toggling maintenance mode');
        }
    });
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
        window.location.href = '{{ route("admin.settings.reset") }}';
    }
}

function testApiConnection() {
    const apiUrl = $('#decryption_api_url').val();
    const statusDiv = $('#apiStatus');

    if (!apiUrl) {
        statusDiv.html('<small class="text-danger"><i class="fas fa-times me-1"></i>Please enter API URL first</small>');
        return;
    }

    statusDiv.html('<small class="text-info"><i class="fas fa-spinner fa-spin me-1"></i>Testing connection...</small>');

    $.ajax({
        url: '{{ route("admin.settings.test-api") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            api_url: apiUrl
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                statusDiv.html('<small class="text-success"><i class="fas fa-check me-1"></i>' + response.message + '</small>');
            } else {
                statusDiv.html('<small class="text-danger"><i class="fas fa-times me-1"></i>' + response.message + '</small>');
            }
        },
        error: function(xhr) {
            statusDiv.html('<small class="text-danger"><i class="fas fa-times me-1"></i>Connection failed</small>');
        }
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top
    $('.row').first().before(alertHtml);

    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
@endpush