# Dependencies
/node_modules
/vendor

# Build files
/public/build
/public/hot
/public/storage

# Environment files
.env
.env.backup
.env.production
.env.local
.env.testing

# Storage
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/app/public/*

# Testing
/.phpunit.cache
.phpunit.result.cache
/coverage
phpunit.xml

# IDE files
/.fleet
/.idea
/.vscode
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/storage/logs/*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Homestead
Homestead.json
Homestead.yaml
auth.json

# Backup files
*.backup
*.bak
*.tmp

# Custom uploads (if any)
/public/uploads/*
!/public/uploads/.gitkeep
