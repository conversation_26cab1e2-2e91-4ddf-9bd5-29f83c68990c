<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\Api\ShahidChannelsController;

class RefreshChannelStreamsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $channelId;
    protected $sessionId;

    /**
     * Create a new job instance.
     */
    public function __construct($channelId = null, $sessionId = null)
    {
        $this->channelId = $channelId;
        $this->sessionId = $sessionId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("🔄 Background refresh job started", [
                'channel_id' => $this->channelId,
                'session_id' => $this->sessionId
            ]);

            // If specific channel ID provided, refresh only that channel
            if ($this->channelId) {
                $this->refreshSpecificChannel($this->channelId);
            } else {
                // Refresh all active channels
                $this->refreshAllActiveChannels();
            }

            Log::info("✅ Background refresh job completed successfully");

        } catch (\Exception $e) {
            Log::error("❌ Background refresh job failed: " . $e->getMessage());
        }
    }

    /**
     * Refresh a specific channel
     */
    private function refreshSpecificChannel($channelId)
    {
        try {
            // Get cached channel data
            $cacheKey = "channel_stream_{$channelId}";
            $cachedData = Cache::get($cacheKey);

            if (!$cachedData) {
                Log::info("ℹ️ No cached data found for channel: {$channelId}");
                return;
            }

            // Check if refresh is needed
            $cachedAt = new \DateTime($cachedData['cached_at']);
            $now = new \DateTime();
            $minutesSinceCached = $now->diff($cachedAt)->i + ($now->diff($cachedAt)->h * 60);

            if ($minutesSinceCached > 5) { // Refresh every 5 minutes in background
                Log::info("🔄 Refreshing channel stream in background: {$channelId}");
                
                // Create controller instance and refresh
                $controller = new ShahidChannelsController();
                $refreshedData = $controller->refreshChannelStreamUrl($channelId, $cachedData);
                
                if ($refreshedData) {
                    // Update cache
                    Cache::put($cacheKey, $refreshedData, now()->addHours(2));
                    Log::info("✅ Channel stream refreshed successfully in background: {$channelId}");
                } else {
                    Log::warning("⚠️ Failed to refresh channel stream in background: {$channelId}");
                }
            }

        } catch (\Exception $e) {
            Log::error("❌ Error refreshing specific channel {$channelId}: " . $e->getMessage());
        }
    }

    /**
     * Refresh all active channels
     */
    private function refreshAllActiveChannels()
    {
        try {
            // Get all cached channels
            $cachePattern = "channel_stream_*";
            $cachedChannels = Cache::get($cachePattern, []);

            if (empty($cachedChannels)) {
                Log::info("ℹ️ No active channels found to refresh");
                return;
            }

            foreach ($cachedChannels as $channelId => $data) {
                $this->refreshSpecificChannel($channelId);
                
                // Small delay between refreshes to avoid overwhelming the server
                usleep(500000); // 0.5 seconds
            }

        } catch (\Exception $e) {
            Log::error("❌ Error refreshing all channels: " . $e->getMessage());
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("❌ RefreshChannelStreamsJob failed: " . $exception->getMessage());
    }
}
