<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProxySettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'host',
        'port',
        'username',
        'password',
        'type',
        'is_active',
        'is_default',
        'description',
        'test_results',
        'last_tested_at'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'test_results' => 'array',
        'last_tested_at' => 'datetime'
    ];

    /**
     * Get the active proxy
     */
    public static function getActiveProxy()
    {
        return self::where('is_active', true)
                  ->where('is_default', true)
                  ->first();
    }

    /**
     * Set this proxy as default (deactivate others)
     */
    public function setAsDefault()
    {
        // إلغاء تفعيل جميع البروكسيات الأخرى
        self::where('id', '!=', $this->id)->update([
            'is_default' => false,
            'is_active' => false
        ]);

        // تفعيل هذا البروكسي
        $this->update([
            'is_default' => true,
            'is_active' => true
        ]);

        return $this;
    }

    /**
     * Test proxy connection
     */
    public function testConnection()
    {
        try {
            Log::info("🔍 Testing proxy connection: {$this->name}");

            $startTime = microtime(true);

            // إعداد البروكسي
            $proxyUrl = $this->getProxyUrl();

            // اختبار الاتصال مع موقع شاهد
            $response = Http::withOptions([
                'proxy' => $proxyUrl,
                'timeout' => 10,
                'connect_timeout' => 5,
                'verify' => false
            ])->get('https://shahid.mbc.net');

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->successful()) {
                $testResults = [
                    'status' => 'success',
                    'response_time' => $responseTime,
                    'status_code' => $response->status(),
                    'tested_at' => now()->toISOString(),
                    'ip_info' => $this->getProxyIpInfo()
                ];

                $this->update([
                    'test_results' => $testResults,
                    'last_tested_at' => now()
                ]);

                Log::info("✅ Proxy test successful: {$this->name} ({$responseTime}ms)");
                return $testResults;
            } else {
                throw new \Exception("HTTP {$response->status()}");
            }

        } catch (\Exception $e) {
            $testResults = [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'tested_at' => now()->toISOString()
            ];

            $this->update([
                'test_results' => $testResults,
                'last_tested_at' => now()
            ]);

            Log::error("❌ Proxy test failed: {$this->name} - {$e->getMessage()}");
            return $testResults;
        }
    }

    /**
     * Get proxy URL for HTTP client
     */
    public function getProxyUrl()
    {
        $auth = '';
        if ($this->username && $this->password) {
            $auth = $this->username . ':' . $this->password . '@';
        }

        return "{$this->type}://{$auth}{$this->host}:{$this->port}";
    }

    /**
     * Get proxy IP information
     */
    private function getProxyIpInfo()
    {
        try {
            $response = Http::withOptions([
                'proxy' => $this->getProxyUrl(),
                'timeout' => 5
            ])->get('https://httpbin.org/ip');

            if ($response->successful()) {
                return $response->json();
            }
        } catch (\Exception $e) {
            Log::warning("Could not get IP info for proxy: {$this->name}");
        }

        return null;
    }

    /**
     * Check if proxy is healthy (tested recently and working)
     */
    public function isHealthy()
    {
        if (!$this->last_tested_at) {
            return false;
        }

        // اعتبر البروكسي صحي إذا تم اختباره في آخر 30 دقيقة ونجح
        $isRecent = $this->last_tested_at->gt(Carbon::now()->subMinutes(30));
        $isWorking = isset($this->test_results['status']) && $this->test_results['status'] === 'success';

        return $isRecent && $isWorking;
    }

    /**
     * Get formatted proxy info for display
     */
    public function getDisplayInfo()
    {
        $info = [
            'name' => $this->name,
            'address' => "{$this->host}:{$this->port}",
            'type' => strtoupper($this->type),
            'status' => $this->is_active ? 'Active' : 'Inactive',
            'health' => $this->isHealthy() ? 'Healthy' : 'Unknown'
        ];

        if ($this->test_results && isset($this->test_results['response_time'])) {
            $info['response_time'] = $this->test_results['response_time'] . 'ms';
        }

        return $info;
    }
}
