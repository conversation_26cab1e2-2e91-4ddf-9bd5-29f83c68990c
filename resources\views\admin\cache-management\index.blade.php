@extends('admin.layouts.app')

@section('title', 'Cache Management')
@section('page-title', 'Cache Management')

@section('breadcrumb')
<li class="breadcrumb-item active">Cache Management</li>
@endsection

@section('content')
<div class="container-fluid">
    <!-- <PERSON><PERSON>tainer -->
    <div id="alertContainer"></div>

    <!-- Cache Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-database fa-2x mb-2"></i>
                    <h6 class="card-title">Application Cache</h6>
                    <p class="card-text" id="appCacheStatus">Loading...</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-film fa-2x mb-2"></i>
                    <h6 class="card-title">Series Cache</h6>
                    <p class="card-text" id="seriesCacheCount">Loading...</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-video fa-2x mb-2"></i>
                    <h6 class="card-title">Movies Cache</h6>
                    <p class="card-text" id="moviesCacheCount">Loading...</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-hdd fa-2x mb-2"></i>
                    <h6 class="card-title">Total Size</h6>
                    <p class="card-text" id="totalCacheSize">Loading...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-broom me-2 text-warning"></i>
                        Cache Management Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Clear All Cache -->
                        <div class="col-md-4 mb-3">
                            <div class="card border border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-trash-alt fa-2x text-danger mb-2"></i>
                                    <h6 class="card-title">Clear All Cache</h6>
                                    <p class="card-text small text-muted">Clears all cache types including application, database, and files</p>
                                    <button class="btn btn-danger" onclick="clearCache('all')">
                                        <i class="fas fa-trash-alt me-2"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Clear Application Cache -->
                        <div class="col-md-4 mb-3">
                            <div class="card border border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                                    <h6 class="card-title">Application Cache</h6>
                                    <p class="card-text small text-muted">Clears Laravel cache, config, routes, and views</p>
                                    <button class="btn btn-primary" onclick="clearCache('application')">
                                        <i class="fas fa-sync me-2"></i>Clear App
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Clear Database Cache -->
                        <div class="col-md-4 mb-3">
                            <div class="card border border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-database fa-2x text-info mb-2"></i>
                                    <h6 class="card-title">Database Cache</h6>
                                    <p class="card-text small text-muted">Clears cached database queries and results</p>
                                    <button class="btn btn-info" onclick="clearCache('database')">
                                        <i class="fas fa-database me-2"></i>Clear DB
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Clear File Cache -->
                        <div class="col-md-4 mb-3">
                            <div class="card border border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-file fa-2x text-warning mb-2"></i>
                                    <h6 class="card-title">File Cache</h6>
                                    <p class="card-text small text-muted">Clears cached files and temporary data</p>
                                    <button class="btn btn-warning" onclick="clearCache('files')">
                                        <i class="fas fa-file me-2"></i>Clear Files
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Clear Series Cache -->
                        <div class="col-md-4 mb-3">
                            <div class="card border border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-film fa-2x text-success mb-2"></i>
                                    <h6 class="card-title">Series Cache</h6>
                                    <p class="card-text small text-muted">Clears cached series data and episodes</p>
                                    <button class="btn btn-success" onclick="clearCache('series')">
                                        <i class="fas fa-film me-2"></i>Clear Series
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Clear Movies Cache -->
                        <div class="col-md-4 mb-3">
                            <div class="card border border-secondary">
                                <div class="card-body text-center">
                                    <i class="fas fa-video fa-2x text-secondary mb-2"></i>
                                    <h6 class="card-title">Movies Cache</h6>
                                    <p class="card-text small text-muted">Clears cached movies data</p>
                                    <button class="btn btn-secondary" onclick="clearCache('movies')">
                                        <i class="fas fa-video me-2"></i>Clear Movies
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Statistics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2 text-info"></i>
                        Cache Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div id="cacheStats">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading cache statistics...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2 text-success"></i>
                        Cache Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Cache Driver:</strong>
                        </div>
                        <div class="col-6" id="cacheDriver">
                            Loading...
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>Last Cleared:</strong>
                        </div>
                        <div class="col-6" id="lastCleared">
                            Loading...
                        </div>
                    </div>
                    <hr>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Regular cache clearing helps maintain optimal performance and frees up storage space.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Cache Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2 text-warning"></i>
                        Recent Cache Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div id="cacheActivity">
                        <p class="text-muted">No recent cache activity to display.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Setup CSRF token
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Load cache statistics
    loadCacheStats();

    // Auto-refresh every 30 seconds
    setInterval(loadCacheStats, 30000);
});

function loadCacheStats() {
    $.ajax({
        url: '{{ route("admin.cache.stats") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateCacheDisplay(response.stats);
            } else {
                showAlert('danger', 'Failed to load cache statistics');
            }
        },
        error: function() {
            showAlert('danger', 'Error loading cache statistics');
        }
    });
}

function updateCacheDisplay(stats) {
    // Update overview cards
    $('#appCacheStatus').text(stats.application_cache.status || 'Unknown');
    $('#seriesCacheCount').text(stats.series_cache.total_series || '0');
    $('#moviesCacheCount').text(stats.movies_cache.total_movies || '0');
    $('#totalCacheSize').text(stats.total_size || 'Unknown');

    // Update cache info
    $('#cacheDriver').text(stats.cache_driver || 'Unknown');
    $('#lastCleared').text(stats.last_cleared || 'Never');

    // Update detailed statistics
    let statsHtml = `
        <table class="table table-sm">
            <tr>
                <td><strong>Series Records:</strong></td>
                <td>${stats.series_cache.total_series || 0}</td>
            </tr>
            <tr>
                <td><strong>Complete Series:</strong></td>
                <td>${stats.series_cache.complete_series || 0}</td>
            </tr>
            <tr>
                <td><strong>Movies Records:</strong></td>
                <td>${stats.movies_cache.total_movies || 0}</td>
            </tr>
            <tr>
                <td><strong>File Cache Files:</strong></td>
                <td>${stats.file_cache.files_count || 0}</td>
            </tr>
            <tr>
                <td><strong>File Cache Size:</strong></td>
                <td>${stats.file_cache.total_size || '0 B'}</td>
            </tr>
        </table>
    `;
    $('#cacheStats').html(statsHtml);
}

function clearCache(type) {
    if (!confirm('Are you sure you want to clear this cache? This action cannot be undone.')) {
        return;
    }

    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Clearing...';

    $.ajax({
        url: '{{ route("admin.cache.clear") }}',
        method: 'POST',
        data: { type: type },
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                loadCacheStats(); // Refresh stats
                
                // Show detailed results
                if (response.results) {
                    let resultsHtml = '<div class="mt-3"><h6>Clear Results:</h6><ul>';
                    for (let key in response.results) {
                        resultsHtml += `<li><strong>${key}:</strong> ${JSON.stringify(response.results[key])}</li>`;
                    }
                    resultsHtml += '</ul></div>';
                    
                    $('#cacheActivity').html(resultsHtml);
                }
            } else {
                showAlert('danger', response.message || 'Failed to clear cache');
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error clearing cache: ' + (xhr.responseJSON?.message || 'Unknown error'));
        },
        complete: function() {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('#alertContainer').html(alertHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
@endpush

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}
</style>
@endpush
