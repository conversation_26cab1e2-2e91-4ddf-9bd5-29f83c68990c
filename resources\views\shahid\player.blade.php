<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $playerData['title'] }} - Shahid Player</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Allow autoplay -->
    <meta http-equiv="Feature-Policy" content="autoplay 'self'">
    <meta http-equiv="Permissions-Policy" content="autoplay=(self)">
    
    <!-- JW Player CSS -->
    <link rel="stylesheet" href="{{ asset('player/tod_skin.css') }}">
    <link rel="stylesheet" href="{{ asset('player/clearkey.min.css') }}">
    
    <!-- Custom CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        .player-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #000;
        }
        
        #jwplayer-container {
            width: 100%;
            height: 100%;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            flex-direction: column;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 18px;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .loading-details {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            max-width: 400px;
        }
        
        .error-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            color: white;
            flex-direction: column;
        }
        
        .error-content {
            text-align: center;
            max-width: 500px;
            padding: 40px;
        }
        
        .error-icon {
            font-size: 60px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .back-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .controls-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 999;
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }
    </style>
</head>
<body>
    <div class="player-container">
        <!-- Controls Overlay -->
        <div class="controls-overlay">
            <button class="control-btn" onclick="goBack()">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للداشبورد
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand me-2"></i>
                ملء الشاشة
            </button>
        </div>
        
        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">جاري تحضير الفيلم...</div>
            <div class="loading-details">
                يتم استخراج مفاتيح DRM وتحضير المشغل<br>
                <strong>{{ $playerData['title'] }}</strong>
            </div>
        </div>
        
        <!-- Error Overlay -->
        <div class="error-overlay" id="errorOverlay">
            <div class="error-content">
                <div class="error-icon">⚠️</div>
                <h3>حدث خطأ أثناء تحضير الفيلم</h3>
                <p id="errorMessage">لم نتمكن من استخراج مفاتيح DRM المطلوبة لتشغيل هذا المحتوى.</p>
                <button class="back-button" onclick="goBack()">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للداشبورد
                </button>
            </div>
        </div>
        
        <!-- JW Player Container -->
        <div id="jwplayer-container"></div>
    </div>

    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <!-- JW Player Scripts -->
    <script src="{{ asset('player/js/jwplayer.js') }}"></script>
    <script src="{{ asset('player/clearkey.js') }}"></script>
    <script src="{{ asset('js/autoplay-enhancer.js') }}"></script>
    
    <script>
        // بيانات المشغل من Laravel
        const playerData = @json($playerData);
        
        console.log('🎬 Player Data:', playerData);
        
        // إعداد JW Player
        jwplayer.key = "XSuP4qMl+9tK17QNb+4+th2Pm9AWgMO/cYH8CI0HGGr7bdjo";

        // تكوين مسار JWPlayer
        const currentDomain = window.location.protocol + '//' + window.location.host;
        const basePath = currentDomain + '/player/js/';

        // إعداد مسار JWPlayer
        if (typeof jwplayer !== 'undefined') {
            jwplayer.defaults = jwplayer.defaults || {};
            jwplayer.defaults.base = basePath;
            console.log('🔧 JWPlayer base path set to:', basePath);
        }
        
        /**
         * تهيئة المشغل
         */
        async function initializePlayer() {
            try {
                console.log('🔧 Initializing JW Player...');
                console.log('📋 Player Data:', playerData);

                // التحقق من وجود البيانات المطلوبة
                if (!playerData.drm_key) {
                    throw new Error('DRM key not available');
                }

                if (!playerData.manifest_url) {
                    throw new Error('Manifest URL not available');
                }

                console.log('🔑 DRM Key:', playerData.drm_key);
                console.log('📺 MPD URL:', playerData.manifest_url);
                console.log('🆔 KID:', playerData.kid);

                // إعداد المشغل
                const player = jwplayer('jwplayer-container').setup({
                    file: playerData.manifest_url,
                    title: playerData.title || 'Shahid Movie',
                    width: '100%',
                    height: '100%',
                    autostart: true,
                    mute: false,  // Don't start muted to allow autoplay
                    preload: 'auto',
                    controls: true,
                    displaytitle: true,
                    displaydescription: false,
                    stretching: 'uniform',
                    skin: {
                        name: 'tod',
                        url: '{{ asset('player/tod_skin.css') }}'
                    },
                    // إعداد DRM
                    drm: {
                        clearkey: {
                            keyId: playerData.kid,
                            key: playerData.drm_key
                        }
                    }
                });

                // أحداث المشغل
                player.on('ready', function() {
                    console.log('✅ Player ready');
                    hideLoading();

                    // Force autoplay if it didn't start automatically
                    setTimeout(() => {
                        if (player.getState() !== 'playing') {
                            console.log('🔄 Attempting to start playback...');
                            player.play().catch(e => {
                                console.warn('⚠️ Autoplay prevented by browser:', e);
                                // Show play button overlay if autoplay fails
                                showPlayButton();
                            });
                        }
                    }, 1000);
                });

                player.on('play', function() {
                    console.log('▶️ Playback started');
                    hidePlayButton();
                });

                player.on('pause', function() {
                    console.log('⏸️ Playback paused');
                });

                player.on('error', function(e) {
                    console.error('❌ Player error:', e);
                    showError('خطأ في المشغل: ' + e.message);
                });

                player.on('setupError', function(e) {
                    console.error('❌ Setup error:', e);
                    showError('خطأ في إعداد المشغل: ' + e.message);
                });

                console.log('✅ Player initialized successfully');
                
            } catch (error) {
                console.error('💥 Failed to initialize player:', error);
                showError(error.message);
            }
        }
        
        /**
         * إخفاء شاشة التحميل
         */
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        /**
         * إظهار زر التشغيل عند فشل التشغيل التلقائي
         */
        function showPlayButton() {
            const container = document.getElementById('jwplayer-container');
            if (!document.getElementById('manual-play-button')) {
                const playButton = document.createElement('div');
                playButton.id = 'manual-play-button';
                playButton.innerHTML = `
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                background: rgba(0,0,0,0.8); border-radius: 50%; width: 80px; height: 80px;
                                display: flex; align-items: center; justify-content: center; cursor: pointer;
                                z-index: 1000; transition: all 0.3s ease;">
                        <i class="fas fa-play" style="color: white; font-size: 24px; margin-left: 4px;"></i>
                    </div>
                `;
                playButton.onclick = function() {
                    jwplayer().play();
                };
                container.appendChild(playButton);
            }
        }

        /**
         * إخفاء زر التشغيل
         */
        function hidePlayButton() {
            const playButton = document.getElementById('manual-play-button');
            if (playButton) {
                playButton.remove();
            }
        }
        
        /**
         * إظهار رسالة خطأ
         */
        function showError(message) {
            hideLoading();
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorOverlay').style.display = 'flex';
        }
        
        /**
         * العودة للداشبورد
         */
        function goBack() {
            window.location.href = '/shahid/movies';
        }
        
        /**
         * تبديل ملء الشاشة
         */
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // بدء تشغيل المشغل عند تحميل الصفحة
        window.addEventListener('load', async function() {
            try {
                // تهيئة المشغل
                await initializePlayer();
                
            } catch (error) {
                console.error('💥 Initialization failed:', error);
                showError('فشل في تحضير الفيلم: ' + error.message);
            }
        });
        
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // منع اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
