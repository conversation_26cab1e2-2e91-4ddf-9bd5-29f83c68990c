<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ShahidController;
use App\Http\Controllers\Api\ShahidApiController;
use App\Http\Controllers\Api\ShahidChannelsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});



// Admin API Routes (Protected with admin.auth middleware and rate limiting)
Route::middleware(['admin.auth', 'throttle:60,1'])->prefix('admin')->name('api.admin.')->group(function () {

    // Dashboard Routes
    Route::get('/dashboard/statistics', [App\Http\Controllers\Admin\AdminDashboardController::class, 'statistics'])->name('dashboard.statistics');
    Route::get('/dashboard/recent-activities', [App\Http\Controllers\Admin\AdminDashboardController::class, 'recentActivities'])->name('dashboard.recent-activities');
    Route::get('/dashboard/subscription-trends', [App\Http\Controllers\Admin\AdminDashboardController::class, 'subscriptionTrends'])->name('dashboard.subscription-trends');
    Route::get('/dashboard/revenue-trends', [App\Http\Controllers\Admin\AdminDashboardController::class, 'revenueTrends'])->name('dashboard.revenue-trends');
    Route::get('/dashboard/top-plans', [App\Http\Controllers\Admin\AdminDashboardController::class, 'topPlans'])->name('dashboard.top-plans');
    Route::get('/dashboard/expiring-subscriptions', [App\Http\Controllers\Admin\AdminDashboardController::class, 'expiringSubscriptions'])->name('dashboard.expiring-subscriptions');
    Route::get('/dashboard/system-info', [App\Http\Controllers\Admin\AdminDashboardController::class, 'systemInfo'])->name('dashboard.system-info');

    // User Management Routes
    Route::apiResource('users', App\Http\Controllers\Admin\UserController::class);
    Route::post('/users/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('/users/{user}/reset-password', [App\Http\Controllers\Admin\UserController::class, 'resetPassword'])->name('users.reset-password');
    Route::get('/users-statistics', [App\Http\Controllers\Admin\UserController::class, 'statistics'])->name('users.statistics');

    // Subscription Plan Management Routes
    Route::apiResource('subscription-plans', App\Http\Controllers\Admin\SubscriptionPlanController::class);
    Route::post('/subscription-plans/{subscriptionPlan}/toggle-status', [App\Http\Controllers\Admin\SubscriptionPlanController::class, 'toggleStatus'])->name('subscription-plans.toggle-status');
    Route::get('/subscription-plans-statistics', [App\Http\Controllers\Admin\SubscriptionPlanController::class, 'statistics'])->name('subscription-plans.statistics');

    // User Subscription Management Routes
    Route::apiResource('user-subscriptions', App\Http\Controllers\Admin\UserSubscriptionController::class);
    Route::post('/user-subscriptions/{userSubscription}/toggle-status', [App\Http\Controllers\Admin\UserSubscriptionController::class, 'toggleStatus'])->name('user-subscriptions.toggle-status');
    Route::post('/user-subscriptions/{userSubscription}/extend', [App\Http\Controllers\Admin\UserSubscriptionController::class, 'extend'])->name('user-subscriptions.extend');
    Route::get('/user-subscriptions-statistics', [App\Http\Controllers\Admin\UserSubscriptionController::class, 'statistics'])->name('user-subscriptions.statistics');
});

// Shahid API Routes (Protected)
Route::middleware('auth')->prefix('shahid')->name('api.shahid.')->group(function () {
    // Content endpoints
    Route::get('/movies', [ShahidController::class, 'getMovies'])->name('movies');
    Route::get('/series', [ShahidController::class, 'getSeries'])->name('series');
    Route::get('/live-channels', [ShahidController::class, 'getLiveChannels'])->name('live.channels');

    // Search and details
    Route::post('/search', [ShahidController::class, 'searchContent'])->name('search');
    Route::post('/content-details', [ShahidController::class, 'getContentDetails'])->name('content.details');

    // Series and episodes
    Route::post('/series/seasons', [ShahidController::class, 'getSeriesSeasons'])->name('series.seasons');
    Route::post('/season/episodes', [ShahidController::class, 'getSeasonEpisodes'])->name('season.episodes');

    // Token management
    Route::post('/set-token', [ShahidController::class, 'setToken'])->name('set.token');
    Route::post('/validate-token', [ShahidController::class, 'validateAndRefreshToken'])->name('validate.token');
});

// Shahid API Routes (Public - no auth required for extraction)
Route::prefix('shahid')->name('api.shahid.')->group(function () {
    // Content extraction endpoints
    Route::get('/extract-movie', [ShahidApiController::class, 'extractMovie'])->name('extract.movie');
    Route::get('/extract-movie-drm', [ShahidApiController::class, 'extractMovieDrm'])->name('extract.movie.drm');
    Route::get('/extract-episode', [ShahidApiController::class, 'extractEpisode'])->name('extract.episode');
    Route::get('/extract-episode-drm', [ShahidApiController::class, 'extractEpisodeDrm'])->name('extract.episode.drm');

    // Public browsing endpoints (no auth required)
    Route::get('/movies', [ShahidApiController::class, 'getMovies'])->name('public.movies');
    Route::get('/series', [ShahidApiController::class, 'getSeries'])->name('public.series');
    Route::get('/search', [ShahidApiController::class, 'searchContent'])->name('public.search');
    Route::get('/live-channels', [ShahidApiController::class, 'getLiveChannels'])->name('public.live.channels');
    Route::get('/content-details', [ShahidApiController::class, 'getContentDetails'])->name('public.content.details');

    // Token management (public)
    Route::post('/save-token', [ShahidApiController::class, 'saveToken'])->name('public.save.token');
});

// Shahid API Routes (Public - no auth required for extraction)
Route::prefix('shahid')->name('api.shahid.')->group(function () {
    // Content extraction endpoints
    Route::get('/extract-movie', [ShahidApiController::class, 'extractMovie'])->name('extract.movie');
    Route::get('/extract-movie-drm', [ShahidApiController::class, 'extractMovieDrm'])->name('extract.movie.drm');
    Route::get('/extract-episode', [ShahidApiController::class, 'extractEpisode'])->name('extract.episode');
    Route::get('/extract-episode-drm', [ShahidApiController::class, 'extractEpisodeDrm'])->name('extract.episode.drm');

    // Public browsing endpoints (no auth required)
    Route::get('/movies', [ShahidApiController::class, 'getMovies'])->name('public.movies');
    Route::get('/series', [ShahidApiController::class, 'getSeries'])->name('public.series');
    Route::get('/search', [ShahidApiController::class, 'searchContent'])->name('public.search');
    Route::get('/live-channels', [ShahidApiController::class, 'getLiveChannels'])->name('public.live.channels');
    Route::get('/content-details', [ShahidApiController::class, 'getContentDetails'])->name('public.content.details');

    // Token management (public)
    Route::post('/save-token', [ShahidApiController::class, 'saveToken'])->name('public.save.token');
});

// Shahid Channels API Routes (Public - no auth required)
Route::prefix('shahid/channels')->name('api.shahid.channels.')->group(function () {
    // Get all channels
    Route::get('/', [ShahidChannelsController::class, 'index'])->name('index');

    // Search channels
    Route::get('/search', [ShahidChannelsController::class, 'search'])->name('search');

    // Get channel by ID
    Route::get('/{id}', [ShahidChannelsController::class, 'show'])->name('show');

    // Get channels by country
    Route::get('/country/{code}', [ShahidChannelsController::class, 'byCountry'])->name('by.country');

    // Refresh channels cache
    Route::post('/refresh', [ShahidChannelsController::class, 'refresh'])->name('refresh');

    // Extract channel data (MPD/HLS with or without DRM)
    Route::get('/{channelId}/extract', [ShahidChannelsController::class, 'extractChannelData'])->name('extract');

    // Extract channel DRM data (like movie DRM extractor)
    Route::post('/{channelId}/extract-drm', [ShahidChannelsController::class, 'extractChannelDrm'])->name('extract.drm');

    // Get available countries
    Route::get('/meta/countries', [ShahidChannelsController::class, 'countries'])->name('countries');

    // Get channels statistics
    Route::get('/meta/stats', [ShahidChannelsController::class, 'stats'])->name('stats');
});

// Test routes (no auth required)
Route::prefix('test')->name('test.')->group(function () {
    Route::post('/series/seasons', [\App\Http\Controllers\TestController::class, 'getSeriesSeasons'])->name('series.seasons');
    Route::post('/season/episodes', [\App\Http\Controllers\TestController::class, 'getSeasonEpisodes'])->name('season.episodes');
});
