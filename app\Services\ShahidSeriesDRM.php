<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use App\Helpers\SettingsHelper;

class ShahidSeriesDRM
{
    private $seriesAPI;

    public function __construct()
    {
        $this->seriesAPI = new ShahidSeriesAPI();
    }

    /**
     * Extract complete DRM data for episode (MPD + PSSH + Keys)
     */
    public function extractEpisodeData($episodeId)
    {
        try {
            Log::info("🎬 Extracting complete DRM data for episode: {$episodeId}");

            // ✅ Check cache first to avoid duplicate extraction
            $cacheKey = "episode_drm_{$episodeId}";
            $cachedResult = Cache::get($cacheKey);

            if ($cachedResult && isset($cachedResult['success']) && $cachedResult['success']) {
                Log::info("⚡ Using cached DRM data from extractEpisodeData (avoiding duplicate extraction)");
                return $cachedResult;
            }

            // ✅ First, get episode details (title, series, season info)
            $episodeDetails = $this->getEpisodeDetails($episodeId);
            Log::info("📋 Episode details extracted", $episodeDetails);

            // First, get episode manifest URL
            $manifestResult = $this->getEpisodeManifest($episodeId);
            if (!$manifestResult['success']) {
                return $manifestResult;
            }

            $mpdUrl = $manifestResult['mpd_url'];
            Log::info("📺 Episode MPD URL: {$mpdUrl}");

            // Extract PSSH from manifest
            $psshResult = $this->extractPsshFromManifest($mpdUrl);
            if (!$psshResult['success']) {
                return $psshResult;
            }

            $pssh = $psshResult['pssh'];
            Log::info("🔐 Episode PSSH extracted: " . substr($pssh, 0, 50) . "...");

            // Extract keys using PSSH and API
            $keyResult = $this->extractEpisodeKeyUsingApi($episodeId, $pssh, $mpdUrl);
            if (!$keyResult['success']) {
                return $keyResult;
            }

            $result = [
                'success' => true,
                'stream_url' => $mpdUrl,
                'pssh' => $pssh,
                'keys' => $keyResult['keys'] ?? [],
                'content_type' => 'episode',
                'content_id' => $episodeId,
                // ✅ Add episode metadata
                'episode_title' => $episodeDetails['title'] ?? 'Unknown Episode',
                'series_title' => $episodeDetails['series_title'] ?? 'Unknown Series',
                'season_number' => $episodeDetails['season_number'] ?? 0,
                'episode_number' => $episodeDetails['episode_number'] ?? 0,
                'full_title' => $this->formatFullTitle($episodeDetails)
            ];

            // ✅ Cache the result for 30 minutes
            Cache::put($cacheKey, $result, 1800);
            Log::info("💾 Episode DRM data cached in extractEpisodeData");

            return $result;

        } catch (\Exception $e) {
            Log::error("💥 Episode DRM extraction error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get episode details (title, series, season info)
     */
    private function getEpisodeDetails($episodeId)
    {
        try {
            Log::info("📋 Getting episode details for: {$episodeId}");

            // Try to get episode details from the series API
            $episodeInfo = $this->seriesAPI->getEpisodeDetails($episodeId);

            if ($episodeInfo && !isset($episodeInfo['error'])) {
                return [
                    'title' => $episodeInfo['title'] ?? 'Unknown Episode',
                    'series_title' => $episodeInfo['series_title'] ?? 'Unknown Series',
                    'season_number' => $episodeInfo['season_number'] ?? 0,
                    'episode_number' => $episodeInfo['episode_number'] ?? 0
                ];
            }

            // Fallback: try to extract from episode ID pattern
            Log::info("📋 Fallback: extracting from episode ID pattern");
            return $this->extractDetailsFromEpisodeId($episodeId);

        } catch (\Exception $e) {
            Log::error("❌ Error getting episode details: " . $e->getMessage());
            return [
                'title' => 'Unknown Episode',
                'series_title' => 'Unknown Series',
                'season_number' => 0,
                'episode_number' => 0
            ];
        }
    }

    /**
     * Extract details from episode ID pattern (fallback)
     */
    private function extractDetailsFromEpisodeId($episodeId)
    {
        // Try to extract season/episode from ID patterns
        if (preg_match('/S(\d+)E(\d+)/i', $episodeId, $matches)) {
            return [
                'title' => "Episode {$matches[2]}",
                'series_title' => 'Unknown Series',
                'season_number' => (int)$matches[1],
                'episode_number' => (int)$matches[2]
            ];
        }

        return [
            'title' => 'Unknown Episode',
            'series_title' => 'Unknown Series',
            'season_number' => 0,
            'episode_number' => 0
        ];
    }

    /**
     * Format full title for display
     */
    private function formatFullTitle($episodeDetails)
    {
        $seriesTitle = $episodeDetails['series_title'] ?? 'Unknown Series';
        $seasonNumber = $episodeDetails['season_number'] ?? 0;
        $episodeNumber = $episodeDetails['episode_number'] ?? 0;
        $episodeTitle = $episodeDetails['title'] ?? 'Unknown Episode';

        if ($seasonNumber > 0 && $episodeNumber > 0) {
            return "{$seriesTitle} S{$seasonNumber}:E{$episodeNumber} - {$episodeTitle}";
        } else {
            return "{$seriesTitle} - {$episodeTitle}";
        }
    }

    /**
     * Extract episode key using API (main method for episodes)
     */
    public function extractEpisodeKey($episodeId)
    {
        try {
            Log::info("🔑 Extracting episode key using API for: {$episodeId}");

            // Get episode playout URL and PSSH
            $episodeData = $this->extractEpisodeData($episodeId);
            
            if (!$episodeData['success']) {
                return $episodeData;
            }

            $pssh = $episodeData['pssh'];
            $mpdUrl = $episodeData['stream_url'];

            // Get license URL dynamically from Shahid DRM API (like Python)
            $licenseUrl = $this->getLicenseUrl($episodeId);

            if (!$licenseUrl) {
                Log::error("❌ Failed to get license URL for episode: {$episodeId}");
                return [
                    'success' => false,
                    'error' => 'Failed to get license URL from Shahid DRM API'
                ];
            }

            Log::info("🔐 Using API to extract key from PSSH");
            Log::info("📡 License URL: {$licenseUrl}");

            // Use API to extract key
            $keyResult = $this->extractKeyUsingPythonApi($pssh, $licenseUrl, null);

            if ($keyResult && isset($keyResult['success']) && $keyResult['success']) {
                Log::info("✅ Episode key extracted successfully using API");
                return [
                    'success' => true,
                    'keys' => [
                        [
                            'key' => $keyResult['key'],
                            'kid' => $keyResult['kid']
                        ]
                    ],
                    'stream_url' => $mpdUrl,
                    'pssh' => $pssh
                ];
            } else {
                Log::error("❌ API key extraction failed for episode");
                return [
                    'success' => false,
                    'error' => $keyResult['error'] ?? 'API key extraction failed'
                ];
            }

        } catch (\Exception $e) {
            Log::error("❌ Error extracting episode key using API: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get episode manifest URL using playout API
     */
    private function getEpisodeManifest($episodeId)
    {
        try {
            Log::info("📡 Getting episode manifest for: {$episodeId}");

            // Get episode playout URL
            $playoutResult = $this->seriesAPI->getEpisodePlayoutUrl($episodeId);

            if (!$playoutResult || isset($playoutResult['error'])) {
                return [
                    'success' => false,
                    'error' => $playoutResult['error'] ?? 'Failed to get episode playout URL'
                ];
            }

            // Extract MPD URL from playout data
            $mpdUrl = null;

            // Check if it's an error response
            if (isset($playoutResult['error'])) {
                Log::error("❌ Playout error for episode {$episodeId}: " . $playoutResult['error']);
                return [
                    'success' => false,
                    'error' => $playoutResult['error']
                ];
            }

            // Try to extract MPD URL from different possible locations
            if (isset($playoutResult['playout'])) {
                $playout = $playoutResult['playout'];

                // Look for MPD URL in various locations
                if (isset($playout['url'])) {
                    $mpdUrl = $playout['url'];
                } elseif (isset($playout['urls'])) {
                    $urls = $playout['urls'];
                    if (is_array($urls) && count($urls) > 0) {
                        $mpdUrl = $urls[0]['url'] ?? null;
                    } elseif (is_array($urls)) {
                        // Try different URL types
                        foreach (['dash', 'mpd', 'hls', 'default'] as $urlType) {
                            if (isset($urls[$urlType])) {
                                $mpdUrl = $urls[$urlType];
                                break;
                            }
                        }
                    }
                } elseif (isset($playout['dash'])) {
                    $mpdUrl = $playout['dash'];
                } elseif (isset($playout['mpd'])) {
                    $mpdUrl = $playout['mpd'];
                }
            }

            // If still no URL found, try direct access
            if (!$mpdUrl && isset($playoutResult['url'])) {
                $mpdUrl = $playoutResult['url'];
            }

            // Clean and validate the MPD URL
            if ($mpdUrl) {
                $mpdUrl = $this->cleanMpdUrl($mpdUrl);
                Log::info("🧹 Cleaned MPD URL: {$mpdUrl}");
            }

            if (!$mpdUrl) {
                Log::error("❌ No MPD URL found in playout data for episode {$episodeId}");
                Log::info("Playout data structure: " . json_encode(array_keys($playoutResult)));
                if (isset($playoutResult['playout'])) {
                    Log::info("Playout keys: " . json_encode(array_keys($playoutResult['playout'])));
                }
                
                return [
                    'success' => false,
                    'error' => 'No MPD URL found in playout data'
                ];
            }

            Log::info("📺 Found MPD URL for episode {$episodeId}: " . substr($mpdUrl, 0, 100) . "...");

            return [
                'success' => true,
                'mpd_url' => $mpdUrl
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error getting episode manifest: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract PSSH from manifest URL
     */
    private function extractPsshFromManifest($mpdUrl)
    {
        try {
            Log::info("🔍 Extracting PSSH from manifest: {$mpdUrl}");

            // Create context with proper headers for Shahid
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Accept: */*',
                        'Accept-Language: ar,en-US;q=0.9,en;q=0.8',
                        'Origin: https://shahid.mbc.net',
                        'Referer: https://shahid.mbc.net/',
                        'x-forwarded-for: *************',
                        'cf-ipcountry: SA'
                    ],
                    'timeout' => 30
                ]
            ]);

            // Download manifest content with proper headers
            $manifestContent = file_get_contents($mpdUrl, false, $context);
            if (!$manifestContent) {
                return [
                    'success' => false,
                    'error' => 'Failed to download manifest'
                ];
            }

            // Extract PSSH from manifest XML
            $pssh = $this->extractPsshFromXml($manifestContent);
            if (!$pssh) {
                return [
                    'success' => false,
                    'error' => 'No PSSH found in manifest'
                ];
            }

            return [
                'success' => true,
                'pssh' => $pssh
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error extracting PSSH from manifest: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract PSSH from XML manifest content
     */
    private function extractPsshFromXml($xmlContent)
    {
        try {
            // Look for ContentProtection elements with PSSH
            if (preg_match('/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/i', $xmlContent, $matches)) {
                return trim($matches[1]);
            }

            // Alternative pattern
            if (preg_match('/<ContentProtection[^>]*>([A-Za-z0-9+\/=]+)<\/ContentProtection>/i', $xmlContent, $matches)) {
                return trim($matches[1]);
            }

            // Look for any base64 content that might be PSSH
            if (preg_match('/([A-Za-z0-9+\/]{100,}={0,2})/i', $xmlContent, $matches)) {
                $potential_pssh = trim($matches[1]);
                // Basic validation - PSSH should be reasonably long
                if (strlen($potential_pssh) > 100) {
                    return $potential_pssh;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error("❌ Error parsing PSSH from XML: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Clean and validate MPD URL
     */
    private function cleanMpdUrl($url)
    {
        try {
            // Decode HTML entities
            $url = html_entity_decode($url, ENT_QUOTES | ENT_HTML5, 'UTF-8');

            // Remove any duplicate URL parts
            // Look for patterns like "url&domain.com/path" and keep only the first part
            if (preg_match('/^(https?:\/\/[^&]+)&/', $url, $matches)) {
                $url = $matches[1];
                Log::info("🔧 Removed duplicate URL part");
            }

            // Remove any trailing parameters that might be malformed
            if (strpos($url, '&') !== false) {
                $parts = explode('&', $url);
                $url = $parts[0];
                Log::info("🔧 Cleaned trailing parameters");
            }

            // Ensure URL ends with .mpd
            if (!preg_match('/\.mpd(\?.*)?$/', $url)) {
                if (strpos($url, '?') !== false) {
                    $url = preg_replace('/\?.*$/', '', $url) . '.mpd';
                } else {
                    $url .= '.mpd';
                }
                Log::info("🔧 Added .mpd extension");
            }

            // Validate URL format
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                Log::error("❌ Invalid URL format after cleaning: {$url}");
                return null;
            }

            Log::info("✅ URL cleaned successfully");
            return $url;

        } catch (\Exception $e) {
            Log::error("❌ Error cleaning MPD URL: " . $e->getMessage());
            return $url; // Return original URL if cleaning fails
        }
    }

    /**
     * Extract key using Python Widevine API
     */
    private function extractKeyUsingPythonApi($pssh, $licenseUrl, $targetKid)
    {
        try {
            Log::info('--- DECRYPTION API APPROACH ---');
            Log::info('Calling Decryption API with real pywidevine');

            // Get API URL from settings
            $apiUrl = SettingsHelper::getDecryptionApiUrl() . '/extract-key';

            // Check API availability (but don't fail if health check fails)
            $apiAvailable = $this->isDecryptionApiAvailable();
            if (!$apiAvailable) {
                Log::warning('Decryption API health check failed, but proceeding with extraction attempt');
            } else {
                Log::info('Decryption API is healthy and ready');
            }

            // Prepare request data
            $requestData = [
                'pssh' => $pssh,
                'license_url' => $licenseUrl,
                'target_kid' => $targetKid
            ];

            Log::info('Sending request to Python API...');
            Log::info('API URL: ' . $apiUrl);
            Log::info('Request data: ' . json_encode($requestData, JSON_PRETTY_PRINT));

            // Increase timeout for key extraction (can take time)
            $response = Http::timeout(120)->connectTimeout(30)->post($apiUrl, $requestData);

            if ($response->successful()) {
                $result = $response->json();
                Log::info('Python API response: ' . json_encode($result, JSON_PRETTY_PRINT));

                if ($result && isset($result['success']) && $result['success']) {
                    if (isset($result['key'])) {
                        Log::info('✓ Key extracted successfully via Python API');
                        Log::info('  → Key ID: ' . ($result['kid'] ?? 'N/A'));
                        Log::info('  → Key: ' . $result['key']);
                        Log::info('  → Total keys found: ' . ($result['total_keys'] ?? 1));

                        // Return success format with key and KID
                        return [
                            'success' => true,
                            'key' => $result['key'],
                            'kid' => $result['kid'] ?? $targetKid
                        ];
                    } else {
                        Log::warning('Python API succeeded but no key returned');
                        return [
                            'success' => false,
                            'error' => 'Python API succeeded but no key returned'
                        ];
                    }
                } else {
                    $error = $result['error'] ?? 'Unknown error';
                    Log::error('Python API failed: ' . $error);
                    return [
                        'success' => false,
                        'error' => $error
                    ];
                }
            } else {
                $error = 'Python API request failed with status: ' . $response->status();
                Log::error($error);
                Log::error('Response body: ' . $response->body());
                return [
                    'success' => false,
                    'error' => $error
                ];
            }

        } catch (\Exception $e) {
            Log::error('Python API approach failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get license URL from Shahid DRM API (copied from working ShahidDRM)
     */
    private function getLicenseUrl($contentId, $country = 'SA')
    {
        try {
            // Generate timestamp in ms (int)
            $timestamp = $this->generateTimestamp();

            // Generate authorization header per Shahid algo (same as ShahidDRM)
            $authorization = $this->generateDrmAuthorization($contentId, $country, $timestamp);

            if (!$authorization) {
                Log::error('Failed to generate DRM authorization');
                return null;
            }

            // Make request to DRM endpoint
            $url = "https://api2.shahid.net/proxy/v2.1/playout/new/drm";
            $params = [
                'request' => '{"assetId":' . (string)$contentId . '}',
                'ts' => (int)$timestamp,
                'country' => $country
            ];

            // Headers as per the provided correct flow (same as ShahidDRM)
            $headers = [
                'language' => 'EN',
                'os_version' => '10',
                'accept-language' => 'en',
                'browser_version' => '93.0',
                'authorization' => $authorization,
                'uuid' => 'web',
                'shahid_os' => 'WINDOWS',
                'sec-ch-ua-platform' => '"Windows"',
                'browser_name' => 'CHROME',
                'sec-ch-ua-mobile' => '?0',
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'content-type' => 'application/json',
                'accept' => 'application/json',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/'
            ];

            // Add token if available
            $token = $this->getShahidToken();
            if ($token) {
                $headers['token'] = $token;
            }

            $response = Http::withHeaders($headers)->get($url, $params);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['signature'])) {
                    Log::info('License URL obtained successfully');
                    return $data['signature'];
                }
            }

            Log::error('Failed to get license URL: ' . $response->status() . ' - ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('License URL error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate DRM authorization header (copied from working ShahidDRM)
     * Mirrors the Python logic: sort keys and join as k=v; remove spaces, HMAC-SHA256 hex
     */
    private function generateDrmAuthorization($contentId, $country, $timestamp)
    {
        try {
            $secretKey = 'z3qQSk17nbajIYUF0dU5f4+O/CxjFizcsEJr9ejOYFw='; // raw ascii key (not base64-decoded)

            // Build params exactly like the Python snippet
            $data = [
                'request' => '{"assetId":' . (string)$contentId . '}',
                'ts' => (int)$timestamp,
                'country' => $country,
            ];

            // Sort by key and join as k=v with ';'
            ksort($data);
            $pairs = [];
            foreach ($data as $k => $v) {
                $pairs[] = $k . '=' . (string)$v;
            }
            $signatureString = implode(';', $pairs);
            // Remove spaces just in case
            $signatureString = str_replace(' ', '', $signatureString);

            $authorization = hash_hmac('sha256', $signatureString, $secretKey, false);

            Log::info('DRM authorization generated successfully');
            return $authorization;

        } catch (\Exception $e) {
            Log::error('DRM authorization generation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Shahid token from storage (copied from working ShahidDRM)
     */
    private function getShahidToken()
    {
        try {
            if (Storage::exists('shahid_token.txt')) {
                return trim(Storage::get('shahid_token.txt'));
            }
        } catch (\Exception $e) {
            Log::error('Error getting Shahid token: ' . $e->getMessage());
        }
        return null;
    }



    /**
     * Generate timestamp in milliseconds
     */
    private function generateTimestamp()
    {
        return intval(microtime(true) * 1000);
    }

    /**
     * Check if Decryption API is available
     */
    private function isDecryptionApiAvailable()
    {
        try {
            $healthUrl = SettingsHelper::getDecryptionApiHealthUrl();
            if (!$healthUrl) {
                Log::warning('No health URL configured for Decryption API');
                return false;
            }

            Log::info('Checking Decryption API health at: ' . $healthUrl);

            // Increase timeout for external API
            $response = Http::timeout(15)->connectTimeout(10)->get($healthUrl);

            if ($response->successful()) {
                $data = $response->json();
                $isHealthy = isset($data['status']) && $data['status'] === 'healthy';
                Log::info('Decryption API health check result: ' . ($isHealthy ? 'healthy' : 'unhealthy'));
                return $isHealthy;
            } else {
                Log::warning('Decryption API health check failed with status: ' . $response->status());
                return false;
            }

        } catch (\Exception $e) {
            Log::warning('Decryption API health check failed: ' . $e->getMessage());
            // Don't fail completely - try to use the API anyway
            Log::info('Proceeding with API call despite health check failure');
            return true;
        }
    }

    /**
     * Extract episode key using API (for AJAX calls)
     */
    private function extractEpisodeKeyUsingApi($episodeId, $pssh, $mpdUrl)
    {
        try {
            // Get license URL dynamically from Shahid DRM API (like Python)
            $licenseUrl = $this->getLicenseUrl($episodeId);

            if (!$licenseUrl) {
                Log::error("❌ Failed to get license URL for episode: {$episodeId}");
                return [
                    'success' => false,
                    'error' => 'Failed to get license URL from Shahid DRM API'
                ];
            }

            Log::info("🔐 Using API to extract key from PSSH");
            Log::info("📡 License URL: {$licenseUrl}");

            // Use API to extract key
            $keyResult = $this->extractKeyUsingPythonApi($pssh, $licenseUrl, null);

            if ($keyResult && isset($keyResult['success']) && $keyResult['success']) {
                Log::info("✅ Episode key extracted successfully using API");
                return [
                    'success' => true,
                    'keys' => [
                        [
                            'key' => $keyResult['key'],
                            'kid' => $keyResult['kid']
                        ]
                    ]
                ];
            } else {
                Log::error("❌ API key extraction failed for episode");
                return [
                    'success' => false,
                    'error' => $keyResult['error'] ?? 'API key extraction failed'
                ];
            }

        } catch (\Exception $e) {
            Log::error("❌ Error extracting episode key using API: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check if series API has valid token
     */
    private function hasValidToken()
    {
        return $this->seriesAPI->hasValidToken();
    }

    /**
     * Get token from series API
     */
    private function getToken()
    {
        return $this->seriesAPI->getToken();
    }
}
