@extends('layouts.app')

@section('title', 'Settings - Shahid Play')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 fw-bold">
                        <i class="fas fa-cog me-2 text-primary"></i>Settings
                    </h1>
                    <p class="text-muted mb-0">Manage your application and service settings</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Grid -->
    <div class="row g-4">
        <!-- Main Settings Column -->
        <div class="col-xl-8">
            <!-- <PERSON>id Token Settings -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-primary text-white border-0 py-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                            <i class="fas fa-key text-white"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0 fw-semibold">Shahid Token Settings</h5>
                            <small class="opacity-75">Configure your authentication token</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- Token Status Alert -->
                    <div class="mb-4">
                        <div id="token-status" class="alert alert-info border-0 rounded-3 shadow-sm">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                <span>Checking token status...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Current Token Info -->
                    <div id="current-token-info" class="mb-4" style="display: none;">
                        <div class="bg-light rounded-3 p-3 border">
                            <h6 class="fw-semibold mb-2 text-dark">
                                <i class="fas fa-info-circle text-info me-1"></i>Current Token
                            </h6>
                            <div class="font-monospace small text-muted text-truncate" id="token-preview"></div>
                        </div>
                    </div>

                    <!-- Token Form -->
                    <form id="token-form">
                        <div class="mb-4">
                            <label for="shahid-token" class="form-label fw-semibold mb-2">
                                <i class="fas fa-paste me-1 text-primary"></i>Shahid Token
                            </label>
                            <textarea
                                class="form-control border-2 shadow-sm"
                                id="shahid-token"
                                rows="5"
                                placeholder="Paste your Shahid token here..."
                                style="font-family: 'Courier New', monospace; font-size: 13px; resize: vertical; line-height: 1.4;"
                            ></textarea>
                            <div class="form-text mt-2">
                                <i class="fas fa-lightbulb text-warning me-1"></i>
                                You can get the token from Shahid app or browser developer tools.
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary px-4 flex-fill">
                                <i class="fas fa-save me-2"></i>Save Token
                            </button>
                            <button type="button" class="btn btn-outline-secondary px-4 flex-fill" id="validate-token-btn">
                                <i class="fas fa-check-circle me-2"></i>Validate Token
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar Column -->
        <div class="col-xl-4">
            <!-- Application Settings -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-info text-white border-0 py-3">
                    <h6 class="card-title mb-0 fw-semibold">
                        <i class="fas fa-sliders-h me-2"></i>Application Settings
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable-caching" checked>
                                <label class="form-check-label fw-medium" for="enable-caching">
                                    <i class="fas fa-database me-1 text-success"></i>Enable Caching
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable-logging" checked>
                                <label class="form-check-label fw-medium" for="enable-logging">
                                    <i class="fas fa-file-alt me-1 text-info"></i>Enable Logging
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto-retry" checked>
                                <label class="form-check-label fw-medium" for="auto-retry">
                                    <i class="fas fa-redo me-1 text-warning"></i>Auto Retry
                                </label>
                            </div>
                        </div>
                        <div class="col-12 mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" id="save-additional-settings-btn">
                                <i class="fas fa-save me-1"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Status Section -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-success text-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                <i class="fas fa-server text-white"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-0 fw-semibold">API Services Status</h5>
                                <small class="opacity-75">Monitor your Shahid services health</small>
                            </div>
                        </div>
                        <button type="button" class="btn btn-light btn-sm" id="test-all-services-btn">
                            <i class="fas fa-flask me-1"></i>Test All Services
                        </button>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <!-- Movies Service -->
                        <div class="col-lg-3 col-md-6">
                            <div class="service-status-card p-3 rounded-3 border h-100">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-primary bg-opacity-10 me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; border-radius: 50%;">
                                            <i class="fas fa-film text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-semibold">Movies Service</h6>
                                            <small class="text-muted">Content API</small>
                                        </div>
                                    </div>
                                    <span id="movies-status" class="badge bg-secondary rounded-pill">Unknown</span>
                                </div>
                            </div>
                        </div>

                        <!-- Series Service -->
                        <div class="col-lg-3 col-md-6">
                            <div class="service-status-card p-3 rounded-3 border h-100">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-info bg-opacity-10 me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; border-radius: 50%;">
                                            <i class="fas fa-tv text-info"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-semibold">Series Service</h6>
                                            <small class="text-muted">Episodes API</small>
                                        </div>
                                    </div>
                                    <span id="series-status" class="badge bg-secondary rounded-pill">Unknown</span>
                                </div>
                            </div>
                        </div>

                        <!-- Live Channels -->
                        <div class="col-lg-3 col-md-6">
                            <div class="service-status-card p-3 rounded-3 border h-100">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-warning bg-opacity-10 me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; border-radius: 50%;">
                                            <i class="fas fa-broadcast-tower text-warning"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-semibold">Live Channels</h6>
                                            <small class="text-muted">Streaming API</small>
                                        </div>
                                    </div>
                                    <span id="channels-status" class="badge bg-secondary rounded-pill">Unknown</span>
                                </div>
                            </div>
                        </div>

                        <!-- Decryption Service -->
                        <div class="col-lg-3 col-md-6">
                            <div class="service-status-card p-3 rounded-3 border h-100">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-danger bg-opacity-10 me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; border-radius: 50%;">
                                            <i class="fas fa-unlock text-danger"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-semibold">Decryption Service</h6>
                                            <small class="text-muted">DRM API</small>
                                        </div>
                                    </div>
                                    <span id="decryption-service-status" class="badge bg-secondary rounded-pill">Unknown</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-dark text-white border-0 py-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                            <i class="fas fa-info-circle text-white"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0 fw-semibold">System Information</h5>
                            <small class="opacity-75">Application and service details</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <!-- Application Info -->
                        <div class="col-lg-6">
                            <h6 class="fw-semibold mb-3 text-primary">
                                <i class="fas fa-desktop me-1"></i>Application Info
                            </h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-dark">v1.0.0</div>
                                        <small class="text-muted">Version</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-dark">{{ app()->version() }}</div>
                                        <small class="text-muted">Laravel</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-dark">{{ PHP_VERSION }}</div>
                                        <small class="text-muted">PHP</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-{{ app()->environment() === 'production' ? 'success' : 'warning' }}">
                                            {{ ucfirst(app()->environment()) }}
                                        </div>
                                        <small class="text-muted">Environment</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Status -->
                        <div class="col-lg-6">
                            <h6 class="fw-semibold mb-3 text-success">
                                <i class="fas fa-heartbeat me-1"></i>Service Status
                            </h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-success">Connected</div>
                                        <small class="text-muted">Database</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-success">Available</div>
                                        <small class="text-muted">Storage</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-info">{{ round(memory_get_usage(true) / 1024 / 1024, 2) }} MB</div>
                                        <small class="text-muted">Memory</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded-2 p-2 text-center">
                                        <div class="fw-semibold text-primary">{{ \App\Models\User::count() }}</div>
                                        <small class="text-muted">Users</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Custom Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
}

/* Card Enhancements */
.card {
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
}

/* Service Status Cards */
.service-status-card {
    transition: all 0.3s ease;
    background: #fff;
}

.service-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: #667eea !important;
}

/* Icon Circles */
.icon-circle {
    transition: all 0.3s ease;
}

/* Alerts */
.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
}

/* Form Switches */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 6px 12px;
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }

    .icon-circle {
        width: 35px !important;
        height: 35px !important;
    }

    .service-status-card {
        margin-bottom: 1rem;
    }
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
    width: 8px;
}

textarea::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

textarea::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize page
    checkTokenStatus();
    loadCurrentToken();

    // Token form submission
    document.getElementById('token-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveToken();
    });

    // Validate token button
    document.getElementById('validate-token-btn').addEventListener('click', function() {
        validateToken();
    });

    // Test all services button
    document.getElementById('test-all-services-btn').addEventListener('click', function() {
        testAllServices();
    });

    // Save additional settings button
    document.getElementById('save-additional-settings-btn').addEventListener('click', function() {
        saveAdditionalSettings();
    });
});

function checkTokenStatus() {
    fetch('/shahid/api/validate-token')
        .then(response => response.json())
        .then(data => {
            const statusDiv = document.getElementById('token-status');
            if (data.success && data.valid) {
                statusDiv.className = 'alert alert-success border-0 rounded-3 shadow-sm';
                statusDiv.innerHTML = '<div class="d-flex align-items-center"><i class="fas fa-check-circle me-2"></i><span>Token is valid and active</span></div>';
            } else {
                statusDiv.className = 'alert alert-warning border-0 rounded-3 shadow-sm';
                statusDiv.innerHTML = '<div class="d-flex align-items-center"><i class="fas fa-exclamation-triangle me-2"></i><span>Token may be expired or invalid</span></div>';
            }
        })
        .catch(error => {
            console.error('Error checking token status:', error);
            const statusDiv = document.getElementById('token-status');
            statusDiv.className = 'alert alert-danger border-0 rounded-3 shadow-sm';
            statusDiv.innerHTML = '<div class="d-flex align-items-center"><i class="fas fa-times-circle me-2"></i><span>Error checking token status</span></div>';
        });
}

function loadCurrentToken() {
    fetch('/shahid/api/get-token')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.token) {
                const tokenTextarea = document.getElementById('shahid-token');
                const tokenInfo = document.getElementById('current-token-info');
                const tokenPreview = document.getElementById('token-preview');

                tokenTextarea.placeholder = 'Token is loaded. Paste new token to replace.';
                tokenTextarea.style.backgroundColor = '#f8f9fa';

                if (data.token) {
                    tokenPreview.textContent = data.token.substring(0, 50) + '...';
                    tokenInfo.style.display = 'block';
                }
            } else {
                document.getElementById('current-token-info').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading current token:', error);
            document.getElementById('current-token-info').style.display = 'none';
        });
}

function saveToken() {
    const token = document.getElementById('shahid-token').value.trim();

    if (!token) {
        showToast('Please enter a token', 'warning');
        return;
    }

    const submitBtn = document.querySelector('#token-form button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

    fetch('/shahid/api/token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ token: token })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            checkTokenStatus();
            loadCurrentToken();
            document.getElementById('shahid-token').value = '';
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error saving token:', error);
        showToast('Error saving token', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

function validateToken() {
    const button = document.getElementById('validate-token-btn');
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Validating...';

    fetch('/shahid/api/validate-token')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.valid) {
                showToast('Token is valid and working correctly', 'success');
                checkTokenStatus();
            } else {
                showToast('Token validation failed: ' + (data.message || 'Invalid token'), 'error');
            }
        })
        .catch(error => {
            console.error('Error validating token:', error);
            showToast('Error validating token', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

function testAllServices() {
    const button = document.getElementById('test-all-services-btn');
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testing...';

    // Reset all status badges
    document.getElementById('movies-status').className = 'badge bg-secondary rounded-pill';
    document.getElementById('movies-status').textContent = 'Testing...';
    document.getElementById('series-status').className = 'badge bg-secondary rounded-pill';
    document.getElementById('series-status').textContent = 'Testing...';
    document.getElementById('channels-status').className = 'badge bg-secondary rounded-pill';
    document.getElementById('channels-status').textContent = 'Testing...';
    document.getElementById('decryption-service-status').className = 'badge bg-secondary rounded-pill';
    document.getElementById('decryption-service-status').textContent = 'Testing...';

    // Test movies API
    fetch('/shahid/api/movies?limit=1')
        .then(response => response.json())
        .then(data => {
            const status = document.getElementById('movies-status');
            if (data.success) {
                status.className = 'badge bg-success rounded-pill';
                status.textContent = 'Available';
            } else {
                status.className = 'badge bg-danger rounded-pill';
                status.textContent = 'Error';
            }
        })
        .catch(error => {
            document.getElementById('movies-status').className = 'badge bg-danger rounded-pill';
            document.getElementById('movies-status').textContent = 'Error';
        });

    // Test series API
    fetch('/shahid/api/series?limit=1')
        .then(response => response.json())
        .then(data => {
            const status = document.getElementById('series-status');
            if (data.success) {
                status.className = 'badge bg-success rounded-pill';
                status.textContent = 'Available';
            } else {
                status.className = 'badge bg-danger rounded-pill';
                status.textContent = 'Error';
            }
        })
        .catch(error => {
            document.getElementById('series-status').className = 'badge bg-danger rounded-pill';
            document.getElementById('series-status').textContent = 'Error';
        });

    // Test channels API
    fetch('/shahid/api/channels?limit=1')
        .then(response => response.json())
        .then(data => {
            const status = document.getElementById('channels-status');
            if (data.success) {
                status.className = 'badge bg-success rounded-pill';
                status.textContent = 'Available';
            } else {
                status.className = 'badge bg-danger rounded-pill';
                status.textContent = 'Error';
            }
        })
        .catch(error => {
            document.getElementById('channels-status').className = 'badge bg-danger rounded-pill';
            document.getElementById('channels-status').textContent = 'Error';
        });

    // Test decryption service (check if API URL is configured)
    fetch('/settings/get/decryption_api_url')
        .then(response => response.json())
        .then(data => {
            const status = document.getElementById('decryption-service-status');
            if (data.success && data.data) {
                status.className = 'badge bg-success rounded-pill';
                status.textContent = 'Configured';
            } else {
                status.className = 'badge bg-warning rounded-pill';
                status.textContent = 'Not Configured';
            }
        })
        .catch(error => {
            document.getElementById('decryption-service-status').className = 'badge bg-danger rounded-pill';
            document.getElementById('decryption-service-status').textContent = 'Error';
        });

    setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalText;
        showToast('All services tested', 'info');
    }, 3000);
}

function saveAdditionalSettings() {
    const enableCaching = document.getElementById('enable-caching').checked;
    const enableLogging = document.getElementById('enable-logging').checked;
    const autoRetry = document.getElementById('auto-retry').checked;

    const button = document.getElementById('save-additional-settings-btn');
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

    const settings = {
        enable_caching: enableCaching,
        enable_logging: enableLogging,
        auto_retry: autoRetry
    };

    Promise.all([
        fetch('/settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ key: 'enable_caching', value: enableCaching })
        }),
        fetch('/settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ key: 'enable_logging', value: enableLogging })
        }),
        fetch('/settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ key: 'auto_retry', value: autoRetry })
        })
    ])
    .then(responses => Promise.all(responses.map(r => r.json())))
    .then(results => {
        const allSuccess = results.every(result => result.success);
        if (allSuccess) {
            showToast('Settings saved successfully', 'success');
        } else {
            showToast('Some settings failed to save', 'warning');
        }
    })
    .catch(error => {
        console.error('Error saving settings:', error);
        showToast('Error saving settings', 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="toast-body d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle text-success' : type === 'error' ? 'times-circle text-danger' : type === 'warning' ? 'exclamation-triangle text-warning' : 'info-circle text-info'} me-2"></i>
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close btn-close-sm ms-2" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Style the toast
    if (type === 'success') {
        toast.classList.add('bg-success', 'text-white');
    } else if (type === 'error') {
        toast.classList.add('bg-danger', 'text-white');
    } else if (type === 'warning') {
        toast.classList.add('bg-warning', 'text-dark');
    } else {
        toast.classList.add('bg-info', 'text-white');
    }

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast, { delay: 5000 });
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
@endpush
