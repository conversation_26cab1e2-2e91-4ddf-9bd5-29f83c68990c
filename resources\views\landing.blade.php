<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teefa Live</title>
    <meta name="description" content="شاهد آلاف الأفلام والمسلسلات والبرامج المباشرة أونلاين. منصتك المفضلة للترفيه العربي والعالمي.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="{{ asset('css/landing-responsive.css') }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Modern Streaming Colors */
            --primary-color: #ff6b35;
            --secondary-color: #1a1a2e;
            --accent-color: #16213e;
            --success-color: #00d4aa;
            --warning-color: #ffa726;
            --danger-color: #ef5350;

            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            --gradient-secondary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            --gradient-hero: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(22, 33, 62, 0.8) 100%);
            --gradient-overlay: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.8) 100%);

            /* Text Colors */
            --text-light: #ffffff;
            --text-gray: #b0b3b8;
            --text-dark: #1a1a2e;

            /* Background Colors */
            --bg-dark: #0f0f23;
            --bg-card: #1e1e3f;
            --bg-light: #ffffff;

            /* Shadows */
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 16px rgba(0,0,0,0.15);
            --shadow-lg: 0 8px 32px rgba(0,0,0,0.2);
            --shadow-xl: 0 16px 48px rgba(0,0,0,0.3);
            --shadow-glow: 0 0 30px rgba(255, 107, 53, 0.3);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--bg-dark);
            color: var(--text-light);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 107, 53, 0.2);
        }

        .header.scrolled {
            background: rgba(15, 15, 35, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: var(--text-light);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 0;
        }

        .nav-menu a:hover {
            color: var(--primary-color);
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-light);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            color: var(--text-light);
        }

        .btn-outline {
            background: transparent;
            color: var(--text-light);
            border: 2px solid var(--text-gray);
        }

        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(255, 107, 53, 0.1);
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-light);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
            background: var(--bg-dark);
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-background img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-hero);
            z-index: 2;
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 3;
            text-align: center;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .hero-content h1 {
            font-size: clamp(3.5rem, 8vw, 7rem);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 20px rgba(0,0,0,0.5);
            color: var(--text-light);
            direction: rtl;
            text-align: center;
            min-height: 200px;
            position: relative;
        }

        .hero-content h1::before {
            content: '|';
            animation: blink 1s infinite;
            color: var(--primary-color);
            font-weight: 400;
            display: var(--show-cursor, inline);
            margin-left: 10px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .hero-content .highlight {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.5));
            display: inline;
            font-weight: 800;
        }

        .hero-content p {
            font-size: 1.5rem;
            color: var(--text-gray);
            margin-bottom: 3rem;
            line-height: 1.6;
            max-width: 600px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.5);
            opacity: 1 !important;
            visibility: visible !important;
            position: relative;
            z-index: 10;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 4rem;
        }

        .hero-stats {
            display: flex;
            gap: 3rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            display: block;
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-gray);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            transform: perspective(1000px) rotateY(-15deg);
        }

        .hero-card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-lg);
            transition: transform 0.3s ease;
            border: 1px solid rgba(0, 123, 255, 0.2);
        }

        .hero-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .hero-card i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .hero-card h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .hero-card p {
            color: var(--text-gray);
            font-size: 0.9rem;
        }

        /* Features Section */
        .features {
            padding: 8rem 0;
            background: var(--bg-dark);
            position: relative;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 107, 53, 0.05) 50%, transparent 100%);
        }

        .features-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .section-header p {
            font-size: 1.2rem;
            color: var(--text-gray);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1.5rem;
        }

        .feature-card {
            background: var(--bg-card);
            padding: 2.5rem;
            border-radius: 20px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 107, 53, 0.2);
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-15px);
            box-shadow: var(--shadow-glow);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-hover);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: var(--text-light);
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .feature-card p {
            color: var(--text-gray);
            line-height: 1.6;
        }

        /* CTA Section */
        .cta {
            padding: 6rem 0;
            background: var(--gradient-secondary);
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .cta h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta p {
            font-size: 1.2rem;
            color: var(--text-gray);
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            background: var(--secondary-color);
            padding: 3rem 0 1rem;
            border-top: 1px solid rgba(108, 117, 125, 0.2);
        }

        .footer-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .footer p {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .social-links a {
            width: 40px;
            height: 40px;
            background: var(--text-gray);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }

            .hero-visual {
                order: -1;
            }

            .hero-cards {
                transform: none;
                grid-template-columns: 1fr;
            }

            .hero-buttons {
                justify-content: center;
            }

            .auth-buttons {
                gap: 0.5rem;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .nav-container {
                padding: 0 1rem;
            }

            .hero-container,
            .features-container,
            .cta-container,
            .footer-container {
                padding: 0 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-large {
                width: 100%;
                max-width: 300px;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <div class="nav-container">
            <div class="logo">شاهد</div>

            <nav class="nav-menu">
                <a href="#home">الرئيسية</a>
                <a href="#features">الأفلام</a>
                <a href="#series">المسلسلات</a>
                <a href="#live">البث المباشر</a>
                <a href="#contact">اتصل بنا</a>
            </nav>

            <div class="auth-buttons">
                <a href="{{ route('login') }}" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </a>
                <a href="" class="btn btn-primary">
                    <i class="fas fa-play"></i>
                    ابدأ المشاهدة
                </a>
            </div>

            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <!-- Background Image -->
        <div class="hero-background">
            <div class="relative overflow-hidden !w-full object-contain aspect-[21/10]">
                <img title="اكتشف المزيد مع اشتراك شاهد الخاص بك!"
                     alt="اكتشف المزيد مع اشتراك شاهد الخاص بك!"
                     decoding="async"
                     class="!w-full object-contain aspect-[21/10] !w-auto"
                     sizes="100%"
                     src="https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80"
                     style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent; visibility: visible;">
            </div>
        </div>

        <!-- Overlay -->
        <div class="hero-overlay"></div>

        <!-- Content -->
        <div class="hero-container">
            <div class="hero-content fade-in-up">
                <h1 id="hero-title">
                    <span class="highlight">Teefa Live</span><br>
                </h1>
                <p id="hero-description" style="opacity: 1;">
                    استمتع بمشاهدة أحدث الأفلام والمسلسلات العربية والعالمية أونلاين.
                    منصتك المفضلة للترفيه مع محتوى حصري وجودة عالية.
                </p>
                <div class="hero-buttons">
                    <a href="" class="btn btn-primary btn-large">
                        <i class="fas fa-play"></i>
                        ابدأ المشاهدة الآن
                    </a>
                    <a href="#features" class="btn btn-outline btn-large">
                        <i class="fas fa-film"></i>
                        تصفح المحتوى
                    </a>
                </div>

                <!-- Stats -->
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">10,000+</span>
                        <span class="stat-label">فيلم</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5,000+</span>
                        <span class="stat-label">مسلسل</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">200+</span>
                        <span class="stat-label">قناة مباشرة</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="features-container">
            <div class="section-header">
                <h2>لماذا تختار شاهد؟</h2>
                <p>استمتع بأفضل تجربة مشاهدة مع مميزات حصرية</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3>أحدث الأفلام</h3>
                    <p>شاهد أحدث الأفلام العربية والعالمية بجودة عالية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tv"></i>
                    </div>
                    <h3>مسلسلات حصرية</h3>
                    <p>مسلسلات عربية وتركية وعالمية حصرية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                    <h3>بث مباشر</h3>
                    <p>قنوات مباشرة للأخبار والرياضة والترفيه</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>شاهد في أي مكان</h3>
                    <p>على الهاتف والتابلت والكمبيوتر والتلفزيون</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>جودة عالية</h3>
                    <p>مشاهدة بجودة HD و 4K</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="cta-container fade-in-up">
            <h2>جاهز لبدء رحلتك؟</h2>
            <p>انضم إلى ملايين المستخدمين الذين يثقون في شاهد لاحتياجاتهم الترفيهية</p>
            <div class="hero-buttons">
                <a href="" class="btn btn-primary btn-large">
                    <i class="fas fa-rocket"></i>
                    ابدأ التجربة المجانية
                </a>
                <a href="{{ route('login') }}" class="btn btn-outline btn-large">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="social-links">
                <a href="#" aria-label="Facebook">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" aria-label="Twitter">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" aria-label="Instagram">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" aria-label="YouTube">
                    <i class="fab fa-youtube"></i>
                </a>
            </div>
            <p>&copy; 2025 شاهد. جميع الحقوق محفوظة.</p>
            <p>وجهتك المثلى للأفلام والمسلسلات والترفيه المباشر.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all fade-in-up elements
        document.querySelectorAll('.fade-in-up').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    </script>

    <style>
        body {
            opacity: 1;
            transition: opacity 0.3s ease;
        }



        /* Additional hover effects */
        .hero-card {
            position: relative;
            overflow: hidden;
        }

        .hero-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .hero-card:hover::before {
            left: 100%;
        }

        /* Loading spinner */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 107, 53, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-dark);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 107, 53, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .loading-text {
            color: var(--text-gray);
            font-size: 1.1rem;
        }

        /* Scroll Progress Indicator */
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: rgba(255, 107, 53, 0.2);
            z-index: 1001;
        }

        .scroll-progress {
            height: 100%;
            background: var(--gradient-primary);
            width: 0%;
            transition: width 0.1s ease;
        }
    </style>

    <!-- Additional JavaScript -->
    <script src="{{ asset('js/landing-page.js') }}"></script>
</body>
</html>
