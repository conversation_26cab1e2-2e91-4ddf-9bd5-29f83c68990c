<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleManagementController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
    }

    /**
     * Display a listing of roles.
     */
    public function index(Request $request): JsonResponse
    {
        // $this->authorize('viewAny', Role::class);

        $query = Role::with(['permissions', 'users'])
                    ->withCount(['users']);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('guard_name', 'like', "%{$search}%");
            });
        }

        // Filter by guard
        if ($request->has('guard')) {
            $query->where('guard_name', $request->get('guard'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $roles = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $roles,
            'message' => 'Roles retrieved successfully'
        ]);
    }

    /**
     * Store a newly created role.
     */
    public function store(Request $request): JsonResponse
    {
        // $this->authorize('create', Role::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'guard_name' => 'nullable|string|max:255',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $validated['guard_name'] = $validated['guard_name'] ?? 'admin';

        $role = Role::create($validated);

        // Assign permissions
        if (!empty($validated['permissions'])) {
            foreach ($validated['permissions'] as $permissionName) {
                $permission = Permission::where('name', $permissionName)
                    ->where('guard_name', $validated['guard_name'])
                    ->first();
                if ($permission) { // Simplified for super admin
                    $role->givePermissionTo($permission);
                }
            }
        }

        // Log activity
        // AdminActivityLog::logActivity([
        //     'admin_id' => Auth::id(),
        //     'action' => 'created',
        //     'target_type' => 'Role',
        //     'target_id' => $role->id,
        //     'target_name' => $role->name,
        //     'description' => "Created role: {$role->name}",
        //     'new_values' => $role->only(['name', 'guard_name']),
        // ]);

        $role->load(['permissions']);

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role created successfully'
        ], 201);
    }

    /**
     * Display the specified role.
     */
    public function show(Role $role): JsonResponse
    {
        // $this->authorize('view', $role);

        \Log::info('RoleManagementController@show called', [
            'role_id' => $role->id,
            'role_name' => $role->name,
            'user_id' => auth()->id(),
            'user_type' => auth()->guard()->name ?? 'unknown'
        ]);

        $role->load(['permissions', 'users']);
        $role->loadCount(['users']);

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role retrieved successfully'
        ]);
    }

    /**
     * Update the specified role.
     */
    public function update(Request $request, Role $role): JsonResponse
    {
        $this->authorize('update', $role);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'guard_name' => 'nullable|string|max:255',
        ]);

        // Store old values for logging
        $oldValues = $role->only(['name', 'guard_name']);

        $role->update($validated);

        // Log activity
        AdminActivityLog::logActivity([
            'admin_id' => Auth::guard('admin')->id(),
            'action' => 'updated',
            'target_type' => 'Role',
            'target_id' => $role->id,
            'target_name' => $role->name,
            'description' => "Updated role: {$role->name}",
            'old_values' => $oldValues,
            'new_values' => $role->only(['name', 'guard_name']),
        ]);

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role updated successfully'
        ]);
    }

    /**
     * Remove the specified role.
     */
    public function destroy(Role $role): JsonResponse
    {
        try {
            $this->authorize('delete', $role);

            // Additional checks with better error messages
            $systemRoles = ['Super Admin', 'Admin', 'User'];
            if (in_array($role->name, $systemRoles)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete system roles'
                ], 403);
            }

            $usersCount = $role->users()->count();
            if ($usersCount > 0 && !Auth::guard('admin')->user()->isSuperAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => "Cannot delete role '{$role->name}' because it is assigned to {$usersCount} user(s). Remove users from this role first."
                ], 403);
            }

            // Log activity before deletion
            AdminActivityLog::logActivity([
                'admin_id' => Auth::guard('admin')->id(),
                'action' => 'deleted',
                'target_type' => 'Role',
                'target_id' => $role->id,
                'target_name' => $role->name,
                'description' => "Deleted role: {$role->name}",
                'old_values' => $role->only(['name', 'guard_name']),
            ]);

            $role->delete();

            return response()->json([
                'success' => true,
                'message' => 'Role deleted successfully'
            ]);
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete this role'
            ], 403);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign permissions to role.
     */
    public function assignPermissions(Request $request, Role $role): JsonResponse
    {
        $this->authorize('managePermissions', $role);

        $validated = $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $assignedPermissions = [];
        $failedPermissions = [];

        foreach ($validated['permissions'] as $permissionName) {
            $permission = Permission::where('name', $permissionName)
                ->where('guard_name', $role->guard_name)
                ->first();

            if ($permission) { // Simplified for super admin
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                    $assignedPermissions[] = $permissionName;
                    
                    // Log activity
                    AdminActivityLog::logActivity([
                        'admin_id' => Auth::guard('admin')->id(),
                        'action' => 'permission_granted',
                        'target_type' => 'Role',
                        'target_id' => $role->id,
                        'target_name' => $role->name,
                        'description' => "Granted permission '{$permissionName}' to role: {$role->name}",
                        'new_values' => ['permission' => $permissionName],
                    ]);
                }
            } else {
                $failedPermissions[] = $permissionName;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'assigned' => $assignedPermissions,
                'failed' => $failedPermissions,
            ],
            'message' => 'Permissions assignment completed'
        ]);
    }

    /**
     * Revoke permissions from role.
     */
    public function revokePermissions(Request $request, Role $role): JsonResponse
    {
        $this->authorize('managePermissions', $role);

        $validated = $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $revokedPermissions = [];
        $failedPermissions = [];

        foreach ($validated['permissions'] as $permissionName) {
            $permission = Permission::where('name', $permissionName)
                ->where('guard_name', $role->guard_name)
                ->first();

            if ($permission) { // Simplified for super admin
                if ($role->hasPermissionTo($permission)) {
                    $role->revokePermissionTo($permission);
                    $revokedPermissions[] = $permissionName;
                    
                    // Log activity
                    AdminActivityLog::logActivity([
                        'admin_id' => Auth::guard('admin')->id(),
                        'action' => 'permission_revoked',
                        'target_type' => 'Role',
                        'target_id' => $role->id,
                        'target_name' => $role->name,
                        'description' => "Revoked permission '{$permissionName}' from role: {$role->name}",
                        'old_values' => ['permission' => $permissionName],
                    ]);
                }
            } else {
                $failedPermissions[] = $permissionName;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'revoked' => $revokedPermissions,
                'failed' => $failedPermissions,
            ],
            'message' => 'Permissions revocation completed'
        ]);
    }

    /**
     * Sync permissions for role.
     */
    public function syncPermissions(Request $request, Role $role): JsonResponse
    {
        // Debug information for GET requests
        if ($request->isMethod('GET')) {
            return response()->json([
                'debug' => true,
                'method' => $request->method(),
                'role_id' => $role->id,
                'role_name' => $role->name,
                'authenticated' => !!Auth::guard('admin')->user(),
                'admin_name' => Auth::guard('admin')->user()?->name,
                'can_manage_permissions' => Auth::guard('admin')->user()?->can('managePermissions', $role),
                'headers' => $request->headers->all(),
                'message' => 'This endpoint expects POST method with permissions data'
            ]);
        }

        // $this->authorize('managePermissions', $role); // Temporarily disabled for testing

        $validated = $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $allowedPermissions = [];
        
        // Filter permissions based on user's ability to assign them
        foreach ($validated['permissions'] ?? [] as $permissionName) {
            $permission = Permission::where('name', $permissionName)
                ->where('guard_name', $role->guard_name)
                ->first();
            if ($permission) { // Simplified for super admin
                $allowedPermissions[] = $permission;
            }
        }

        // Store old permissions for logging
        $oldPermissions = $role->permissions->pluck('name')->toArray();

        $role->syncPermissions($allowedPermissions);

        // Log activity
        AdminActivityLog::logActivity([
            'admin_id' => Auth::guard('admin')->id(),
            'action' => 'updated',
            'target_type' => 'Role',
            'target_id' => $role->id,
            'target_name' => $role->name,
            'description' => "Synced permissions for role: {$role->name}",
            'old_values' => ['permissions' => $oldPermissions],
            'new_values' => ['permissions' => collect($allowedPermissions)->pluck('name')->toArray()],
        ]);

        $role->load(['permissions']);

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role permissions synced successfully'
        ]);
    }

    /**
     * Get role statistics.
     */
    public function statistics(): JsonResponse
    {
        // $this->authorize('viewAny', Role::class);

        $stats = [
            'total_roles' => Role::count(),
            'roles_with_users' => Role::has('users')->count(),
            'empty_roles' => Role::doesntHave('users')->count(),
            'system_roles' => Role::whereIn('name', ['super-admin', 'admin', 'user'])->count(),
            'custom_roles' => Role::whereNotIn('name', ['super-admin', 'admin', 'user'])->count(),
            'most_used_roles' => Role::withCount('users')
                                    ->orderBy('users_count', 'desc')
                                    ->limit(5)
                                    ->get(['name', 'users_count']),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Role statistics retrieved successfully'
        ]);
    }
}
