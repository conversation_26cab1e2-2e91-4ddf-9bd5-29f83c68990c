<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Services\ShahidDRM;

/**
 * FairPlay DRM Test Controller
 * For testing FairPlay DRM functionality on iOS devices
 */
class FairPlayTestController extends Controller
{
    protected $shahidDRM;

    public function __construct(ShahidDRM $shahidDRM)
    {
        $this->shahidDRM = $shahidDRM;
    }

    /**
     * Show FairPlay test page
     */
    public function index()
    {
        return view('fairplay-test', [
            'title' => 'اختبار FairPlay DRM - شاهد',
            'sample_content_id' => '4992343679206767',
            'sample_country' => 'EG'
        ]);
    }

    /**
     * Test FairPlay DRM license
     */
    public function testLicense(Request $request)
    {
        try {
            $contentId = $request->input('content_id', '4992343679206767');
            $country = $request->input('country', 'EG');

            Log::info('=== FAIRPLAY LICENSE TEST API ===');
            Log::info('Content ID: ' . $contentId);
            Log::info('Country: ' . $country);

            // Test DRM license
            $result = $this->shahidDRM->testFairPlayDRM($contentId, $country);

            return response()->json([
                'success' => $result['success'],
                'data' => $result,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('FairPlay test API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get FairPlay DRM info
     */
    public function getDRMInfo(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            $country = $request->input('country', 'EG');

            if (!$contentId) {
                return response()->json([
                    'success' => false,
                    'error' => 'Content ID is required'
                ], 400);
            }

            Log::info('Getting FairPlay DRM info for: ' . $contentId);

            $drmInfo = $this->shahidDRM->getFairPlayDRMInfo($contentId, $country);

            return response()->json([
                'success' => $drmInfo['success'],
                'data' => $drmInfo,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('FairPlay DRM info API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get HLS stream URL
     */
    public function getStreamUrl(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            $country = $request->input('country', 'EG');

            if (!$contentId) {
                return response()->json([
                    'success' => false,
                    'error' => 'Content ID is required'
                ], 400);
            }

            Log::info('Getting HLS stream URL for: ' . $contentId);

            $streamData = $this->shahidDRM->getShahidStreamUrl($contentId, $country);

            return response()->json([
                'success' => $streamData['success'],
                'data' => $streamData,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Stream URL API error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Create complete FairPlay setup
     */
    public function createCompleteSetup(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            $country = $request->input('country', 'EG');

            if (!$contentId) {
                return response()->json([
                    'success' => false,
                    'error' => 'Content ID is required'
                ], 400);
            }

            Log::info('Creating complete FairPlay setup for: ' . $contentId);

            $setup = $this->shahidDRM->createCompleteFairPlaySetup($contentId, $country);

            return response()->json([
                'success' => $setup['success'],
                'data' => $setup,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Complete setup API error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Create FairPlay player config
     */
    public function createPlayerConfig(Request $request)
    {
        try {
            $hlsUrl = $request->input('hls_url');
            $contentId = $request->input('content_id');
            $country = $request->input('country', 'EG');

            if (!$hlsUrl || !$contentId) {
                return response()->json([
                    'success' => false,
                    'error' => 'HLS URL and Content ID are required'
                ], 400);
            }

            Log::info('Creating FairPlay player config');
            Log::info('HLS URL: ' . substr($hlsUrl, 0, 100) . '...');
            Log::info('Content ID: ' . $contentId);

            $config = $this->shahidDRM->createFairPlayPlayerConfig($hlsUrl, $contentId, $country);

            return response()->json([
                'success' => $config['success'],
                'data' => $config,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('FairPlay player config API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Test certificate download
     */
    public function testCertificate(Request $request)
    {
        try {
            $certificateUrl = $request->input('certificate_url');

            if (!$certificateUrl) {
                return response()->json([
                    'success' => false,
                    'error' => 'Certificate URL is required'
                ], 400);
            }

            Log::info('Testing certificate download: ' . $certificateUrl);

            // Use reflection to access private method
            $reflection = new \ReflectionClass($this->shahidDRM);
            $method = $reflection->getMethod('testFairPlayCertificate');
            $method->setAccessible(true);
            
            $result = $method->invoke($this->shahidDRM, $certificateUrl);

            return response()->json([
                'success' => $result['success'],
                'data' => $result,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Certificate test API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get Shahid DRM license data
     */
    public function getShahidLicense(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            $country = $request->input('country', 'EG');

            if (!$contentId) {
                return response()->json([
                    'success' => false,
                    'error' => 'Content ID is required'
                ], 400);
            }

            Log::info('Getting Shahid DRM license for: ' . $contentId);

            $licenseData = $this->shahidDRM->getShahidDRMLicense($contentId, $country);

            return response()->json([
                'success' => $licenseData['success'],
                'data' => $licenseData,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Shahid license API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Simple test endpoint
     */
    public function simpleTest(Request $request)
    {
        try {
            $contentId = $request->input('content_id', '69217');
            $country = $request->input('country', 'EG');

            Log::info('Simple test for: ' . $contentId);

            // Test 1: Check if token exists
            $token = $this->shahidDRM->getShahidToken();
            $hasToken = !empty($token);

            // Test 2: Try to get series info (simpler than DRM)
            $testResult = [
                'content_id' => $contentId,
                'country' => $country,
                'has_token' => $hasToken,
                'token_length' => $hasToken ? strlen($token) : 0,
                'timestamp' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Simple test completed',
                'data' => $testResult
            ]);

        } catch (\Exception $e) {
            Log::error('Simple test error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Compare DRM methods
     */
    public function compareDRMMethods(Request $request)
    {
        try {
            $contentId = $request->input('content_id', '69217');
            $country = $request->input('country', 'EG');

            Log::info('Comparing DRM methods for: ' . $contentId);

            // Test 1: FairPlay DRM (current method)
            $fairplayResult = $this->shahidDRM->getFairPlayDRMInfo($contentId, $country);

            // Test 2: Series DRM (working method)
            $seriesDRM = app('App\Services\ShahidSeriesDRM');
            $seriesResult = $seriesDRM->getDRMInfo($contentId, $country);

            return response()->json([
                'success' => true,
                'data' => [
                    'content_id' => $contentId,
                    'country' => $country,
                    'fairplay_method' => $fairplayResult,
                    'series_method' => $seriesResult,
                    'comparison' => [
                        'fairplay_success' => $fairplayResult['success'] ?? false,
                        'series_success' => $seriesResult['success'] ?? false,
                        'both_working' => ($fairplayResult['success'] ?? false) && ($seriesResult['success'] ?? false)
                    ]
                ],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('DRM comparison error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Extract decryption key from DRM data
     */
    public function extractKey(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            $country = $request->input('country', 'EG');

            Log::info('Extracting key for: ' . $contentId);

            // First get DRM info
            $drmResult = $this->shahidDRM->getFairPlayDRMInfo($contentId, $country);

            if (!$drmResult['success']) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to get DRM info: ' . $drmResult['error']
                ]);
            }

            $drmData = $drmResult;

            // Extract key information
            $keyData = [
                'kid' => $drmData['kid'] ?? null,
                'license_url' => $drmData['license_url'] ?? null,
                'certificate_url' => $drmData['certificate_url'] ?? null,
                'user_token' => $drmData['user_token'] ?? null
            ];

            // Try to extract key from license URL or user token
            $extractedKey = $this->extractKeyFromDRMData($keyData, $contentId);

            if ($extractedKey) {
                $keyData['key'] = $extractedKey['key'] ?? null;
                $keyData['pssh'] = $extractedKey['pssh'] ?? null;
            }

            Log::info('Key extraction completed');

            return response()->json([
                'success' => true,
                'data' => $keyData,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Key extraction error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Extract key from DRM data
     */
    private function extractKeyFromDRMData($drmData, $contentId = null)
    {
        try {
            $result = [];

            // Extract from user token (JWT)
            if (isset($drmData['user_token'])) {
                $tokenParts = explode('.', $drmData['user_token']);
                if (count($tokenParts) >= 2) {
                    $payload = json_decode(base64_decode($tokenParts[1]), true);

                    if (isset($payload['drmTokenInfo'])) {
                        $drmInfo = $payload['drmTokenInfo'];

                        // Extract key information
                        if (isset($drmInfo['kid'])) {
                            $result['kid_from_token'] = $drmInfo['kid'];
                        }

                        if (isset($drmInfo['fairplay'])) {
                            $result['fairplay_info'] = $drmInfo['fairplay'];
                        }
                    }
                }
            }

            // Extract KID from license URL
            if (isset($drmData['license_url'])) {
                $url = $drmData['license_url'];
                if (preg_match('/KID=([^&]+)/', $url, $matches)) {
                    $result['kid'] = $matches[1];
                }

                // Extract other parameters
                if (preg_match('/BrandGuid=([^&]+)/', $url, $matches)) {
                    $result['brand_guid'] = $matches[1];
                }
            }

            // Try to get real key from license server
            if (isset($drmData['license_url']) && isset($drmData['certificate_url'])) {
                $realKey = $this->getRealKeyFromLicenseServer($drmData, $contentId);
                if ($realKey) {
                    $result['key'] = $realKey['key'];
                    $result['key_type'] = 'REAL_KEY';
                    $result['note'] = 'Real decryption key extracted from license server';
                } else {
                    // Fallback to sample for demonstration
                    $result['key'] = 'SAMPLE_KEY_' . substr(md5($result['kid'] ?? 'default'), 0, 16);
                    $result['key_type'] = 'SAMPLE_KEY';
                    $result['note'] = 'Sample key - Real key extraction failed. Need proper FairPlay implementation.';
                }
            } else {
                $result['key'] = 'NO_LICENSE_URL';
                $result['key_type'] = 'ERROR';
                $result['note'] = 'Cannot extract key without license URL';
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Key extraction from DRM data failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get real key from license server
     */
    private function getRealKeyFromLicenseServer($drmData, $contentId = null)
    {
        try {
            Log::info('🔑 Attempting to get real key from license server...');

            $licenseUrl = $drmData['license_url'];
            $certificateUrl = $drmData['certificate_url'];

            Log::info('License URL: ' . $licenseUrl);
            Log::info('Certificate URL: ' . $certificateUrl);

            // Step 1: Get FairPlay certificate
            $certificate = $this->getFairPlayCertificate($certificateUrl);
            if (!$certificate) {
                Log::error('Failed to get FairPlay certificate');
                return null;
            }

            Log::info('✅ FairPlay certificate obtained');

            // Step 2: Extract the correct Asset ID/KID from DRM data
            $assetId = $this->extractAssetIdFromDRMData($drmData, $contentId);
            Log::info('Using Asset ID: ' . $assetId);

            // Step 3: Create and try multiple SPC formats (with certificate)
            $licenseResponse = $this->tryMultipleSPCFormats($licenseUrl, $assetId, $certificate);
            if (!$licenseResponse) {
                Log::error('Failed to get license response with any SPC format');
                return null;
            }

            Log::info('✅ License response received');

            // Step 4: Extract key from license response
            $keyData = $this->extractKeyFromLicenseResponse($licenseResponse);
            if ($keyData) {
                Log::info('✅ Real key extracted successfully');
                Log::info('Key type: ' . $keyData['type']);
                Log::info('Key format: ' . $keyData['format']);

                return [
                    'key' => $keyData['key'],
                    'key_type' => $keyData['type'],
                    'key_format' => $keyData['format'],
                    'key_description' => $keyData['description'],
                    'certificate' => base64_encode($certificate),
                    'license_response' => is_array($licenseResponse) ? json_encode($licenseResponse) : $licenseResponse,
                    'ckc' => $keyData['ckc'] ?? null,
                    'success_method' => $keyData['method'] ?? 'unknown'
                ];
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Real key extraction failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get FairPlay certificate from server
     */
    private function getFairPlayCertificate($certificateUrl)
    {
        try {
            Log::info('Fetching FairPlay certificate from: ' . $certificateUrl);

            $response = Http::timeout(30)->get($certificateUrl);

            if ($response->successful()) {
                $certificate = $response->body();
                Log::info('✅ Certificate fetched successfully. Size: ' . strlen($certificate) . ' bytes');

                // Validate certificate format
                if (strpos($certificate, '-----BEGIN CERTIFICATE-----') !== false) {
                    // Convert PEM to DER if needed
                    Log::info('Certificate is in PEM format, converting to DER...');
                    $certificate = $this->pemToDer($certificate);
                    Log::info('✅ Certificate converted to DER format. New size: ' . strlen($certificate) . ' bytes');
                } else {
                    Log::info('Certificate appears to be in DER format already');
                }

                // Basic DER validation
                if (strlen($certificate) > 0 && ord($certificate[0]) === 0x30) {
                    Log::info('✅ Certificate DER format validated (starts with 0x30)');
                } else {
                    Log::warning('⚠️ Certificate may not be in proper DER format');
                }

                return $certificate;
            }

            Log::error('Failed to fetch certificate: ' . $response->status());
            return null;
        } catch (\Exception $e) {
            Log::error('Certificate fetch failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create sample SPC for testing
     */
    private function createSampleSPC($kid)
    {
        try {
            Log::info('Creating SPC for KID: ' . $kid);

            // Try multiple SPC formats to find the one that works
            $formats = [
                'standard' => $this->createStandardFairPlaySPC($kid),
                'minimal' => $this->createMinimalSPC($kid),
                'shahid_specific' => $this->createShahidSpecificSPC($kid),
                'ios_like' => $this->createiOSLikeSPC($kid)
            ];

            // Return the standard format first, others will be tried if it fails
            return $formats['standard'];

        } catch (\Exception $e) {
            Log::error('SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create standard FairPlay SPC format
     */
    private function createStandardFairPlaySPC($assetId, $certificate = null)
    {
        try {
            // Standard FairPlay SPC format based on Apple's specification
            $spc = '';

            // 1. SPC Header (8 bytes)
            $spc .= pack('N', 0x00000001); // Version
            $spc .= pack('N', 0x00000000); // Flags

            // 2. Content ID (Asset ID)
            $contentId = str_replace('-', '', $assetId); // Remove hyphens
            $contentIdBytes = hex2bin($contentId);
            if ($contentIdBytes === false) {
                $contentIdBytes = $assetId; // Use as string if not hex
            }

            $spc .= pack('N', strlen($contentIdBytes)); // Content ID length
            $spc .= $contentIdBytes; // Content ID

            // 3. Random challenge (16 bytes)
            $challenge = random_bytes(16);
            $spc .= pack('N', 16); // Challenge length
            $spc .= $challenge; // Challenge data

            // 4. Timestamp (4 bytes)
            $spc .= pack('N', time());

            Log::info('Standard SPC created. Length: ' . strlen($spc) . ' bytes');
            Log::info('Content ID: ' . bin2hex($contentIdBytes));

            return $spc;

        } catch (\Exception $e) {
            Log::error('Standard SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create minimal SPC format
     */
    private function createMinimalSPC($kid)
    {
        try {
            // Minimal SPC - just the content ID
            $contentId = str_replace('-', '', $kid);
            $contentIdBytes = hex2bin($contentId);
            if ($contentIdBytes === false) {
                $contentIdBytes = $kid;
            }

            Log::info('Minimal SPC created. Length: ' . strlen($contentIdBytes) . ' bytes');
            return $contentIdBytes;

        } catch (\Exception $e) {
            Log::error('Minimal SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create Shahid-specific SPC format
     */
    private function createShahidSpecificSPC($kid)
    {
        try {
            // Shahid might expect a specific format
            // Try with the exact KID as provided by their API
            $spc = '';

            // Use the KID exactly as provided
            $spc .= $kid;

            // Add some standard FairPlay elements
            $spc .= pack('N', time()); // Timestamp
            $spc .= random_bytes(8); // Random data

            Log::info('Shahid-specific SPC created. Length: ' . strlen($spc) . ' bytes');
            return $spc;

        } catch (\Exception $e) {
            Log::error('Shahid-specific SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create iOS-like SPC format
     */
    private function createiOSLikeSPC($assetId, $certificate = null)
    {
        try {
            // Mimic what iOS AVContentKeySession would send
            $spc = '';

            // Magic bytes that iOS might use
            $spc .= pack('N', 0x46504C59); // 'FPLY' magic

            // Version
            $spc .= pack('N', 0x00000001);

            // Content ID
            $contentId = str_replace('-', '', $assetId);
            $contentIdBytes = hex2bin($contentId);
            if ($contentIdBytes === false) {
                $contentIdBytes = $assetId;
            }

            $spc .= pack('N', strlen($contentIdBytes));
            $spc .= $contentIdBytes;

            // Challenge
            $challenge = random_bytes(16);
            $spc .= pack('N', 16);
            $spc .= $challenge;

            // Additional iOS-specific data
            $spc .= pack('N', time());
            $spc .= random_bytes(32);

            Log::info('iOS-like SPC created. Length: ' . strlen($spc) . ' bytes');
            Log::info('SPC hex preview: ' . substr(bin2hex($spc), 0, 64) . '...');

            return $spc;

        } catch (\Exception $e) {
            Log::error('iOS-like SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create SPC with embedded certificate (as per FairPlay spec)
     */
    private function createSPCWithCertificate($assetId, $certificate)
    {
        try {
            if (!$certificate) {
                Log::warning('No certificate provided for SPC with certificate');
                return null;
            }

            Log::info('Creating SPC with embedded certificate...');

            // FairPlay SPC format based on Apple's official specification
            // This is the exact format that iOS AVContentKeySession would send

            // Convert asset ID to proper format
            $contentId = str_replace('-', '', $assetId);

            // Try to convert to binary if it's hex, otherwise use as string
            if (ctype_xdigit($contentId) && strlen($contentId) == 32) {
                // It's a hex UUID without dashes
                $contentIdBytes = hex2bin($contentId);
                Log::info('Using hex-decoded content ID');
            } else {
                // Use the original asset ID as string
                $contentIdBytes = $assetId;
                Log::info('Using string content ID');
            }

            // Create the SPC payload exactly as iOS would
            $spc = '';

            // Method 1: Standard iOS format
            // [4-byte length][content_id][certificate]
            $spc .= pack('N', strlen($contentIdBytes)); // Big-endian 32-bit length
            $spc .= $contentIdBytes; // Content ID bytes
            $spc .= $certificate; // DER-encoded certificate

            Log::info('SPC with certificate created. Total length: ' . strlen($spc) . ' bytes');
            Log::info('Content ID length: ' . strlen($contentIdBytes) . ' bytes');
            Log::info('Certificate length: ' . strlen($certificate) . ' bytes');
            Log::info('Content ID hex: ' . bin2hex($contentIdBytes));

            return $spc;

        } catch (\Exception $e) {
            Log::error('SPC with certificate creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create Apple-compliant SPC format (based on Shaka Player issue #4829)
     */
    private function createAppleCompliantSPC($assetId, $certificate)
    {
        try {
            if (!$certificate) {
                return null;
            }

            Log::info('Creating Apple-compliant SPC based on Shaka Player issue #4829...');

            // Convert UUID string to 16-byte binary format
            // Remove dashes and convert hex to binary
            $cleanKid = str_replace('-', '', $assetId);

            if (strlen($cleanKid) === 32 && ctype_xdigit($cleanKid)) {
                // It's a valid hex UUID
                $contentIdBytes = hex2bin($cleanKid);
                Log::info('✅ UUID converted to 16-byte binary format');
            } else {
                Log::error('❌ Invalid UUID format: ' . $assetId);
                return null;
            }

            // Verify we have exactly 16 bytes
            if (strlen($contentIdBytes) !== 16) {
                Log::error('❌ Content ID must be exactly 16 bytes, got: ' . strlen($contentIdBytes));
                return null;
            }

            // Create SPC according to FairPlay specification
            $spc = '';

            // 1. Content ID length (4 bytes, big-endian) - always 16 for UUID
            $spc .= pack('N', 16);

            // 2. Content ID (16 bytes - UUID in binary format)
            $spc .= $contentIdBytes;

            // 3. Certificate (DER format)
            $spc .= $certificate;

            Log::info('✅ Apple-compliant SPC created successfully');
            Log::info('   - Content ID length: 16 bytes (UUID)');
            Log::info('   - Certificate length: ' . strlen($certificate) . ' bytes');
            Log::info('   - Total SPC length: ' . strlen($spc) . ' bytes');
            Log::info('   - Content ID hex: ' . bin2hex($contentIdBytes));

            return $spc;

        } catch (\Exception $e) {
            Log::error('Apple-compliant SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create SPC format based on Shaka Player recommendations
     */
    private function createShakaPlayerSPC($assetId, $certificate)
    {
        try {
            if (!$certificate) {
                return null;
            }

            Log::info('Creating Shaka Player compatible SPC...');

            // Based on Shaka Player issue #4829 recommendations
            // Some servers expect the content ID as a string, not binary

            $contentId = $assetId; // Keep as string

            // Create SPC with string content ID
            $spc = '';

            // 1. Content ID length (4 bytes, big-endian)
            $spc .= pack('N', strlen($contentId));

            // 2. Content ID (as UTF-8 string)
            $spc .= $contentId;

            // 3. Certificate (DER format)
            $spc .= $certificate;

            Log::info('✅ Shaka Player SPC created successfully');
            Log::info('   - Content ID: ' . $contentId . ' (string format)');
            Log::info('   - Content ID length: ' . strlen($contentId) . ' bytes');
            Log::info('   - Certificate length: ' . strlen($certificate) . ' bytes');
            Log::info('   - Total SPC length: ' . strlen($spc) . ' bytes');

            return $spc;

        } catch (\Exception $e) {
            Log::error('Shaka Player SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create iOS-like SPC format (based on real iOS app data)
     */
    private function createCorrectFairPlaySPC($assetId, $certificate)
    {
        try {
            if (!$certificate) {
                return null;
            }

            Log::info('Creating iOS-like FairPlay SPC (based on real iOS data)...');

            // Based on the real iOS SPC that starts with AAAAAQ (length 1)
            // This suggests a completely different format

            $spc = '';

            // Method 1: Try iOS-like format with length 1
            $spc .= pack('N', 1); // Length = 1 (like iOS)
            $spc .= pack('N', 0); // Additional data
            $spc .= pack('N', 0); // Additional data

            // Add some content identifier
            $cleanKid = str_replace('-', '', $assetId);
            if (strlen($cleanKid) === 32 && ctype_xdigit($cleanKid)) {
                $contentIdBytes = hex2bin($cleanKid);
                $spc .= substr($contentIdBytes, 0, 8); // First 8 bytes of KID
            }

            // Add certificate
            $spc .= $certificate;

            Log::info('✅ iOS-like SPC created');
            Log::info('   - Format: iOS-compatible (length=1)');
            Log::info('   - Certificate length: ' . strlen($certificate) . ' bytes');
            Log::info('   - Total SPC length: ' . strlen($spc) . ' bytes');
            Log::info('   - SPC starts with: ' . bin2hex(substr($spc, 0, 16)));

            return $spc;

        } catch (\Exception $e) {
            Log::error('iOS-like SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create minimal iOS SPC (just like the real iOS app)
     */
    private function createMinimalIOSSPC($assetId, $certificate)
    {
        try {
            if (!$certificate) {
                return null;
            }

            Log::info('Creating minimal iOS SPC...');

            // Try to replicate the exact iOS SPC format
            // Real iOS SPC: AAAAAQAAAADDWH/Updeu...
            // This decodes to specific binary pattern

            $spc = '';

            // Start with the iOS pattern
            $spc .= pack('N', 1); // 00000001 (AAAAAQ in base64)
            $spc .= pack('N', 0); // 00000000 (AAAAA in base64)
            $spc .= pack('N', 0); // 00000000 (continuing pattern)

            // Add some identifier based on content
            $cleanKid = str_replace('-', '', $assetId);
            if (strlen($cleanKid) === 32 && ctype_xdigit($cleanKid)) {
                $kidBytes = hex2bin($cleanKid);
                // Use parts of the KID in a specific way
                $spc .= substr($kidBytes, 0, 4);
                $spc .= substr($kidBytes, 8, 4);
                $spc .= substr($kidBytes, 12, 4);
            }

            // Add certificate
            $spc .= $certificate;

            Log::info('✅ Minimal iOS SPC created');
            Log::info('   - Pattern: Mimics real iOS app');
            Log::info('   - Total length: ' . strlen($spc) . ' bytes');
            Log::info('   - Base64 preview: ' . substr(base64_encode($spc), 0, 32) . '...');

            return $spc;

        } catch (\Exception $e) {
            Log::error('Minimal iOS SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create raw iOS SPC (decode the exact iOS SPC data)
     */
    private function createRawIOSSPC($assetId, $certificate)
    {
        try {
            Log::info('Creating RAW iOS SPC (exact iOS data)...');

            // The real iOS SPC from your screenshot:
            // AAAAAQAAAADDWH/Updeu CUhFaCcqjBh4MNt9uhcSOKk3eJUTNnAzwQaDMy OsAs2AvaX

            // Let's decode this and see the pattern
            $realIOSSPC = 'AAAAAQAAAADDWH/UpdeucUhFaCcqjBh4MNt9uhcSOKk3eJUTNnAzwQaDMyOsAs2AvaX';

            try {
                $decodedSPC = base64_decode($realIOSSPC);
                Log::info('Real iOS SPC decoded length: ' . strlen($decodedSPC) . ' bytes');
                Log::info('Real iOS SPC hex: ' . bin2hex($decodedSPC));

                // Analyze the pattern
                $pattern = substr($decodedSPC, 0, 16);
                Log::info('iOS SPC pattern (first 16 bytes): ' . bin2hex($pattern));

            } catch (\Exception $e) {
                Log::warning('Could not decode real iOS SPC: ' . $e->getMessage());
            }

            // Create our own SPC based on the iOS pattern
            $spc = '';

            // Try to replicate the iOS pattern exactly
            $spc .= pack('N', 1); // 00000001 (AAAAAQ)
            $spc .= pack('N', 0); // 00000000 (AAAAA)
            $spc .= pack('N', 0); // 00000000 (continuing)

            // Add some content based on our asset
            $cleanKid = str_replace('-', '', $assetId);
            if (strlen($cleanKid) === 32 && ctype_xdigit($cleanKid)) {
                $kidBytes = hex2bin($cleanKid);

                // Use the KID in a way that might match iOS pattern
                $spc .= substr($kidBytes, 0, 8); // First 8 bytes
                $spc .= substr($kidBytes, 8, 8); // Next 8 bytes
            }

            // Add certificate
            $spc .= $certificate;

            Log::info('✅ Raw iOS SPC created');
            Log::info('   - Mimics exact iOS pattern');
            Log::info('   - Total length: ' . strlen($spc) . ' bytes');
            Log::info('   - Our SPC hex (first 32 bytes): ' . bin2hex(substr($spc, 0, 32)));
            Log::info('   - Our SPC base64 preview: ' . substr(base64_encode($spc), 0, 32) . '...');

            return $spc;

        } catch (\Exception $e) {
            Log::error('Raw iOS SPC creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract the correct Asset ID from DRM data
     */
    private function extractAssetIdFromDRMData($drmData, $fallbackContentId)
    {
        try {
            Log::info('Extracting Asset ID from DRM data...');
            Log::info('DRM data keys: ' . implode(', ', array_keys($drmData)));

            // First priority: Extract KID from license URL (most reliable)
            if (isset($drmData['license_url'])) {
                $licenseUrl = $drmData['license_url'];
                Log::info('Checking license URL for KID: ' . substr($licenseUrl, 0, 100) . '...');

                // Extract KID parameter from URL
                if (preg_match('/[?&]KID=([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i', $licenseUrl, $matches)) {
                    $kid = $matches[1];
                    Log::info("✅ Found KID in license URL: " . $kid);
                    Log::info("✅ This KID will be used as the Asset ID for SPC creation");
                    return $kid;
                }

                // Try other KID patterns
                if (preg_match('/KID=([^&]+)/', $licenseUrl, $matches)) {
                    $kid = urldecode($matches[1]);
                    Log::info("Found KID parameter in URL: " . $kid);
                    Log::info("✅ This KID will be used as the Asset ID for SPC creation");
                    return $kid;
                }
            }

            // Second priority: Try different possible keys for the asset ID
            $possibleKeys = ['kid', 'key_id', 'content_id', 'asset_id', 'guid', 'id'];

            foreach ($possibleKeys as $key) {
                if (isset($drmData[$key]) && !empty($drmData[$key])) {
                    Log::info("Found Asset ID in '{$key}': " . $drmData[$key]);
                    return $drmData[$key];
                }
            }

            // Third priority: Extract from any URL in the DRM data
            foreach ($drmData as $key => $value) {
                if (is_string($value) && strpos($value, 'http') === 0) {
                    // Try to extract UUID from URL
                    if (preg_match('/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i', $value, $matches)) {
                        Log::info("Extracted UUID from URL in '{$key}': " . $matches[1]);
                        return $matches[1];
                    }
                }
            }

            // Fourth priority: Use the content_id from request
            if (!empty($fallbackContentId)) {
                Log::info('Using fallback content ID: ' . $fallbackContentId);
                return $fallbackContentId;
            }

            Log::warning('Could not extract Asset ID from DRM data, using fallback');
            return $fallbackContentId ?: 'unknown';

        } catch (\Exception $e) {
            Log::error('Asset ID extraction failed: ' . $e->getMessage());
            return $fallbackContentId ?: 'unknown';
        }
    }

    /**
     * Try multiple SPC formats until one works
     */
    private function tryMultipleSPCFormats($licenseUrl, $assetId, $certificate = null)
    {
        try {
            Log::info('Trying multiple SPC formats for Asset ID: ' . $assetId);

            // Create different SPC formats (prioritized by compatibility)
            $spcFormats = [
                'raw_ios_spc' => $this->createRawIOSSPC($assetId, $certificate),
                'minimal_ios' => $this->createMinimalIOSSPC($assetId, $certificate),
                'correct_fairplay' => $this->createCorrectFairPlaySPC($assetId, $certificate),
                'apple_compliant_uuid' => $this->createAppleCompliantSPC($assetId, $certificate),
                'shaka_player_format' => $this->createShakaPlayerSPC($assetId, $certificate),
                'with_certificate' => $this->createSPCWithCertificate($assetId, $certificate),
                'minimal' => $this->createMinimalSPC($assetId)
            ];

            foreach ($spcFormats as $formatName => $spc) {
                if (!$spc) {
                    Log::warning("Skipping {$formatName} format - creation failed");
                    continue;
                }

                Log::info("Trying {$formatName} SPC format...");
                Log::info("SPC length: " . strlen($spc) . " bytes");

                // Try sending this SPC format
                $response = $this->sendSPCToLicenseServer($licenseUrl, $spc, $formatName);
                if ($response) {
                    Log::info("✅ {$formatName} SPC format worked!");
                    return $response;
                }

                Log::warning("❌ {$formatName} SPC format failed");
            }

            Log::error('All SPC formats failed');
            return null;

        } catch (\Exception $e) {
            Log::error('Multiple SPC formats attempt failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send SPC to license server (iOS-compatible format)
     */
    private function sendSPCToLicenseServer($licenseUrl, $spc, $formatName = 'unknown')
    {
        try {
            Log::info("Sending {$formatName} SPC to license server (iOS-compatible)...");
            Log::info('SPC length: ' . strlen($spc) . ' bytes');
            Log::info('SPC hex preview: ' . substr(bin2hex($spc), 0, 64) . '...');

            // Convert SPC to base64 for form data (as iOS does)
            $spcBase64 = base64_encode($spc);
            Log::info('SPC base64 length: ' . strlen($spcBase64) . ' characters');
            Log::info('SPC base64 preview: ' . substr($spcBase64, 0, 64) . '...');

            // Send as form data like iOS app does (exact iOS format)
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Accept' => 'application/json',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Accept-Language' => 'en-US,en;q=0.9',
                    'Connection' => 'keep-alive',
                    'User-Agent' => 'Shahid/4351 CFNetwork/3860.100.1 Darwin/25.0.0',
                    // iOS-specific headers
                    'X-Apple-Device-Model' => 'iPhone15,2',
                    'X-Apple-OS-Version' => '18.6',
                    'X-Playback-Session-Id' => uniqid('fps_', true)
                ])
                ->asForm()
                ->post($licenseUrl, [
                    'spc' => $spcBase64
                ]);

            Log::info('License server response status: ' . $response->status());
            Log::info('License server response headers: ' . json_encode($response->headers()));

            if ($response->successful()) {
                Log::info('✅ License server responded successfully!');
                Log::info('License response length: ' . strlen($response->body()) . ' bytes');
                Log::info('License response preview: ' . substr($response->body(), 0, 200) . '...');

                // Parse JSON response (Uplynk format)
                try {
                    $responseData = json_decode($response->body(), true);
                    if (isset($responseData['ckc'])) {
                        Log::info('✅ CKC (Content Key Context) found in response!');
                        Log::info('CKC length: ' . strlen($responseData['ckc']) . ' characters');

                        // Decode the CKC to get the actual key
                        $ckc = base64_decode($responseData['ckc']);
                        Log::info('✅ CKC decoded successfully. Length: ' . strlen($ckc) . ' bytes');
                        Log::info('CKC hex preview: ' . substr(bin2hex($ckc), 0, 64) . '...');

                        return $responseData; // Return the full response with CKC
                    } else {
                        Log::warning('No CKC found in JSON response');
                        return $response->body();
                    }
                } catch (\Exception $e) {
                    Log::warning('Could not parse JSON response: ' . $e->getMessage());
                    return $response->body();
                }
            }

            Log::error('License server response: ' . $response->status() . ' - ' . $response->body());

            // Try alternative approaches
            if ($response->status() >= 400) {
                Log::info('Form data failed, trying alternative methods...');

                // Method 1: Raw binary
                $binaryResult = $this->sendRawBinarySPC($licenseUrl, $spc, $formatName);
                if ($binaryResult) {
                    return $binaryResult;
                }

                // Method 2: Different form field name
                $altFormResult = $this->sendAlternativeFormSPC($licenseUrl, $spc, $formatName);
                if ($altFormResult) {
                    return $altFormResult;
                }

                // Method 3: JSON payload
                $jsonResult = $this->sendJSONSPC($licenseUrl, $spc, $formatName);
                if ($jsonResult) {
                    return $jsonResult;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error('License server request failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send raw binary SPC (fallback method)
     */
    private function sendRawBinarySPC($licenseUrl, $spc, $formatName)
    {
        try {
            Log::info("Trying raw binary SPC for {$formatName}...");

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/octet-stream',
                    'Accept' => 'application/octet-stream',
                    'User-Agent' => 'AVFoundation/1.0',
                    'X-Playback-Session-Id' => uniqid('fps_', true)
                ])
                ->withBody($spc, 'application/octet-stream')
                ->post($licenseUrl);

            Log::info('Raw binary response status: ' . $response->status());

            if ($response->successful()) {
                Log::info('✅ Raw binary SPC worked!');
                return $response->body();
            }

            Log::warning('❌ Raw binary SPC also failed: ' . $response->status());
            return null;

        } catch (\Exception $e) {
            Log::error('Raw binary SPC failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send SPC with alternative form field names
     */
    private function sendAlternativeFormSPC($licenseUrl, $spc, $formatName)
    {
        try {
            Log::info("Trying alternative form field names for {$formatName}...");

            $spcBase64 = base64_encode($spc);

            // Try different field names that might be expected
            $fieldNames = ['spc', 'payload', 'data', 'request', 'fairplay_spc', 'content'];

            foreach ($fieldNames as $fieldName) {
                Log::info("Trying field name: {$fieldName}");

                $response = Http::timeout(30)
                    ->withHeaders([
                        'Content-Type' => 'application/x-www-form-urlencoded',
                        'Accept' => 'application/json',
                        'User-Agent' => 'Shahid/4351 CFNetwork/3860.100.1 Darwin/25.0.0'
                    ])
                    ->asForm()
                    ->post($licenseUrl, [
                        $fieldName => $spcBase64
                    ]);

                if ($response->successful()) {
                    Log::info("✅ Alternative form field '{$fieldName}' worked!");
                    return $response->body();
                }

                Log::info("Field '{$fieldName}' failed: " . $response->status());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Alternative form SPC failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send SPC as JSON payload
     */
    private function sendJSONSPC($licenseUrl, $spc, $formatName)
    {
        try {
            Log::info("Trying JSON payload for {$formatName}...");

            $spcBase64 = base64_encode($spc);

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => 'Shahid/4351 CFNetwork/3860.100.1 Darwin/25.0.0'
                ])
                ->post($licenseUrl, [
                    'spc' => $spcBase64
                ]);

            if ($response->successful()) {
                Log::info('✅ JSON SPC worked!');
                return $response->body();
            }

            Log::info('JSON SPC failed: ' . $response->status());
            return null;

        } catch (\Exception $e) {
            Log::error('JSON SPC failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Try alternative SPC format
     */
    private function tryAlternativeSPCFormat($licenseUrl, $originalSpc)
    {
        try {
            Log::info('Trying alternative SPC formats...');

            // Get the KID from the current context (you might need to pass this)
            // For now, let's try different encoding methods

            $alternatives = [
                'base64' => [
                    'data' => base64_encode($originalSpc),
                    'content_type' => 'text/plain',
                    'description' => 'Base64 encoded SPC'
                ],
                'url_encoded' => [
                    'data' => urlencode(base64_encode($originalSpc)),
                    'content_type' => 'application/x-www-form-urlencoded',
                    'description' => 'URL encoded Base64 SPC'
                ],
                'json_wrapped' => [
                    'data' => json_encode(['spc' => base64_encode($originalSpc)]),
                    'content_type' => 'application/json',
                    'description' => 'JSON wrapped SPC'
                ],
                'raw_hex' => [
                    'data' => bin2hex($originalSpc),
                    'content_type' => 'text/plain',
                    'description' => 'Hex encoded SPC'
                ]
            ];

            foreach ($alternatives as $format => $config) {
                Log::info("Trying {$config['description']}...");

                $response = Http::timeout(30)
                    ->withHeaders([
                        'Content-Type' => $config['content_type'],
                        'Accept' => 'application/octet-stream',
                        'User-Agent' => 'AVFoundation/1.0',
                        'X-Playback-Session-Id' => uniqid('fps_', true)
                    ])
                    ->withBody($config['data'], $config['content_type'])
                    ->post($licenseUrl);

                Log::info("Response status for {$format}: " . $response->status());

                if ($response->successful()) {
                    Log::info("✅ {$config['description']} worked!");
                    return $response->body();
                }

                Log::warning("❌ {$config['description']} failed: " . $response->status());
                if ($response->body()) {
                    Log::info("Response body: " . substr($response->body(), 0, 200));
                }
            }

            Log::error('All alternative SPC formats failed');
            return null;

        } catch (\Exception $e) {
            Log::error('Alternative SPC format failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract key from license response (Uplynk format)
     */
    private function extractKeyFromLicenseResponse($licenseResponse)
    {
        try {
            Log::info('Extracting key from license response (Uplynk format)...');

            // Handle array response (from our JSON parsing)
            if (is_array($licenseResponse)) {
                Log::info('License response is already parsed array');
                $jsonData = $licenseResponse;
            } else {
                Log::info('License response length: ' . strlen($licenseResponse) . ' bytes');
                // Try to parse as JSON first (FairPlay often returns JSON with CKC)
                $jsonData = json_decode($licenseResponse, true);
            }

            if ($jsonData && isset($jsonData['ckc'])) {
                Log::info('✅ Found CKC (Content Key Context) in JSON response');
                $ckc = $jsonData['ckc'];

                // Decode URL-encoded characters
                $ckc = str_replace('\u002B', '+', $ckc);
                $ckc = str_replace('\u002F', '/', $ckc);

                Log::info('CKC length: ' . strlen($ckc) . ' characters');
                Log::info('CKC preview: ' . substr($ckc, 0, 50) . '...');

                // Decode the CKC to extract the actual content key
                try {
                    $ckcBinary = base64_decode($ckc);
                    Log::info('✅ CKC decoded to binary. Length: ' . strlen($ckcBinary) . ' bytes');
                    Log::info('CKC binary hex preview: ' . substr(bin2hex($ckcBinary), 0, 64) . '...');

                    // Try to extract the actual content key from CKC
                    $contentKey = $this->extractContentKeyFromCKC($ckcBinary);

                    return [
                        'type' => 'FAIRPLAY_CKC',
                        'method' => 'uplynk_json_format',
                        'ckc' => $ckc,
                        'key' => $contentKey ?: $ckc,
                        'format' => 'base64',
                        'description' => 'FairPlay Content Key Context - Use this directly with AVContentKeySession'
                    ];

                } catch (\Exception $e) {
                    Log::warning('Could not decode CKC: ' . $e->getMessage());
                    return [
                        'type' => 'FAIRPLAY_CKC_RAW',
                        'method' => 'uplynk_json_format',
                        'ckc' => $ckc,
                        'key' => $ckc,
                        'format' => 'base64',
                        'description' => 'FairPlay CKC (raw) - Use this directly with AVContentKeySession'
                    ];
                }
            }

            // Handle string response
            if (is_string($licenseResponse)) {

            // If not JSON, try to parse as binary
            $hex = bin2hex($licenseResponse);
            Log::info('License response hex preview: ' . substr($hex, 0, 100) . '...');

            // Look for FairPlay magic bytes or patterns
            if (strpos($hex, '464b4353') !== false) { // 'FKCS' in hex
                Log::info('Found FairPlay Key Container signature');
                // Extract key from binary format
                return $this->extractKeyFromBinaryResponse($licenseResponse);
            }

            // Look for 32-character hex strings (128-bit keys)
            if (preg_match('/([a-f0-9]{32})/i', $hex, $matches)) {
                Log::info('Found potential 128-bit key in hex format');
                return [
                    'type' => 'HEX_KEY',
                    'key' => strtoupper($matches[1]),
                    'format' => 'hex',
                    'description' => 'Extracted 128-bit key from binary response'
                ];
            }

            // Fallback: return the raw response for manual inspection
            Log::warning('Could not parse license response, returning raw data');
            return [
                'type' => 'RAW_RESPONSE',
                'key' => base64_encode($licenseResponse),
                'format' => 'base64',
                'description' => 'Raw license response - may need manual parsing'
            ];
            }

        } catch (\Exception $e) {
            Log::error('Key extraction from license failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract content key from CKC (Content Key Context)
     */
    private function extractContentKeyFromCKC($ckcBinary)
    {
        try {
            Log::info('Attempting to extract content key from CKC...');

            // CKC is an encrypted container that contains the actual content key
            // The structure varies by implementation, but we can try to find patterns

            $hex = bin2hex($ckcBinary);
            Log::info('CKC hex length: ' . strlen($hex) . ' characters');

            // Look for potential key patterns (128-bit = 32 hex chars, 256-bit = 64 hex chars)
            if (preg_match('/([a-f0-9]{32})/i', $hex, $matches)) {
                Log::info('Found potential 128-bit key in CKC');
                return strtoupper($matches[1]);
            }

            if (preg_match('/([a-f0-9]{64})/i', $hex, $matches)) {
                Log::info('Found potential 256-bit key in CKC');
                return strtoupper($matches[1]);
            }

            // Look for FairPlay specific patterns
            if (strpos($hex, '464b4353') !== false) { // 'FKCS' magic
                Log::info('Found FairPlay Key Container signature in CKC');
                // Extract key after the signature
                $pos = strpos($hex, '464b4353') + 8;
                if ($pos + 32 <= strlen($hex)) {
                    $key = substr($hex, $pos, 32);
                    Log::info('Extracted key after FKCS signature');
                    return strtoupper($key);
                }
            }

            Log::info('Could not extract specific key from CKC, returning null');
            return null;

        } catch (\Exception $e) {
            Log::error('CKC key extraction failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract key from binary FairPlay response
     */
    private function extractKeyFromBinaryResponse($binaryResponse)
    {
        try {
            // This would contain the actual FairPlay binary parsing logic
            // For now, return the binary data encoded
            return [
                'type' => 'BINARY_KEY',
                'key' => base64_encode($binaryResponse),
                'format' => 'base64',
                'description' => 'Binary FairPlay key container'
            ];
        } catch (\Exception $e) {
            Log::error('Binary key extraction failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Health check for FairPlay system
     */
    public function healthCheck()
    {
        try {
            $health = [
                'fairplay_support' => true,
                'shahid_drm_service' => class_exists('App\Services\ShahidDRM'),
                'device_file_exists' => $this->shahidDRM->hasDeviceFile(),
                'timestamp' => now()->toISOString(),
                'version' => '1.0.0'
            ];

            return response()->json([
                'success' => true,
                'health' => $health
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Extract real key from license server OR provide player-ready config
     */
    public function extractRealKey(Request $request)
    {
        try {
            Log::info('🔑 Starting real key extraction...');

            $contentId = $request->input('content_id');
            Log::info('Content ID: ' . $contentId);

            // Get DRM data first
            $drmResult = $this->shahidDRM->getFairPlayDRMInfo($contentId, $request->input('country', 'EG'));

            if (!$drmResult['success']) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to get DRM data'
                ]);
            }

            $drmData = $drmResult;

            // Try Method 1: Extract real key from license server
            Log::info('🔄 Method 1: Trying to extract real key from license server...');
            $realKeyData = $this->getRealKeyFromLicenseServer($drmData, $contentId);

            if ($realKeyData) {
                Log::info('✅ Method 1 succeeded: Real key extracted!');
                return response()->json([
                    'success' => true,
                    'method' => 'real_key_extraction',
                    'data' => $realKeyData
                ]);
            }

            // Method 2: Provide player-ready configuration (FALLBACK)
            Log::info('🔄 Method 1 failed. Trying Method 2: Player-ready configuration...');
            $playerConfig = $this->createPlayerReadyConfig($drmData, $contentId);

            if ($playerConfig) {
                Log::info('✅ Method 2 succeeded: Player-ready config created!');
                return response()->json([
                    'success' => true,
                    'method' => 'player_ready_config',
                    'data' => $playerConfig
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => 'Both methods failed'
            ]);

        } catch (\Exception $e) {
            Log::error('Real key extraction failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Real key extraction failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create player-ready configuration (Method 2)
     */
    private function createPlayerReadyConfig($drmData, $contentId)
    {
        try {
            Log::info('🎯 Creating player-ready FairPlay configuration...');

            $certificateUrl = $drmData['certificate_url'];
            $licenseUrl = $drmData['license_url'];

            // Extract KID from license URL
            $kid = $this->extractAssetIdFromDRMData($drmData, $contentId);

            // Get certificate
            $certificate = $this->getFairPlayCertificate($certificateUrl);
            if (!$certificate) {
                Log::error('Failed to get certificate for player config');
                return null;
            }

            // Extract additional FairPlay parameters from license URL
            $fairplayParams = $this->extractFairPlayParams($licenseUrl);

            // Create player-ready configuration
            $config = [
                'key' => 'PLAYER_READY_CONFIG',
                'key_type' => 'PLAYER_CONFIG',
                'key_format' => 'json',
                'key_description' => 'Ready-to-use FairPlay configuration for video players',

                // FairPlay configuration
                'fairplay_config' => [
                    'certificate_url' => $certificateUrl,
                    'license_url' => $licenseUrl,
                    'certificate_base64' => base64_encode($certificate),
                    'kid' => $kid,
                    'content_id' => $contentId,
                    'brand_guid' => $fairplayParams['brand_guid'] ?? null,
                    'iv' => $fairplayParams['iv'] ?? '1F3A1D7CE87A4CD3846038FCF4D05F82' // Fixed IV for FairPlay
                ],

                // Player-specific configurations
                'video_js_config' => [
                    'keySystems' => [
                        'com.apple.fps.1_0' => [
                            'certificateUri' => $certificateUrl,
                            'licenseUri' => $licenseUrl
                        ]
                    ]
                ],

                'shaka_player_config' => [
                    'drm' => [
                        'servers' => [
                            'com.apple.fps.1_0' => $licenseUrl
                        ],
                        'advanced' => [
                            'com.apple.fps.1_0' => [
                                'serverCertificateUri' => $certificateUrl
                            ]
                        ]
                    ]
                ],

                'hls_js_config' => [
                    'emeEnabled' => true,
                    'drmSystems' => [
                        'com.apple.fps.1_0' => [
                            'licenseUrl' => $licenseUrl,
                            'serverCertificate' => base64_encode($certificate)
                        ]
                    ]
                ],

                // Raw data for custom implementations
                'raw_data' => [
                    'certificate' => base64_encode($certificate),
                    'license_url' => $licenseUrl,
                    'kid' => $kid,
                    'brand_guid' => $fairplayParams['brand_guid'] ?? null,
                    'iv' => $fairplayParams['iv'] ?? '1F3A1D7CE87A4CD3846038FCF4D05F82'
                ],

                // HLS manifest ready format
                'hls_manifest_key' => [
                    'method' => 'SAMPLE-AES',
                    'uri' => $licenseUrl,
                    'keyformat' => 'com.apple.streamingkeydelivery',
                    'keyformatversions' => '1',
                    'iv' => $fairplayParams['iv'] ? '0x' . strtoupper($fairplayParams['iv']) : '0x1F3A1D7CE87A4CD3846038FCF4D05F82'
                ]
            ];

            Log::info('✅ Player-ready config created successfully');
            Log::info('   - Certificate URL: ' . $certificateUrl);
            Log::info('   - License URL: ' . substr($licenseUrl, 0, 100) . '...');
            Log::info('   - KID: ' . $kid);
            Log::info('   - Certificate size: ' . strlen($certificate) . ' bytes');
            Log::info('   - Brand GUID: ' . ($fairplayParams['brand_guid'] ?? 'N/A'));
            Log::info('   - IV: ' . ($fairplayParams['iv'] ?? '1F3A1D7CE87A4CD3846038FCF4D05F82'));

            return $config;

        } catch (\Exception $e) {
            Log::error('Player-ready config creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract FairPlay parameters from license URL
     */
    private function extractFairPlayParams($licenseUrl)
    {
        try {
            $params = [];

            // Extract BrandGuid
            if (preg_match('/BrandGuid=([^&]+)/', $licenseUrl, $matches)) {
                $params['brand_guid'] = urldecode($matches[1]);
                Log::info('Extracted BrandGuid: ' . $params['brand_guid']);
            }

            // Extract IV
            if (preg_match('/IV=([^&]+)/', $licenseUrl, $matches)) {
                $params['iv'] = urldecode($matches[1]);
                Log::info('Extracted IV: ' . $params['iv']);
            }

            // Extract KID (already done elsewhere but good to have)
            if (preg_match('/KID=([^&]+)/', $licenseUrl, $matches)) {
                $params['kid'] = urldecode($matches[1]);
                Log::info('Extracted KID: ' . $params['kid']);
            }

            return $params;

        } catch (\Exception $e) {
            Log::error('FairPlay params extraction failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Convert PEM certificate to DER format
     */
    private function pemToDer($pemCertificate)
    {
        try {
            // Remove PEM headers and footers
            $pemCertificate = str_replace(['-----BEGIN CERTIFICATE-----', '-----END CERTIFICATE-----'], '', $pemCertificate);
            $pemCertificate = str_replace(["\r", "\n", " "], '', $pemCertificate);

            // Decode base64
            return base64_decode($pemCertificate);

        } catch (\Exception $e) {
            Log::error('PEM to DER conversion failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get player data for testing in the player
     */
    public function getPlayerData($assetId)
    {
        try {
            Log::info("Getting player data for asset: {$assetId}");

            // Get complete FairPlay setup
            $setupData = $this->createCompleteSetup(new \Illuminate\Http\Request(['content_id' => $assetId]));

            if (!$setupData || !$setupData->getData()) {
                return response()->json(['error' => 'Could not get FairPlay setup'], 500);
            }

            $data = $setupData->getData(true);

            // Check if setup failed
            if (!$data['success']) {
                Log::error('FairPlay setup failed: ' . ($data['error'] ?? 'Unknown error'));
                return response()->json(['error' => 'FairPlay setup failed: ' . ($data['error'] ?? 'Unknown error')], 500);
            }

            // Check if data is wrapped in 'data' key
            $setupInfo = isset($data['data']) ? $data['data'] : $data;

            if (!isset($setupInfo['player_config'])) {
                Log::error('Player config not found. Available keys: ' . implode(', ', array_keys($setupInfo)));
                Log::error('Full setup data: ' . json_encode($setupInfo, JSON_PRETTY_PRINT));
                return response()->json(['error' => 'Player config not found'], 500);
            }

            $playerConfig = $setupInfo['player_config'];

            // Extract DRM info from the correct location
            $drmInfo = $setupInfo['drm_info'] ?? [];
            $fairplayConfig = $playerConfig['drm']['fairplay'] ?? [];

            // Format data for the player
            $playerData = [
                'title' => "Shahid Asset {$assetId}",
                'hls' => $setupInfo['hls_url'] ?? '',  // HLS URL is in setupInfo, not playerConfig
                'type' => 'hls',
                'drm' => [
                    'fairplay' => [
                        'certificateUrl' => $fairplayConfig['certificateUrl'] ?? ($drmInfo['certificate_url'] ?? ''),
                        'licenseUrl' => $fairplayConfig['licenseUrl'] ?? ($drmInfo['license_url'] ?? ''),
                        'keyId' => $drmInfo['kid'] ?? '',
                        'iv' => $drmInfo['iv'] ?? '',
                        'brandGuid' => $drmInfo['brand_guid'] ?? ''
                    ]
                ],
                'raw_config' => $playerConfig,
                'drm_info' => $drmInfo,  // Add DRM info for debugging
                'setup_info' => $setupInfo  // Add full setup info for debugging
            ];

            Log::info('✅ Player data prepared successfully');
            return response()->json($playerData);

        } catch (\Exception $e) {
            Log::error('Player data preparation failed: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Test player with FairPlay data
     */
    public function testPlayer($assetId)
    {
        try {
            Log::info("Opening test player for asset: {$assetId}");

            // Get player data
            $playerDataResponse = $this->getPlayerData($assetId);
            $playerData = $playerDataResponse->getData(true);

            if (isset($playerData['error'])) {
                return response()->json(['error' => $playerData['error']], 500);
            }

            // Build player URL with parameters
            $playerUrl = url('/player/shahid_player.html') . '?' . http_build_query([
                'title' => $playerData['title'],
                'hls' => $playerData['hls'],
                'type' => 'hls',
                'keyId' => $playerData['drm']['fairplay']['keyId'],
                'licenseUrl' => $playerData['drm']['fairplay']['licenseUrl'],
                'certificateUrl' => $playerData['drm']['fairplay']['certificateUrl'],
                'iv' => $playerData['drm']['fairplay']['iv'],
                'brandGuid' => $playerData['drm']['fairplay']['brandGuid']
            ]);

            Log::info('✅ Player URL generated: ' . substr($playerUrl, 0, 100) . '...');

            return response()->json([
                'player_url' => $playerUrl,
                'player_data' => $playerData,
                'instructions' => [
                    '1. Open the player URL in a browser',
                    '2. The player will automatically load with FairPlay configuration',
                    '3. Check browser console for DRM logs',
                    '4. Test playback on iOS Safari for best results'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Test player preparation failed: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
