@extends('admin.layouts.app')

@section('title', 'Service Unavailable - 503')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <div class="error-icon mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #34495e, #2c3e50); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-wrench text-white" style="font-size: 60px;"></i>
                        </div>
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-secondary mb-3">503</h1>
                    
                    <!-- Error Title -->
                    <h2 class="h3 text-dark mb-3">Service Temporarily Unavailable</h2>
                    
                    <!-- Error Message -->
                    <p class="text-muted mb-4 lead">
                        We're currently performing scheduled maintenance.
                        <br>The service will be back online shortly.
                    </p>

                    <!-- Maintenance Info -->
                    <div class="alert alert-light border-0 mb-4" style="background-color: #f8f9fa;">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-tools text-secondary fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">Maintenance in Progress</h6>
                                <ul class="mb-0 text-muted small">
                                    <li>System updates and improvements</li>
                                    <li>Database optimization</li>
                                    <li>Security enhancements</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Estimated Time -->
                    <div class="alert alert-info border-0 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-clock text-info fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">Estimated Completion</h6>
                                <p class="mb-0 text-muted small">
                                    We expect the maintenance to be completed within the next 30 minutes.
                                    Thank you for your patience.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Maintenance Progress</h6>
                        <div class="progress" style="height: 8px; border-radius: 10px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-info" 
                                 role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <small class="text-muted">65% Complete</small>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <button onclick="location.reload()" class="btn btn-primary px-4">
                            <i class="fas fa-sync-alt me-2"></i>Check Again
                        </button>
                        <button onclick="history.back()" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </button>
                    </div>

                    <!-- Auto Refresh -->
                    <div class="mt-4 pt-4 border-top">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="spinner-border spinner-border-sm text-info me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <small class="text-muted">
                                Auto-checking in <span id="countdown">30</span> seconds...
                            </small>
                        </div>
                        <div class="mt-2">
                            <button onclick="cancelAutoRefresh()" class="btn btn-link btn-sm text-muted">
                                Cancel auto-check
                            </button>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="mt-4">
                        <small class="text-muted">
                            For urgent matters, please contact: 
                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </small>
                    </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: spin 4s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}



.btn {
    border-radius: 8px;
    font-weight: 500;
}

.progress {
    background-color: #e9ecef;
}
</style>

<script>
let countdownTimer;
let countdownValue = 30;

function startCountdown() {
    const countdownElement = document.getElementById('countdown');
    
    countdownTimer = setInterval(function() {
        countdownValue--;
        countdownElement.textContent = countdownValue;
        
        if (countdownValue <= 0) {
            location.reload();
        }
    }, 1000);
}

function cancelAutoRefresh() {
    if (countdownTimer) {
        clearInterval(countdownTimer);
        document.querySelector('.spinner-border').style.display = 'none';
        document.querySelector('.mt-4.pt-4.border-top').innerHTML = 
            '<small class="text-muted">Auto-check cancelled</small>';
    }
}

// Start countdown when page loads
document.addEventListener('DOMContentLoaded', function() {
    startCountdown();
});
</script>
@endsection
