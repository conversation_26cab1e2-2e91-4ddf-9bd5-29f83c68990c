<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FairPlay Static Test</title>
    <style>
        html, body, #player { height: 100%; margin: 0; background: #000; }
        #debug-console { 
            position: fixed; 
            bottom: 0; 
            left: 0; 
            right: 0; 
            height: 200px; 
            background: rgba(0,0,0,0.8); 
            color: #0f0; 
            font-family: monospace; 
            font-size: 10px; 
            overflow-y: scroll; 
            padding: 5px; 
            z-index: 9999;
            border-top: 1px solid #333;
        }
        #debug-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
        }
        .debug-btn {
            background: rgba(0,0,0,0.7);
            color: white;
            border: 1px solid #333;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            display: inline-block;
        }
        .debug-btn:active {
            background: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div id="player" data-skin-url="/player/tod_skin.css"></div>
    <button id="playStreamButton" style="display:none"></button>
    
    <!-- Debug Controls -->
    <div id="debug-controls">
        <button class="debug-btn" onclick="copyDebugLogs()">📋 نسخ Logs</button>
        <button class="debug-btn" onclick="copyImportantInfo()">🔑 نسخ FairPlay</button>
        <button class="debug-btn" onclick="clearDebugConsole()">🗑️ مسح</button>
        <button class="debug-btn" onclick="toggleDebugConsole()">👁️ إخفاء/إظهار</button>
    </div>
    
    <div id="debug-console"></div>

    <script src="/player/js/jwplayer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <script>
        // JW Player initialization for FairPlay
        if (typeof jwplayer !== 'undefined') {
            try {
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.base = window.location.origin + '/player/js/';
                if (jwplayer.utils && jwplayer.utils.repo) {
                    jwplayer.utils.repo = window.location.origin + '/player/js/';
                }
                // Use a valid JW Player key (this is a demo key, replace with your own)
                jwplayer.key = 'Z8lq0BAJBEu//qi4oQ7e5kmmCB4pOlIsjYLVL95r9jE=';
                jwplayer.defaults.analytics = false;
                jwplayer.defaults.advertising = false;
                jwplayer.defaults.related = false;
                jwplayer.defaults.sharing = false;
                console.log('🔧 JW Player initialized for FairPlay');
            } catch(e) { 
                console.warn('JW Player init warning:', e.message); 
            }
        }
    </script>
    <script>
        // قراءة البيانات من URL parameters أو sessionStorage (إذا تم تمريرها من صفحة الاختبار)
        function getTestData() {
            // جرب قراءة من URL parameters أولاً
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('hls')) {
                return {
                    title: urlParams.get('title') || 'Shahid Asset (من الاختبار)',
                    hls: urlParams.get('hls'),
                    certificateUrl: urlParams.get('certificateUrl') || 'https://shahid.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
                    licenseUrl: urlParams.get('licenseUrl') || '',
                    kid: urlParams.get('keyId') || urlParams.get('kid') || '827802f4-e896-4769-ba77-f1e656ed1bd4',
                    iv: urlParams.get('iv') || '1f3a1d7ce87a4cd3846038fcf4d05f82'
                };
            }
            
            // جرب قراءة من sessionStorage
            try {
                const stored = sessionStorage.getItem('fairplay_extracted_data');
                if (stored) {
                    const parsed = JSON.parse(stored);
                    console.log('📦 تم العثور على بيانات مستخرجة في sessionStorage:', parsed);
                    return parsed;
                }
            } catch (e) {
                console.warn('فشل في قراءة البيانات من sessionStorage:', e.message);
            }
            
            // البيانات الافتراضية (ثابتة للاختبار)
            return {
                title: 'Shahid Asset 49923436792142',
                hls: 'https://mbcvod-enc.edgenextcdn.net/out/v1/b9ab8e5fc3d44bb2a28ab3c9cb8b2ae7/de5f5ce1bd4d43eb9896d42d0de1dab6/75166141e593471998b1061968ca6824/index.m3u8?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264',
                certificateUrl: 'https://shahid.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
                licenseUrl: 'https://shahid.la.drm.cloud/acquire-license/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4&KID=827802f4-e896-4769-ba77-f1e656ed1bd4&IV=1f3a1d7ce87a4cd3846038fcf4d05f82&UserToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjEuNzU1NzM2MzNFOSwiZHJtVG9rZW5JbmZvIjp7ImV4cCI6IjIwMjUtMDgtMjFUMDA6MzI6MTAuMDAwMTU2Iiwia2lkIjpbIioiXSwicCI6eyJwZXJzIjpmYWxzZSwiZWQiOiIyMDI1LTA4LTIxVDAwOjMyOjEwLjAwMDE1NiIsImV4YyI6eyJXaWRldmluZUNhblJlbmV3IjpmYWxzZSwiRmFpcnBsYXlSZW50YWxEdXJhdGlvblNlY29uZHMiOjg2MDAwLCJGYWlycGxheUxlYXNlRHVyYXRpb25TZWNvbmRzIjo4NjAwMH19LCJ3aWRldmluZSI6eyJkaXNhYmxlX2FuYWxvZyI6dHJ1ZSwiaGRjcCI6IkhEQ1BfVjJfMiIsImNnbXMiOiJDT1BZX05FVkVSIn0sImZhaXJwbGF5Ijp7ImhkY3AiOnRydWV9LCJwbGF5cmVhZHkiOnsidW5jb21wcmVzc2VkX2RpZ2l0YWxfYXVkaW9fb3BsIjozMDAsInVuY29tcHJlc3NlZF9kaWdpdGFsX3ZpZGVvX29wbCI6MzAwLCJjb21wcmVzc2VkX2RpZ2l0YWxfYXVkaW9fb3BsIjozMDAsImNvbXByZXNzZWRfZGlnaXRhbF92aWRlb19vcGwiOjUwMH19fQ.IMAd3ixOnctasxnd8UVzP5JQO-XjoHS1y0ZegbYspvY',
                kid: '827802f4-e896-4769-ba77-f1e656ed1bd4',
                iv: '1f3a1d7ce87a4cd3846038fcf4d05f82'
            };
        }
        
        const STATIC_TEST = getTestData();
        
        // عرض مصدر البيانات
        if (new URLSearchParams(window.location.search).get('hls')) {
            console.log('📥 تم تحميل البيانات من URL parameters (من صفحة الاختبار)');
        } else if (sessionStorage.getItem('fairplay_extracted_data')) {
            console.log('📥 تم تحميل البيانات من sessionStorage (من صفحة الاختبار)');
        } else {
            console.log('📥 تم تحميل البيانات الافتراضية (اختبار ثابت)');
        }
        
        console.log('📋 البيانات المستخدمة:', STATIC_TEST);

        // إعداد المتغيرات العامة قبل تحميل المشغل
        window.__FAIRPLAY_CERT_URL__ = STATIC_TEST.certificateUrl;
        window.__FAIRPLAY_LICENSE_URL__ = STATIC_TEST.licenseUrl;

        // Debug console for mobile
        const debugDiv = document.getElementById('debug-console');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        let debugLogs = []; // Store all logs for copying
        
        function addToDebug(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logEntry = `[${timestamp}] ${type}: ${message}`;
            debugLogs.push(logEntry);
            
            if (debugDiv) {
                debugDiv.innerHTML += `<div>${logEntry}</div>`;
                debugDiv.scrollTop = debugDiv.scrollHeight;
            }
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToDebug('LOG', ...args);
        };
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToDebug('ERROR', ...args);
        };
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToDebug('WARN', ...args);
        };

        // Debug control functions
        window.copyDebugLogs = function() {
            const allLogs = debugLogs.join('\n');
            
            // Try modern clipboard API first
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(allLogs).then(() => {
                    alert('✅ تم نسخ الـ logs إلى الحافظة!');
                }).catch(() => {
                    // Fallback to textarea method
                    fallbackCopy(allLogs);
                });
            } else {
                // Fallback for older browsers
                fallbackCopy(allLogs);
            }
        };
        
        function fallbackCopy(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.opacity = '0';
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                document.execCommand('copy');
                alert('✅ تم نسخ الـ logs إلى الحافظة!');
            } catch (err) {
                // Show logs in alert as last resort
                const shortLogs = text.substring(0, 1000) + (text.length > 1000 ? '\n... (truncated)' : '');
                prompt('نسخ الـ logs يدوياً:', shortLogs);
            }
            
            document.body.removeChild(textarea);
        }
        
        window.clearDebugConsole = function() {
            debugLogs = [];
            if (debugDiv) {
                debugDiv.innerHTML = '';
            }
            console.log('🗑️ Debug console cleared');
        };
        
        window.toggleDebugConsole = function() {
            if (debugDiv) {
                if (debugDiv.style.display === 'none') {
                    debugDiv.style.display = 'block';
                    console.log('👁️ Debug console shown');
                } else {
                    debugDiv.style.display = 'none';
                    console.log('👁️ Debug console hidden');
                }
            }
        };
        
        window.copyImportantInfo = function() {
            // Extract important FairPlay info from logs
            const importantLogs = debugLogs.filter(log => 
                log.includes('SKD') || 
                log.includes('Certificate') || 
                log.includes('License') ||
                log.includes('initData') ||
                log.includes('Platform check') ||
                log.includes('FairPlay') ||
                log.includes('ERROR') ||
                log.includes('WARN')
            );
            
            const summary = [
                '=== FairPlay Debug Summary ===',
                `Timestamp: ${new Date().toISOString()}`,
                `User Agent: ${navigator.userAgent}`,
                `Test URL: ${window.location.href}`,
                '',
                '=== Configuration ===',
                `Certificate URL: ${STATIC_TEST.certificateUrl}`,
                `License URL: ${STATIC_TEST.licenseUrl}`,
                `HLS URL: ${STATIC_TEST.hls}`,
                `KID: ${STATIC_TEST.kid}`,
                `IV: ${STATIC_TEST.iv}`,
                '',
                '=== Debug Logs ===',
                ...importantLogs
            ].join('\n');
            
            // Copy summary
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(summary).then(() => {
                    alert('✅ تم نسخ معلومات FairPlay المهمة!');
                }).catch(() => {
                    fallbackCopy(summary);
                });
            } else {
                fallbackCopy(summary);
            }
        };

        // اضبط زِر التشغيل بالبيانات وثم شغّل تلقائياً
        document.addEventListener('DOMContentLoaded', function(){
            const btn = document.getElementById('playStreamButton');
            btn.dataset.streamType = 'hls';
            btn.dataset.hlsUrl = STATIC_TEST.hls;
            btn.dataset.title = STATIC_TEST.title;
            btn.dataset.licenseUrl = STATIC_TEST.licenseUrl;
            btn.dataset.certificateUrl = STATIC_TEST.certificateUrl;
            btn.dataset.keyData = ''; // Empty for HLS/FairPlay
            
            console.log('📋 Static test data configured:', STATIC_TEST);
            console.log('📋 Button dataset configured:', btn.dataset);

            // شغّل تلقائياً بعد تأكد من تحميل المشغل
            setTimeout(() => {
                console.log('🚀 Auto-clicking play button...');
                btn.click();
            }, 500);
        });
    </script>
    <script src="/player/fairplay_player.js"></script>
</body>
</html>

