<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Channel Extractor Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 800px;
            padding: 30px;
        }
        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
        .loading {
            display: none;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-broadcast-tower me-2"></i>
                    Channel Extractor Test
                </h2>
                <p class="text-muted">Test the new channel extraction system</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="channelId" class="form-label">Channel ID:</label>
                        <input type="text" class="form-control" id="channelId" placeholder="Enter channel ID" value="1234">
                    </div>
                    <button class="btn btn-primary w-100" onclick="extractChannel()">
                        <i class="fas fa-play me-2"></i>Extract Channel Data
                    </button>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Quick Test Channels:</label>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="testChannel('1234')">Test Channel 1234</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="testChannel('5678')">Test Channel 5678</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="testChannel('9999')">Test Channel 9999</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="loading text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Extracting channel data...</p>
            </div>

            <div id="results"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testChannel(channelId) {
            document.getElementById('channelId').value = channelId;
            extractChannel();
        }

        function extractChannel() {
            const channelId = document.getElementById('channelId').value;
            if (!channelId) {
                alert('Please enter a channel ID');
                return;
            }

            // Show loading
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';

            // Make API call
            fetch(`/api/shahid/channels/${channelId}/extract-drm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    channel_id: channelId,
                    channel_title: `Test Channel ${channelId}`
                })
            })
            .then(response => response.json())
            .then(data => {
                // Hide loading
                document.querySelector('.loading').style.display = 'none';
                
                // Display results
                displayResults(data);
            })
            .catch(error => {
                // Hide loading
                document.querySelector('.loading').style.display = 'none';
                
                // Display error
                displayError(error);
            });
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            if (data.success) {
                const channelData = data.data;
                
                resultsDiv.innerHTML = `
                    <div class="result-card">
                        <h5 class="text-success mb-3">
                            <i class="fas fa-check-circle me-2"></i>Extraction Successful
                        </h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Channel:</strong> ${channelData.channel_title}<br>
                                <strong>ID:</strong> ${channelData.channel_id}<br>
                                <strong>Stream Type:</strong> <span class="badge bg-info">${channelData.stream_type}</span><br>
                                <strong>Encrypted:</strong> <span class="badge ${channelData.is_encrypted ? 'bg-warning' : 'bg-success'}">${channelData.is_encrypted ? 'Yes (' + (channelData.drm_type || 'Unknown') + ')' : 'No'}</span>
                            </div>
                            <div class="col-md-6">
                                ${channelData.quality_info && channelData.quality_info.length > 0 ? `
                                    <strong>Available Qualities:</strong><br>
                                    ${channelData.quality_info.map(q => `${q.width}x${q.height} (${Math.round(q.bandwidth/1000)}k)`).join('<br>')}
                                ` : ''}
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>Stream URL:</strong>
                            <div class="code-block">${channelData.stream_url || 'N/A'}</div>
                        </div>

                        ${channelData.is_encrypted ? `
                            <div class="mb-3">
                                <strong>KID:</strong>
                                <div class="code-block">${channelData.kid || 'N/A'}</div>
                            </div>

                            <div class="mb-3">
                                <strong>Key:</strong>
                                <div class="code-block">${channelData.key || 'N/A'}</div>
                            </div>

                            ${channelData.pssh ? `
                                <div class="mb-3">
                                    <strong>PSSH:</strong>
                                    <div class="code-block">${channelData.pssh}</div>
                                </div>
                            ` : ''}

                            ${channelData.formatted_key ? `
                                <div class="mb-3">
                                    <strong>Formatted Key:</strong>
                                    <div class="code-block">${channelData.formatted_key}</div>
                                </div>
                            ` : ''}
                        ` : ''}

                        <div class="mt-3">
                            <strong>Raw Response:</strong>
                            <div class="code-block">${JSON.stringify(channelData, null, 2)}</div>
                        </div>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="result-card border-danger">
                        <h5 class="text-danger mb-3">
                            <i class="fas fa-exclamation-circle me-2"></i>Extraction Failed
                        </h5>
                        <p><strong>Error:</strong> ${data.message}</p>
                        <div class="code-block">${JSON.stringify(data, null, 2)}</div>
                    </div>
                `;
            }
        }

        function displayError(error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-card border-danger">
                    <h5 class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>Network Error
                    </h5>
                    <p><strong>Error:</strong> ${error.message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
