<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class FixUserRelationships extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:user-relationships';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix user relationships and ensure proper data structure';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing user relationships and data structure...');

        // 1. Check and fix users table structure
        $this->info("\n1. Checking users table structure:");
        $this->checkUsersTable();

        // 2. Check subscription tables
        $this->info("\n2. Checking subscription tables:");
        $this->checkSubscriptionTables();

        // 3. Fix user data
        $this->info("\n3. Fixing user data:");
        $this->fixUserData();

        // 4. Test relationships
        $this->info("\n4. Testing relationships:");
        $this->testRelationships();

        $this->info("\n✅ User relationships fix completed!");
        return 0;
    }

    private function checkUsersTable()
    {
        $columns = Schema::getColumnListing('users');
        $requiredColumns = [
            'subscription_expiry' => 'timestamp',
            'remaining_days' => 'integer',
            'is_active' => 'boolean',
            'user_info' => 'text'
        ];

        foreach ($requiredColumns as $column => $type) {
            if (in_array($column, $columns)) {
                $this->info("✅ Column '{$column}' exists");
            } else {
                $this->warn("⚠️  Column '{$column}' missing - adding it...");
                $this->addMissingColumn($column, $type);
            }
        }
    }

    private function addMissingColumn($column, $type)
    {
        try {
            switch ($column) {
                case 'subscription_expiry':
                    Schema::table('users', function ($table) {
                        $table->timestamp('subscription_expiry')->nullable();
                    });
                    break;
                case 'remaining_days':
                    Schema::table('users', function ($table) {
                        $table->integer('remaining_days')->default(0);
                    });
                    break;
                case 'is_active':
                    Schema::table('users', function ($table) {
                        $table->boolean('is_active')->default(true);
                    });
                    break;
                case 'user_info':
                    Schema::table('users', function ($table) {
                        $table->text('user_info')->nullable();
                    });
                    break;
            }
            $this->info("✅ Added column '{$column}'");
        } catch (\Exception $e) {
            $this->error("❌ Failed to add column '{$column}': " . $e->getMessage());
        }
    }

    private function checkSubscriptionTables()
    {
        $tables = ['subscription_plans', 'user_subscriptions'];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                $this->info("✅ Table '{$table}' exists with {$count} records");
            } else {
                $this->warn("⚠️  Table '{$table}' does not exist");
            }
        }
    }

    private function fixUserData()
    {
        $users = User::all();
        $fixed = 0;

        foreach ($users as $user) {
            $needsUpdate = false;
            $updates = [];

            // Fix is_active if null
            if (is_null($user->is_active)) {
                $updates['is_active'] = true;
                $needsUpdate = true;
            }

            // Fix remaining_days if null and subscription_expiry exists
            if ($user->subscription_expiry && is_null($user->remaining_days)) {
                $remainingDays = now()->diffInDays($user->subscription_expiry, false);
                $updates['remaining_days'] = max(0, $remainingDays);
                $needsUpdate = true;
            }

            // Fix user_info if empty
            if (empty($user->user_info)) {
                $userInfo = [
                    'name' => $user->name,
                    'application' => $user->application ?? 'ALL',
                ];
                $updates['user_info'] = json_encode($userInfo);
                $needsUpdate = true;
            }

            // Fix user_type if not set
            if (empty($user->user_type)) {
                $updates['user_type'] = 'user';
                $needsUpdate = true;
            }

            if ($needsUpdate) {
                $user->update($updates);
                $fixed++;
                $this->info("✅ Fixed user: {$user->name} (ID: {$user->id})");
            }
        }

        $this->info("📊 Fixed {$fixed} users out of {$users->count()} total users");
    }

    private function testRelationships()
    {
        $user = User::first();
        if (!$user) {
            $this->warn("⚠️  No users found to test");
            return;
        }

        $this->info("Testing with user: {$user->name} (ID: {$user->id})");

        // Test basic properties
        try {
            $this->info("✅ Basic properties accessible");
            $this->info("   - Name: {$user->name}");
            $this->info("   - Email: {$user->email}");
            $this->info("   - Active: " . ($user->is_active ? 'Yes' : 'No'));
            $this->info("   - Application: " . ($user->application ?? 'N/A'));
        } catch (\Exception $e) {
            $this->error("❌ Error accessing basic properties: " . $e->getMessage());
        }

        // Test relationships
        try {
            if (method_exists($user, 'subscriptions')) {
                $subscriptions = $user->subscriptions;
                $this->info("✅ Subscriptions relationship works: " . $subscriptions->count() . " subscriptions");
            } else {
                $this->warn("⚠️  Subscriptions relationship not defined");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error testing subscriptions: " . $e->getMessage());
        }

        try {
            if (method_exists($user, 'activeSubscription')) {
                $activeSubscription = $user->activeSubscription;
                if ($activeSubscription) {
                    $this->info("✅ Active subscription found: ID {$activeSubscription->id}");
                } else {
                    $this->info("ℹ️  No active subscription");
                }
            } else {
                $this->warn("⚠️  ActiveSubscription relationship not defined");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error testing active subscription: " . $e->getMessage());
        }
    }
}
