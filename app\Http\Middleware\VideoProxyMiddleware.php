<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VideoProxyMiddleware
{
    /**
     * Handle an incoming request for video proxy
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Set headers for video streaming
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
        $response->headers->set('Access-Control-Expose-Headers', 'Content-Range, Content-Length, Accept-Ranges');
        
        // Enable range requests for video streaming
        $response->headers->set('Accept-Ranges', 'bytes');
        
        // Disable caching for dynamic content
        if ($request->is('video-proxy/manifest*')) {
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        } else {
            // Cache video segments for better performance
            $response->headers->set('Cache-Control', 'public, max-age=3600');
        }

        return $response;
    }
}
