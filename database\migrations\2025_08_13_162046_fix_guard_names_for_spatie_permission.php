<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\Admin;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Step 1: Create admin guard permissions (duplicate existing permissions for admin guard)
        $webPermissions = Permission::where('guard_name', 'web')->get();

        foreach ($webPermissions as $webPermission) {
            Permission::firstOrCreate([
                'name' => $webPermission->name,
                'guard_name' => 'admin'
            ]);
        }

        // Step 2: Create admin guard roles (duplicate existing roles for admin guard)
        $webRoles = Role::where('guard_name', 'web')->get();

        foreach ($webRoles as $webRole) {
            $adminRole = Role::firstOrCreate([
                'name' => $webRole->name,
                'guard_name' => 'admin'
            ]);

            // Copy permissions from web role to admin role
            $rolePermissions = $webRole->permissions;
            foreach ($rolePermissions as $permission) {
                $adminPermission = Permission::where('name', $permission->name)
                    ->where('guard_name', 'admin')
                    ->first();

                if ($adminPermission) {
                    $adminRole->givePermissionTo($adminPermission);
                }
            }
        }

        // Step 3: Migrate existing Admin model role assignments
        $admins = Admin::all();

        foreach ($admins as $admin) {
            // Get current roles (which are probably web guard)
            $currentRoles = $admin->roles;

            // Remove all current roles
            $admin->syncRoles([]);

            // Re-assign roles with admin guard
            foreach ($currentRoles as $role) {
                $adminRole = Role::where('name', $role->name)
                    ->where('guard_name', 'admin')
                    ->first();

                if ($adminRole) {
                    $admin->assignRole($adminRole);
                }
            }
        }

        echo "Guard names fixed successfully!\n";
        echo "- Created admin guard permissions and roles\n";
        echo "- Migrated Admin model role assignments to admin guard\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove admin guard roles and permissions
        Role::where('guard_name', 'admin')->delete();
        Permission::where('guard_name', 'admin')->delete();

        // Reset cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        echo "Admin guard roles and permissions removed.\n";
    }
};
