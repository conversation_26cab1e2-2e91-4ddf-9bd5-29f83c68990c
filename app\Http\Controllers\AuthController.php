<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use App\Models\User;

class AuthController extends Controller
{
    private $apiBaseUrl = 'https://play-drm.com';

    /**
     * Show login form
     */
    public function showLogin()
    {
        // If already logged in as regular user, redirect to dashboard
        if (Auth::guard('web')->check()) {
            return redirect()->route('dashboard');
        }

        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('email', 'password');
        $rememberMe = $request->has('remember_me');

        // Find user by email - ONLY regular users (not admins)
        $user = User::where('email', $credentials['email'])
                   ->where(function($query) {
                       $query->where('user_type', '!=', 'admin')
                             ->orWhereNull('user_type')
                             ->orWhere('user_type', 'user');
                   })
                   ->where('is_admin', false)
                   ->first();

        if (!$user) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email address not found in our user system'
                ], 422);
            }
            return back()->withErrors(['email' => 'Email address not found in our user system']);
        }

        // Prevent admin users from logging in through user portal
        if ($user->isAdmin()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin users must use the admin login portal at /admin/login'
                ], 422);
            }
            return back()->withErrors(['email' => 'Admin users must use the admin login portal at /admin/login']);
        }

        // Check password
        if (!Hash::check($credentials['password'], $user->password)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid password'
                ], 422);
            }
            return back()->withErrors(['password' => 'Invalid password']);
        }

        // Check if user is active
        if (!$user->is_active) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Your account has been deactivated. Please contact support'
                ], 422);
            }
            return back()->withErrors(['email' => 'Your account has been deactivated. Please contact support']);
        }

        // Login user
        Auth::login($user, $rememberMe);

        // Mark session as user session (not admin)
        $request->session()->put('is_user_session', true);
        $request->session()->put('user_id', $user->id);
        $request->session()->forget('is_admin_session');

        // Update last login info
        $user->update([
            'last_login_at' => now(),
            'last_login_ip' => $request->ip(),
        ]);

        // Regenerate session
        $request->session()->regenerate();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Welcome back, ' . $user->name . '!',
                'redirect' => route('dashboard')
            ]);
        }

        return redirect()->intended(route('dashboard'))->with('success', 'Welcome back, ' . $user->name . '!');
    }




    /**
     * Logout user
     */
    public function logout()
    {
        // Clear user session markers
        request()->session()->forget('is_user_session');
        request()->session()->forget('user_id');

        Auth::logout();
        Session::flush();

        return redirect()->route('login')->with('info', 'You have been logged out successfully');
    }




}
