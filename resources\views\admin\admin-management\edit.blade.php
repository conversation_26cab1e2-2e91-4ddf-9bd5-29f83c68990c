@extends('admin.layouts.app')

@section('title', 'Edit Admin User')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-1">Edit Admin User</h4>
        <p class="text-muted mb-0">Update admin information, roles, and permissions.</p>
    </div>
    <div>
        <a href="{{ route('admin.admin-management.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Admin Management
        </a>
    </div>
</div>

<!-- Admin Info Card -->
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img id="admin-avatar" src="" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;" alt="Admin Avatar">
                </div>
                <h5 id="admin-name" class="mb-1">Loading...</h5>
                <p id="admin-email" class="text-muted mb-2">Loading...</p>
                <p id="admin-position" class="text-muted small mb-3">Loading...</p>
                
                <div class="d-flex justify-content-center gap-2 mb-3">
                    <span id="admin-status-badge" class="badge">Loading...</span>
                    <span id="admin-type-badge" class="badge">Loading...</span>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="mb-0" id="admin-created-users">-</h6>
                            <small class="text-muted">Created Users</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-0" id="admin-last-activity">-</h6>
                        <small class="text-muted">Last Activity</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning btn-sm" id="suspend-btn" onclick="suspendAdmin()">
                        <i class="fas fa-ban me-2"></i>Suspend Admin
                    </button>
                    <button class="btn btn-outline-success btn-sm" id="unsuspend-btn" onclick="unsuspendAdmin()" style="display: none;">
                        <i class="fas fa-check me-2"></i>Unsuspend Admin
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="resetPassword()">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="viewActivityLog()">
                        <i class="fas fa-history me-2"></i>Activity Log
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Edit Form -->
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="editTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab">
                            <i class="fas fa-user me-2"></i>Basic Information
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="roles-permissions-tab" data-bs-toggle="tab" data-bs-target="#roles-permissions" type="button" role="tab">
                            <i class="fas fa-user-tag me-2"></i>Roles & Permissions
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                            <i class="fas fa-shield-alt me-2"></i>Security
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="editTabsContent">
                    <!-- Basic Information Tab -->
                    <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                        <form id="basic-info-form">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Name *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Department</label>
                                    <select class="form-select" name="department">
                                        <option value="">Select Department</option>
                                        <option value="IT">IT</option>
                                        <option value="Customer Service">Customer Service</option>
                                        <option value="Sales">Sales</option>
                                        <option value="Marketing">Marketing</option>
                                        <option value="HR">HR</option>
                                        <option value="Finance">Finance</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Position</label>
                                    <input type="text" class="form-control" name="position">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Phone</label>
                                    <input type="text" class="form-control" name="phone">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" name="is_active">
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label class="form-label">Bio</label>
                                    <textarea class="form-control" name="bio" rows="3"></textarea>
                                </div>
                                @if(auth('admin')->check() && auth('admin')->user()->isSuperAdmin())
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="is_super_admin" id="is_super_admin">
                                                <label class="form-check-label" for="is_super_admin">
                                                    Super Admin
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_manage_admins" id="can_manage_admins">
                                                <label class="form-check-label" for="can_manage_admins">
                                                    Can Manage Admins
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Basic Information
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Roles & Permissions Tab -->
                    <div class="tab-pane fade" id="roles-permissions" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Current Roles</h6>
                                <div id="current-roles" class="mb-3">
                                    <!-- Dynamic content -->
                                </div>
                                
                                <h6>Available Roles</h6>
                                <div id="available-roles" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                    <!-- Dynamic content -->
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary btn-sm" onclick="updateRoles()">
                                        <i class="fas fa-sync me-2"></i>Update Roles
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Effective Permissions</h6>
                                <div id="effective-permissions" class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                    <!-- Dynamic content -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Tab -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Password Management</h6>
                                <form id="password-form">
                                    <div class="mb-3">
                                        <label class="form-label">New Password</label>
                                        <input type="password" class="form-control" name="password">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Confirm Password</label>
                                        <input type="password" class="form-control" name="password_confirmation">
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="force_password_change" id="force_password_change">
                                            <label class="form-check-label" for="force_password_change">
                                                Force password change on next login
                                            </label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key me-2"></i>Update Password
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h6>Security Information</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Last Login</span>
                                        <span id="last-login" class="text-muted">-</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Last Login IP</span>
                                        <span id="last-login-ip" class="text-muted">-</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Password Changed</span>
                                        <span id="password-changed" class="text-muted">-</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Account Created</span>
                                        <span id="account-created" class="text-muted">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suspend Modal -->
<div class="modal fade" id="suspendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Suspend Admin</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="suspend-form">
                <div class="modal-body">
                    <p>Are you sure you want to suspend this admin?</p>
                    <div class="mb-3">
                        <label class="form-label">Reason *</label>
                        <textarea class="form-control" name="reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Suspend Admin</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.admin-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
}

.role-badge {
    font-size: 0.8rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.permission-badge {
    font-size: 0.7rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom: 2px solid #007bff;
    color: #007bff;
}

.list-group-item {
    border-left: none;
    border-right: none;
    padding-left: 0;
    padding-right: 0;
}
</style>
@endpush

@push('scripts')
<script>
// Get admin ID from the passed admin object
const adminId = {{ $admin->id }};

class AdminEdit {
    constructor() {
        this.adminId = adminId;
        this.adminData = null;
        this.roles = [];
        this.init();
    }

    async init() {
        await this.loadAdminData();
        await this.loadRoles();
        this.setupEventListeners();
        this.populateForm();
    }

    setupEventListeners() {
        // Form submissions
        console.log('Setting up event listeners');

        const basicForm = document.getElementById('basic-info-form');
        if (basicForm) {
            basicForm.addEventListener('submit', (e) => this.handleBasicInfoUpdate(e));
            console.log('Basic info form listener attached');
        }

        const passwordForm = document.getElementById('password-form');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => this.handlePasswordUpdate(e));
            console.log('Password form listener attached');
        }

        const suspendForm = document.getElementById('suspend-form');
        if (suspendForm) {
            suspendForm.addEventListener('submit', (e) => this.handleSuspend(e));
            console.log('Suspend form listener attached');
        }
    }

    async loadAdminData() {
        try {
            const response = await fetch(`/admin/admin-management/api/${this.adminId}`);

            if (!response.ok) {
                const text = await response.text();
                throw new Error(`HTTP ${response.status}: ${text}`);
            }

            const data = await response.json();

            if (data.success) {
                this.adminData = data.data;
            } else {
                this.showAlert('Error loading admin data: ' + data.message, 'danger');
            }
        } catch (error) {
            console.error('Error loading admin data:', error);
            this.showAlert('Error loading admin data: ' + error.message, 'danger');
        }
    }

    async loadRoles() {
        try {
            const response = await fetch('/admin/roles/api');
            const data = await response.json();
            
            if (data.success) {
                this.roles = data.data.data;
            }
        } catch (error) {
            console.error('Error loading roles:', error);
        }
    }

    populateForm() {
        if (!this.adminData) return;

        const admin = this.adminData;

        // Update admin info display
        document.getElementById('admin-avatar').src = admin.avatar_url || `https://ui-avatars.com/api/?name=${admin.name.charAt(0)}&background=667eea&color=fff&size=120`;
        document.getElementById('admin-name').textContent = admin.name;
        document.getElementById('admin-email').textContent = admin.email;
        document.getElementById('admin-position').textContent = admin.position || 'No position set';
        
        // Status badges
        const statusBadge = document.getElementById('admin-status-badge');
        if (admin.suspended_at) {
            statusBadge.className = 'badge bg-warning';
            statusBadge.textContent = 'Suspended';
            document.getElementById('suspend-btn').style.display = 'none';
            document.getElementById('unsuspend-btn').style.display = 'block';
        } else if (admin.is_active) {
            statusBadge.className = 'badge bg-success';
            statusBadge.textContent = 'Active';
        } else {
            statusBadge.className = 'badge bg-secondary';
            statusBadge.textContent = 'Inactive';
        }

        const typeBadge = document.getElementById('admin-type-badge');
        if (admin.is_super_admin) {
            typeBadge.className = 'badge bg-danger';
            typeBadge.textContent = 'Super Admin';
        } else {
            typeBadge.className = 'badge bg-primary';
            typeBadge.textContent = 'Admin';
        }

        // Stats
        document.getElementById('admin-created-users').textContent = admin.created_users_count || 0;
        document.getElementById('admin-last-activity').textContent = admin.last_activity_at ? 
            new Date(admin.last_activity_at).toLocaleDateString() : 'Never';

        // Populate basic info form
        const form = document.getElementById('basic-info-form');
        form.name.value = admin.name;
        form.email.value = admin.email;
        form.department.value = admin.department || '';
        form.position.value = admin.position || '';
        form.phone.value = admin.phone || '';
        form.is_active.value = admin.is_active ? '1' : '0';
        form.bio.value = admin.bio || '';
        
        if (form.is_super_admin) form.is_super_admin.checked = admin.is_super_admin;
        if (form.can_manage_admins) form.can_manage_admins.checked = admin.can_manage_admins;

        // Populate security info
        document.getElementById('last-login').textContent = admin.last_login_at ? 
            new Date(admin.last_login_at).toLocaleString() : 'Never';
        document.getElementById('last-login-ip').textContent = admin.last_login_ip || 'Unknown';
        document.getElementById('password-changed').textContent = admin.password_changed_at ? 
            new Date(admin.password_changed_at).toLocaleDateString() : 'Unknown';
        document.getElementById('account-created').textContent = new Date(admin.created_at).toLocaleDateString();

        // Populate roles
        this.populateRoles();
    }

    populateRoles() {
        if (!this.adminData || !this.roles.length) return;

        const currentRoles = this.adminData.roles || [];
        const currentRoleNames = currentRoles.map(r => r.name);

        // Current roles
        const currentRolesContainer = document.getElementById('current-roles');
        currentRolesContainer.innerHTML = currentRoles.map(role => 
            `<span class="badge bg-primary role-badge">${role.name}</span>`
        ).join('') || '<p class="text-muted">No roles assigned</p>';

        // Available roles
        const availableRolesContainer = document.getElementById('available-roles');
        availableRolesContainer.innerHTML = this.roles.map(role => `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${role.name}" 
                       id="role-${role.id}" ${currentRoleNames.includes(role.name) ? 'checked' : ''}>
                <label class="form-check-label" for="role-${role.id}">
                    ${role.name}
                </label>
            </div>
        `).join('');

        // Effective permissions
        this.updateEffectivePermissions();
    }

    updateEffectivePermissions() {
        if (!this.adminData) return;

        const permissions = this.adminData.permissions || [];
        const permissionsContainer = document.getElementById('effective-permissions');
        
        if (permissions.length > 0) {
            permissionsContainer.innerHTML = permissions.map(permission => 
                `<span class="badge bg-secondary permission-badge">${permission.name}</span>`
            ).join('');
        } else {
            permissionsContainer.innerHTML = '<p class="text-muted">No permissions assigned</p>';
        }
    }

    async handleBasicInfoUpdate(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        try {
            const response = await fetch(`/admin/admin-management/api/${this.adminId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Basic information updated successfully!', 'success');
                await this.loadAdminData();
                this.populateForm();
            } else {
                this.showAlert(result.message || 'Error updating basic information', 'danger');
            }
        } catch (error) {
            console.error('Error updating basic information:', error);
            this.showAlert('Error updating basic information', 'danger');
        }
    }

    async updateRoles() {
        const checkboxes = document.querySelectorAll('#available-roles input[type="checkbox"]:checked');
        const selectedRoles = Array.from(checkboxes).map(cb => cb.value);

        try {
            const response = await fetch(`/admin/admin-management/api/${this.adminId}/sync-roles`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ roles: selectedRoles })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Roles updated successfully!', 'success');
                await this.loadAdminData();
                this.populateRoles();
            } else {
                this.showAlert(result.message || 'Error updating roles', 'danger');
            }
        } catch (error) {
            console.error('Error updating roles:', error);
            this.showAlert('Error updating roles', 'danger');
        }
    }

    async handlePasswordUpdate(e) {
        e.preventDefault();
        console.log('Password update form submitted');

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());
        console.log('Form data:', data);

        // Validate password confirmation
        if (data.password !== data.password_confirmation) {
            this.showAlert('Passwords do not match', 'danger');
            return;
        }

        const url = `/admin/admin-management/api/${this.adminId}/update-password`;
        console.log('Making POST request to:', url);

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            console.log('Response status:', response.status);
            const result = await response.json();
            console.log('Response result:', result);

            if (result.success) {
                this.showAlert('Password updated successfully!', 'success');
                e.target.reset();
            } else {
                this.showAlert(result.message || 'Error updating password', 'danger');
            }
        } catch (error) {
            console.error('Error updating password:', error);
            this.showAlert('Error updating password', 'danger');
        }
    }

    async handleSuspend(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        try {
            const response = await fetch(`/admin/admin-management/api/${this.adminId}/suspend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Admin suspended successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('suspendModal')).hide();
                await this.loadAdminData();
                this.populateForm();
            } else {
                this.showAlert(result.message || 'Error suspending admin', 'danger');
            }
        } catch (error) {
            console.error('Error suspending admin:', error);
            this.showAlert('Error suspending admin', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Global functions
function suspendAdmin() {
    const modal = new bootstrap.Modal(document.getElementById('suspendModal'));
    modal.show();
}

async function unsuspendAdmin() {
    if (!confirm('Are you sure you want to unsuspend this admin?')) return;

    try {
        const response = await fetch(`/admin/admin-management/api/${adminId}/unsuspend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const result = await response.json();

        if (result.success) {
            window.adminEdit.showAlert('Admin unsuspended successfully!', 'success');
            await window.adminEdit.loadAdminData();
            window.adminEdit.populateForm();
        } else {
            window.adminEdit.showAlert(result.message || 'Error unsuspending admin', 'danger');
        }
    } catch (error) {
        console.error('Error unsuspending admin:', error);
        window.adminEdit.showAlert('Error unsuspending admin', 'danger');
    }
}

function resetPassword() {
    // Focus on security tab and password field
    const securityTab = new bootstrap.Tab(document.getElementById('security-tab'));
    securityTab.show();
    setTimeout(() => {
        document.querySelector('#security input[name="password"]').focus();
    }, 100);
}

function viewActivityLog() {
    // Implementation for activity log
    console.log('View activity log');
}

function updateRoles() {
    window.adminEdit.updateRoles();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.adminEdit = new AdminEdit();
});
</script>
@endpush
