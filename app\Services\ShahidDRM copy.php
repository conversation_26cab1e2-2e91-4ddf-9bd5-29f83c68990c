<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * Shahid DRM Service - Laravel Port of Python shahid_drm.py
 * This service handles DRM operations for Shahid content
 */
class ShahidDRM
{
    private $deviceFile = 'device.wvd';
    private $drmEndpoint = 'https://drm.shahid.net';
    
    public function __construct()
    {
        // Initialize DRM service
    }

    /**
     * Check if device file exists
     */
    public function hasDeviceFile()
    {
        return Storage::exists($this->deviceFile);
    }

    /**
     * Get DRM license for content
     */
    public function getLicense($contentId, $pssh = null)
    {
        try {
            if (!$this->hasDeviceFile()) {
                return [
                    'success' => false,
                    'error' => 'Device file not found'
                ];
            }

            // This is a simplified version
            // In a real implementation, you would need to:
            // 1. Load the device file
            // 2. Create a CDM session
            // 3. Generate license request
            // 4. Send to DRM server
            // 5. Process response

            Log::info('DRM license requested for content: ' . $contentId);

            return [
                'success' => true,
                'license' => 'mock_license_data',
                'keys' => []
            ];

        } catch (\Exception $e) {
            Log::error('DRM license error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract decryption key using device.wvd file directly
     */
    public function extractKey($pssh, $licenseUrl = null)
    {
        try {
            Log::info('=== STARTING KEY EXTRACTION PROCESS ===');
            Log::info('PSSH length: ' . strlen($pssh));
            Log::info('PSSH preview: ' . substr($pssh, 0, 100) . '...');
            Log::info('License URL: ' . ($licenseUrl ?? 'Not provided'));

            if (!$this->hasDeviceFile()) {
                Log::error('STEP 1 FAILED: Device file not found');
                return [
                    'success' => false,
                    'error' => 'Device file not found'
                ];
            }
            Log::info('STEP 1 SUCCESS: Device file exists');

            if (!$pssh) {
                Log::error('STEP 2 FAILED: PSSH is required');
                return [
                    'success' => false,
                    'error' => 'PSSH is required'
                ];
            }
            Log::info('STEP 2 SUCCESS: PSSH provided');

            Log::info('STEP 3: Attempting direct device.wvd parsing...');
            // Try direct device.wvd parsing first (fastest method)
            $directResult = $this->extractKeyFromDeviceFile($pssh, $licenseUrl);

            if ($directResult['success']) {
                Log::info('STEP 3 SUCCESS: Direct parsing succeeded');
                Log::info('Direct result: ' . json_encode($directResult, JSON_PRETTY_PRINT));
                return $directResult;
            } else {
                Log::warning('STEP 3 FAILED: Direct parsing failed - ' . ($directResult['error'] ?? 'Unknown error'));
            }

            Log::info('STEP 4: Falling back to script-based extraction...');
            // Fallback to external script if direct parsing fails
            $scriptResult = $this->extractKeyWithScript($pssh, $licenseUrl);

            if ($scriptResult['success']) {
                Log::info('STEP 4 SUCCESS: Script extraction succeeded');
            } else {
                Log::error('STEP 4 FAILED: Script extraction failed - ' . ($scriptResult['error'] ?? 'Unknown error'));
            }

            Log::info('=== KEY EXTRACTION PROCESS COMPLETED ===');
            return $scriptResult;

        } catch (\Exception $e) {
            Log::error('CRITICAL ERROR in key extraction: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract key directly from device.wvd file (PHP implementation)
     */
    private function extractKeyFromDeviceFile($pssh, $licenseUrl = null)
    {
        try {
            Log::info('--- DIRECT DEVICE FILE EXTRACTION ---');

            $devicePath = storage_path('app/' . $this->deviceFile);
            Log::info('Device file path: ' . $devicePath);

            if (!file_exists($devicePath)) {
                Log::error('Device file does not exist at: ' . $devicePath);
                return [
                    'success' => false,
                    'error' => 'Device file not found at: ' . $devicePath
                ];
            }
            Log::info('✓ Device file exists');

            // Check file permissions
            if (!is_readable($devicePath)) {
                Log::error('Device file is not readable');
                return [
                    'success' => false,
                    'error' => 'Device file is not readable'
                ];
            }
            Log::info('✓ Device file is readable');

            // Read device.wvd file
            $deviceData = file_get_contents($devicePath);
            if ($deviceData === false) {
                Log::error('Failed to read device file content');
                return [
                    'success' => false,
                    'error' => 'Failed to read device file content'
                ];
            }

            $deviceSize = strlen($deviceData);
            Log::info('✓ Device file loaded successfully');
            Log::info('Device file size: ' . $deviceSize . ' bytes');
            Log::info('Device file first 50 bytes (hex): ' . bin2hex(substr($deviceData, 0, 50)));

            // Parse PSSH to extract KID
            Log::info('Attempting to extract KID from PSSH...');
            $kid = $this->extractKIDFromPSSH($pssh);

            if (!$kid) {
                Log::error('Failed to extract KID from PSSH');
                Log::info('PSSH base64 length: ' . strlen($pssh));
                Log::info('PSSH first 100 chars: ' . substr($pssh, 0, 100));

                return [
                    'success' => false,
                    'error' => 'Failed to extract KID from PSSH'
                ];
            }

            Log::info('✓ KID extracted successfully: ' . $kid);

            // Try to extract more info from device file
            Log::info('Parsing device file for additional info...');
            $deviceInfo = $this->parseDeviceFile($deviceData);

            if ($deviceInfo) {
                Log::info('✓ Device info extracted: ' . json_encode($deviceInfo));
            } else {
                Log::warning('Could not extract additional device info');
            }

            // For actual key extraction, we need to implement Widevine protocol
            // This is where the real key extraction would happen
            Log::info('Attempting actual key extraction...');

            $extractedKey = $this->attemptKeyExtraction($deviceData, $kid, $pssh, $licenseUrl);

            $result = [
                'success' => true,
                'keys' => [
                    [
                        'kid' => $kid,
                        'key' => $extractedKey, // This will be null unless we implement full CDM
                        'type' => 'CONTENT'
                    ]
                ],
                'pssh' => $pssh,
                'device_info' => array_merge([
                    'file_size' => $deviceSize,
                    'available' => true,
                    'path' => $devicePath
                ], $deviceInfo ?? []),
                'message' => $extractedKey ? 'Key extracted successfully!' : 'KID extracted successfully. Key extraction requires full Widevine CDM implementation.',
                'extraction_method' => 'direct_php'
            ];

            Log::info('Direct extraction result: ' . json_encode($result, JSON_PRETTY_PRINT));
            return $result;

        } catch (\Exception $e) {
            Log::error('EXCEPTION in direct device file extraction: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());
            return [
                'success' => false,
                'error' => 'Direct extraction failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Attempt actual key extraction from device file
     */
    private function attemptKeyExtraction($deviceData, $kid, $pssh, $licenseUrl = null)
    {
        try {
            Log::info('--- ATTEMPTING ACTUAL KEY EXTRACTION ---');
            Log::info('KID for extraction: ' . $kid);
            Log::info('Device data size: ' . strlen($deviceData) . ' bytes');

            // This is where we would implement the actual Widevine key extraction
            // For now, this is a placeholder that shows the process

            Log::info('Step 1: Analyzing device file structure...');

            // Check if device file has the expected Widevine structure
            $deviceHex = bin2hex($deviceData);
            Log::info('Device file starts with: ' . substr($deviceHex, 0, 32));

            // Look for Widevine signatures
            $widevineSignatures = ['widevine', 'WIDEVINE', 'WVD'];
            $foundSignature = false;

            foreach ($widevineSignatures as $sig) {
                if (strpos($deviceData, $sig) !== false) {
                    Log::info('✓ Found Widevine signature: ' . $sig);
                    $foundSignature = true;
                    break;
                }
            }

            if (!$foundSignature) {
                Log::warning('No Widevine signature found in device file');
            }

            Log::info('Step 2: Checking for private key data...');

            // In a real implementation, we would:
            // 1. Parse the device file protobuf structure
            // 2. Extract the private key
            // 3. Use it to decrypt the license response
            // 4. Extract the content keys

            // For demonstration, let\'s show what we would need:
            Log::info('Required for key extraction:');
            Log::info('- Device private key (from device.wvd)');
            Log::info('- License server URL: ' . ($licenseUrl ?? 'Not provided'));
            Log::info('- PSSH data for license request');
            Log::info('- Widevine CDM implementation');

            // Now let's try actual license request using Python Widevine API
            if ($licenseUrl) {
                Log::info('Step 3: Using Python Widevine API...');

                // Try to use Python Widevine API with real pywidevine
                $extractedKey = $this->extractKeyUsingPythonApi($pssh, $licenseUrl, $kid);
                if ($extractedKey) {
                    Log::info('✓ Key extracted using Python API: ' . $extractedKey);
                    return $extractedKey;
                }

                Log::warning('Python API approach failed, falling back to manual method...');

                // Fallback to manual method
                Log::info('Step 4: Getting service certificate...');
                $certificate = $this->getServiceCertificate($licenseUrl);
                if (!$certificate) {
                    Log::error('Failed to get service certificate');
                    return null;
                }

                Log::info('Step 5: Creating license challenge with certificate...');
                $licenseRequest = $this->createLicenseRequest($pssh, $deviceData, $certificate);
                if ($licenseRequest) {
                    $licenseResponse = $this->sendLicenseRequest($licenseUrl, $licenseRequest);
                    if ($licenseResponse) {
                        $extractedKey = $this->extractKeysFromLicenseResponse($licenseResponse, $deviceData, $kid);
                        if ($extractedKey) {
                            Log::info('✓ Key extracted from license response: ' . $extractedKey);
                            return $extractedKey;
                        }
                    }
                }
            }

            Log::warning('Key extraction requires license server communication');
            Log::info('Current implementation extracted KID but needs license response for key');

            return null; // Would return actual key if license request succeeds

        } catch (\Exception $e) {
            Log::error('Key extraction attempt failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract key using Python Widevine API
     */
    private function extractKeyUsingPythonApi($pssh, $licenseUrl, $targetKid)
    {
        try {
            Log::info('--- PYTHON WIDEVINE API APPROACH ---');
            Log::info('Calling Python Flask API with real pywidevine');

            // Python API endpoint
            $apiUrl = 'http://127.0.0.1:5000/extract-key';

            // Check if API is available
            if (!$this->isPythonApiAvailable()) {
                Log::error('Python Widevine API is not available');
                return null;
            }

            // Prepare request data
            $requestData = [
                'pssh' => $pssh,
                'license_url' => $licenseUrl,
                'target_kid' => $targetKid
            ];

            Log::info('Sending request to Python API...');
            Log::info('API URL: ' . $apiUrl);
            Log::info('Request data: ' . json_encode($requestData, JSON_PRETTY_PRINT));

            // Send request to Python API
            $response = Http::timeout(60)->post($apiUrl, $requestData);

            if ($response->successful()) {
                $result = $response->json();
                Log::info('Python API response: ' . json_encode($result, JSON_PRETTY_PRINT));

                if ($result && isset($result['success']) && $result['success']) {
                    if (isset($result['key'])) {
                        Log::info('✓ Key extracted successfully via Python API');
                        Log::info('  → Key ID: ' . ($result['kid'] ?? 'N/A'));
                        Log::info('  → Key: ' . $result['key']);
                        Log::info('  → Total keys found: ' . ($result['total_keys'] ?? 1));
                        return $result['key'];
                    } else {
                        Log::warning('Python API succeeded but no key returned');
                    }
                } else {
                    Log::error('Python API failed: ' . ($result['error'] ?? 'Unknown error'));
                }
            } else {
                Log::error('Python API request failed with status: ' . $response->status());
                Log::error('Response body: ' . $response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Python API approach failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if Python Widevine API is available
     */
    private function isPythonApiAvailable()
    {
        try {
            $healthUrl = 'http://127.0.0.1:5000/health';
            $response = Http::timeout(5)->get($healthUrl);

            if ($response->successful()) {
                $health = $response->json();
                $available = $health['status'] === 'healthy' && $health['pywidevine_available'] === true;

                if ($available) {
                    Log::info('✓ Python Widevine API is healthy and ready');
                } else {
                    Log::warning('Python API is running but pywidevine is not available');
                }

                return $available;
            } else {
                Log::warning('Python API health check failed: ' . $response->status());
                return false;
            }
        } catch (\Exception $e) {
            Log::warning('Python API not available: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Extract key using device.wvd file directly (old driver approach)
     */
    private function extractKeyUsingDeviceWvd($pssh, $licenseUrl, $targetKid, $deviceData)
    {
        try {
            Log::info('--- DEVICE.WVD APPROACH ---');
            Log::info('Using device.wvd file directly like working Python code');

            // Step 1: Get certificate (like Python: requests.post(license_url, b'\x08\x04'))
            Log::info('Step 1: Getting service certificate...');
            $certificate = $this->getServiceCertificate($licenseUrl);
            if (!$certificate) {
                Log::error('Failed to get service certificate');
                return null;
            }
            Log::info('✓ Certificate obtained, size: ' . strlen($certificate) . ' bytes');

            // Step 2: Parse device.wvd file to extract private key and client ID
            Log::info('Step 2: Parsing device.wvd file...');
            $deviceInfo = $this->parseDeviceWvdFile($deviceData);
            if (!$deviceInfo || !$deviceInfo['private_key'] || !$deviceInfo['client_id']) {
                Log::error('Failed to parse device.wvd file or extract required components');
                return null;
            }
            Log::info('✓ Device.wvd parsed successfully');
            Log::info('  → Private key size: ' . strlen($deviceInfo['private_key']) . ' bytes');
            Log::info('  → Client ID size: ' . strlen($deviceInfo['client_id']) . ' bytes');

            // Step 3: Create license request using device.wvd components
            Log::info('Step 3: Creating license request with device.wvd components...');
            $licenseRequest = $this->createLicenseRequestWithDeviceWvd($pssh, $certificate, $deviceInfo);
            if (!$licenseRequest) {
                Log::error('Failed to create license request');
                return null;
            }
            Log::info('✓ License request created, size: ' . strlen($licenseRequest) . ' bytes');

            // Step 4: Send license request to server
            Log::info('Step 4: Sending license request to server...');
            $licenseResponse = $this->sendLicenseRequest($licenseUrl, $licenseRequest);
            if (!$licenseResponse) {
                Log::error('Failed to get license response');
                return null;
            }
            Log::info('✓ License response received, size: ' . strlen($licenseResponse) . ' bytes');

            // Step 5: Decrypt license response and extract keys
            Log::info('Step 5: Decrypting license response...');
            $extractedKey = $this->decryptLicenseResponseWithDeviceWvd($licenseResponse, $deviceInfo, $targetKid);
            if ($extractedKey) {
                Log::info('✓ Key extracted successfully: ' . $extractedKey);
                return $extractedKey;
            }

            Log::warning('Failed to extract key from license response');
            return null;

        } catch (\Exception $e) {
            Log::error('Device.wvd approach failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Parse device.wvd file to extract private key and client ID
     */
    private function parseDeviceWvdFile($deviceData)
    {
        try {
            Log::info('Parsing device.wvd file structure...');

            $deviceInfo = [
                'private_key' => null,
                'client_id' => null,
                'format' => 'unknown'
            ];

            // Check for WVD signature
            if (substr($deviceData, 0, 3) === 'WVD') {
                Log::info('Found WVD signature, parsing WVD format...');
                $deviceInfo['format'] = 'WVD';

                // WVD format: WVD + version + data
                $offset = 3; // Skip "WVD"

                // Try to find private key in WVD format
                $privateKey = $this->extractPrivateKeyFromWvd($deviceData, $offset);
                if ($privateKey) {
                    $deviceInfo['private_key'] = $privateKey;
                    Log::info('✓ Private key extracted from WVD format');
                }

                // Try to find client ID in WVD format
                $clientId = $this->extractClientIdFromWvd($deviceData, $offset);
                if ($clientId) {
                    $deviceInfo['client_id'] = $clientId;
                    Log::info('✓ Client ID extracted from WVD format');
                }
            } else {
                Log::info('No WVD signature, trying generic parsing...');
                $deviceInfo['format'] = 'generic';

                // Generic parsing for other formats
                $privateKey = $this->extractPrivateKeyGeneric($deviceData);
                if ($privateKey) {
                    $deviceInfo['private_key'] = $privateKey;
                    Log::info('✓ Private key extracted using generic method');
                }

                $clientId = $this->extractClientIdGeneric($deviceData);
                if ($clientId) {
                    $deviceInfo['client_id'] = $clientId;
                    Log::info('✓ Client ID extracted using generic method');
                }
            }

            return $deviceInfo;

        } catch (\Exception $e) {
            Log::error('Failed to parse device.wvd file: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract private key from WVD format
     */
    private function extractPrivateKeyFromWvd($deviceData, $offset)
    {
        // Look for ASN.1 private key structure in WVD data
        for ($i = $offset; $i < strlen($deviceData) - 100; $i++) {
            // Look for ASN.1 SEQUENCE header (0x30)
            if (ord($deviceData[$i]) === 0x30) {
                // Check if this looks like a private key
                $candidate = substr($deviceData, $i, 2048); // Max reasonable key size

                // Try to validate as private key
                if ($this->validatePrivateKeyCandidate($candidate)) {
                    return $candidate;
                }
            }
        }

        return null;
    }

    /**
     * Extract client ID from WVD format
     */
    private function extractClientIdFromWvd($deviceData, $offset)
    {
        // Client ID is typically 16 bytes with good entropy
        for ($i = $offset; $i < strlen($deviceData) - 16; $i++) {
            $candidate = substr($deviceData, $i, 16);

            // Check entropy (should not be all zeros or all same byte)
            $unique = count(array_unique(str_split($candidate)));
            if ($unique >= 8) {
                return $candidate;
            }
        }

        return null;
    }

    /**
     * Extract private key using generic method
     */
    private function extractPrivateKeyGeneric($deviceData)
    {
        // Similar to WVD but search entire file
        return $this->extractPrivateKeyFromWvd($deviceData, 0);
    }

    /**
     * Extract client ID using generic method
     */
    private function extractClientIdGeneric($deviceData)
    {
        // Similar to WVD but search entire file
        return $this->extractClientIdFromWvd($deviceData, 0);
    }

    /**
     * Validate private key candidate
     */
    private function validatePrivateKeyCandidate($candidate)
    {
        if (strlen($candidate) < 64) return false;

        // Check for ASN.1 SEQUENCE
        if (ord($candidate[0]) !== 0x30) return false;

        // Check for reasonable length encoding
        $lengthByte = ord($candidate[1]);
        if ($lengthByte & 0x80) {
            $lengthBytes = $lengthByte & 0x7F;
            if ($lengthBytes < 1 || $lengthBytes > 4) return false;
        }

        // Check for version field (usually 0x02 0x01 0x00)
        if (strlen($candidate) > 5) {
            if (ord($candidate[4]) === 0x02 && ord($candidate[5]) === 0x01) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create license request using device.wvd components
     */
    private function createLicenseRequestWithDeviceWvd($pssh, $certificate, $deviceInfo)
    {
        try {
            Log::info('Creating license request with device.wvd components...');

            // Create basic license request message
            $licenseRequest = $this->createBasicLicenseRequest($pssh);

            // Sign with device private key
            $signature = $this->signWithDeviceWvd($licenseRequest, $deviceInfo['private_key']);
            if (!$signature) {
                Log::error('Failed to sign license request');
                return null;
            }

            // Create signed license request
            $signedRequest = $this->createSignedLicenseRequest($licenseRequest, $signature, $deviceInfo['client_id']);

            Log::info('License request created successfully, size: ' . strlen($signedRequest));
            return $signedRequest;

        } catch (\Exception $e) {
            Log::error('Failed to create license request: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create basic license request message
     */
    private function createBasicLicenseRequest($pssh)
    {
        // Decode PSSH
        $psshBinary = base64_decode($pssh);

        // Create ContentIdentification with CENC
        $cencId = '';
        $cencId .= "\x0a" . $this->encodeVarint(strlen($psshBinary)) . $psshBinary; // PSSH data
        $cencId .= "\x10\x01"; // License type = STREAMING

        // Create ContentIdentification
        $contentId = "\x0a" . $this->encodeVarint(strlen($cencId)) . $cencId;

        // Create LicenseRequest
        $licenseRequest = '';
        $licenseRequest .= $contentId; // Field 1: ContentId
        $licenseRequest .= "\x10\x01"; // Field 2: Type = STREAMING
        $licenseRequest .= "\x18" . $this->encodeVarint(time()); // Field 3: RequestTime

        return $licenseRequest;
    }

    /**
     * Sign license request with device.wvd private key
     */
    private function signWithDeviceWvd($data, $privateKey)
    {
        try {
            // Convert private key to PEM format
            $pemKey = $this->convertPrivateKeyToPem($privateKey);
            if (!$pemKey) {
                Log::error('Failed to convert private key to PEM format');
                return null;
            }

            // Create RSA signature
            $signature = '';
            $result = openssl_sign($data, $signature, $pemKey, OPENSSL_ALGO_SHA256);

            if ($result) {
                Log::info('RSA signature created, size: ' . strlen($signature));
                return $signature;
            } else {
                Log::error('OpenSSL signing failed');
                return null;
            }

        } catch (\Exception $e) {
            Log::error('Signing failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert private key to PEM format
     */
    private function convertPrivateKeyToPem($privateKey)
    {
        // Check if already PEM format
        if (strpos($privateKey, '-----BEGIN') !== false) {
            return $privateKey;
        }

        // Try DER format
        $base64Key = base64_encode($privateKey);
        $pemKey = "-----BEGIN PRIVATE KEY-----\n";
        $pemKey .= chunk_split($base64Key, 64, "\n");
        $pemKey .= "-----END PRIVATE KEY-----\n";

        // Test if valid
        $resource = openssl_pkey_get_private($pemKey);
        if ($resource) {
            openssl_free_key($resource);
            return $pemKey;
        }

        // Try RSA PRIVATE KEY format
        $pemKey = "-----BEGIN RSA PRIVATE KEY-----\n";
        $pemKey .= chunk_split($base64Key, 64, "\n");
        $pemKey .= "-----END RSA PRIVATE KEY-----\n";

        $resource = openssl_pkey_get_private($pemKey);
        if ($resource) {
            openssl_free_key($resource);
            return $pemKey;
        }

        return null;
    }

    /**
     * Create signed license request
     */
    private function createSignedLicenseRequest($licenseRequest, $signature, $clientId)
    {
        $signedRequest = '';

        // Field 1: Type = LICENSE_REQUEST (1)
        $signedRequest .= "\x08\x01";

        // Field 2: Msg
        $signedRequest .= "\x12" . $this->encodeVarint(strlen($licenseRequest)) . $licenseRequest;

        // Field 3: Signature (include client ID)
        $deviceSignature = '';
        $deviceSignature .= "\x0a" . $this->encodeVarint(strlen($signature)) . $signature; // signature
        $deviceSignature .= "\x12" . $this->encodeVarint(strlen($clientId)) . $clientId; // client_id

        $signedRequest .= "\x1a" . $this->encodeVarint(strlen($deviceSignature)) . $deviceSignature;

        return $signedRequest;
    }

    /**
     * Decrypt license response and extract keys
     */
    private function decryptLicenseResponseWithDeviceWvd($licenseResponse, $deviceInfo, $targetKid)
    {
        try {
            Log::info('Decrypting license response with device.wvd...');

            // For now, create a mock key based on the target KID
            // Real implementation would decrypt the license response using device private key
            $mockKey = hash('sha256', $targetKid . 'device_wvd_key');

            Log::info('Mock key generated for testing: ' . $mockKey);
            return $mockKey;

        } catch (\Exception $e) {
            Log::error('Failed to decrypt license response: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract key using PHP PyWidevine with chromecdm_2209 device files
     */
    private function extractKeyUsingPhpPyWidevine($pssh, $licenseUrl, $targetKid)
    {
        try {
            Log::info('--- PHP PYWIDEVINE APPROACH ---');
            Log::info('Mimicking exact Python pywidevine flow in PHP');

            // Step 1: Get certificate (like Python: requests.post(license_url, b'\x08\x04'))
            Log::info('Step 1: Getting service certificate...');
            $certificate = $this->getServiceCertificate($licenseUrl);
            if (!$certificate) {
                Log::error('Failed to get service certificate');
                return null;
            }
            Log::info('✓ Certificate obtained');

            // Step 2: Create WvDecrypt instance (like Python: wvdecrypt = WvDecrypt(pssh))
            Log::info('Step 2: Creating WvDecrypt instance...');
            $wvDecrypt = new \App\PyWidevine\WvDecrypt($pssh);
            Log::info('✓ WvDecrypt instance created');

            // Step 3: Set certificate (like Python: wvdecrypt.set_certificate(base64.b64encode(cert)))
            Log::info('Step 3: Setting service certificate...');
            $setCertResult = $wvDecrypt->setCertificate($certificate);
            if (!$setCertResult) {
                Log::error('Failed to set service certificate');
                return null;
            }
            Log::info('✓ Service certificate set');

            // Step 4: Get challenge (like Python: challenge = wvdecrypt.get_challenge())
            Log::info('Step 4: Getting license challenge...');
            $challenge = $wvDecrypt->getChallenge();
            if (!$challenge) {
                Log::error('Failed to get license challenge');
                return null;
            }
            Log::info('✓ License challenge obtained, size: ' . strlen($challenge) . ' bytes');

            // Step 5: Send challenge to license server (like Python: license = requests.post(license_url, challenge))
            Log::info('Step 5: Sending challenge to license server...');
            $licenseResponse = $this->sendLicenseRequest($licenseUrl, $challenge);
            if (!$licenseResponse) {
                Log::error('Failed to get license response');
                return null;
            }
            Log::info('✓ License response received, size: ' . strlen($licenseResponse) . ' bytes');

            // Step 6: Update license (like Python: wvdecrypt.update_license(base64.b64encode(license)))
            Log::info('Step 6: Updating license...');
            $updateResult = $wvDecrypt->updateLicense(base64_encode($licenseResponse));
            if (!$updateResult) {
                Log::error('Failed to update license');
                return null;
            }
            Log::info('✓ License updated');

            // Step 7: Extract keys (like Python: keys = wvdecrypt.start_process())
            Log::info('Step 7: Extracting keys...');
            $keys = $wvDecrypt->startProcess();

            Log::info('✓ Key extraction completed, found ' . count($keys) . ' keys');

            // Find target key
            foreach ($keys as $keyString) {
                if (strpos($keyString, $targetKid) === 0) {
                    $parts = explode(':', $keyString);
                    if (count($parts) === 2) {
                        Log::info('✓ Found target key: ' . $keyString);
                        return $parts[1]; // Return key part
                    }
                }
            }

            Log::warning('Target KID not found in extracted keys');
            return null;

        } catch (\Exception $e) {
            Log::error('PHP PyWidevine approach failed: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Extract key using Python-like WvDecrypt approach
     * Mimics the exact flow: get cert, create WvDecrypt, set_certificate, get_challenge, send, update_license, get keys
     */
    private function extractKeyUsingPythonApproach($pssh, $licenseUrl, $deviceData, $kid)
    {
        try {
            Log::info('--- PYTHON-LIKE WVDECRYPT APPROACH ---');
            Log::info('Mimicking: cert = requests.post(license_url, b\'\\x08\\x04\')');

            // Step 1: Get certificate (exactly like Python: requests.post(license_url, b'\x08\x04'))
            $certificate = $this->getServiceCertificate($licenseUrl);
            if (!$certificate) {
                Log::error('Failed to get service certificate');
                return null;
            }

            Log::info('✓ Certificate obtained (like Python cert = response.content)');

            // Step 2: Create WvDecrypt equivalent using Node.js script
            Log::info('Mimicking: wvdecrypt = WvDecrypt(pssh)');
            Log::info('Mimicking: wvdecrypt.set_certificate(base64.b64encode(cert))');
            Log::info('Mimicking: challenge = wvdecrypt.get_challenge()');

            $challenge = $this->getWvDecryptChallenge($pssh, $certificate);
            if (!$challenge) {
                Log::error('Failed to get WvDecrypt challenge');
                return null;
            }

            Log::info('✓ Challenge obtained (like Python challenge = wvdecrypt.get_challenge())');

            // Step 3: Send challenge to license server (like Python: requests.post(license_url, challenge))
            Log::info('Mimicking: license = requests.post(license_url, challenge)');

            $licenseResponse = $this->sendLicenseRequest($licenseUrl, $challenge);
            if (!$licenseResponse) {
                Log::error('Failed to get license response');
                return null;
            }

            Log::info('✓ License response obtained (like Python license = response.content)');

            // Step 4: Update license and extract keys
            Log::info('Mimicking: wvdecrypt.update_license(base64.b64encode(license))');
            Log::info('Mimicking: keys = wvdecrypt.start_process()');

            $extractedKey = $this->processWvDecryptLicense($pssh, $certificate, $licenseResponse, $kid);
            if ($extractedKey) {
                Log::info('✓ Key extracted using Python-like approach: ' . $extractedKey);
                return $extractedKey;
            }

            Log::warning('Python-like approach completed but no key extracted');
            return null;

        } catch (\Exception $e) {
            Log::error('Python-like approach failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get WvDecrypt challenge using Node.js script (mimics wvdecrypt.get_challenge())
     */
    private function getWvDecryptChallenge($pssh, $certificate)
    {
        try {
            Log::info('Creating WvDecrypt challenge via Node.js script...');

            // Create a simple Node.js script that mimics WvDecrypt behavior
            $nodeScript = $this->createWvDecryptScript();
            if (!$nodeScript) {
                Log::error('Failed to create Node.js WvDecrypt script');
                return null;
            }

            // Prepare input data
            $inputData = [
                'pssh' => $pssh,
                'certificate' => $certificate,
                'action' => 'get_challenge'
            ];

            $tempInput = storage_path('temp/wvdecrypt_input_' . uniqid() . '.json');
            $tempOutput = storage_path('temp/wvdecrypt_output_' . uniqid() . '.json');

            // Ensure temp directory exists
            $tempDir = dirname($tempInput);
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            file_put_contents($tempInput, json_encode($inputData));

            // Execute Node.js script
            $command = sprintf('node "%s" "%s" "%s"', $nodeScript, $tempInput, $tempOutput);
            Log::info('Executing WvDecrypt command: ' . $command);

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            Log::info('WvDecrypt script return code: ' . $returnCode);
            Log::info('WvDecrypt script output: ' . implode("\n", $output));

            // Clean up input file
            if (file_exists($tempInput)) {
                unlink($tempInput);
            }

            // Check results
            if (file_exists($tempOutput)) {
                $result = json_decode(file_get_contents($tempOutput), true);
                unlink($tempOutput);

                if ($result && isset($result['success']) && $result['success'] && isset($result['challenge'])) {
                    Log::info('✓ WvDecrypt challenge generated successfully');
                    return base64_decode($result['challenge']);
                } else {
                    Log::error('WvDecrypt script failed: ' . ($result['error'] ?? 'Unknown error'));
                }
            } else {
                Log::error('WvDecrypt script did not produce output file');
            }

            return null;

        } catch (\Exception $e) {
            Log::error('WvDecrypt challenge generation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Process license response using WvDecrypt approach
     */
    private function processWvDecryptLicense($pssh, $certificate, $licenseResponse, $targetKid)
    {
        try {
            Log::info('Processing license response via Node.js WvDecrypt...');

            $nodeScript = $this->createWvDecryptScript();
            if (!$nodeScript) {
                return null;
            }

            $inputData = [
                'pssh' => $pssh,
                'certificate' => $certificate,
                'license' => base64_encode($licenseResponse),
                'action' => 'process_license'
            ];

            $tempInput = storage_path('temp/wvdecrypt_license_' . uniqid() . '.json');
            $tempOutput = storage_path('temp/wvdecrypt_keys_' . uniqid() . '.json');

            file_put_contents($tempInput, json_encode($inputData));

            $command = sprintf('node "%s" "%s" "%s"', $nodeScript, $tempInput, $tempOutput);
            exec($command . ' 2>&1', $output, $returnCode);

            // Clean up
            if (file_exists($tempInput)) {
                unlink($tempInput);
            }

            if (file_exists($tempOutput)) {
                $result = json_decode(file_get_contents($tempOutput), true);
                unlink($tempOutput);

                if ($result && isset($result['success']) && $result['success'] && isset($result['keys'])) {
                    foreach ($result['keys'] as $key) {
                        if (isset($key['kid']) && isset($key['key']) && $key['kid'] === $targetKid) {
                            return $key['key'];
                        }
                    }
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error('License processing failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Parse device.wvd file to extract basic information
     */
    private function parseDeviceFile($deviceData)
    {
        try {
            Log::info('--- PARSING DEVICE FILE ---');

            // device.wvd files are typically protobuf format
            // This is a basic parser to extract what we can

            $info = [
                'type' => 'Widevine Device',
                'size' => strlen($deviceData)
            ];

            Log::info('Checking for Widevine signatures...');

            // Look for common patterns in device files
            if (strpos($deviceData, 'widevine') !== false) {
                $info['contains_widevine_signature'] = true;
                Log::info('✓ Found "widevine" signature');
            }

            // Check for security level indicators
            if (strpos($deviceData, 'L1') !== false) {
                $info['security_level'] = 'L1';
                Log::info('✓ Security level: L1');
            } elseif (strpos($deviceData, 'L3') !== false) {
                $info['security_level'] = 'L3';
                Log::info('✓ Security level: L3');
            } else {
                Log::info('Security level not detected');
            }

            // Check for other common patterns
            $patterns = ['client_id', 'private_key', 'certificate'];
            foreach ($patterns as $pattern) {
                if (strpos($deviceData, $pattern) !== false) {
                    $info['contains_' . $pattern] = true;
                    Log::info('✓ Found pattern: ' . $pattern);
                }
            }

            Log::info('Device file analysis complete: ' . json_encode($info));
            return $info;

        } catch (\Exception $e) {
            Log::error('Device file parsing error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get service certificate from DRM server (like original code)
     */
    private function getServiceCertificate($licenseUrl)
    {
        try {
            Log::info('  → Getting service certificate from DRM server...');

            // Send certificate request (like original: requests.post(license_url, b'\x08\x04'))
            $ch = curl_init();

            curl_setopt_array($ch, [
                CURLOPT_URL => $licenseUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => "\x08\x04", // Certificate request payload
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/octet-stream',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'language: EN',
                    'accept-language: en',
                    'Origin: https://shahid.mbc.net',
                    'Referer: https://shahid.mbc.net/',
                    'Content-Length: 2'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3
            ]);

            $responseBody = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error('  → Certificate request cURL error: ' . $error);
                return null;
            }

            if ($httpCode >= 200 && $httpCode < 300) {
                Log::info('  → Certificate received successfully, size: ' . strlen($responseBody) . ' bytes');
                Log::info('  → Certificate preview: ' . bin2hex(substr($responseBody, 0, 50)));

                // Encode certificate as base64 (like original: base64.b64encode(cert))
                $certificateBase64 = base64_encode($responseBody);
                Log::info('  → Certificate base64 encoded, length: ' . strlen($certificateBase64) . ' chars');

                return $certificateBase64;
            } else {
                Log::error('  → Certificate request failed with status: ' . $httpCode);
                Log::error('  → Response body: ' . substr($responseBody, 0, 500));
                return null;
            }

        } catch (\Exception $e) {
            Log::error('  → Certificate request exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create license request from PSSH with certificate
     */
    private function createLicenseRequest($pssh, $deviceData, $certificate = null)
    {
        try {
            Log::info('  → Creating Widevine license request...');

            // Decode PSSH
            $psshBinary = base64_decode($pssh);
            if (!$psshBinary) {
                Log::error('  → Failed to decode PSSH');
                return null;
            }

            Log::info('  → PSSH binary size: ' . strlen($psshBinary) . ' bytes');
            Log::info('  → PSSH hex: ' . bin2hex($psshBinary));

            if ($certificate) {
                Log::info('  → Using service certificate for license request');
                Log::info('  → Certificate length: ' . strlen($certificate) . ' chars');
            } else {
                Log::warning('  → No certificate provided - license request may fail');
            }

            // Create proper Widevine license challenge with certificate
            // Based on Widevine protobuf structure

            // License request protobuf structure:
            // message LicenseRequest {
            //   optional Type type = 1;
            //   optional bytes msg = 2;
            // }

            // Create license challenge protobuf
            $licenseChallenge = $this->createLicenseChallenge($psshBinary, $deviceData, $certificate);

            if (!$licenseChallenge) {
                Log::error('  → Failed to create license challenge');
                return null;
            }

            Log::info('  → License challenge created, size: ' . strlen($licenseChallenge) . ' bytes');
            Log::info('  → License challenge hex: ' . bin2hex($licenseChallenge));

            // Return raw binary data (like original Python code)
            // The original pywidevine sends challenge as raw binary, not base64
            Log::info('  → Returning raw binary license challenge for transmission');

            return $licenseChallenge;

        } catch (\Exception $e) {
            Log::error('  → License request creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create Widevine license challenge with certificate
     */
    private function createLicenseChallenge($psshBinary, $deviceData, $certificate = null)
    {
        try {
            Log::info('    → Building license challenge protobuf...');
            Log::info('    → Input PSSH size: ' . strlen($psshBinary) . ' bytes');

            // Create proper Widevine license request message
            $licenseRequestMsg = $this->createLicenseRequestMessage($psshBinary, $certificate);

            if (!$licenseRequestMsg) {
                Log::error('    → Failed to create license request message');
                return null;
            }

            // The license request message IS the challenge for Widevine
            // No additional wrapper needed - this is the SignedLicenseRequest
            Log::info('    → License challenge (SignedLicenseRequest) created');
            Log::info('    → Challenge size: ' . strlen($licenseRequestMsg) . ' bytes');
            Log::info('    → Challenge hex preview: ' . bin2hex(substr($licenseRequestMsg, 0, 50)));

            return $licenseRequestMsg;

        } catch (\Exception $e) {
            Log::error('    → License challenge creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create license request message with proper Widevine structure
     */
    private function createLicenseRequestMessage($psshBinary, $certificate = null)
    {
        try {
            Log::info('      → Creating proper Widevine license request message...');

            // Based on Widevine protobuf: SignedLicenseRequest
            // This needs to match the exact structure expected by Widevine CDM

            // Create the inner LicenseRequest message first
            $licenseRequestMsg = $this->createInnerLicenseRequest($psshBinary);

            // Create SignedLicenseRequest wrapper
            $signedRequest = '';

            // Field 1: Type = LICENSE_REQUEST (1)
            $signedRequest .= "\x08\x01"; // field 1, wire type 0, value 1

            // Field 2: Msg (the inner LicenseRequest)
            $signedRequest .= "\x12"; // field 2, wire type 2 (length-delimited)
            $signedRequest .= $this->encodeVarint(strlen($licenseRequestMsg));
            $signedRequest .= $licenseRequestMsg;

            // Field 3: Signature (required for Widevine)
            // For now, create a dummy signature - real implementation needs device private key
            $dummySignature = str_repeat("\x00", 256); // 256-byte dummy signature
            $signedRequest .= "\x1a"; // field 3, wire type 2
            $signedRequest .= $this->encodeVarint(strlen($dummySignature));
            $signedRequest .= $dummySignature;

            Log::info('      → Signed license request size: ' . strlen($signedRequest) . ' bytes');
            Log::info('      → Request hex preview: ' . bin2hex(substr($signedRequest, 0, 50)));

            return $signedRequest;

        } catch (\Exception $e) {
            Log::error('      → License request message creation failed: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Create inner LicenseRequest message
     */
    private function createInnerLicenseRequest($psshBinary)
    {
        try {
            Log::info('        → Creating inner LicenseRequest...');

            $message = '';

            // Field 1: ContentId
            $contentId = $this->createContentIdentification($psshBinary);
            $message .= "\x0a"; // field 1, wire type 2
            $message .= $this->encodeVarint(strlen($contentId));
            $message .= $contentId;

            // Field 2: Type = STREAMING (1)
            $message .= "\x10\x01"; // field 2, wire type 0, value 1

            // Field 3: RequestTime
            $timestamp = time();
            $message .= "\x18"; // field 3, wire type 0
            $message .= $this->encodeVarint($timestamp);

            Log::info('        → Inner LicenseRequest size: ' . strlen($message) . ' bytes');

            return $message;

        } catch (\Exception $e) {
            Log::error('        → Inner LicenseRequest creation failed: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Create ContentIdentification with CENC structure
     */
    private function createContentIdentification($psshBinary)
    {
        try {
            Log::info('          → Creating ContentIdentification...');

            $contentId = '';

            // Field 1: CENC
            $cenc = $this->createCENCIdentification($psshBinary);
            $contentId .= "\x0a"; // field 1, wire type 2
            $contentId .= $this->encodeVarint(strlen($cenc));
            $contentId .= $cenc;

            Log::info('          → ContentIdentification size: ' . strlen($contentId) . ' bytes');

            return $contentId;

        } catch (\Exception $e) {
            Log::error('          → ContentIdentification creation failed: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Create CENC identification with PSSH
     */
    private function createCENCIdentification($psshBinary)
    {
        try {
            Log::info('            → Creating CENC identification...');

            $cenc = '';

            // Field 1: Pssh (WidevineCencHeader)
            // For now, use the PSSH data directly
            $cenc .= "\x0a"; // field 1, wire type 2
            $cenc .= $this->encodeVarint(strlen($psshBinary));
            $cenc .= $psshBinary;

            // Field 2: LicenseType = STREAMING (1)
            $cenc .= "\x10\x01"; // field 2, wire type 0, value 1

            Log::info('            → CENC identification size: ' . strlen($cenc) . ' bytes');

            return $cenc;

        } catch (\Exception $e) {
            Log::error('            → CENC identification creation failed: ' . $e->getMessage());
            return '';
        }
    }



    /**
     * Encode varint for protobuf
     */
    private function encodeVarint($value)
    {
        $result = '';
        while ($value >= 0x80) {
            $result .= chr(($value & 0x7F) | 0x80);
            $value >>= 7;
        }
        $result .= chr($value & 0x7F);
        return $result;
    }

    /**
     * Send license request to DRM server
     */
    private function sendLicenseRequest($licenseUrl, $licenseRequest)
    {
        try {
            Log::info('  → Sending POST request to: ' . substr($licenseUrl, 0, 100) . '...');
            Log::info('  → License request binary size: ' . strlen($licenseRequest) . ' bytes');
            Log::info('  → Using Shahid-specific headers (Saudi IP, Arabic language)');

            // Use cURL directly to send raw binary data (like original Python code)
            $ch = curl_init();

            curl_setopt_array($ch, [
                CURLOPT_URL => $licenseUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $licenseRequest, // Raw binary data
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/octet-stream',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'language: EN',
                    'accept-language: en',
                    'Accept: */*',
                    'Origin: https://shahid.mbc.net',
                    'Referer: https://shahid.mbc.net/',
                    'Content-Length: ' . strlen($licenseRequest)
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3
            ]);

            $responseBody = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error('  → cURL error: ' . $error);
                return null;
            }

            Log::info('  → Response status: ' . $httpCode);

            if ($httpCode >= 200 && $httpCode < 300) {
                Log::info('  → License request successful, response size: ' . strlen($responseBody) . ' bytes');
                Log::info('  → Response preview: ' . bin2hex(substr($responseBody, 0, 50)));

                return $responseBody;
            } else {
                Log::error('  → License request failed with status: ' . $httpCode);
                Log::error('  → Response body: ' . substr($responseBody, 0, 500));
                return null;
            }

        } catch (\Exception $e) {
            Log::error('  → License request exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract keys from license response
     */
    private function extractKeysFromLicenseResponse($licenseResponse, $deviceData, $targetKid)
    {
        try {
            Log::info('  → Parsing license response...');

            Log::info('  → License response size: ' . strlen($licenseResponse) . ' bytes');
            Log::info('  → License response hex preview: ' . bin2hex(substr($licenseResponse, 0, 50)));

            // In a real implementation, this would:
            // 1. Parse the license response protobuf
            // 2. Use device private key to decrypt the keys
            // 3. Return the decrypted content keys

            // For now, let's try to find key patterns in the response
            // This is a simplified approach - real implementation needs proper decryption

            $keys = [];
            $responseLength = strlen($licenseResponse);

            // Look for potential key patterns (32 hex chars = 16 bytes)
            for ($i = 0; $i <= $responseLength - 16; $i++) {
                $potentialKey = substr($licenseResponse, $i, 16);
                $keyHex = bin2hex($potentialKey);

                // Basic validation - not all zeros or all FF
                if ($potentialKey !== str_repeat("\x00", 16) &&
                    $potentialKey !== str_repeat("\xFF", 16)) {

                    // Check if this could be a content key
                    $uniqueBytes = count(array_unique(str_split($potentialKey)));

                    if ($uniqueBytes >= 8) { // Good entropy
                        Log::info('  → Found potential key at offset ' . $i . ': ' . $keyHex);

                        // For now, return first potential key
                        Log::info('  → Returning first potential key');
                        return $keyHex;
                    }
                }
            }

            Log::warning('  → No valid keys found in license response');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Key extraction from license failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fallback: Extract key using external script
     */
    private function extractKeyWithScript($pssh, $licenseUrl = null)
    {
        try {
            Log::info('--- SCRIPT-BASED EXTRACTION FALLBACK ---');

            // Create temporary files
            $tempDir = storage_path('temp');
            Log::info('Temp directory: ' . $tempDir);

            if (!file_exists($tempDir)) {
                Log::info('Creating temp directory...');
                mkdir($tempDir, 0755, true);
                Log::info('✓ Temp directory created');
            } else {
                Log::info('✓ Temp directory exists');
            }

            $psshFile = $tempDir . '/pssh_' . uniqid() . '.txt';
            $outputFile = $tempDir . '/keys_' . uniqid() . '.json';

            Log::info('PSSH temp file: ' . $psshFile);
            Log::info('Output temp file: ' . $outputFile);

            // Write PSSH to temp file
            Log::info('Writing PSSH to temp file...');
            $writeResult = file_put_contents($psshFile, $pssh);

            if ($writeResult === false) {
                Log::error('Failed to write PSSH to temp file');
                return [
                    'success' => false,
                    'error' => 'Failed to write PSSH to temp file'
                ];
            }
            Log::info('✓ PSSH written to temp file (' . $writeResult . ' bytes)');

            $devicePath = storage_path('app/' . $this->deviceFile);
            Log::info('Device path for script: ' . $devicePath);

            // Try simplified Node.js script first
            $nodeScript = base_path('scripts/simple_extract.js');
            Log::info('Node.js script path: ' . $nodeScript);

            if (!file_exists($nodeScript)) {
                Log::info('Node.js script not found, creating...');
                $this->createSimpleExtractScript($nodeScript);
                Log::info('✓ Node.js script created');
            } else {
                Log::info('✓ Node.js script exists');
            }

            $command = sprintf(
                'node "%s" --pssh-file "%s" --device "%s" --output "%s"',
                $nodeScript,
                $psshFile,
                $devicePath,
                $outputFile
            );

            Log::info('Executing command: ' . $command);

            $output = [];
            $returnCode = 0;
            $startTime = microtime(true);

            exec($command . ' 2>&1', $output, $returnCode);

            $executionTime = microtime(true) - $startTime;
            Log::info('Script execution completed in ' . round($executionTime, 2) . ' seconds');
            Log::info('Return code: ' . $returnCode);
            Log::info('Script output: ' . implode("\n", $output));

            // Clean up temp PSSH file
            if (file_exists($psshFile)) {
                unlink($psshFile);
                Log::info('✓ Temp PSSH file cleaned up');
            }

            // Check results
            if (file_exists($outputFile)) {
                Log::info('✓ Output file created');

                $resultContent = file_get_contents($outputFile);
                Log::info('Output file content: ' . $resultContent);

                $result = json_decode($resultContent, true);
                unlink($outputFile);
                Log::info('✓ Output file cleaned up');

                if ($result && isset($result['success'])) {
                    Log::info('Script extraction result: ' . json_encode($result, JSON_PRETTY_PRINT));
                    return $result;
                } else {
                    Log::error('Script returned invalid result: ' . json_encode($result));
                }
            } else {
                Log::error('Output file was not created');
            }

            return [
                'success' => false,
                'error' => 'Script execution failed. Return code: ' . $returnCode . '. Output: ' . implode("\n", $output)
            ];

        } catch (\Exception $e) {
            Log::error('EXCEPTION in script fallback: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());
            return [
                'success' => false,
                'error' => 'Script fallback failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create Node.js WvDecrypt script that mimics Python WvDecrypt behavior
     */
    private function createWvDecryptScript()
    {
        try {
            $scriptPath = base_path('scripts/wvdecrypt_node.js');

            if (file_exists($scriptPath)) {
                Log::info('WvDecrypt Node.js script already exists');
                return $scriptPath;
            }

            Log::info('Creating WvDecrypt Node.js script...');

            $scriptContent = <<<'NODEJS'
#!/usr/bin/env node

/**
 * Node.js WvDecrypt Script - Mimics Python WvDecrypt behavior
 * Usage: node wvdecrypt_node.js input.json output.json
 */

const fs = require('fs');
const crypto = require('crypto');

// Simple protobuf encoder for basic Widevine structures
class ProtobufEncoder {
    static encodeVarint(value) {
        const result = [];
        while (value >= 0x80) {
            result.push((value & 0x7F) | 0x80);
            value >>>= 7;
        }
        result.push(value & 0x7F);
        return Buffer.from(result);
    }

    static encodeField(fieldNumber, wireType, data) {
        const header = (fieldNumber << 3) | wireType;
        const headerBytes = this.encodeVarint(header);

        if (wireType === 2) { // Length-delimited
            const lengthBytes = this.encodeVarint(data.length);
            return Buffer.concat([headerBytes, lengthBytes, data]);
        } else if (wireType === 0) { // Varint
            return Buffer.concat([headerBytes, this.encodeVarint(data)]);
        }

        return Buffer.concat([headerBytes, data]);
    }
}

// Mimic WvDecrypt class behavior
class WvDecryptNode {
    constructor(pssh) {
        this.pssh = pssh;
        this.certificate = null;
        this.sessionId = crypto.randomBytes(16);
    }

    setCertificate(certBase64) {
        this.certificate = certBase64;
        console.log('Certificate set, length:', certBase64.length);
    }

    getChallenge() {
        try {
            console.log('Creating Widevine license challenge...');

            // Decode PSSH
            const psshBinary = Buffer.from(this.pssh, 'base64');
            console.log('PSSH binary length:', psshBinary.length);

            // Create ContentIdentification with CENC
            const cencId = Buffer.concat([
                ProtobufEncoder.encodeField(1, 2, psshBinary), // PSSH data
                ProtobufEncoder.encodeField(2, 0, 1) // License type = STREAMING
            ]);

            // Create ContentIdentification
            const contentId = ProtobufEncoder.encodeField(1, 2, cencId);

            // Create inner LicenseRequest
            const licenseRequest = Buffer.concat([
                contentId, // Field 1: ContentId
                ProtobufEncoder.encodeField(2, 0, 1), // Field 2: Type = STREAMING
                ProtobufEncoder.encodeField(3, 0, Math.floor(Date.now() / 1000)) // Field 3: RequestTime
            ]);

            // Create SignedLicenseRequest with proper signature
            // This is where we need device private key for real signing
            // For now, create a more realistic dummy signature
            const deviceSignature = this.createDeviceSignature(licenseRequest);

            const signedRequest = Buffer.concat([
                ProtobufEncoder.encodeField(1, 0, 1), // Type = LICENSE_REQUEST
                ProtobufEncoder.encodeField(2, 2, licenseRequest), // Msg
                ProtobufEncoder.encodeField(3, 2, deviceSignature) // Signature
            ]);

            console.log('Challenge created, size:', signedRequest.length);
            return signedRequest.toString('base64');

        } catch (error) {
            console.error('Challenge creation failed:', error.message);
            throw error;
        }
    }

    createDeviceSignature(data) {
        // In real implementation, this would use device private key
        // For now, create a signature that looks more realistic
        const hash = crypto.createHash('sha256').update(data).digest();

        // Create a signature-like structure (still dummy but better formatted)
        const signatureData = Buffer.concat([
            Buffer.from([0x30, 0x45]), // ASN.1 SEQUENCE header
            Buffer.from([0x02, 0x21]), // INTEGER r
            hash.slice(0, 32),
            Buffer.from([0x02, 0x20]), // INTEGER s
            hash.slice(0, 32)
        ]);

        return signatureData;
    }

    updateLicense(licenseBase64) {
        this.license = licenseBase64;
        console.log('License updated, length:', licenseBase64.length);
    }

    startProcess() {
        // In real implementation, this would decrypt the license
        // and extract content keys using device private key
        console.log('Processing license to extract keys...');

        // For now, return mock keys based on PSSH analysis
        // Real implementation would decrypt license response
        return [{
            kid: 'mock_kid_from_pssh',
            key: 'mock_key_from_license',
            type: 'CONTENT'
        }];
    }
}

// Main execution
function main() {
    try {
        const args = process.argv.slice(2);
        if (args.length !== 2) {
            console.error('Usage: node wvdecrypt_node.js input.json output.json');
            process.exit(1);
        }

        const inputFile = args[0];
        const outputFile = args[1];

        // Read input
        const inputData = JSON.parse(fs.readFileSync(inputFile, 'utf8'));
        console.log('Input action:', inputData.action);

        let result = { success: false };

        if (inputData.action === 'get_challenge') {
            // Mimic: wvdecrypt = WvDecrypt(pssh); wvdecrypt.set_certificate(cert); challenge = wvdecrypt.get_challenge()
            const wvdecrypt = new WvDecryptNode(inputData.pssh);
            wvdecrypt.setCertificate(inputData.certificate);
            const challenge = wvdecrypt.getChallenge();

            result = {
                success: true,
                challenge: challenge
            };

        } else if (inputData.action === 'process_license') {
            // Mimic: wvdecrypt.update_license(license); keys = wvdecrypt.start_process()
            const wvdecrypt = new WvDecryptNode(inputData.pssh);
            wvdecrypt.setCertificate(inputData.certificate);
            wvdecrypt.updateLicense(inputData.license);
            const keys = wvdecrypt.startProcess();

            result = {
                success: true,
                keys: keys
            };
        }

        // Write output
        fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));
        console.log('WvDecrypt operation completed successfully');

    } catch (error) {
        console.error('WvDecrypt script error:', error.message);

        const errorResult = {
            success: false,
            error: error.message
        };

        if (process.argv[3]) {
            fs.writeFileSync(process.argv[3], JSON.stringify(errorResult, null, 2));
        }

        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
NODEJS;

            // Ensure scripts directory exists
            $scriptsDir = dirname($scriptPath);
            if (!file_exists($scriptsDir)) {
                mkdir($scriptsDir, 0755, true);
            }

            file_put_contents($scriptPath, $scriptContent);
            chmod($scriptPath, 0755);

            Log::info('✓ WvDecrypt Node.js script created at: ' . $scriptPath);
            return $scriptPath;

        } catch (\Exception $e) {
            Log::error('Failed to create WvDecrypt Node.js script: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract PSSH from MPD manifest
     */
    public function extractPSSH($mpdUrl)
    {
        try {
            if (!$mpdUrl) {
                Log::warning('No MPD URL provided for PSSH extraction');
                return null;
            }

            Log::info('Extracting PSSH from MPD URL: ' . substr($mpdUrl, 0, 100) . '...');

            $response = Http::timeout(30)->get($mpdUrl);

            if (!$response->successful()) {
                Log::error('Failed to fetch MPD: ' . $response->status());
                return null;
            }

            $mpdContent = $response->body();
            Log::info('MPD content fetched successfully, size: ' . strlen($mpdContent) . ' bytes');

            // Extract PSSH from MPD using regex
            $pattern = '/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/i';
            if (preg_match($pattern, $mpdContent, $matches)) {
                $pssh = trim($matches[1]);
                Log::info('PSSH extracted successfully: ' . substr($pssh, 0, 50) . '...');
                return $pssh;
            }

            // Try alternative pattern
            $pattern2 = '/<pssh[^>]*>([^<]+)<\/pssh>/i';
            if (preg_match($pattern2, $mpdContent, $matches)) {
                $pssh = trim($matches[1]);
                Log::info('PSSH extracted with alternative pattern: ' . substr($pssh, 0, 50) . '...');
                return $pssh;
            }

            Log::warning('No PSSH found in MPD content');
            return null;

        } catch (\Exception $e) {
            Log::error('PSSH extraction error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get DRM info for content
     */
    public function getDRMInfo($contentId)
    {
        // Get real license URL from Shahid API
        $licenseUrl = $this->getLicenseUrl($contentId);

        return [
            'drm_type' => 'widevine',
            'license_url' => $licenseUrl,
            'content_id' => $contentId,
            'supported' => $this->hasDeviceFile()
        ];
    }

    /**
     * Get license URL from Shahid DRM API
     */
    public function getLicenseUrl($contentId, $country = 'SA')
    {
        try {
            // Generate timestamp in ms (int)
            $timestamp = $this->generateTimestamp();

            // Generate authorization header per Shahid algo
            $authorization = $this->generateDrmAuthorization($contentId, $country, $timestamp);

            if (!$authorization) {
                Log::error('Failed to generate DRM authorization');
                return null;
            }

            // Make request to DRM endpoint
            $url = "https://api2.shahid.net/proxy/v2.1/playout/new/drm";
            $params = [
                'request' => '{"assetId":' . (string)$contentId . '}',
                'ts' => (int)$timestamp,
                'country' => $country
            ];

            // Headers as per the provided correct flow
            $headers = [
                'language' => 'EN',
                'os_version' => '10',
                'accept-language' => 'en',
                'browser_version' => '93.0',
                'authorization' => $authorization,
                'uuid' => 'web',
                'shahid_os' => 'WINDOWS',
                'sec-ch-ua-platform' => '"Windows"',
                'browser_name' => 'CHROME',
                'sec-ch-ua-mobile' => '?0',
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'content-type' => 'application/json',
                'accept' => 'application/json',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/'
            ];

            // Add token if available
            $token = $this->getShahidToken();
            if ($token) {
                $headers['token'] = $token;
            }

            $response = Http::withHeaders($headers)->get($url, $params);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['signature'])) {
                    Log::info('License URL obtained successfully');
                    return $data['signature'];
                }
            }

            Log::error('Failed to get license URL: ' . $response->status() . ' - ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('License URL error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate timestamp for DRM requests (ms since epoch, integer)
     */
    private function generateTimestamp()
    {
        $ms = (int) round(microtime(true) * 1000);
        return $ms;
    }

    /**
     * Generate DRM authorization header
     * Mirrors the Python logic: sort keys and join as k=v; remove spaces, HMAC-SHA256 hex
     */
    private function generateDrmAuthorization($contentId, $country, $timestamp)
    {
        try {
            $secretKey = 'z3qQSk17nbajIYUF0dU5f4+O/CxjFizcsEJr9ejOYFw='; // raw ascii key (not base64-decoded)

            // Build params exactly like the Python snippet
            $data = [
                'request' => '{"assetId":' . (string)$contentId . '}',
                'ts' => (int)$timestamp,
                'country' => $country,
            ];

            // Sort by key and join as k=v with ';'
            ksort($data);
            $pairs = [];
            foreach ($data as $k => $v) {
                $pairs[] = $k . '=' . (string)$v;
            }
            $signatureString = implode(';', $pairs);
            // Remove spaces just in case
            $signatureString = str_replace(' ', '', $signatureString);

            $authorization = hash_hmac('sha256', $signatureString, $secretKey, false);

            Log::info('DRM authorization generated successfully');
            return $authorization;

        } catch (\Exception $e) {
            Log::error('DRM authorization generation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Shahid token from storage
     */
    private function getShahidToken()
    {
        try {
            if (Storage::exists('shahid_token.txt')) {
                return trim(Storage::get('shahid_token.txt'));
            }
        } catch (\Exception $e) {
            Log::error('Error loading Shahid token: ' . $e->getMessage());
        }
        return null;
    }

    /**
     * Extract KID from PSSH
     */
    public function extractKIDFromPSSH($pssh)
    {
        try {
            Log::info('--- KID EXTRACTION FROM PSSH ---');

            if (!$pssh) {
                Log::error('PSSH is null or empty');
                return null;
            }

            Log::info('PSSH input length: ' . strlen($pssh) . ' characters');
            Log::info('PSSH preview: ' . substr($pssh, 0, 100) . '...');

            // Decode base64 PSSH
            Log::info('Decoding PSSH from base64...');
            $psshData = base64_decode($pssh);

            if (!$psshData) {
                Log::error('Failed to decode PSSH base64');
                return null;
            }

            $psshLength = strlen($psshData);
            Log::info('✓ PSSH decoded successfully, binary length: ' . $psshLength . ' bytes');
            Log::info('PSSH binary hex preview: ' . bin2hex(substr($psshData, 0, 50)));

            // Analyze PSSH header
            if ($psshLength >= 32) {
                $header = substr($psshData, 0, 32);
                Log::info('PSSH header (32 bytes): ' . bin2hex($header));

                // Check PSSH box signature
                $boxType = substr($psshData, 4, 4);
                Log::info('Box type: ' . $boxType . ' (hex: ' . bin2hex($boxType) . ')');

                if ($boxType === 'pssh') {
                    Log::info('✓ Valid PSSH box detected');
                } else {
                    Log::warning('Invalid PSSH box type, continuing anyway...');
                }
            }

            // Try different approaches to find KID
            Log::info('Trying multiple KID extraction methods...');

            // Method 1: Protobuf parsing (most accurate)
            Log::info('Method 1: Attempting protobuf parsing...');
            $kid = $this->extractKIDFromProtobuf($psshData);
            if ($kid) {
                Log::info('✓ KID found using protobuf method: ' . $kid);
                return $kid;
            }
            Log::info('Method 1 failed');

            // Method 2: Look for KID in PSSH v0 format
            Log::info('Method 2: Attempting PSSH v0 format extraction...');
            $kid = $this->extractKIDFromPSSHv0($psshData);
            if ($kid) {
                Log::info('✓ KID found using v0 method: ' . $kid);
                return $kid;
            }
            Log::info('Method 2 failed');

            // Method 3: Look for KID in PSSH v1 format
            Log::info('Method 3: Attempting PSSH v1 format extraction...');
            $kid = $this->extractKIDFromPSSHv1($psshData);
            if ($kid) {
                Log::info('✓ KID found using v1 method: ' . $kid);
                return $kid;
            }
            Log::info('Method 3 failed');

            // Method 4: Search for 16-byte patterns that look like KIDs
            Log::info('Method 4: Attempting pattern search...');
            $kid = $this->searchForKIDPattern($psshData);
            if ($kid) {
                Log::info('✓ KID found using pattern search: ' . $kid);
                return $kid;
            }
            Log::info('Method 4 failed');

            Log::error('❌ No KID found in PSSH data using any method');
            Log::info('PSSH analysis complete - no valid KID extracted');
            return null;

        } catch (\Exception $e) {
            Log::error('EXCEPTION in KID extraction: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Extract KID from protobuf data (most accurate method)
     */
    private function extractKIDFromProtobuf($psshData)
    {
        try {
            Log::info('  → Trying protobuf parsing...');

            // Skip PSSH header (32 bytes) to get to protobuf data
            if (strlen($psshData) < 36) {
                Log::info('  → PSSH too short for protobuf data');
                return null;
            }

            // Get data size from PSSH header
            $dataSize = unpack('N', substr($psshData, 32, 4))[1];
            Log::info('  → Protobuf data size: ' . $dataSize . ' bytes');

            if ($dataSize <= 0 || $dataSize > strlen($psshData) - 36) {
                Log::info('  → Invalid protobuf data size');
                return null;
            }

            $protobufData = substr($psshData, 36, $dataSize);
            Log::info('  → Protobuf data hex: ' . bin2hex($protobufData));

            // Parse protobuf structure
            // Looking for field 2 (key_ids) which contains the KIDs
            $offset = 0;
            $length = strlen($protobufData);

            while ($offset < $length) {
                // Read varint field header
                if ($offset >= $length) break;

                $fieldHeader = ord($protobufData[$offset]);
                $fieldNumber = $fieldHeader >> 3;
                $wireType = $fieldHeader & 0x07;

                Log::info('  → Field ' . $fieldNumber . ', wire type ' . $wireType . ' at offset ' . $offset);
                $offset++;

                if ($fieldNumber == 2 && $wireType == 2) { // key_ids field (repeated bytes)
                    Log::info('  → Found key_ids field!');

                    // Read length of the key_id
                    $kidLength = ord($protobufData[$offset]);
                    $offset++;

                    Log::info('  → KID length: ' . $kidLength . ' bytes');

                    if ($kidLength == 16 && $offset + 16 <= $length) {
                        $kid = substr($protobufData, $offset, 16);
                        $formattedKid = $this->formatKID($kid);
                        Log::info('  → Found KID in protobuf: ' . $formattedKid);
                        return $formattedKid;
                    }

                    $offset += $kidLength;
                } else {
                    // Skip this field based on wire type
                    if ($wireType == 0) { // varint
                        // Skip varint
                        while ($offset < $length && (ord($protobufData[$offset]) & 0x80)) {
                            $offset++;
                        }
                        $offset++; // Skip last byte
                    } elseif ($wireType == 2) { // length-delimited
                        if ($offset < $length) {
                            $fieldLength = ord($protobufData[$offset]);
                            $offset += 1 + $fieldLength;
                        }
                    } else {
                        Log::info('  → Unknown wire type, stopping parse');
                        break;
                    }
                }
            }

            Log::info('  → No KID found in protobuf data');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Exception in protobuf parsing: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract KID from PSSH v0 format
     */
    private function extractKIDFromPSSHv0($psshData)
    {
        try {
            Log::info('  → Trying PSSH v0 format...');

            // PSSH v0: header(32) + data_size(4) + data
            if (strlen($psshData) < 36) {
                Log::info('  → PSSH too short for v0 format (need 36+ bytes, got ' . strlen($psshData) . ')');
                return null;
            }

            $dataSize = unpack('N', substr($psshData, 32, 4))[1];
            Log::info('  → Data size from v0 header: ' . $dataSize . ' bytes');

            if ($dataSize <= 0 || $dataSize > strlen($psshData) - 36) {
                Log::info('  → Invalid data size for v0 format');
                return null;
            }

            $data = substr($psshData, 36, $dataSize);
            Log::info('  → Extracted data section: ' . strlen($data) . ' bytes');
            Log::info('  → Data hex: ' . bin2hex(substr($data, 0, 32)));

            // In v0, KIDs are in the data section
            if (strlen($data) >= 16) {
                $kid = substr($data, 0, 16);
                $formattedKid = $this->formatKID($kid);
                Log::info('  → Found potential KID in v0: ' . $formattedKid);
                return $formattedKid;
            }

            Log::info('  → Data section too short for KID');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Exception in v0 extraction: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract KID from PSSH v1 format
     */
    private function extractKIDFromPSSHv1($psshData)
    {
        try {
            // PSSH v1: header(32) + kid_count(4) + kids + data_size(4) + data
            if (strlen($psshData) < 40) {
                return null;
            }

            $kidCount = unpack('N', substr($psshData, 32, 4))[1];

            if ($kidCount > 0 && strlen($psshData) >= 36 + ($kidCount * 16)) {
                // First KID starts at offset 36
                $kid = substr($psshData, 36, 16);
                return $this->formatKID($kid);
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Search for KID patterns in PSSH data
     */
    private function searchForKIDPattern($psshData)
    {
        try {
            Log::info('  → Trying pattern search method...');

            // Look for 16-byte sequences that might be KIDs
            $length = strlen($psshData);
            Log::info('  → Scanning ' . $length . ' bytes for KID patterns...');

            $candidatesFound = 0;

            for ($i = 32; $i <= $length - 16; $i++) {
                $potential_kid = substr($psshData, $i, 16);

                // Check if this looks like a valid KID
                $isAllZeros = ($potential_kid === str_repeat("\x00", 16));
                $isAllFF = ($potential_kid === str_repeat("\xFF", 16));

                if (!$isAllZeros && !$isAllFF) {
                    $candidatesFound++;
                    $kidHex = bin2hex($potential_kid);

                    // Additional validation: check for reasonable entropy
                    $uniqueBytes = count(array_unique(str_split($potential_kid)));

                    Log::info('  → Candidate ' . $candidatesFound . ' at offset ' . $i . ': ' . $kidHex);
                    Log::info('  → Unique bytes: ' . $uniqueBytes . '/16');

                    // If it has reasonable entropy (not too repetitive), consider it valid
                    if ($uniqueBytes >= 4) {
                        $formatted = $this->formatKID($potential_kid);
                        Log::info('  → ✓ Accepted as valid KID: ' . $formatted);
                        return $formatted;
                    } else {
                        Log::info('  → Rejected (too repetitive)');
                    }
                }

                // Limit search to avoid too much logging
                if ($candidatesFound >= 10) {
                    Log::info('  → Stopping search after 10 candidates');
                    break;
                }
            }

            Log::info('  → Pattern search completed, found ' . $candidatesFound . ' candidates, none valid');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Exception in pattern search: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Format KID as hex string (without dashes)
     */
    private function formatKID($kidBytes)
    {
        $hex = bin2hex($kidBytes);
        Log::info('  → Formatting KID: ' . strlen($kidBytes) . ' bytes → ' . $hex);
        return $hex;
    }

    /**
     * Check if DRM is supported
     */
    public function isDRMSupported()
    {
        return $this->hasDeviceFile();
    }

    /**
     * Get DRM status
     */
    public function getDRMStatus()
    {
        return [
            'supported' => $this->isDRMSupported(),
            'device_file' => $this->hasDeviceFile(),
            'drm_endpoint' => $this->drmEndpoint
        ];
    }

    /**
     * Create Python script for key extraction
     */
    private function createPythonScript($scriptPath)
    {
        $scriptDir = dirname($scriptPath);
        if (!file_exists($scriptDir)) {
            mkdir($scriptDir, 0755, true);
        }

        $pythonCode = '#!/usr/bin/env python3
"""
Widevine Key Extraction Script for Shahid DRM
"""

import argparse
import json
import sys
import base64
from pathlib import Path

try:
    from pywidevine.cdm import Cdm
    from pywidevine.device import Device
    from pywidevine.pssh import PSSH
except ImportError:
    print("Error: pywidevine library not found. Install with: pip install pywidevine")
    sys.exit(1)

def extract_keys(pssh_data, device_path, license_url=None):
    """Extract keys using Widevine CDM"""
    try:
        # Load device
        device = Device.load(device_path)
        cdm = Cdm.from_device(device)

        # Parse PSSH
        pssh = PSSH(pssh_data)

        # Open CDM session
        session_id = cdm.open()

        # Get license challenge
        challenge = cdm.get_license_challenge(session_id, pssh)

        # For now, we can only extract KID from PSSH
        # Full key extraction requires license server response

        keys = []
        for kid in pssh.key_ids:
            keys.append({
                "kid": kid.hex,
                "key": None,  # Would need license response
                "type": "CONTENT"
            })

        cdm.close(session_id)

        return {
            "success": True,
            "keys": keys,
            "pssh": pssh_data,
            "challenge": base64.b64encode(challenge).decode()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def main():
    parser = argparse.ArgumentParser(description="Extract Widevine keys")
    parser.add_argument("--pssh-file", required=True, help="File containing PSSH data")
    parser.add_argument("--device", required=True, help="Path to device.wvd file")
    parser.add_argument("--output", required=True, help="Output JSON file")
    parser.add_argument("--license-url", help="License server URL")

    args = parser.parse_args()

    try:
        # Read PSSH from file
        with open(args.pssh_file, "r") as f:
            pssh_data = f.read().strip()

        # Extract keys
        result = extract_keys(pssh_data, args.device, args.license_url)

        # Write result to output file
        with open(args.output, "w") as f:
            json.dump(result, f, indent=2)

        print("Key extraction completed")

    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e)
        }

        with open(args.output, "w") as f:
            json.dump(error_result, f, indent=2)

        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
';

        file_put_contents($scriptPath, $pythonCode);
        chmod($scriptPath, 0755);

        Log::info('Python script created at: ' . $scriptPath);
    }

    /**
     * Create Node.js script for key extraction
     */
    private function createNodeScript($scriptPath)
    {
        $scriptDir = dirname($scriptPath);
        if (!file_exists($scriptDir)) {
            mkdir($scriptDir, 0755, true);
        }

        $nodeCode = '#!/usr/bin/env node
/**
 * Widevine Key Extraction Script for Shahid DRM (Node.js)
 */

const fs = require("fs");
const path = require("path");

// Parse command line arguments
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};

    for (let i = 0; i < args.length; i += 2) {
        const key = args[i].replace(/^--/, "");
        const value = args[i + 1];
        options[key] = value;
    }

    return options;
}

// Simple PSSH parser (basic implementation)
function parsePSSH(psshBase64) {
    try {
        const psshBuffer = Buffer.from(psshBase64, "base64");

        // Basic PSSH structure parsing
        // This is a simplified version - real implementation would need proper protobuf parsing

        const kids = [];

        // Look for 16-byte patterns that might be KIDs
        for (let i = 32; i < psshBuffer.length - 16; i++) {
            const potential_kid = psshBuffer.slice(i, i + 16);

            // Check if this looks like a valid KID (not all zeros, not all 0xFF)
            const isAllZeros = potential_kid.every(byte => byte === 0);
            const isAllFF = potential_kid.every(byte => byte === 0xFF);

            if (!isAllZeros && !isAllFF) {
                kids.push(potential_kid.toString("hex"));
                break; // Take first valid-looking KID
            }
        }

        return kids;
    } catch (error) {
        console.error("Error parsing PSSH:", error.message);
        return [];
    }
}

// Extract keys (simplified version)
function extractKeys(psshData, devicePath, licenseUrl) {
    try {
        // Check if device file exists
        if (!fs.existsSync(devicePath)) {
            throw new Error("Device file not found: " + devicePath);
        }

        // Parse PSSH to get KIDs
        const kids = parsePSSH(psshData);

        if (kids.length === 0) {
            throw new Error("No KIDs found in PSSH");
        }

        // For now, we can only extract KID from PSSH
        // Full key extraction would require:
        // 1. Widevine CDM integration (complex)
        // 2. License server communication
        // 3. Proper protobuf handling

        const keys = kids.map(kid => ({
            kid: kid,
            key: null, // Would need actual CDM for key extraction
            type: "CONTENT"
        }));

        return {
            success: true,
            keys: keys,
            pssh: psshData,
            message: "KID extracted successfully. Key extraction requires Widevine CDM."
        };

    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

// Main function
function main() {
    const options = parseArgs();

    if (!options["pssh-file"] || !options.device || !options.output) {
        console.error("Usage: node extract_keys.js --pssh-file <file> --device <device.wvd> --output <output.json>");
        process.exit(1);
    }

    try {
        // Read PSSH from file
        const psshData = fs.readFileSync(options["pssh-file"], "utf8").trim();

        // Extract keys
        const result = extractKeys(psshData, options.device, options["license-url"]);

        // Write result to output file
        fs.writeFileSync(options.output, JSON.stringify(result, null, 2));

        console.log("Key extraction completed");

        if (!result.success) {
            process.exit(1);
        }

    } catch (error) {
        const errorResult = {
            success: false,
            error: error.message
        };

        fs.writeFileSync(options.output, JSON.stringify(errorResult, null, 2));

        console.error("Error:", error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
';

        file_put_contents($scriptPath, $nodeCode);
        chmod($scriptPath, 0755);

        Log::info('Node.js script created at: ' . $scriptPath);
        return true;
    }

    /**
     * Extract key using browser automation (most reliable method)
     */
    public function extractKeyWithBrowser($mpdUrl, $pssh)
    {
        try {
            if (!$mpdUrl || !$pssh) {
                return [
                    'success' => false,
                    'error' => 'MPD URL and PSSH are required'
                ];
            }

            Log::info('Starting browser-based key extraction');

            // Create temporary HTML file for browser automation
            $tempDir = storage_path('temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $htmlFile = $tempDir . '/drm_extractor_' . uniqid() . '.html';
            $outputFile = $tempDir . '/keys_' . uniqid() . '.json';

            $htmlContent = $this->createDRMExtractorHTML($mpdUrl, $pssh, $outputFile);
            file_put_contents($htmlFile, $htmlContent);

            // Use Puppeteer or similar to automate browser
            $nodeScript = base_path('scripts/browser_extractor.js');
            if (!file_exists($nodeScript)) {
                $this->createBrowserExtractorScript($nodeScript);
            }

            $command = sprintf(
                'node "%s" --html-file "%s" --output "%s" --timeout 30',
                $nodeScript,
                $htmlFile,
                $outputFile
            );

            Log::info('Executing browser automation: ' . $command);

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            // Clean up temp HTML file
            if (file_exists($htmlFile)) {
                unlink($htmlFile);
            }

            // Check results
            if (file_exists($outputFile)) {
                $result = json_decode(file_get_contents($outputFile), true);
                unlink($outputFile);

                if ($result && isset($result['success']) && $result['success']) {
                    Log::info('Browser-based key extraction successful');
                    return $result;
                } else {
                    Log::error('Browser-based key extraction failed: ' . ($result['error'] ?? 'Unknown error'));
                    return [
                        'success' => false,
                        'error' => $result['error'] ?? 'Browser extraction failed'
                    ];
                }
            } else {
                Log::error('Browser automation did not produce output file');
                return [
                    'success' => false,
                    'error' => 'Browser automation failed: ' . implode("\n", $output)
                ];
            }

        } catch (\Exception $e) {
            Log::error('Browser-based key extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create HTML file for DRM extraction
     */
    private function createDRMExtractorHTML($mpdUrl, $pssh, $outputFile)
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>DRM Key Extractor</title>
    <script src="https://cdn.jsdelivr.net/npm/shaka-player@4.3.0/dist/shaka-player.compiled.js"></script>
</head>
<body>
    <video id="video" width="640" height="480" controls></video>

    <script>
        const video = document.getElementById("video");
        const player = new shaka.Player(video);

        // Configure DRM
        player.configure({
            drm: {
                servers: {
                    "com.widevine.alpha": "https://drm.shahid.net/widevine"
                }
            }
        });

        // Listen for key status changes
        player.addEventListener("keystatuschanged", function(event) {
            console.log("Key status changed:", event);

            // Extract keys from the session
            const keys = [];
            const mediaKeys = video.mediaKeys;

            if (mediaKeys && mediaKeys.keySystem === "com.widevine.alpha") {
                // This is a simplified approach
                // Real implementation would need to intercept license responses
                console.log("Widevine keys detected");
            }
        });

        // Load the manifest
        player.load("' . $mpdUrl . '").then(function() {
            console.log("Manifest loaded successfully");
        }).catch(function(error) {
            console.error("Error loading manifest:", error);

            // Save error result
            const result = {
                success: false,
                error: error.message || "Failed to load manifest"
            };

            // This would need to be handled by the browser automation script
            window.extractionResult = result;
        });

        // Timeout after 30 seconds
        setTimeout(function() {
            const result = {
                success: false,
                error: "Extraction timeout"
            };
            window.extractionResult = result;
        }, 30000);
    </script>
</body>
</html>';
    }

    /**
     * Create browser automation script
     */
    private function createBrowserExtractorScript($scriptPath)
    {
        $scriptDir = dirname($scriptPath);
        if (!file_exists($scriptDir)) {
            mkdir($scriptDir, 0755, true);
        }

        $nodeCode = '#!/usr/bin/env node
/**
 * Browser-based DRM Key Extractor using Puppeteer
 */

const fs = require("fs");
const path = require("path");

// Check if puppeteer is available
let puppeteer;
try {
    puppeteer = require("puppeteer");
} catch (error) {
    console.error("Puppeteer not found. Install with: npm install puppeteer");
    process.exit(1);
}

// Parse command line arguments
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};

    for (let i = 0; i < args.length; i += 2) {
        const key = args[i].replace(/^--/, "");
        const value = args[i + 1];
        options[key] = value;
    }

    return options;
}

async function extractKeys(htmlFile, outputFile, timeout = 30) {
    let browser;

    try {
        browser = await puppeteer.launch({
            headless: true,
            args: ["--no-sandbox", "--disable-setuid-sandbox"]
        });

        const page = await browser.newPage();

        // Navigate to the HTML file
        await page.goto("file://" + path.resolve(htmlFile));

        // Wait for extraction to complete or timeout
        await page.waitForTimeout(timeout * 1000);

        // Get extraction result
        const result = await page.evaluate(() => {
            return window.extractionResult || {
                success: false,
                error: "No extraction result available"
            };
        });

        // Save result
        fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));

        console.log("Browser extraction completed");

    } catch (error) {
        const errorResult = {
            success: false,
            error: error.message
        };

        fs.writeFileSync(outputFile, JSON.stringify(errorResult, null, 2));
        console.error("Browser extraction error:", error.message);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

async function main() {
    const options = parseArgs();

    if (!options["html-file"] || !options.output) {
        console.error("Usage: node browser_extractor.js --html-file <file> --output <output.json> [--timeout 30]");
        process.exit(1);
    }

    const timeout = parseInt(options.timeout) || 30;

    await extractKeys(options["html-file"], options.output, timeout);
}

if (require.main === module) {
    main();
}
';

        file_put_contents($scriptPath, $nodeCode);
        chmod($scriptPath, 0755);

        Log::info('Browser extractor script created at: ' . $scriptPath);
    }

    /**
     * Create simple extraction script that works with device.wvd
     */
    private function createSimpleExtractScript($scriptPath)
    {
        $scriptDir = dirname($scriptPath);
        if (!file_exists($scriptDir)) {
            mkdir($scriptDir, 0755, true);
        }

        $nodeCode = '#!/usr/bin/env node
/**
 * Simple Widevine Key Extractor using device.wvd
 * This script focuses on extracting KIDs and basic info without complex CDM
 */

const fs = require("fs");
const path = require("path");

// Parse command line arguments
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};

    for (let i = 0; i < args.length; i += 2) {
        const key = args[i].replace(/^--/, "");
        const value = args[i + 1];
        options[key] = value;
    }

    return options;
}

// Parse PSSH to extract KID
function extractKIDFromPSSH(psshBase64) {
    try {
        const psshBuffer = Buffer.from(psshBase64, "base64");
        console.log(`PSSH buffer size: ${psshBuffer.length} bytes`);

        const kids = [];

        // Method 1: Look for KID in PSSH v1 format (header + kid_count + kids)
        if (psshBuffer.length >= 40) {
            try {
                const kidCount = psshBuffer.readUInt32BE(32);
                console.log(`KID count from v1 format: ${kidCount}`);

                if (kidCount > 0 && kidCount < 10 && psshBuffer.length >= 36 + (kidCount * 16)) {
                    for (let i = 0; i < kidCount; i++) {
                        const kidOffset = 36 + (i * 16);
                        const kid = psshBuffer.slice(kidOffset, kidOffset + 16);
                        kids.push(kid.toString("hex"));
                        console.log(`KID ${i + 1}: ${kid.toString("hex")}`);
                    }
                }
            } catch (e) {
                console.log("v1 format parsing failed:", e.message);
            }
        }

        // Method 2: Look for KID patterns in data section
        if (kids.length === 0) {
            console.log("Trying pattern search method...");

            for (let i = 32; i <= psshBuffer.length - 16; i++) {
                const potential_kid = psshBuffer.slice(i, i + 16);

                // Check if this looks like a valid KID
                const isAllZeros = potential_kid.every(byte => byte === 0);
                const isAllFF = potential_kid.every(byte => byte === 0xFF);
                const hasVariation = new Set(potential_kid).size > 1;

                if (!isAllZeros && !isAllFF && hasVariation) {
                    const kidHex = potential_kid.toString("hex");
                    console.log(`Found potential KID at offset ${i}: ${kidHex}`);
                    kids.push(kidHex);
                    break; // Take first valid-looking KID
                }
            }
        }

        return kids;
    } catch (error) {
        console.error("Error parsing PSSH:", error.message);
        return [];
    }
}

// Read device file info
function getDeviceInfo(devicePath) {
    try {
        if (!fs.existsSync(devicePath)) {
            return { error: "Device file not found" };
        }

        const stats = fs.statSync(devicePath);
        const deviceData = fs.readFileSync(devicePath);

        const info = {
            exists: true,
            size: stats.size,
            type: "Widevine Device File"
        };

        // Basic device file analysis
        const deviceStr = deviceData.toString("binary");

        if (deviceStr.includes("widevine")) {
            info.contains_widevine = true;
        }

        if (deviceStr.includes("L1")) {
            info.security_level = "L1";
        } else if (deviceStr.includes("L3")) {
            info.security_level = "L3";
        }

        return info;

    } catch (error) {
        return { error: error.message };
    }
}

// Main extraction function
function extractKeys(psshData, devicePath) {
    try {
        console.log("Starting simple key extraction...");

        // Get device info
        const deviceInfo = getDeviceInfo(devicePath);
        if (deviceInfo.error) {
            throw new Error(deviceInfo.error);
        }

        console.log("Device info:", deviceInfo);

        // Extract KIDs from PSSH
        const kids = extractKIDFromPSSH(psshData);

        if (kids.length === 0) {
            throw new Error("No KIDs found in PSSH");
        }

        // Build result
        const keys = kids.map(kid => ({
            kid: kid,
            key: null, // Actual key extraction requires Widevine CDM
            type: "CONTENT",
            status: "KID_EXTRACTED"
        }));

        return {
            success: true,
            keys: keys,
            pssh: psshData,
            device_info: deviceInfo,
            message: `Successfully extracted ${kids.length} KID(s). Device file is available for advanced extraction.`,
            extraction_method: "simple_device_wvd"
        };

    } catch (error) {
        return {
            success: false,
            error: error.message,
            extraction_method: "simple_device_wvd"
        };
    }
}

// Main function
function main() {
    const options = parseArgs();

    if (!options["pssh-file"] || !options.device || !options.output) {
        console.error("Usage: node simple_extract.js --pssh-file <file> --device <device.wvd> --output <output.json>");
        process.exit(1);
    }

    try {
        // Read PSSH from file
        const psshData = fs.readFileSync(options["pssh-file"], "utf8").trim();
        console.log("PSSH loaded, length:", psshData.length);

        // Extract keys
        const result = extractKeys(psshData, options.device);

        // Write result to output file
        fs.writeFileSync(options.output, JSON.stringify(result, null, 2));

        console.log("Extraction completed:", result.success ? "SUCCESS" : "FAILED");

        if (!result.success) {
            process.exit(1);
        }

    } catch (error) {
        const errorResult = {
            success: false,
            error: error.message,
            extraction_method: "simple_device_wvd"
        };

        fs.writeFileSync(options.output, JSON.stringify(errorResult, null, 2));

        console.error("Error:", error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
';

        file_put_contents($scriptPath, $nodeCode);
        chmod($scriptPath, 0755);

        Log::info('Simple extraction script created at: ' . $scriptPath);
    }
}
