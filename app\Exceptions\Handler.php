<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception)
    {
        // Check if this is an admin route and user is authenticated
        if ($this->isAdminRoute($request) && auth()->check()) {
            return $this->renderAdminError($request, $exception);
        }

        return parent::render($request, $exception);
    }

    /**
     * Check if the current route is an admin route
     */
    protected function isAdminRoute(Request $request): bool
    {
        return $request->is('admin/*') || $request->is('admin');
    }

    /**
     * Render error pages within admin dashboard layout
     */
    protected function renderAdminError(Request $request, Throwable $exception)
    {
        // Get the status code
        $statusCode = $this->getStatusCode($exception);

        // For AJAX requests, return JSON response
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $this->getErrorMessage($exception, $statusCode),
                'error_code' => $statusCode
            ], $statusCode);
        }

        // Check if we have a custom error view for this status code
        $errorView = "errors.{$statusCode}";

        if (view()->exists($errorView)) {
            return response()->view($errorView, [
                'exception' => $exception,
                'statusCode' => $statusCode
            ], $statusCode);
        }

        // Fallback to generic error page
        return response()->view('errors.layout', [
            'exception' => $exception,
            'statusCode' => $statusCode,
            'code' => $statusCode,
            'title' => $this->getErrorTitle($statusCode),
            'message' => $this->getErrorMessage($exception, $statusCode)
        ], $statusCode);
    }

    /**
     * Get the status code from exception
     */
    protected function getStatusCode(Throwable $exception): int
    {
        if ($exception instanceof HttpException) {
            return $exception->getStatusCode();
        }

        return 500;
    }

    /**
     * Get error title based on status code
     */
    protected function getErrorTitle(int $statusCode): string
    {
        return match($statusCode) {
            403 => 'Access Denied',
            404 => 'Page Not Found',
            419 => 'Session Expired',
            500 => 'Internal Server Error',
            503 => 'Service Unavailable',
            default => 'Error Occurred'
        };
    }

    /**
     * Get error message based on exception and status code
     */
    protected function getErrorMessage(Throwable $exception, int $statusCode): string
    {
        // In production, don't show detailed error messages
        if (app()->environment('production')) {
            return match($statusCode) {
                403 => 'You do not have permission to access this resource.',
                404 => 'The requested page could not be found.',
                419 => 'Your session has expired. Please refresh the page.',
                500 => 'An internal server error occurred. Please try again later.',
                503 => 'The service is temporarily unavailable. Please try again later.',
                default => 'An error occurred while processing your request.'
            };
        }

        // In development, show more detailed messages
        return $exception->getMessage() ?: $this->getErrorMessage($exception, $statusCode);
    }
}
