@extends('admin.layouts.app')

@section('title', 'Page Not Found - 404')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <div class="error-icon mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #ffa726, #ff9800); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-search text-white" style="font-size: 60px;"></i>
                        </div>
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-warning mb-3">404</h1>
                    
                    <!-- Error Title -->
                    <h2 class="h3 text-dark mb-3">Page Not Found</h2>
                    
                    <!-- Error Message -->
                    <p class="text-muted mb-4 lead">
                        The page you're looking for doesn't exist or has been moved.
                        <br>Let's get you back on track!
                    </p>

                    <!-- Search Suggestions -->
                    <div class="alert alert-light border-0 mb-4" style="background-color: #f8f9fa;">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-lightbulb text-warning fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">Suggestions:</h6>
                                <ul class="mb-0 text-muted small">
                                    <li>Check the URL for typos</li>
                                    <li>Use the navigation menu to find what you need</li>
                                    <li>Return to the dashboard and start over</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Navigation -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3">Quick Navigation:</h6>
                            <div class="d-flex flex-wrap gap-2 justify-content-center">
                                @if(auth()->user() && auth()->user()->safeHasPermissionTo('view users'))
                                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-users me-1"></i>Users
                                </a>
                                @endif
                                
                                @if(auth()->user() && auth()->user()->safeHasPermissionTo('view subscriptions'))
                                <a href="{{ route('admin.user-subscriptions.index') }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-credit-card me-1"></i>Subscriptions
                                </a>
                                @endif
                                
                                @if(auth()->user() && auth()->user()->safeHasPermissionTo('view reports'))
                                <a href="{{ route('admin.reports.index') }}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-chart-bar me-1"></i>Reports
                                </a>
                                @endif
                                
                                @if(auth()->user() && auth()->user()->safeHasPermissionTo('view settings'))
                                <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-cog me-1"></i>Settings
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <button onclick="history.back()" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary px-4">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                    </div>

                    <!-- Current URL Info -->
                    <div class="mt-4 pt-4 border-top">
                        <small class="text-muted">
                            Requested URL: <code>{{ request()->url() }}</code>
                        </small>
                    </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}



.btn {
    border-radius: 8px;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.875rem;
}
</style>
@endsection
