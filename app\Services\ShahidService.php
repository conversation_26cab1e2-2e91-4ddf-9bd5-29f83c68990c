<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Services\ShahidDRM;

class ShahidService
{
    private $baseUrl = 'https://api3.shahid.net';
    private $token;
    private $tokenFile = 'shahid_token.txt';
    private $apiEndpoints = [
        'movies' => '/api/v2/movies',
        'series' => '/api/v2/series',
        'channels' => '/api/v2/channels',
        'search' => '/api/v2/search',
        'content' => '/api/v2/content',
        'stream' => '/api/v2/stream'
    ];

    public function __construct()
    {
        $this->loadToken();
    }

    /**
     * Load Shahid token from storage
     */
    private function loadToken()
    {
        try {
            if (Storage::exists($this->tokenFile)) {
                $this->token = trim(Storage::get($this->tokenFile));
            }
        } catch (\Exception $e) {
            Log::error('Error loading Shahid token: ' . $e->getMessage());
        }
    }

    /**
     * Save Shahid token to storage
     */
    public function saveToken($token)
    {
        try {
            $this->token = trim($token);
            Storage::put($this->tokenFile, $this->token);
            return true;
        } catch (\Exception $e) {
            Log::error('Error saving Shahid token: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if token is valid
     */
    public function hasValidToken()
    {
        // Load token if not already loaded
        if (empty($this->token)) {
            $this->loadToken();
        }

        return !empty($this->token);
    }

    /**
     * Get current token
     */
    public function getToken()
    {
        if (empty($this->token)) {
            $this->loadToken();
        }

        return $this->token;
    }

    /**
     * Get movies from Shahid API
     */
    public function getMovies($country = 'EG', $limit = 50)
    {
        try {
            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'User-Agent' => 'Shahid-Laravel/1.0',
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/movies', [
                'country' => $country,
                'limit' => $limit,
                'offset' => 0
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            return ['error' => 'Failed to fetch movies: ' . $response->status()];

        } catch (\Exception $e) {
            Log::error('Error fetching Shahid movies: ' . $e->getMessage());
            return ['error' => 'Network error: ' . $e->getMessage()];
        }
    }

    /**
     * Get series from Shahid API
     */
    public function getSeries($country = 'EG', $limit = 50)
    {
        try {
            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'User-Agent' => 'Shahid-Laravel/1.0',
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/series', [
                'country' => $country,
                'limit' => $limit,
                'offset' => 0
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            return ['error' => 'Failed to fetch series: ' . $response->status()];

        } catch (\Exception $e) {
            Log::error('Error fetching Shahid series: ' . $e->getMessage());
            return ['error' => 'Network error: ' . $e->getMessage()];
        }
    }

    /**
     * Get live channels from Shahid API
     */
    public function getLiveChannels()
    {
        try {
            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'User-Agent' => 'Shahid-Laravel/1.0',
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/live-channels');

            if ($response->successful()) {
                return $response->json();
            }

            return ['error' => 'Failed to fetch live channels: ' . $response->status()];

        } catch (\Exception $e) {
            Log::error('Error fetching Shahid live channels: ' . $e->getMessage());
            return ['error' => 'Network error: ' . $e->getMessage()];
        }
    }

    /**
     * Search content by ID or URL
     */
    public function searchContent($contentIdOrUrl)
    {
        try {
            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            // Extract content ID from URL if needed
            $contentId = $this->extractContentId($contentIdOrUrl);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'User-Agent' => 'Shahid-Laravel/1.0',
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/content/' . $contentId);

            if ($response->successful()) {
                return $response->json();
            }

            return ['error' => 'Content not found'];

        } catch (\Exception $e) {
            Log::error('Error searching Shahid content: ' . $e->getMessage());
            return ['error' => 'Search error: ' . $e->getMessage()];
        }
    }

    /**
     * Extract content ID from URL
     */
    private function extractContentId($input)
    {
        // If it's already an ID, return as is
        if (is_numeric($input)) {
            return $input;
        }

        // Extract ID from Shahid URL patterns
        $patterns = [
            '/\/movie\/(\d+)/',
            '/\/series\/(\d+)/',
            '/\/episode\/(\d+)/',
            '/contentId=(\d+)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input, $matches)) {
                return $matches[1];
            }
        }

        return $input; // Return as is if no pattern matches
    }

    /**
     * Get content details with streaming info
     */
    public function getContentDetails($contentId)
    {
        try {
            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            // Use the same API endpoint as the Python version
            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid' => 'web',
            ];

            $params = [
                'request' => '{"id":"' . $contentId . '","productType":"MOVIE"}',
                'country' => 'EG',
            ];

            $response = Http::withHeaders($headers)->timeout(30)->get('https://api3.shahid.net/proxy/v2.1/product/id', $params);

            if ($response->successful()) {
                $data = $response->json();

                // Get streaming info if available
                $streamingInfo = $this->getStreamingInfo($contentId);
                if ($streamingInfo && !isset($streamingInfo['error'])) {
                    $data['streaming'] = $streamingInfo;
                }

                return [
                    'type' => 'movie',
                    'id' => $contentId,
                    'data' => $data
                ];
            }

            Log::error("Failed to get content details: " . $response->status() . " - " . $response->body());
            return ['error' => 'Failed to get content details: ' . $response->status()];

        } catch (\Exception $e) {
            Log::error('Error getting Shahid content details: ' . $e->getMessage());
            return ['error' => 'Error: ' . $e->getMessage()];
        }
    }

    /**
     * Get streaming information for content
     */
    public function getStreamingInfo($contentId)
    {
        try {
            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'User-Agent' => 'Shahid-Laravel/1.0',
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/content/' . $contentId . '/stream');

            if ($response->successful()) {
                return $response->json();
            }

            return ['error' => 'Failed to get streaming info'];

        } catch (\Exception $e) {
            Log::error('Error getting Shahid streaming info: ' . $e->getMessage());
            return ['error' => 'Streaming error: ' . $e->getMessage()];
        }
    }

    /**
     * Validate and refresh token if needed
     */
    public function validateToken()
    {
        try {
            $hasToken = $this->hasValidToken();

            if (!$hasToken) {
                return [
                    'valid' => false,
                    'has_token' => false,
                    'message' => 'No token found'
                ];
            }

            // Try a simple API call to test the token
            $response = Http::withHeaders([
                'token' => $this->token,
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept' => 'application/json',
            ])->get('https://api2.shahid.net/proxy/v2.1/user/profile');

            $isValid = $response->successful();

            return [
                'valid' => $isValid,
                'has_token' => true,
                'message' => $isValid ? 'Token is valid' : 'Token may be expired'
            ];

        } catch (\Exception $e) {
            Log::error('Error validating Shahid token: ' . $e->getMessage());
            return [
                'valid' => false,
                'has_token' => $this->hasValidToken(),
                'message' => 'Error validating token: ' . $e->getMessage()
            ];
        }
    }

    /**
     * استخراج بيانات الفيلم للتشغيل (MPD, PSSH, License URL)
     */
    public function extractMovieData($contentId)
    {
        try {
            Log::info("🎬 Extracting movie data for content ID: {$contentId}");

            if (!$this->hasValidToken()) {
                return ['success' => false, 'error' => 'No valid token found'];
            }

            // 1. الحصول على تفاصيل المحتوى
            $contentDetails = $this->getContentDetails($contentId);
            if (!$contentDetails || isset($contentDetails['error'])) {
                return ['success' => false, 'error' => 'Failed to get content details'];
            }

            // 2. استخراج رابط MPD من تفاصيل المحتوى
            $manifestUrl = null;
            $data = $contentDetails['data'] ?? [];

            // البحث عن رابط MPD في البيانات
            if (isset($data['streaming']['manifest_url'])) {
                $manifestUrl = $data['streaming']['manifest_url'];
            } elseif (isset($data['manifest_url'])) {
                $manifestUrl = $data['manifest_url'];
            } else {
                // إنشاء رابط MPD افتراضي
                $manifestUrl = "https://shahid-vod.akamaized.net/content/{$contentId}/manifest.mpd";
            }

            // 3. الحصول على معلومات DRM
            $drmInfo = $this->getDrmInfo($contentId);
            if (!$drmInfo || !isset($drmInfo['success']) || !$drmInfo['success']) {
                return ['success' => false, 'error' => 'Failed to get DRM info'];
            }

            $result = [
                'success' => true,
                'content_id' => $contentId,
                'manifest_url' => $manifestUrl,
                'pssh' => $drmInfo['pssh'] ?? null,
                'license_url' => $drmInfo['license_url'] ?? null,
                'kid' => $drmInfo['kid'] ?? null,
                'title' => $data['title'] ?? "Shahid Content {$contentId}",
                'poster' => $data['poster'] ?? $data['image'] ?? null
            ];

            Log::info("✅ Movie data extracted successfully");
            Log::info("📺 Manifest URL: " . $manifestUrl);
            Log::info("🔐 PSSH: " . substr($result['pssh'] ?? '', 0, 50) . "...");

            return $result;

        } catch (\Exception $e) {
            Log::error("💥 Error extracting movie data: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * الحصول على معلومات DRM للمحتوى
     */
    private function getDrmInfo($contentId)
    {
        try {
            // استخدام ShahidDRM service للحصول على معلومات DRM
            $shahidDRM = app(ShahidDRM::class);
            return $shahidDRM->extractKey($contentId);

        } catch (\Exception $e) {
            Log::error("Error getting DRM info: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
