<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ContentSecurityPolicy
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Check if this is a player route, channels page, or dynamic proxy route
        if ($request->is('shahid/play/*') || $request->is('movies/play/*') || $request->is('player/*') ||
            $request->is('shahid/channels') || $request->is('shahid/channel/*') || $request->is('*/play/*')) {
            // More permissive CSP for player pages
            $csp = [
                "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https: http:",
                "media-src 'self' blob: data: https: http: *.edgenextcdn.net *.shahid.net *.mbc.net",
                "connect-src 'self' https: http: ws: wss: blob: *.edgenextcdn.net *.shahid.net *.mbc.net",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https: http: cdn.jsdelivr.net cdnjs.cloudflare.com",
                "script-src-elem 'self' 'unsafe-inline' 'unsafe-eval' blob: https: http: cdn.jsdelivr.net cdnjs.cloudflare.com",
                "style-src 'self' 'unsafe-inline' https: http: cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com",
                "style-src-elem 'self' 'unsafe-inline' https: http: cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com",
                "font-src 'self' data: https: http: fonts.gstatic.com cdnjs.cloudflare.com",
                "img-src 'self' data: blob: https: http: *.shahid.net *.mbc.net",
                "worker-src 'self' blob: data:",
                "child-src 'self' blob:",
                "frame-src 'self' blob:",
                "object-src 'none'",
                "base-uri 'self'"
            ];
        } else {
            // Standard CSP for other pages
            $csp = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com",
                "script-src-elem 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com",
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com",
                "style-src-elem 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com",
                "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net",
                "img-src 'self' data: https: http:",
                "connect-src 'self' https:",
                "frame-src 'self'",
                "object-src 'none'",
                "base-uri 'self'"
            ];
        }

        $response->headers->set('Content-Security-Policy', implode('; ', $csp));

        // Additional security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        return $response;
    }
}
