<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('series_cache', function (Blueprint $table) {
            $table->id();
            $table->string('country', 10)->index(); // EG, SA, etc.
            $table->string('series_id')->index();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('poster_url')->nullable();
            $table->json('metadata')->nullable(); // year, genres, cast, etc.
            $table->json('seasons_data')->nullable(); // complete seasons and episodes data
            $table->integer('total_seasons')->default(0);
            $table->integer('total_episodes')->default(0);
            $table->timestamp('last_updated')->nullable();
            $table->boolean('is_complete')->default(false); // whether all seasons/episodes are cached
            $table->timestamps();

            // Unique constraint for country + series_id
            $table->unique(['country', 'series_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('series_cache');
    }
};
