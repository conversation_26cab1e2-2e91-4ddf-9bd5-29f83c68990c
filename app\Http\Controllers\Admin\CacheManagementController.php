<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use App\Models\SeriesCache;
use App\Models\MoviesCache;

class CacheManagementController extends Controller
{
    /**
     * Display cache management page
     */
    public function index()
    {
        return view('admin.cache-management.index');
    }

    /**
     * Get cache statistics
     */
    public function getStats()
    {
        try {
            $stats = [
                'application_cache' => $this->getApplicationCacheStats(),
                'database_cache' => $this->getDatabaseCacheStats(),
                'file_cache' => $this->getFileCacheStats(),
                'series_cache' => $this->getSeriesCacheStats(),
                'movies_cache' => $this->getMoviesCacheStats(),
                'total_size' => $this->getTotalCacheSize(),
                'cache_driver' => config('cache.default'),
                'last_cleared' => Cache::get('cache_last_cleared', 'Never')
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting cache stats: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error getting cache statistics'
            ]);
        }
    }

    /**
     * Clear all cache
     */
    public function clearAll(Request $request)
    {
        try {
            $type = $request->get('type', 'all');
            $results = [];

            Log::info("🧹 Starting cache clear operation", ['type' => $type, 'admin' => auth('admin')->user()->name]);

            switch ($type) {
                case 'application':
                    $results = $this->clearApplicationCache();
                    break;

                case 'database':
                    $results = $this->clearDatabaseCache();
                    break;

                case 'files':
                    $results = $this->clearFileCache();
                    break;

                case 'series':
                    $results = $this->clearSeriesCache();
                    break;

                case 'movies':
                    $results = $this->clearMoviesCache();
                    break;

                case 'all':
                default:
                    $results = $this->clearAllCache();
                    break;
            }

            // Record cache clear time
            Cache::put('cache_last_cleared', now()->format('Y-m-d H:i:s'), 86400);

            Log::info("✅ Cache clear completed", $results);

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully',
                'results' => $results,
                'cleared_at' => now()->format('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error clearing cache: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Clear application cache (Laravel cache)
     */
    private function clearApplicationCache()
    {
        $results = [];

        try {
            // Clear application cache
            Artisan::call('cache:clear');
            $results['application_cache'] = 'Cleared';

            // Clear config cache
            Artisan::call('config:clear');
            $results['config_cache'] = 'Cleared';

            // Clear route cache
            Artisan::call('route:clear');
            $results['route_cache'] = 'Cleared';

            // Clear view cache
            Artisan::call('view:clear');
            $results['view_cache'] = 'Cleared';

            // Clear compiled views
            Artisan::call('optimize:clear');
            $results['optimize_cache'] = 'Cleared';

        } catch (\Exception $e) {
            $results['application_cache'] = 'Error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Clear database cache
     */
    private function clearDatabaseCache()
    {
        $results = [];

        try {
            // Clear Laravel cache store
            Cache::flush();
            $results['cache_store'] = 'Flushed';

            // Clear specific cache tags if using tagged cache
            if (method_exists(Cache::store(), 'tags')) {
                Cache::tags(['series', 'movies', 'channels', 'episodes'])->flush();
                $results['tagged_cache'] = 'Flushed';
            }

        } catch (\Exception $e) {
            $results['database_cache'] = 'Error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Clear file cache
     */
    private function clearFileCache()
    {
        $results = [];

        try {
            $cachePaths = [
                storage_path('framework/cache/data'),
                storage_path('framework/sessions'),
                storage_path('framework/views'),
                storage_path('logs'),
                base_path('bootstrap/cache')
            ];

            foreach ($cachePaths as $path) {
                if (File::exists($path)) {
                    $files = File::files($path);
                    $deleted = 0;

                    foreach ($files as $file) {
                        if (File::delete($file)) {
                            $deleted++;
                        }
                    }

                    $results[basename($path)] = "Deleted {$deleted} files";
                } else {
                    $results[basename($path)] = 'Path not found';
                }
            }

        } catch (\Exception $e) {
            $results['file_cache'] = 'Error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Clear series cache
     */
    private function clearSeriesCache()
    {
        $results = [];

        try {
            $count = SeriesCache::count();
            SeriesCache::truncate();
            $results['series_database'] = "Deleted {$count} series records";

            // Clear series-related cache keys
            $keys = ['series_*', 'episodes_*', 'series_details_*'];
            foreach ($keys as $pattern) {
                Cache::forget($pattern);
            }
            $results['series_cache_keys'] = 'Cleared';

        } catch (\Exception $e) {
            $results['series_cache'] = 'Error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Clear movies cache
     */
    private function clearMoviesCache()
    {
        $results = [];

        try {
            if (class_exists(MoviesCache::class)) {
                $count = MoviesCache::count();
                MoviesCache::truncate();
                $results['movies_database'] = "Deleted {$count} movies records";
            }

            // Clear movies-related cache keys
            $keys = ['movies_*', 'movie_details_*'];
            foreach ($keys as $pattern) {
                Cache::forget($pattern);
            }
            $results['movies_cache_keys'] = 'Cleared';

        } catch (\Exception $e) {
            $results['movies_cache'] = 'Error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Clear all cache types
     */
    private function clearAllCache()
    {
        $results = [];

        $results['application'] = $this->clearApplicationCache();
        $results['database'] = $this->clearDatabaseCache();
        $results['files'] = $this->clearFileCache();
        $results['series'] = $this->clearSeriesCache();
        $results['movies'] = $this->clearMoviesCache();

        return $results;
    }

    /**
     * Get application cache statistics
     */
    private function getApplicationCacheStats()
    {
        return [
            'driver' => config('cache.default'),
            'status' => 'Active'
        ];
    }

    /**
     * Get database cache statistics
     */
    private function getDatabaseCacheStats()
    {
        try {
            return [
                'series_count' => SeriesCache::count(),
                'movies_count' => class_exists(MoviesCache::class) ? MoviesCache::count() : 0
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get file cache statistics
     */
    private function getFileCacheStats()
    {
        try {
            $cachePath = storage_path('framework/cache/data');
            $size = 0;
            $files = 0;

            if (File::exists($cachePath)) {
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($cachePath)
                );

                foreach ($iterator as $file) {
                    if ($file->isFile()) {
                        $size += $file->getSize();
                        $files++;
                    }
                }
            }

            return [
                'files_count' => $files,
                'total_size' => $this->formatBytes($size)
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get series cache statistics
     */
    private function getSeriesCacheStats()
    {
        try {
            return [
                'total_series' => SeriesCache::count(),
                'complete_series' => SeriesCache::where('is_complete', true)->count(),
                'latest_update' => SeriesCache::latest('updated_at')->value('updated_at')
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get movies cache statistics
     */
    private function getMoviesCacheStats()
    {
        try {
            if (!class_exists(MoviesCache::class)) {
                return ['total_movies' => 0];
            }

            return [
                'total_movies' => MoviesCache::count(),
                'latest_update' => MoviesCache::latest('updated_at')->value('updated_at')
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get total cache size
     */
    private function getTotalCacheSize()
    {
        try {
            $paths = [
                storage_path('framework/cache'),
                storage_path('framework/sessions'),
                storage_path('framework/views')
            ];

            $totalSize = 0;
            foreach ($paths as $path) {
                if (File::exists($path)) {
                    $totalSize += $this->getDirectorySize($path);
                }
            }

            return $this->formatBytes($totalSize);
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get directory size
     */
    private function getDirectorySize($path)
    {
        $size = 0;
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
