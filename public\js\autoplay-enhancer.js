/**
 * Autoplay Enhancer for JWPlayer
 * This script enhances autoplay functionality and handles browser restrictions
 */

(function() {
    'use strict';

    const AutoplayEnhancer = {
        player: null,
        autoplayAttempts: 0,
        maxAttempts: 3,
        
        init: function(playerInstance) {
            this.player = playerInstance;
            this.setupAutoplayHandling();
            this.detectAutoplaySupport();
        },
        
        setupAutoplayHandling: function() {
            if (!this.player) return;
            
            const self = this;
            
            // Listen for player ready event
            this.player.on('ready', function() {
                console.log('🎬 AutoplayEnhancer: Player ready');
                self.attemptAutoplay();
            });
            
            // Listen for play events
            this.player.on('play', function() {
                console.log('▶️ AutoplayEnhancer: Playback started successfully');
                self.hideManualPlayButton();
            });
            
            // Listen for pause events
            this.player.on('pause', function() {
                console.log('⏸️ AutoplayEnhancer: Playback paused');
            });
            
            // Listen for autostart events
            this.player.on('autostart', function(event) {
                if (event.autostart === false) {
                    console.warn('⚠️ AutoplayEnhancer: Autostart was blocked');
                    self.showManualPlayButton();
                }
            });
        },
        
        detectAutoplaySupport: function() {
            // Test if autoplay is supported
            const video = document.createElement('video');
            video.muted = true;
            video.autoplay = true;
            video.src = 'data:video/mp4;base64,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';
            
            const canAutoplay = video.play();
            if (canAutoplay && typeof canAutoplay.then === 'function') {
                canAutoplay.then(() => {
                    console.log('✅ AutoplayEnhancer: Browser supports autoplay');
                }).catch(() => {
                    console.warn('⚠️ AutoplayEnhancer: Browser blocks autoplay');
                });
            }
        },
        
        attemptAutoplay: function() {
            if (!this.player || this.autoplayAttempts >= this.maxAttempts) return;
            
            this.autoplayAttempts++;
            console.log(`🔄 AutoplayEnhancer: Attempt ${this.autoplayAttempts}/${this.maxAttempts}`);
            
            const self = this;
            
            // Wait a bit for the player to be fully ready
            setTimeout(() => {
                const state = self.player.getState();
                console.log('🎬 AutoplayEnhancer: Player state:', state);
                
                if (state !== 'playing' && state !== 'buffering') {
                    console.log('🔄 AutoplayEnhancer: Attempting to start playback...');
                    
                    const playPromise = self.player.play();
                    
                    if (playPromise && typeof playPromise.then === 'function') {
                        playPromise.then(() => {
                            console.log('✅ AutoplayEnhancer: Autoplay successful');
                            self.hideManualPlayButton();
                        }).catch((error) => {
                            console.warn('⚠️ AutoplayEnhancer: Autoplay failed:', error);
                            self.handleAutoplayFailure();
                        });
                    } else {
                        // For older browsers or JWPlayer versions
                        setTimeout(() => {
                            if (self.player.getState() !== 'playing') {
                                self.handleAutoplayFailure();
                            }
                        }, 2000);
                    }
                }
            }, 500 * this.autoplayAttempts); // Increasing delay for each attempt
        },
        
        handleAutoplayFailure: function() {
            console.warn('⚠️ AutoplayEnhancer: All autoplay attempts failed');
            this.showManualPlayButton();
            
            // Try to enable user interaction detection
            this.enableUserInteractionDetection();
        },
        
        showManualPlayButton: function() {
            const container = this.getPlayerContainer();
            if (!container || document.getElementById('autoplay-manual-button')) return;
            
            const playButton = document.createElement('div');
            playButton.id = 'autoplay-manual-button';
            playButton.innerHTML = `
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                            background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; 
                            width: 100px; height: 100px; display: flex; align-items: center; 
                            justify-content: center; cursor: pointer; z-index: 10000; 
                            transition: all 0.3s ease; box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
                            border: 3px solid rgba(255, 255, 255, 0.3);">
                    <i class="fas fa-play" style="color: white; font-size: 32px; margin-left: 6px; 
                                                  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);"></i>
                </div>
                <div style="position: absolute; top: 60%; left: 50%; transform: translate(-50%, 0); 
                            color: white; text-align: center; font-size: 16px; font-weight: bold; 
                            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5); z-index: 10000;">
                    اضغط للتشغيل
                </div>
            `;
            
            const self = this;
            playButton.onclick = function() {
                self.player.play();
                self.hideManualPlayButton();
            };
            
            // Add hover effect
            playButton.onmouseenter = function() {
                this.firstElementChild.style.transform = 'translate(-50%, -50%) scale(1.1)';
                this.firstElementChild.style.boxShadow = '0 12px 35px rgba(255, 107, 107, 0.6)';
            };
            
            playButton.onmouseleave = function() {
                this.firstElementChild.style.transform = 'translate(-50%, -50%) scale(1)';
                this.firstElementChild.style.boxShadow = '0 8px 25px rgba(255, 107, 107, 0.4)';
            };
            
            container.appendChild(playButton);
        },
        
        hideManualPlayButton: function() {
            const playButton = document.getElementById('autoplay-manual-button');
            if (playButton) {
                playButton.style.opacity = '0';
                setTimeout(() => {
                    if (playButton.parentNode) {
                        playButton.parentNode.removeChild(playButton);
                    }
                }, 300);
            }
        },
        
        enableUserInteractionDetection: function() {
            const self = this;
            
            // Listen for any user interaction to retry autoplay
            const interactionEvents = ['click', 'touchstart', 'keydown'];
            
            function onUserInteraction() {
                if (self.player && self.player.getState() !== 'playing') {
                    console.log('👆 AutoplayEnhancer: User interaction detected, retrying autoplay');
                    self.player.play();
                }
                
                // Remove listeners after first interaction
                interactionEvents.forEach(event => {
                    document.removeEventListener(event, onUserInteraction);
                });
            }
            
            interactionEvents.forEach(event => {
                document.addEventListener(event, onUserInteraction, { once: true });
            });
        },
        
        getPlayerContainer: function() {
            // Try to find the JWPlayer container
            const containers = [
                document.getElementById('jwplayer-container'),
                document.querySelector('.jwplayer'),
                document.querySelector('[id*="jwplayer"]')
            ];
            
            return containers.find(container => container !== null);
        }
    };
    
    // Make AutoplayEnhancer available globally
    window.AutoplayEnhancer = AutoplayEnhancer;
    
    // Auto-initialize if JWPlayer is available
    if (typeof jwplayer !== 'undefined') {
        // Wait for player to be created
        const checkForPlayer = setInterval(() => {
            const player = jwplayer();
            if (player && typeof player.on === 'function') {
                clearInterval(checkForPlayer);
                AutoplayEnhancer.init(player);
            }
        }, 100);
        
        // Stop checking after 10 seconds
        setTimeout(() => {
            clearInterval(checkForPlayer);
        }, 10000);
    }

})();
