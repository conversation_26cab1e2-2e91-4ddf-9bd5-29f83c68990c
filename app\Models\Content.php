<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Content extends Model
{
    use HasFactory;

    protected $table = 'content';

    protected $fillable = [
        'content_id',
        'title',
        'description',
        'type',
        'source',
        'poster_url',
        'thumbnail_url',
        'metadata',
        'mpd_url',
        'drm_info',
        'duration',
        'quality',
        'language',
        'is_coming_soon',
        'release_date',
        'views_count',
    ];

    protected $casts = [
        'metadata' => 'array',
        'drm_info' => 'array',
        'is_coming_soon' => 'boolean',
        'release_date' => 'datetime',
        'duration' => 'integer',
        'views_count' => 'integer',
    ];

    /**
     * Scope for movies
     */
    public function scopeMovies($query)
    {
        return $query->where('type', 'movie');
    }

    /**
     * Scope for series
     */
    public function scopeSeries($query)
    {
        return $query->where('type', 'series');
    }

    /**
     * Scope for episodes
     */
    public function scopeEpisodes($query)
    {
        return $query->where('type', 'episode');
    }

    /**
     * Scope for coming soon content
     */
    public function scopeComingSoon($query)
    {
        return $query->where('is_coming_soon', true);
    }

    /**
     * Scope for Shahid content
     */
    public function scopeShahid($query)
    {
        return $query->where('source', 'shahid');
    }

    /**
     * Increment views count
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) {
            return 'Unknown';
        }

        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($hours > 0) {
            return "{$hours}h {$minutes}m";
        }

        return "{$minutes}m";
    }
}
