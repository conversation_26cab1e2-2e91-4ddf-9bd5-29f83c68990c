@extends('admin.layouts.app')

@section('title', 'Role Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Create and manage user roles with specific permissions and access levels.</p>
    </div>
    @can('create', <PERSON><PERSON>\Permission\Models\Role::class)
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRoleModal">
        <i class="fas fa-plus me-2"></i>Create New Role
    </button>
    @endcan
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Roles</h6>
                        <h3 class="mb-0" id="total-roles">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tag fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Active Roles</h6>
                        <h3 class="mb-0" id="active-roles">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Empty Roles</h6>
                        <h3 class="mb-0" id="empty-roles">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-times fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">System Roles</h6>
                        <h3 class="mb-0" id="system-roles">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cog fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">Search</label>
                <input type="text" class="form-control" id="search-input" placeholder="Search roles...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Guard</label>
                <select class="form-select" id="guard-filter">
                    <option value="">All Guards</option>
                    <option value="web">Web</option>
                    <option value="api">API</option>
                </select>
            </div>
            <div class="col-md-5">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" id="apply-filters">
                        <i class="fas fa-filter me-1"></i>Apply
                    </button>
                    <button class="btn btn-outline-secondary" id="clear-filters">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Roles Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Roles</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="roles-table">
                <thead>
                    <tr>
                        <th>Role Name</th>
                        <th>Guard</th>
                        <th>Users Count</th>
                        <th>Permissions</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="roles-tbody">
                    <!-- Dynamic content -->
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Roles pagination" id="pagination-container">
            <!-- Dynamic pagination -->
        </nav>
    </div>
</div>

<!-- Create Role Modal -->
@can('create', Spatie\Permission\Models\Role::class)
<div class="modal fade" id="createRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="create-role-form">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Role Name *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Guard Name</label>
                            <select class="form-select" name="guard_name">
                                <option value="admin" selected>Admin</option>
                                <option value="web">Web</option>
                                <option value="api">API</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Permissions</label>
                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <div id="permissions-list">
                                    <!-- Dynamic permissions -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Role</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan

<!-- Edit Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="edit-role-form">
                <div class="modal-body">
                    <input type="hidden" id="edit-role-id" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Role Name *</label>
                            <input type="text" id="edit-role-name" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Guard Name</label>
                            <select id="edit-role-guard" class="form-select" name="guard_name">
                                <option value="web">Web</option>
                                <option value="admin">Admin</option>
                                <option value="api">API</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Description</label>
                            <textarea id="edit-role-description" class="form-control" name="description" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Role</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Manage Permissions Modal -->
<div class="modal fade" id="managePermissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manage Role Permissions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>Role: <span id="role-name-display"></span></h6>
                </div>
                <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                    <div id="role-permissions-list">
                        <!-- Dynamic permissions -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-permissions">Save Permissions</button>
            </div>
        </div>
    </div>
</div>

<!-- Role Details Modal -->
<div class="modal fade" id="roleDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Role Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="role-details-content">
                <!-- Dynamic content -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.permission-category {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

.permission-item {
    padding: 0.25rem 0;
}

.permission-item .form-check {
    margin-bottom: 0;
}

.role-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.system-role {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.permissions-preview {
    max-height: 100px;
    overflow-y: auto;
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/admin/role-management.js') }}"></script>
@endpush
