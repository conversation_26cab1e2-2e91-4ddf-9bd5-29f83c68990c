@extends('layouts.app')

@section('title', 'Series Details - Shahid Play')

@section('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('styles')
<!-- Mobile Touch Support CSS -->
<link rel="stylesheet" href="{{ asset('css/mobile-touch-support.css') }}">
<style>
/* Force styles to apply */
* {
    box-sizing: border-box;
}
.series-details-container {
    padding: 0;
    margin: 0;
    min-height: 100vh;
    background: #f8f9fa;
}

.series-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 30px;
    position: relative;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.back-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 25px;
    border-radius: 30px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.series-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.series-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 25px;
    opacity: 0.9;
}

.series-meta {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
}

.meta-item {
    background: rgba(255, 255, 255, 0.15);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-title i {
    color: #667eea;
}

.season-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 20px !important;
    margin-top: 20px !important;
}

.season-card {
    background: white !important;
    border-radius: 20px !important;
    padding: 30px !important;
    text-align: center !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: 2px solid transparent !important;
    flex: 1 !important;
    min-width: 300px !important;
    max-width: 400px !important;
    position: relative !important;
    overflow: hidden !important;
    margin-bottom: 20px !important;
}

.season-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.season-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25);
    border-color: #667eea;
}

.season-number {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    display: inline-block;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.season-title {
    font-size: 1.4rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 12px;
    line-height: 1.3;
}

.season-episodes {
    color: #7f8c8d;
    margin-bottom: 25px;
    font-size: 1rem;
    font-weight: 500;
}

.season-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.season-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(45deg, #5a67d8, #6b46c1);
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Episodes Styles */
.episodes-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
    gap: 20px !important;
    margin-top: 20px !important;
}

.episode-card {
    background: white !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: 2px solid transparent !important;
}

.episode-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2) !important;
    border-color: #667eea !important;
}

.episode-thumbnail {
    width: 100% !important;
    height: 200px !important;
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    position: relative !important;
    overflow: hidden !important;
}

.episode-thumbnail img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

.episode-play-overlay {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(0,0,0,0.7) !important;
    color: white !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.episode-card:hover .episode-play-overlay,
.episode-card.touch-active .episode-play-overlay {
    opacity: 1 !important;
}

/* Mobile touch support - always show play button on mobile */
@media (max-width: 768px) {
    .episode-play-overlay {
        opacity: 0.9 !important;
        background: rgba(0,0,0,0.8) !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
    }

    .episode-card:active .episode-play-overlay,
    .episode-card.touch-active .episode-play-overlay {
        opacity: 1 !important;
        transform: translate(-50%, -50%) scale(1.1) !important;
        background: rgba(102, 126, 234, 0.9) !important;
    }

    /* Make episode cards more touch-friendly */
    .episode-card {
        margin-bottom: 15px !important;
    }

    .episode-thumbnail {
        height: 180px !important;
    }

    .episode-play-overlay {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.2rem !important;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .episode-play-overlay {
        width: 45px !important;
        height: 45px !important;
        font-size: 1rem !important;
    }

    .episode-thumbnail {
        height: 160px !important;
    }
}

/* Ensure play button is always visible and clickable on touch devices */
.touch-device .episode-play-overlay {
    opacity: 0.8 !important;
    pointer-events: auto !important;
}

/* Animation for touch feedback */
@keyframes touchPulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
    100% { transform: translate(-50%, -50%) scale(1); }
}

.episode-card.touch-active .episode-play-overlay {
    animation: touchPulse 0.3s ease-in-out;
}

.episode-info {
    padding: 20px !important;
}

.episode-number {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 4px 12px !important;
    border-radius: 15px !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    display: inline-block !important;
    margin-bottom: 10px !important;
}

.episode-title {
    font-size: 1.1rem !important;
    font-weight: bold !important;
    color: #2c3e50 !important;
    margin-bottom: 8px !important;
    line-height: 1.3 !important;
}

.episode-description {
    color: #7f8c8d !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    margin-bottom: 12px !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
}

.episode-duration {
    color: #95a5a6 !important;
    font-size: 0.85rem !important;
    font-weight: 500 !important;
}

/* Coming Soon Styles */
.episode-card.coming-soon {
    position: relative !important;
    opacity: 0.8 !important;
}

.episode-card.coming-soon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
    border-radius: 15px;
}

.coming-soon-badge {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
    color: white !important;
    padding: 8px 15px !important;
    border-radius: 20px !important;
    font-size: 0.7rem !important;
    font-weight: 700 !important;
    z-index: 2 !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4) !important;
    text-transform: none !important;
    letter-spacing: 0.3px !important;
    max-width: calc(100% - 30px) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    animation: pulse-glow 2s ease-in-out infinite alternate !important;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        transform: scale(1);
    }
    100% {
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        transform: scale(1.02);
    }
}

.coming-soon-time {
    background: rgba(255, 107, 107, 0.1) !important;
    color: #e74c3c !important;
    padding: 8px 15px !important;
    border-radius: 20px !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    margin-top: 10px !important;
    display: inline-block !important;
    border: 1px solid rgba(231, 76, 60, 0.2) !important;
}

.episode-card.coming-soon .episode-play-overlay {
    background: rgba(255, 107, 107, 0.8) !important;
}

.episode-card.coming-soon .episode-play-overlay i::before {
    content: '\f017' !important; /* Clock icon */
}

.episode-status {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    margin-top: 10px !important;
}

.status-badge {
    padding: 4px 12px !important;
    border-radius: 15px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.status-available {
    background: rgba(46, 204, 113, 0.1) !important;
    color: #27ae60 !important;
    border: 1px solid rgba(39, 174, 96, 0.2) !important;
}

.status-coming-soon {
    background: rgba(255, 107, 107, 0.1) !important;
    color: #e74c3c !important;
    border: 1px solid rgba(231, 76, 60, 0.2) !important;
}

.status-recently-available {
    background: rgba(52, 152, 219, 0.1) !important;
    color: #3498db !important;
    border: 1px solid rgba(52, 152, 219, 0.2) !important;
}

.status-coming-soon-expired {
    background: rgba(255, 193, 7, 0.1) !important;
    color: #f39c12 !important;
    border: 1px solid rgba(243, 156, 18, 0.2) !important;
}

/* Cast Styles */
.cast-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-top: 20px !important;
}

.cast-card {
    background: white !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
    padding: 20px !important;
    border: 2px solid transparent !important;
}

.cast-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2) !important;
    border-color: #667eea !important;
}

.cast-avatar {
    width: 80px !important;
    height: 80px !important;
    border-radius: 50% !important;
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    margin: 0 auto 15px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.8rem !important;
    font-weight: bold !important;
    position: relative !important;
    overflow: hidden !important;
}

.cast-avatar img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 50% !important;
}

.cast-name {
    font-size: 1.1rem !important;
    font-weight: bold !important;
    color: #2c3e50 !important;
    margin-bottom: 8px !important;
    line-height: 1.3 !important;
}

.cast-role {
    color: #7f8c8d !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    background: rgba(102, 126, 234, 0.1) !important;
    padding: 4px 12px !important;
    border-radius: 15px !important;
    display: inline-block !important;
}

.cast-rank {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
    color: white !important;
    width: 25px !important;
    height: 25px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.75rem !important;
    font-weight: bold !important;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4) !important;
}

/* Section Spacing */
.section-spacing {
    margin-top: 50px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .series-details-container {
        padding: 15px;
    }

    .series-header {
        padding: 25px;
        margin-bottom: 20px;
    }

    .series-title {
        font-size: 2rem;
    }

    .back-button {
        position: static;
        display: inline-block;
        margin-bottom: 15px;
    }

    .series-meta {
        gap: 15px;
    }

    .season-grid {
        flex-direction: column;
        gap: 15px;
    }

    .season-card {
        min-width: auto;
        max-width: none;
        padding: 25px;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .episodes-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    .cast-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        gap: 15px !important;
    }

    .cast-card {
        padding: 15px !important;
    }

    .cast-avatar {
        width: 60px !important;
        height: 60px !important;
        font-size: 1.4rem !important;
    }

    .episode-thumbnail {
        height: 180px !important;
    }

    .coming-soon-badge {
        top: 10px !important;
        right: 10px !important;
        padding: 6px 12px !important;
        font-size: 0.75rem !important;
    }

    .episode-status {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .status-badge {
        font-size: 0.7rem !important;
        padding: 3px 10px !important;
    }
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
@endsection

@section('content')
<div class="series-details-container">
    <!-- Loading Indicator -->
    <div id="loadingIndicator" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.9); display: flex; justify-content: center; align-items: center; z-index: 9999; color: white; font-size: 18px;">
        <div style="text-align: center;">
            <div style="width: 80px; height: 80px; border: 4px solid rgba(0, 123, 255, 0.3); border-left: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div style="font-size: 20px; margin-bottom: 10px;">جاري تحميل المسلسل...</div>
            <div style="font-size: 16px; font-weight: bold; color: #007bff;" id="loadingSeriesTitle">Loading series details...</div>
            <div style="font-size: 14px; opacity: 0.8; margin-top: 15px;" id="loadingText">Fetching episodes from Shahid API...</div>
        </div>
    </div>

    <!-- Series Header -->
    <div id="seriesHeader" class="series-header d-none">
        <a href="{{ route('shahid.series') }}" class="back-button">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Series
        </a>
        
        <h1 id="seriesTitle" class="series-title">Loading...</h1>
        <p id="seriesDescription" class="series-description">Loading description...</p>
        
        <div class="series-meta">
            <div class="meta-item">
                <i class="fas fa-calendar me-2"></i>
                <span id="seriesYear">-</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-list me-2"></i>
                <span id="seasonsCount">-</span> Seasons
            </div>
            <div class="meta-item" id="episodesMetaItem">
                <i class="fas fa-play-circle me-2"></i>
                <span id="totalEpisodes">-</span> Episodes
            </div>
            <div class="meta-item">
                <i class="fas fa-users me-2"></i>
                <span id="seriesCast">-</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-globe me-2"></i>
                <span id="seriesDialect">-</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-tags me-2"></i>
                <span id="seriesGenres">-</span>
            </div>
        </div>
    </div>

    <!-- Cast Section -->
    <div id="castSection" class="d-none">
        <h2 class="section-title">
            <i class="fas fa-users"></i>
            Cast & Crew
        </h2>
        <div id="castContainer" class="cast-grid">
            <!-- Cast will be loaded here -->
        </div>
    </div>

    <!-- Seasons Section -->
    <div id="seasonsSection" class="d-none section-spacing">
        <h2 class="section-title">
            <i class="fas fa-list"></i>
            Seasons
        </h2>
        <div id="seasonsContainer" class="season-grid">
            <!-- Seasons will be loaded here -->
        </div>
    </div>

    <!-- Episodes Section -->
    <div id="episodesSection" class="d-none">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0">
                <i class="fas fa-play-circle"></i>
                <span id="currentSeasonTitle">Episodes</span>
            </h2>
            <button id="backToSeasons" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Seasons
            </button>
        </div>
        <div id="episodesContainer" class="episodes-grid">
            <!-- Episodes will be loaded here -->
        </div>
    </div>

    <!-- Error Message -->
    <div id="errorMessage" class="alert alert-danger d-none" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <span id="errorText">An error occurred.</span>
    </div>
</div>
@endsection

@section('scripts')
<script>
const seriesId = '{{ $seriesId }}';

$(document).ready(function() {
    // Detect touch device and add class
    if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
        $('body').addClass('touch-device');
    }

    loadSeriesDetails();

    // Back to seasons button
    $('#backToSeasons').click(function() {
        showSeasonsView();
    });
});

function loadSeriesDetails() {
    showLoading('تحميل تفاصيل المسلسل...', 'جاري جلب المواسم والحلقات من Shahid API');

    // Load series info and seasons
    loadSeasons();
}

function loadSeasons() {
    $.ajax({
        url: '/shahid/api/series/seasons',
        method: 'POST',
        data: {
            series_id: seriesId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            hideLoading();
            if (response.success && response.data.length > 0) {
                displaySeasons(response.data);
                showSeriesHeader(response.data[0], response.data);
                displayCast(response.data[0]);
            } else {
                console.warn('⚠️ No seasons found for this series');
            }
        },
        error: function(xhr) {
            hideLoading();
            console.error('❌ Error loading seasons:', xhr);
        }
    });
}

function showSeriesHeader(firstSeason, allSeasons) {
    // Extract series name from first season
    let seriesName = firstSeason.title || 'Unknown Series';
    if (seriesName.includes('Season')) {
        seriesName = seriesName.replace(/Season \d+.*$/i, '').trim();
    }

    // Get series details from the first season (which contains series info)
    const seriesDetails = firstSeason.series_details || firstSeason;

    // Set title and description
    $('#seriesTitle').text(seriesDetails.title || seriesName);
    $('#seriesDescription').text(seriesDetails.description || 'Watch all seasons and episodes');

    // Set year (from production date or release date)
    let year = '-';
    if (seriesDetails.productionDate) {
        year = new Date(seriesDetails.productionDate).getFullYear();
    } else if (seriesDetails.releaseDate) {
        year = new Date(seriesDetails.releaseDate).getFullYear();
    } else if (seriesDetails.createdDate) {
        year = new Date(seriesDetails.createdDate).getFullYear();
    }
    $('#seriesYear').text(year);

    // Set seasons count
    $('#seasonsCount').text(allSeasons ? allSeasons.length : 1);

    // Set total episodes count (actual available episodes)
    let totalEpisodes = 0;
    let plannedEpisodes = 0;

    if (allSeasons) {
        totalEpisodes = allSeasons.reduce((sum, season) => sum + (season.episodes_count || 0), 0);
        plannedEpisodes = allSeasons.reduce((sum, season) => sum + (season.planned_episodes || 0), 0);
    } else {
        totalEpisodes = seriesDetails.totalNumberOfEpisodes || 0;
        plannedEpisodes = seriesDetails.plannedEpisodes || seriesDetails.numberOfEpisodes || 0;
    }

    // Show actual vs planned episodes if different
    let episodeText = totalEpisodes.toString();
    let tooltipText = '';

    if (plannedEpisodes > 0 && plannedEpisodes !== totalEpisodes) {
        episodeText = `${totalEpisodes} / ${plannedEpisodes}`;
        tooltipText = `${totalEpisodes} episodes available now, ${plannedEpisodes} episodes planned total`;
        $('#episodesMetaItem').attr('title', tooltipText).attr('data-bs-toggle', 'tooltip');
    } else {
        $('#episodesMetaItem').removeAttr('title').removeAttr('data-bs-toggle');
    }

    $('#totalEpisodes').text(episodeText);

    // Set cast (simplified for meta)
    let cast = 'Various Artists';
    if (seriesDetails.persons && seriesDetails.persons.length > 0) {
        const topActors = seriesDetails.persons
            .filter(person => person.fullName)
            .slice(0, 2) // Take first 2 actors only
            .map(person => person.fullName);
        if (topActors.length > 0) {
            cast = topActors.join(', ');
            if (seriesDetails.persons.length > 2) {
                cast += ` & ${seriesDetails.persons.length - 2} more`;
            }
        }
    }
    $('#seriesCast').text(cast);

    // Set dialect
    let dialect = '-';
    if (seriesDetails.dialect && seriesDetails.dialect.title) {
        dialect = seriesDetails.dialect.title;
    }
    $('#seriesDialect').text(dialect);

    // Set genres
    let genres = '-';
    if (seriesDetails.genres && seriesDetails.genres.length > 0) {
        genres = seriesDetails.genres
            .map(genre => genre.title)
            .filter(title => title)
            .join(', ');
    }
    $('#seriesGenres').text(genres || '-');

    $('#seriesHeader').removeClass('d-none');
}

function displayCast(firstSeason) {
    const seriesDetails = firstSeason.series_details || firstSeason;
    const cast = seriesDetails.persons || [];

    if (cast.length === 0) {
        $('#castSection').addClass('d-none');
        return;
    }

    const container = $('#castContainer');
    container.empty();

    // Sort cast by rank (lower rank = higher priority)
    const sortedCast = cast.sort((a, b) => (a.rank || 999) - (b.rank || 999));

    // Show top 8 cast members
    const topCast = sortedCast.slice(0, 8);

    topCast.forEach((person, index) => {
        const fullName = person.fullName || person.name || `${person.firstName || ''} ${person.lastName || ''}`.trim();
        const initials = getInitials(fullName);
        const role = person.role || 'Actor';
        const rank = person.rank || (index + 1);

        const castCard = $(`
            <div class="cast-card">
                <div class="cast-avatar">
                    ${initials}
                    <div class="cast-rank">${rank}</div>
                </div>
                <div class="cast-name">${fullName}</div>
                <div class="cast-role">${role}</div>
            </div>
        `);

        container.append(castCard);
    });

    $('#castSection').removeClass('d-none');
}

function getInitials(name) {
    if (!name) return '?';

    const words = name.trim().split(' ');
    if (words.length === 1) {
        return words[0].charAt(0).toUpperCase();
    }

    return words[0].charAt(0).toUpperCase() + words[words.length - 1].charAt(0).toUpperCase();
}

function displaySeasons(seasons) {
    const container = $('#seasonsContainer');
    container.empty();

    seasons.forEach((season, index) => {
        const seasonCard = $(`
            <div class="season-card" onclick="viewSeason('${season.id}', '${season.title}')">
                <div class="season-number">Season ${season.season_number || (index + 1)}</div>
                <div class="season-title">${season.title}</div>
                <div class="season-episodes">${season.episodes_count || 0} Episodes</div>
                <button class="season-btn">
                    <i class="fas fa-play me-2"></i>
                    View Episodes
                </button>
            </div>
        `);
        container.append(seasonCard);
    });

    $('#seasonsSection').removeClass('d-none');
}

function viewSeason(seasonId, seasonTitle) {
    showLoading(seasonTitle, 'جاري تحميل الحلقات...');
    $('#currentSeasonTitle').text(seasonTitle + ' Episodes');

    // Load episodes for this season
    $.ajax({
        url: '/shahid/api/series/episodes',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: JSON.stringify({
            season_id: seasonId
        }),
        success: function(response) {
            hideLoading();
            if (response.success && response.data.length > 0) {
                displayEpisodes(response.data);
                showEpisodesView();
            } else {
                console.warn('⚠️ No episodes found for this season');
            }
        },
        error: function(xhr) {
            hideLoading();
            console.error('❌ Error loading episodes:', xhr);
        }
    });
}

function showLoading(title = '', message = '') {
    $('#loadingIndicator').show();
    $('#seriesHeader').addClass('d-none');
    $('#castSection').addClass('d-none');
    $('#seasonsSection').addClass('d-none');
    $('#errorMessage').addClass('d-none');

    if (title) {
        $('#loadingSeriesTitle').text(title);
    }
    if (message) {
        $('#loadingText').text(message);
    }
}

function hideLoading() {
    $('#loadingIndicator').hide();
}

function showError(message) {
    // Just log to console instead of showing UI error
    console.error('❌ Error:', message);
    hideLoading();
}

/**
 * Show episode loading screen
 */
function showEpisodeLoading(episodeTitle) {
    // Remove any existing loading screen
    hideEpisodeLoading();

    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'episodeLoading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 18px;
    `;

    // Add CSS animation for spinner
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    loadingDiv.innerHTML = `
        <div style="text-align: center;" onclick="event.stopPropagation();">
            <div style="width: 80px; height: 80px; border: 4px solid rgba(0, 123, 255, 0.3); border-left: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div style="font-size: 20px; margin-bottom: 10px;">جاري تحضير الحلقة...</div>
            <div style="font-size: 16px; font-weight: bold; color: #007bff;">${episodeTitle}</div>
            <div style="font-size: 14px; margin-top: 15px; color: #ccc;">يرجى الانتظار...</div>
            <div style="font-size: 12px; margin-top: 20px; color: #999;">اضغط ESC للإلغاء</div>
        </div>
    `;

    // Add click to cancel (click outside content)
    loadingDiv.addEventListener('click', function(e) {
        if (e.target === loadingDiv) {
            hideEpisodeLoading();
            console.log('🎬 Episode loading cancelled by user click');
        }
    });

    // Add ESC key to cancel
    const escHandler = function(e) {
        if (e.key === 'Escape') {
            hideEpisodeLoading();
            document.removeEventListener('keydown', escHandler);
            console.log('🎬 Episode loading cancelled by ESC key');
        }
    };
    document.addEventListener('keydown', escHandler);
    document.body.appendChild(loadingDiv);

    // Auto-hide after 30 seconds as safety measure
    setTimeout(() => {
        hideEpisodeLoading();
        console.warn('⚠️ Episode loading screen auto-hidden after timeout');
    }, 30000);

    console.log('🎬 Episode loading screen shown for:', episodeTitle);
}

/**
 * Hide episode loading screen
 */
function hideEpisodeLoading() {
    const loadingDiv = document.getElementById('episodeLoading');
    if (loadingDiv) {
        loadingDiv.remove();
        console.log('🎬 Episode loading screen hidden');
    }
}

function displayEpisodes(episodes) {
    const container = $('#episodesContainer');
    container.empty();

    episodes.forEach((episode, index) => {
        // Determine episode status and styling
        const isComingSoon = episode.availability_status === 'coming_soon' ||
                           episode.availability_status === 'coming_soon_expired' ||
                           episode.availability_status === 'coming_soon_old' ||
                           episode.stream_state === 'COMING_SOON';
        const isRecentlyAvailable = episode.availability_status === 'recently_available';
        const comingSoonClass = isComingSoon ? 'coming-soon' : '';
        const clickAction = isComingSoon ? `onclick="showComingSoonMessage('${episode.title}', '${episode.availability_message || 'This episode is coming soon'}')"` : `onclick="playEpisode('${episode.id}', '${episode.title}')"`;
        const cursorStyle = isComingSoon ? 'style="cursor: pointer;"' : '';

        // Format availability message
        let statusBadge = '';
        let comingSoonBadge = '';

        if (isComingSoon) {
            // Use the availability message in the badge instead of "Coming Soon"
            const badgeText = episode.availability_message || 'Coming Soon';
            comingSoonBadge = `<div class="coming-soon-badge">${badgeText}</div>`;

            // Keep the status badge empty or minimal for coming soon episodes
            statusBadge = '';

            // Add special styling for expired coming soon but still show the date
            if (episode.availability_status === 'coming_soon_expired') {
                if (episode.start_date) {
                    const formattedDate = formatComingSoonDate(episode.start_date);
                    statusBadge = `<div class="status-badge status-coming-soon-expired">${formattedDate}</div>`;
                } else {
                    statusBadge = `<div class="status-badge status-coming-soon-expired">${episode.availability_message}</div>`;
                }
            }
        } else if (isRecentlyAvailable) {
            statusBadge = `<div class="status-badge status-recently-available">New Episode</div>`;
        } else {
            statusBadge = `<div class="status-badge status-available">Available</div>`;
        }

        // Add tooltip for coming soon episodes
        const tooltipAttr = isComingSoon ? `title="${episode.availability_message}" data-bs-toggle="tooltip"` : '';

        const episodeCard = $(`
            <div class="episode-card ${comingSoonClass}" ${clickAction} ${cursorStyle} ${tooltipAttr}>
                <div class="episode-thumbnail">
                    <img src="${episode.thumbnail || '/images/default-episode.jpg'}" alt="${episode.title}" onerror="this.style.display='none'">
                    <div class="episode-play-overlay">
                        <i class="fas ${isComingSoon ? 'fa-clock' : 'fa-play'}"></i>
                    </div>
                    ${comingSoonBadge}
                </div>
                <div class="episode-info">
                    <div class="episode-number">Episode ${episode.episode_number || (index + 1)}</div>
                    <div class="episode-title">${episode.title || 'Episode ' + (index + 1)}</div>
                    <div class="episode-description">${episode.description || 'No description available'}</div>
                    <div class="episode-status">
                        <div class="episode-duration">
                            <i class="fas fa-clock me-1"></i>
                            ${formatDuration(episode.duration)}
                        </div>
                        ${statusBadge}
                    </div>
                    ${episode.stream_state ? `<div class="text-muted" style="font-size: 0.75rem; margin-top: 5px;">Stream: ${episode.stream_state}</div>` : ''}
                </div>
            </div>
        `);
        container.append(episodeCard);
    });

    // Add touch support for mobile devices
    addMobileTouchSupport();

    // Refresh external touch support if available
    if (window.TouchSupport) {
        setTimeout(() => {
            window.TouchSupport.refresh();
        }, 100);
    }
}

function addMobileTouchSupport() {
    // Detect if device supports touch
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isMobile = window.innerWidth <= 768;

    console.log('Touch support:', isTouchDevice, 'Mobile:', isMobile);

    // Add touch events for mobile play button visibility
    $('.episode-card').each(function() {
        const $card = $(this);
        const $overlay = $card.find('.episode-play-overlay');

        // For mobile devices, show play button by default
        if (isMobile) {
            $overlay.css('opacity', '0.9');
        }

        // Touch start - show play button with animation
        $card.on('touchstart', function(e) {
            e.preventDefault();
            $card.addClass('touch-active');
            $overlay.css({
                'opacity': '1',
                'transform': 'translate(-50%, -50%) scale(1.1)'
            });
        });

        // Touch end - handle click and reset
        $card.on('touchend', function(e) {
            e.preventDefault();

            // Trigger click if it's a tap (not a scroll)
            const touch = e.originalEvent.changedTouches[0];
            const element = document.elementFromPoint(touch.clientX, touch.clientY);
            if (element && $(element).closest('.episode-card').length) {
                // This is a tap, trigger the click
                setTimeout(() => {
                    $card.trigger('click');
                }, 100);
            }

            setTimeout(() => {
                $card.removeClass('touch-active');
                $overlay.css('transform', 'translate(-50%, -50%) scale(1)');
                // Keep visible on mobile
                if (!isMobile) {
                    $overlay.css('opacity', '0');
                }
            }, 200);
        });

        // Touch cancel - reset state
        $card.on('touchcancel', function(e) {
            $card.removeClass('touch-active');
            $overlay.css('transform', 'translate(-50%, -50%) scale(1)');
            if (!isMobile) {
                $overlay.css('opacity', '0');
            }
        });

        // For desktop - maintain original hover behavior
        if (!isTouchDevice) {
            $card.on('mouseenter', function() {
                $overlay.css('opacity', '1');
            });

            $card.on('mouseleave', function() {
                $overlay.css('opacity', '0');
            });
        }
    });

    // Handle window resize
    $(window).on('resize', function() {
        const newIsMobile = window.innerWidth <= 768;
        if (newIsMobile !== isMobile) {
            // Refresh touch support when screen size changes
            setTimeout(() => {
                addMobileTouchSupport();
            }, 100);
        }
    });
}

function showSeasonsView() {
    $('#castSection').removeClass('d-none');
    $('#seasonsSection').removeClass('d-none');
    $('#episodesSection').addClass('d-none');
    $('#errorMessage').addClass('d-none');
}

function showEpisodesView() {
    $('#castSection').addClass('d-none');
    $('#seasonsSection').addClass('d-none');
    $('#episodesSection').removeClass('d-none');
    $('#errorMessage').addClass('d-none');
}

function playEpisode(episodeId, episodeTitle) {
    console.log(`🎬 Starting smart playback for episode: ${episodeTitle} (ID: ${episodeId})`);

    // Show episode loading screen
    showEpisodeLoading(episodeTitle);

    // Use Universal Player - detects device and chooses appropriate DRM
    $.ajax({
        url: `/universal/player-data/${episodeId}`,
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            hideEpisodeLoading();
            console.log('✅ Universal player data extracted:', response);

            if (response.success && response.player_url) {
                console.log(`🎯 Device: ${response.platform}, DRM: ${response.drm_type}`);
                console.log('🎬 Redirecting to universal player:', response.player_url);
                
                // Redirect to device-appropriate player
                window.location.href = response.player_url;
            } else {
                console.error('❌ Universal player failed:', response.error);
                showAlert('danger', `Failed to prepare player: ${response.error || 'Unknown error'}`);
            }
        },
        error: function(xhr) {
            hideEpisodeLoading();
            console.error('❌ Universal player error:', xhr);

            let errorMessage = 'Error occurred while preparing universal player';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }

            console.error('❌ Universal player error:', errorMessage);
        }
    });
}

function openEpisodePlayerWithKey(episodeId, episodeTitle, key, kid) {
    // Hide episode loading screen
    hideEpisodeLoading();

    console.log('🎮 Opening episode player with extracted key');
    console.log('📺 Episode:', episodeTitle);
    console.log('🔑 Key:', key.substring(0, 10) + '...');
    console.log('🆔 KID:', kid);

    // Get episode playout URL first (same pattern as movies)
    $.ajax({
        url: '/api/episode-playout',
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            episode_id: episodeId
        },
        success: function(playoutResponse) {
            if (playoutResponse.success && playoutResponse.mpd_url) {
                // Open player with MPD URL and extracted key (same as movies)
                const playerUrl = new URL('/shahid/player', window.location.origin);
                playerUrl.searchParams.set('type', 'dash');
                playerUrl.searchParams.set('mpd', playoutResponse.mpd_url);
                playerUrl.searchParams.set('key', key);
                playerUrl.searchParams.set('keyId', kid);
                playerUrl.searchParams.set('title', episodeTitle);

                // Open in same window to avoid popup blockers
                window.location.href = playerUrl.toString();
                console.log('🚀 Episode player opened with key:', playerUrl.toString());
            } else {
                // Fallback: use direct episode player route
                const playerUrl = new URL('/shahid/play/episode/' + episodeId, window.location.origin);
                playerUrl.searchParams.set('key', key);
                playerUrl.searchParams.set('keyId', kid);
                playerUrl.searchParams.set('title', episodeTitle);

                // Open in same window to avoid popup blockers
                window.location.href = playerUrl.toString();
                console.log('🚀 Episode player opened (fallback):', playerUrl.toString());
            }
        },
        error: function() {
            // Hide loading screen
            hideEpisodeLoading();

            // Fallback: use direct episode player route
            const playerUrl = new URL('/shahid/play/episode/' + episodeId, window.location.origin);
            playerUrl.searchParams.set('key', key);
            playerUrl.searchParams.set('keyId', kid);
            playerUrl.searchParams.set('title', episodeTitle);

            // Open in same window to avoid popup blockers
            window.location.href = playerUrl.toString();
            console.log('🚀 Episode player opened (error fallback):', playerUrl.toString());
        }
    });
}

function showComingSoonMessage(episodeTitle, availabilityMessage) {
    // Create a nice modal-style alert
    const message = `
        <div style="text-align: center; padding: 20px;">
            <i class="fas fa-clock" style="font-size: 3rem; color: #ff6b6b; margin-bottom: 15px;"></i>
            <h4 style="color: #2c3e50; margin-bottom: 10px;">Episode Coming Soon!</h4>
            <p style="color: #7f8c8d; margin-bottom: 15px;">"${episodeTitle}"</p>
            <p style="color: #e74c3c; font-weight: 600;">${availabilityMessage}</p>
            <p style="color: #95a5a6; font-size: 0.9rem;">Please check back later.</p>
        </div>
    `;

    // For now use alert, but this could be replaced with a proper modal
    alert(`Episode "${episodeTitle}" is coming soon!\n\n${availabilityMessage}\n\nPlease check back later.`);
}

function formatDuration(duration) {
    if (!duration) return 'Unknown';

    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);

    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
}

function formatComingSoonDate(dateString) {
    if (!dateString) return 'قريباً';

    // Parse the date and convert to Egypt timezone (UTC+2)
    const date = new Date(dateString);

    // Convert to Egypt timezone
    const egyptDate = new Date(date.getTime() + (2 * 60 * 60 * 1000)); // Add 2 hours for Egypt timezone
    const now = new Date();
    const nowEgypt = new Date(now.getTime() + (2 * 60 * 60 * 1000));

    // Check if it's today, tomorrow, or later
    const egyptDateOnly = new Date(egyptDate.getFullYear(), egyptDate.getMonth(), egyptDate.getDate());
    const nowEgyptDateOnly = new Date(nowEgypt.getFullYear(), nowEgypt.getMonth(), nowEgypt.getDate());
    const diffDays = Math.ceil((egyptDateOnly - nowEgyptDateOnly) / (1000 * 60 * 60 * 24));

    const hours = egyptDate.getHours();
    const minutes = egyptDate.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const timeString = `${displayHours}:${minutes} ${ampm}`;

    if (diffDays === 0) {
        // Today - show "Coming Tonight"
        return `Coming Tonight ${timeString}`;
    } else if (diffDays === 1) {
        // Tomorrow - show "Coming Tomorrow"
        return `Coming Tomorrow ${timeString}`;
    } else {
        // Later - show date and time: DD/MM - HH:MM AM/PM
        const day = egyptDate.getDate().toString().padStart(2, '0');
        const month = (egyptDate.getMonth() + 1).toString().padStart(2, '0');
        return `${day}/${month} - ${timeString}`;
    }
}

function formatComingSoonDateArabic(dateString) {
    if (!dateString) return 'قريباً';

    const date = new Date(dateString);

    // Format in Arabic style: DD/MM - HH:MM صباحاً/مساءً
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');

    // Convert to 12-hour format with Arabic AM/PM
    const ampm = hours >= 12 ? 'مساءً' : 'صباحاً';
    const displayHours = hours % 12 || 12;

    return `${day}/${month} - ${displayHours}:${minutes} ${ampm}`;
}
</script>

<!-- Mobile Touch Support JavaScript -->
<script src="{{ asset('js/mobile-touch-support.js') }}"></script>
@endsection
