<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminManagerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth('admin')->check()) {
            return redirect()->route('admin.login')->with('error', 'Please login to access this area.');
        }

        $user = auth('admin')->user();

        // Check if user exists and is properly loaded
        if (!$user || !$user->id) {
            abort(403, 'Access denied. Please login as admin.');
        }

        // Check if user can manage admins
        try {
            if (!$user->canManageAdmins()) {
                abort(403, 'Access denied. Admin management privileges required.');
            }
        } catch (\Exception $e) {
            \Log::error('Error checking admin permissions: ' . $e->getMessage());
            abort(403, 'Access denied. Permission check failed.');
        }

        // Check if user is suspended
        if ($user->isSuspended()) {
            auth('admin')->logout();
            return redirect()->route('admin.login')->with('error', 'Your account has been suspended.');
        }

        // Update last activity
        $user->updateLastActivity();

        return $next($request);
    }
}
