@extends('layouts.app')

@section('title', 'Dashboard - Shahid Play')

@section('styles')
<style>
/* Dashboard Modern Design */
.dashboard-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    color: white;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.dashboard-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.feature-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: none;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.quick-action-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge.success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.warning {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-badge.danger {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-hero {
        padding: 1.5rem;
        text-align: center;
    }

    .dashboard-hero h1 {
        font-size: 1.75rem;
    }

    .feature-card {
        margin-bottom: 1rem;
    }

    .quick-action-btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .dashboard-hero {
        padding: 1rem;
    }

    .dashboard-hero h1 {
        font-size: 1.5rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}
</style>
@endsection

@section('content')
<div class="container-fluid" dir="ltr">
    <!-- Hero Section -->
    <div class="dashboard-hero">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="mb-2 fw-bold">
                    <i class="fas fa-play-circle me-2"></i>
                    Welcome to Shahid Play
                </h1>
                <p class="mb-3 opacity-90">Your gateway to premium Arabic entertainment content</p>
                <div class="d-flex flex-wrap gap-2">
                    @if($shahidStatus['connected'])
                        <span class="status-badge success">
                            <i class="fas fa-check-circle"></i>
                            API Connected
                        </span>
                    @else
                        <span class="status-badge danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            API Disconnected
                        </span>
                    @endif

                    @if($startupStatus)
                        <span class="status-badge {{ $startupStatus['color'] === 'success' ? 'success' : ($startupStatus['color'] === 'warning' ? 'warning' : 'danger') }}">
                            <i class="fas fa-{{ $startupStatus['color'] === 'success' ? 'crown' : ($startupStatus['color'] === 'warning' ? 'clock' : 'exclamation-triangle') }}"></i>
                            {{ $startupStatus['title'] }}
                        </span>
                    @endif
                </div>
            </div>
            <div class="col-lg-4 text-lg-end text-center mt-3 mt-lg-0">
                <div class="text-white-50">
                    <i class="fas fa-clock me-1"></i>
                    Last Login: {{ auth()->user()->login_time ? auth()->user()->login_time->format('M d, Y H:i') : 'First time' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3 fw-bold">
                <i class="fas fa-bolt me-2 text-primary"></i>
                Quick Actions
            </h4>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="feature-card text-center">
                <div class="feature-icon bg-primary bg-opacity-10 text-primary mx-auto">
                    <i class="fas fa-film"></i>
                </div>
                <h5 class="fw-bold mb-2">Browse Movies</h5>
                <p class="text-muted mb-3">Explore thousands of movies from Shahid's vast collection</p>
                <a href="{{ route('shahid.movies') }}" class="quick-action-btn">
                    <i class="fas fa-play"></i>
                    Watch Movies
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="feature-card text-center">
                <div class="feature-icon bg-success bg-opacity-10 text-success mx-auto">
                    <i class="fas fa-tv"></i>
                </div>
                <h5 class="fw-bold mb-2">Watch Series</h5>
                <p class="text-muted mb-3">Discover amazing TV series and binge-watch your favorites</p>
                <a href="{{ route('shahid.series') }}" class="quick-action-btn">
                    <i class="fas fa-play"></i>
                    Watch Series
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="feature-card text-center">
                <div class="feature-icon bg-warning bg-opacity-10 text-warning mx-auto">
                    <i class="fas fa-broadcast-tower"></i>
                </div>
                <h5 class="fw-bold mb-2">Live Channels</h5>
                <p class="text-muted mb-3">Watch live TV channels and stay updated with latest content</p>
                <a href="{{ route('shahid.channels') }}" class="quick-action-btn">
                    <i class="fas fa-satellite-dish"></i>
                    Live TV
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="feature-card text-center">
                <div class="feature-icon bg-info bg-opacity-10 text-info mx-auto">
                    <i class="fas fa-cog"></i>
                </div>
                <h5 class="fw-bold mb-2">Settings</h5>
                <p class="text-muted mb-3">Manage your account settings and preferences</p>
                <a href="{{ route('settings.index') }}" class="quick-action-btn">
                    <i class="fas fa-tools"></i>
                    Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="row">
        <div class="col-12 mb-3">
            <h4 class="mb-3 fw-bold">
                <i class="fas fa-user-circle me-2 text-primary"></i>
                Account Information
            </h4>
        </div>

        <!-- User Profile Card -->
        <div class="col-lg-6 mb-4">
            <div class="feature-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon bg-primary bg-opacity-10 text-primary me-3" style="width: 50px; height: 50px; font-size: 1.25rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">Profile Details</h5>
                        <p class="text-muted mb-0 small">Your account information</p>
                    </div>
                </div>
                <div class="row g-3">
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-user me-2 text-muted"></i>
                            <span class="text-muted small">Full Name</span>
                        </div>
                        <p class="fw-semibold mb-0">{{ auth()->user()->name ?? 'Not specified' }}</p>
                    </div>
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope me-2 text-muted"></i>
                            <span class="text-muted small">Email Address</span>
                        </div>
                        <p class="fw-semibold mb-0">{{ auth()->user()->email ?? 'Not specified' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Card -->
        <div class="col-lg-6 mb-4">
            <div class="feature-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon bg-warning bg-opacity-10 text-warning me-3" style="width: 50px; height: 50px; font-size: 1.25rem;">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">Subscription</h5>
                        <p class="text-muted mb-0 small">Your plan and status</p>
                    </div>
                </div>
                <div class="row g-3">
                    <div class="col-12">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-star me-2 text-muted"></i>
                            <span class="text-muted small">Current Plan</span>
                        </div>
                        <p class="fw-semibold mb-0 text-primary">{{ $subscriptionPlan }}</p>
                    </div>
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shield-alt me-2 text-muted"></i>
                            <span class="text-muted small">Status</span>
                        </div>
                        <span class="badge bg-{{ $statusColor }} px-3 py-2">{{ $subscriptionStatus }}</span>
                    </div>
                    @if($remainingDays != 999)
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-clock me-2 text-muted"></i>
                            <span class="text-muted small">Days Remaining</span>
                        </div>
                        <p class="fw-semibold mb-0 text-{{ $statusColor }}">
                            {{ $remainingDays }} {{ $remainingDays == 1 ? 'day' : 'days' }}
                        </p>
                    </div>
                    @else
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-infinity me-2 text-muted"></i>
                            <span class="text-muted small">Type</span>
                        </div>
                        <p class="fw-semibold mb-0 text-success">Unlimited</p>
                    </div>
                    @endif
                    @if(auth()->user()->subscription_expiry)
                    <div class="col-12">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-calendar-times me-2 text-muted"></i>
                            <span class="text-muted small">Expiry Date</span>
                        </div>
                        <p class="fw-semibold mb-0 text-{{ $statusColor }}">
                            {{ auth()->user()->subscription_expiry->format('M d, Y \a\t H:i') }}
                        </p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row">
        <div class="col-12 mb-3">
            <h4 class="mb-3 fw-bold">
                <i class="fas fa-server me-2 text-primary"></i>
                System Status
            </h4>
        </div>

        <!-- API Status Card -->
        <div class="col-lg-6 mb-4">
            <div class="feature-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon bg-{{ $shahidStatus['connected'] ? 'success' : 'danger' }} bg-opacity-10 text-{{ $shahidStatus['connected'] ? 'success' : 'danger' }} me-3" style="width: 50px; height: 50px; font-size: 1.25rem;">
                        <i class="fas fa-{{ $shahidStatus['connected'] ? 'check-circle' : 'exclamation-triangle' }}"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">Shahid API</h5>
                        <p class="text-muted mb-0 small">Connection status</p>
                    </div>
                </div>
                <div class="row g-3">
                    <div class="col-12">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-plug me-2 text-muted"></i>
                            <span class="text-muted small">Status</span>
                        </div>
                        <span class="badge bg-{{ $shahidStatus['connected'] ? 'success' : 'danger' }} px-3 py-2">
                            {{ $shahidStatus['token_status'] }}
                        </span>
                    </div>
                    <div class="col-12">
                        <p class="text-muted mb-0 small">
                            @if($shahidStatus['connected'])
                                ✅ Token is valid and successfully connected to Shahid service
                            @else
                                ❌ Token is invalid or expired - Please update the token
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Info Card -->
        <div class="col-lg-6 mb-4">
            <div class="feature-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon bg-info bg-opacity-10 text-info me-3" style="width: 50px; height: 50px; font-size: 1.25rem;">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">System Info</h5>
                        <p class="text-muted mb-0 small">Server details</p>
                    </div>
                </div>
                <div class="row g-3">
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-desktop me-2 text-muted"></i>
                            <span class="text-muted small">Platform</span>
                        </div>
                        <p class="fw-semibold mb-0">{{ $systemInfo['platform'] ?? 'Unknown' }}</p>
                    </div>
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-code me-2 text-muted"></i>
                            <span class="text-muted small">PHP Version</span>
                        </div>
                        <p class="fw-semibold mb-0">{{ $systemInfo['php_version'] ?? 'Unknown' }}</p>
                    </div>
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fab fa-laravel me-2 text-muted"></i>
                            <span class="text-muted small">Laravel</span>
                        </div>
                        <p class="fw-semibold mb-0">{{ $systemInfo['laravel_version'] ?? 'Unknown' }}</p>
                    </div>
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-server me-2 text-muted"></i>
                            <span class="text-muted small">Server</span>
                        </div>
                        <p class="fw-semibold mb-0 small">{{ Str::limit($systemInfo['server_software'] ?? 'Unknown', 20) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
