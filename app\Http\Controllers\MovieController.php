<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\ShahidDRM;

class MovieController extends Controller
{
    private $shahidDRM;

    public function __construct()
    {
        $this->shahidDRM = new ShahidDRM();
    }

    /**
     * عرض قائمة الأفلام
     */
    public function index()
    {
        // هنا يمكنك جلب قائمة الأفلام من API أو قاعدة البيانات
        $movies = $this->getMoviesList();
        
        return view('movies.index', compact('movies'));
    }

    /**
     * تشغيل فيلم مع استخراج المفاتيح بشكل صامت
     */
    public function play($contentId)
    {
        try {
            Log::info("🎬 Starting movie playback for content ID: {$contentId}");

            // 1. استخراج بيانات الفيلم
            $movieData = $this->getMovieData($contentId);
            if (!$movieData) {
                return response()->json(['error' => 'Movie not found'], 404);
            }

            // 2. استخراج المفتاح بشكل صامت
            Log::info("🔑 Extracting DRM key silently...");
            $drmKey = $this->shahidDRM->extractKey($contentId);
            
            if (!$drmKey) {
                Log::error("❌ Failed to extract DRM key for content: {$contentId}");
                return response()->json(['error' => 'Failed to extract DRM key'], 500);
            }

            Log::info("✅ DRM key extracted successfully: {$drmKey}");

            // 3. تحضير بيانات المشغل
            $playerData = [
                'content_id' => $contentId,
                'title' => $movieData['title'] ?? 'Shahid Movie',
                'poster' => $movieData['poster'] ?? '',
                'manifest_url' => $movieData['manifest_url'] ?? '',
                'drm_key' => $drmKey,
                'license_url' => $movieData['license_url'] ?? '',
                'subtitles' => $movieData['subtitles'] ?? []
            ];

            Log::info("🎯 Player data prepared successfully");

            return view('movies.player', compact('playerData'));

        } catch (\Exception $e) {
            Log::error("💥 Error in movie playback: " . $e->getMessage());
            return response()->json(['error' => 'Playback failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * API endpoint لاستخراج المفتاح (AJAX)
     */
    public function extractKey(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            
            if (!$contentId) {
                return response()->json(['error' => 'Content ID required'], 400);
            }

            Log::info("🔄 AJAX key extraction request for: {$contentId}");

            // استخراج المفتاح
            $drmKey = $this->shahidDRM->extractKey($contentId);
            
            if ($drmKey) {
                Log::info("✅ AJAX key extraction successful");
                return response()->json([
                    'success' => true,
                    'key' => $drmKey,
                    'content_id' => $contentId
                ]);
            } else {
                Log::error("❌ AJAX key extraction failed");
                return response()->json(['error' => 'Key extraction failed'], 500);
            }

        } catch (\Exception $e) {
            Log::error("💥 AJAX key extraction error: " . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * جلب بيانات الفيلم
     */
    private function getMovieData($contentId)
    {
        try {
            // هنا يمكنك استدعاء Shahid API لجلب بيانات الفيلم
            // مؤقتاً سأستخدم بيانات تجريبية
            
            return [
                'title' => 'Shahid Movie ' . $contentId,
                'poster' => 'https://via.placeholder.com/300x450/1a1a1a/ffffff?text=Movie+' . $contentId,
                'manifest_url' => "https://shahid-vod.akamaized.net/content/{$contentId}/manifest.mpd",
                'license_url' => "https://shahid-ga.la.drm.cloud/acquire-license/widevine?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4&UserToken=...",
                'subtitles' => []
            ];

        } catch (\Exception $e) {
            Log::error("Error fetching movie data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * جلب قائمة الأفلام
     */
    private function getMoviesList()
    {
        // بيانات تجريبية للأفلام
        return [
            [
                'id' => '30600042280',
                'title' => 'فيلم تجريبي 1',
                'poster' => 'https://via.placeholder.com/300x450/2c3e50/ffffff?text=Movie+1',
                'description' => 'وصف الفيلم الأول'
            ],
            [
                'id' => '30600042281',
                'title' => 'فيلم تجريبي 2', 
                'poster' => 'https://via.placeholder.com/300x450/34495e/ffffff?text=Movie+2',
                'description' => 'وصف الفيلم الثاني'
            ],
            [
                'id' => '30600042282',
                'title' => 'فيلم تجريبي 3',
                'poster' => 'https://via.placeholder.com/300x450/16a085/ffffff?text=Movie+3', 
                'description' => 'وصف الفيلم الثالث'
            ]
        ];
    }
}
