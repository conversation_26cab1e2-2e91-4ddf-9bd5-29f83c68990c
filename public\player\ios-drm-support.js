/**
 * iOS DRM Support for Encrypted Content
 * Handles FairPlay and Clear Key DRM on iOS devices
 */

window.iOSDRMSupport = {
    
    // Detect if running on iOS
    isIOS: function() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    },

    // Detect if running on Safari
    isSafari: function() {
        return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    },

    // Check if device supports FairPlay
    supportsFairPlay: function() {
        return this.isIOS() && window.WebKitMediaKeys;
    },

    // Check if device supports DASH
    supportsDASH: function() {
        return this.isIOS() && (
            // Check for native DASH support
            (window.MediaSource && MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E"')) ||
            // Check for Shaka Player support
            (typeof shaka !== 'undefined')
        );
    },

    // Setup iOS-optimized player config
    setupiOSPlayer: function(config) {
        console.log('🍎 Setting up iOS-optimized player...');

        if (!this.isIOS()) {
            console.log('📱 Not iOS device, using standard config');
            return config;
        }

        // Check if we have DASH content
        const isDASH = this.isDASHContent(config);
        console.log('📺 DASH content detected:', isDASH);

        const iOSConfig = {
            ...config,
            // iOS-specific optimizations
            hlsjsdefault: false, // Use native HLS on iOS
            preload: 'metadata',
            autostart: false, // iOS requires user interaction
            mute: true, // Start muted to allow autoplay

            // iOS video settings
            playsinline: true,
            controls: true,

            // Disable features that don't work well on iOS
            sharing: false,
            related: false,
            logo: false,

            // DASH-specific settings for iOS
            ...(isDASH && {
                // Force Shaka Player for DASH on iOS
                hlsjsdefault: false,
                // Enable Shaka for DASH
                cast: false, // Disable cast for stability
                // Add DASH provider
                providers: [{
                    name: 'shaka',
                    url: '/player/js/provider.shaka.js'
                }]
            }),

            // iOS-specific events
            events: {
                ...config.events,
                onReady: this.handleiOSReady.bind(this),
                onPlay: this.handleiOSPlay.bind(this),
                onError: this.handleiOSError.bind(this)
            }
        };

        // Handle DRM for iOS
        if (config.drm || config.clearkey) {
            iOSConfig.drm = this.setupiOSDRM(config, isDASH);
        }

        // Convert DASH to HLS if needed
        if (isDASH && !this.supportsDASH()) {
            console.log('🔄 Converting DASH to HLS for iOS compatibility...');
            iOSConfig = this.convertDASHToHLS(iOSConfig);
        }

        console.log('🍎 iOS config prepared:', iOSConfig);
        return iOSConfig;
    },

    // Check if content is DASH
    isDASHContent: function(config) {
        if (config.file && config.file.includes('.mpd')) return true;
        if (config.playlist && config.playlist[0] && config.playlist[0].file && config.playlist[0].file.includes('.mpd')) return true;
        if (config.playlist && config.playlist[0] && config.playlist[0].sources) {
            return config.playlist[0].sources.some(source =>
                source.file && source.file.includes('.mpd') ||
                source.type === 'dash'
            );
        }
        return false;
    },

    // Convert DASH to HLS for iOS compatibility
    convertDASHToHLS: function(config) {
        console.log('🔄 Converting DASH to HLS for iOS...');

        // This is a placeholder - in real implementation you'd need:
        // 1. A server-side converter
        // 2. Or alternative HLS URLs
        // 3. Or fallback content

        const convertedConfig = { ...config };

        // Example conversion (replace with your actual conversion logic)
        if (config.file && config.file.includes('.mpd')) {
            // Convert .mpd to .m3u8 (this needs server-side support)
            convertedConfig.file = config.file.replace('.mpd', '.m3u8');
            console.log('🔄 Converted DASH URL to HLS:', convertedConfig.file);
        }

        if (config.playlist && config.playlist[0]) {
            const playlist = config.playlist[0];
            if (playlist.file && playlist.file.includes('.mpd')) {
                playlist.file = playlist.file.replace('.mpd', '.m3u8');
                console.log('🔄 Converted playlist DASH URL to HLS:', playlist.file);
            }

            // Convert sources
            if (playlist.sources) {
                playlist.sources = playlist.sources.map(source => {
                    if (source.file && source.file.includes('.mpd')) {
                        return {
                            ...source,
                            file: source.file.replace('.mpd', '.m3u8'),
                            type: 'hls'
                        };
                    }
                    return source;
                });
            }
        }

        return convertedConfig;
    },

    // Setup DRM for iOS
    setupiOSDRM: function(config, isDASH = false) {
        console.log('🔐 Setting up iOS DRM...', { isDASH });

        const drmConfig = {};

        // Handle Clear Key DRM
        if (config.clearkey || (config.drm && config.drm.clearkey)) {
            console.log('🔑 Setting up Clear Key for iOS...');

            const clearKeyData = config.clearkey || config.drm.clearkey;

            if (isDASH) {
                // DASH Clear Key format
                drmConfig.clearkey = {
                    keyId: clearKeyData.keyId || clearKeyData.kid,
                    key: clearKeyData.key || clearKeyData.k,
                    // DASH-specific settings
                    laUrl: clearKeyData.laUrl || 'data:application/json;base64,' + btoa(JSON.stringify({
                        keys: [{
                            kty: 'oct',
                            kid: clearKeyData.keyId || clearKeyData.kid,
                            k: clearKeyData.key || clearKeyData.k
                        }]
                    }))
                };
            } else {
                // HLS Clear Key format
                drmConfig.clearkey = {
                    keyId: clearKeyData.keyId || clearKeyData.kid,
                    key: clearKeyData.key || clearKeyData.k
                };
            }
        }

        // Handle FairPlay DRM (works with both HLS and DASH)
        if (config.drm && config.drm.fairplay) {
            console.log('🍎 Setting up FairPlay DRM...');

            if (this.supportsFairPlay()) {
                drmConfig.fairplay = {
                    certificateUrl: config.drm.fairplay.certificateUrl,
                    licenseUrl: config.drm.fairplay.licenseUrl,
                    // iOS-specific FairPlay settings
                    contentId: config.drm.fairplay.contentId || 'default',
                    certificateRequestHeaders: {
                        'User-Agent': this.getiOSUserAgent()
                    },
                    licenseRequestHeaders: {
                        'User-Agent': this.getiOSUserAgent(),
                        'Content-Type': 'application/octet-stream'
                    }
                };
            } else {
                console.warn('⚠️ FairPlay not supported on this device');
            }
        }

        // Handle Widevine for DASH (note: not supported in Safari)
        if (isDASH && config.drm && config.drm.widevine && !this.isSafari()) {
            console.log('🔐 Setting up Widevine for DASH (non-Safari)...');
            drmConfig.widevine = config.drm.widevine;
        }

        return drmConfig;
    },

    // Get iOS-appropriate User Agent
    getiOSUserAgent: function() {
        if (this.isIOS()) {
            return navigator.userAgent; // Use real iOS user agent
        }
        return 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1';
    },

    // Handle iOS ready event
    handleiOSReady: function() {
        console.log('🍎 iOS player ready');
        
        // Add iOS-specific optimizations
        const player = jwplayer('player');
        if (!player) return;

        // Enable picture-in-picture if supported
        if (this.supportsPiP()) {
            console.log('📺 Picture-in-Picture supported');
        }

        // Handle iOS fullscreen
        this.setupiOSFullscreen(player);
    },

    // Handle iOS play event
    handleiOSPlay: function() {
        console.log('▶️ iOS playback started');
        
        // Unmute after user interaction (iOS requirement)
        const player = jwplayer('player');
        if (player && player.getMute()) {
            setTimeout(() => {
                player.setMute(false);
                console.log('🔊 Unmuted after user interaction');
            }, 500);
        }
    },

    // Handle iOS errors
    handleiOSError: function(error) {
        console.error('🍎 iOS Player Error:', error);
        
        // Handle iOS-specific errors
        switch(error.code) {
            case 224003: // DRM error
                console.log('🔐 DRM error on iOS, attempting fallback...');
                this.attemptDRMFallback();
                break;
            case 100001: // Network error
                console.log('🌐 Network error on iOS, checking connection...');
                this.checkiOSConnectivity();
                break;
            default:
                console.log('⚠️ Unknown iOS error:', error.code);
        }
    },

    // Check if Picture-in-Picture is supported
    supportsPiP: function() {
        return this.isIOS() && 'pictureInPictureEnabled' in document;
    },

    // Setup iOS fullscreen handling
    setupiOSFullscreen: function(player) {
        if (!this.isIOS()) return;

        // iOS fullscreen handling
        player.on('fullscreen', (event) => {
            if (event.fullscreen) {
                console.log('📱 Entered iOS fullscreen');
                // Lock orientation if possible
                if (screen.orientation && screen.orientation.lock) {
                    screen.orientation.lock('landscape').catch(e => {
                        console.log('📱 Could not lock orientation:', e);
                    });
                }
            } else {
                console.log('📱 Exited iOS fullscreen');
            }
        });
    },

    // Attempt DRM fallback for iOS
    attemptDRMFallback: function() {
        console.log('🔄 Attempting DRM fallback for iOS...');
        
        const player = jwplayer('player');
        if (!player) return;

        // Try to reload without DRM
        const config = player.getConfig();
        const fallbackConfig = {
            ...config,
            drm: undefined // Remove DRM
        };

        setTimeout(() => {
            try {
                player.load(fallbackConfig.playlist || fallbackConfig.file);
                console.log('🔄 DRM fallback attempted');
            } catch (error) {
                console.error('❌ DRM fallback failed:', error);
            }
        }, 2000);
    },

    // Check iOS connectivity
    checkiOSConnectivity: function() {
        console.log('🔍 Checking iOS connectivity...');
        
        // Use iOS-specific connectivity check
        if (navigator.connection) {
            console.log('📶 Connection type:', navigator.connection.effectiveType);
            console.log('📶 Downlink:', navigator.connection.downlink);
        }

        // Simple connectivity test
        fetch('/health.php', { method: 'HEAD' })
            .then(response => {
                console.log('✅ iOS connectivity OK');
            })
            .catch(error => {
                console.error('❌ iOS connectivity failed:', error);
            });
    },

    // Create iOS-optimized HLS URL
    optimizeHLSForIOS: function(url) {
        if (!this.isIOS() || !url.includes('.m3u8')) {
            return url;
        }

        // Add iOS-specific parameters
        const separator = url.includes('?') ? '&' : '?';
        return `${url}${separator}ios=1&mobile=1`;
    },

    // Initialize iOS support
    init: function() {
        if (this.isIOS()) {
            console.log('🍎 iOS DRM Support initialized');
            console.log('📱 Device:', navigator.userAgent);
            console.log('🔐 FairPlay supported:', this.supportsFairPlay());
            console.log('📺 PiP supported:', this.supportsPiP());
        }
    }
};

// Auto-initialize
document.addEventListener('DOMContentLoaded', function() {
    window.iOSDRMSupport.init();
});

console.log('🍎 iOS DRM Support loaded');
