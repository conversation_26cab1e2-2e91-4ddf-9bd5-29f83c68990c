<!-- Admin Header - Fixed Top Navigation -->
<header class="admin-header" style="position: fixed; top: 0; left: 250px; right: 0; height: 60px; background: white; border-bottom: 1px solid #e9ecef; z-index: 999; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <div class="container-fluid h-100">
        <div class="row h-100 align-items-center">
            <!-- Left Side - Page Title and Breadcrumb -->
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <!-- Mobile Menu Toggle -->
                    <button class="btn btn-link d-md-none me-3" onclick="toggleMobileSidebar()" style="color: #667eea;">
                        <i class="fas fa-bars fa-lg"></i>
                    </button>
                    
                    <!-- Page Title -->
                    <div>
                        <h4 class="mb-0" style="color: #333; font-weight: 700;">
                            @yield('page-title', 'Dashboard')
                        </h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0" style="font-size: 0.875rem;">
                                <li class="breadcrumb-item">
                                    <a href="{{ route('admin.dashboard') }}" style="color: #667eea; text-decoration: none;">
                                        <i class="fas fa-home me-1"></i>Admin
                                    </a>
                                </li>
                                @yield('breadcrumb')
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - User Info and Actions -->
            <div class="col-md-6">
                <div class="d-flex align-items-center justify-content-end">
                    <!-- Notifications -->
                    <div class="dropdown me-3">
                        <button class="btn btn-link position-relative" type="button" data-bs-toggle="dropdown" style="color: #667eea;">
                            <i class="fas fa-bell fa-lg"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount" style="font-size: 0.6rem;">
                                0
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li class="dropdown-header">
                                <i class="fas fa-bell me-2"></i>Notifications
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <div id="notificationsList">
                                <li class="dropdown-item text-muted text-center py-3">
                                    <i class="fas fa-inbox me-2"></i>No new notifications
                                </li>
                            </div>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="#" onclick="markAllAsRead()">
                                    <i class="fas fa-check-double me-2"></i>Mark all as read
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="dropdown me-3">
                        <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-1"></i>Quick Add
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#" onclick="openCreateUserModal()">
                                    <i class="fas fa-user-plus me-2"></i>New User
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="openCreatePlanModal()">
                                    <i class="fas fa-tags me-2"></i>New Plan
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="openCreateSubscriptionModal()">
                                    <i class="fas fa-credit-card me-2"></i>New Subscription
                                </a>
                            </li>
                            @can('createAdmin', App\Models\User::class)
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.admin-management.index') }}">
                                    <i class="fas fa-user-cog me-2"></i>New Admin
                                </a>
                            </li>
                            @endcan
                            @can('create', Spatie\Permission\Models\Role::class)
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.roles.index') }}">
                                    <i class="fas fa-user-tag me-2"></i>New Role
                                </a>
                            </li>
                            @endcan
                        </ul>
                    </div>
                    
                    <!-- User Profile Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown" style="color: #333; text-decoration: none;">
                            <div class="me-2" style="width: 35px; height: 35px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                                {{ strtoupper(substr(Auth::guard('admin')->user()->name, 0, 1)) }}
                            </div>
                            <div class="text-start d-none d-md-block">
                                <div style="font-weight: 600; font-size: 0.9rem;">{{ Auth::guard('admin')->user()->name }}</div>
                                <div style="font-size: 0.75rem; color: #6c757d;">Administrator</div>
                            </div>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="width: 30px; height: 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.8rem;">
                                        {{ strtoupper(substr(Auth::guard('admin')->user()->name, 0, 1)) }}
                                    </div>
                                    <div>
                                        <div style="font-weight: 600;">{{ Auth::guard('admin')->user()->name }}</div>
                                        <div style="font-size: 0.75rem; color: #6c757d;">{{ Auth::guard('admin')->user()->email }}</div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.settings.index') }}">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('dashboard') }}">
                                    <i class="fas fa-external-link-alt me-2"></i>User Dashboard
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{{ route('admin.logout') }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Spacer for fixed header -->
<div style="height: 60px;"></div>

<!-- Mobile Header Overlay -->
<div class="mobile-header-overlay d-md-none" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 998;" onclick="toggleMobileSidebar()"></div>

<script>
// Load notifications on page load
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    
    // Refresh notifications every 30 seconds
    setInterval(loadNotifications, 30000);
});

function loadNotifications() {
    // Load expiring subscriptions as notifications using web route
    $.ajax({
        url: '/admin/ajax/dashboard/expiring-subscriptions',
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Accept': 'application/json'
        }
    })
    .done(function(data) {
        if (data.success) {
            updateNotifications(data.data);
        }
    })
    .fail(function(xhr, status, error) {
        console.error('Error loading notifications:', error);
        // Fallback - hide notifications if API fails
        const notificationCount = document.getElementById('notificationCount');
        const notificationsList = document.getElementById('notificationsList');
        if (notificationCount) notificationCount.style.display = 'none';
        if (notificationsList) {
            notificationsList.innerHTML = `
                <li class="dropdown-item text-muted text-center py-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>Unable to load notifications
                </li>
            `;
        }
    });
}

function updateNotifications(expiringSubscriptions) {
    const notificationCount = document.getElementById('notificationCount');
    const notificationsList = document.getElementById('notificationsList');
    
    if (expiringSubscriptions.length === 0) {
        notificationCount.textContent = '0';
        notificationCount.style.display = 'none';
        notificationsList.innerHTML = `
            <li class="dropdown-item text-muted text-center py-3">
                <i class="fas fa-inbox me-2"></i>No new notifications
            </li>
        `;
    } else {
        notificationCount.textContent = expiringSubscriptions.length;
        notificationCount.style.display = 'block';
        
        let html = '';
        expiringSubscriptions.slice(0, 5).forEach(subscription => {
            const urgency = subscription.remaining_days <= 3 ? 'text-danger' : 'text-warning';
            html += `
                <li>
                    <a class="dropdown-item py-2" href="/admin/user-subscriptions">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle ${urgency} me-2"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold" style="font-size: 0.85rem;">${subscription.user_name}</div>
                                <div class="text-muted" style="font-size: 0.75rem;">
                                    ${subscription.plan_name} expires in ${subscription.remaining_days} days
                                </div>
                            </div>
                        </div>
                    </a>
                </li>
            `;
        });
        
        if (expiringSubscriptions.length > 5) {
            html += `
                <li>
                    <a class="dropdown-item text-center text-primary" href="/admin/user-subscriptions">
                        <i class="fas fa-ellipsis-h me-2"></i>View ${expiringSubscriptions.length - 5} more
                    </a>
                </li>
            `;
        }
        
        notificationsList.innerHTML = html;
    }
}

function markAllAsRead() {
    const notificationCount = document.getElementById('notificationCount');
    const notificationsList = document.getElementById('notificationsList');
    
    notificationCount.textContent = '0';
    notificationCount.style.display = 'none';
    notificationsList.innerHTML = `
        <li class="dropdown-item text-muted text-center py-3">
            <i class="fas fa-inbox me-2"></i>No new notifications
        </li>
    `;
}

// Quick action functions
function openCreateUserModal() {
    if (typeof createUserModal !== 'undefined') {
        // If on users page, open the modal
        $('#createUserModal').modal('show');
    } else {
        // Otherwise, redirect to users page
        window.location.href = '/admin/users';
    }
}

function openCreatePlanModal() {
    if (typeof createPlanModal !== 'undefined') {
        $('#createPlanModal').modal('show');
    } else {
        window.location.href = '/admin/subscription-plans';
    }
}

function openCreateSubscriptionModal() {
    if (typeof createSubscriptionModal !== 'undefined') {
        $('#createSubscriptionModal').modal('show');
    } else {
        window.location.href = '/admin/user-subscriptions';
    }
}

// Responsive header adjustments
window.addEventListener('resize', function() {
    const header = document.querySelector('.admin-header');
    if (window.innerWidth <= 767) {
        header.style.left = '0';
    } else {
        header.style.left = '250px';
    }
});

// Initial responsive check
if (window.innerWidth <= 767) {
    document.querySelector('.admin-header').style.left = '0';
}
</script>

<style>
/* Header responsive styles */
@media (max-width: 767px) {
    .admin-header {
        left: 0 !important;
        height: 60px !important;
    }

    .content-area {
        padding-top: 37px !important;
    }
}

/* Notification badge animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.badge {
    animation: pulse 2s infinite;
}

/* Dropdown improvements */
.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}
</style>
