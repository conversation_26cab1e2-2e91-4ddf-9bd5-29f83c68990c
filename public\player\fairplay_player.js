/**
 * Simple Video Player for CEF
 * Simplified version for CEF compatibility
 */


console.log("🎬 Simple Player.js loaded for CEF");

// Load player fixes for hosting compatibility
if (typeof jwplayer !== 'undefined') {
    console.log("🔧 Applying hosting fixes...");

    // Apply chunk loading fix
    const originalJWSetup = jwplayer.prototype.setup;
    jwplayer.prototype.setup = function(config) {
        console.log("🔧 Enhanced setup with hosting fixes");

        // Add stability settings
        const enhancedConfig = {
            ...config,
            preload: 'metadata',
            controls: true,
            displaytitle: false,
            displaydescription: false,
            // Disable problematic features on shared hosting
            sharing: false,
            related: false,
            advertising: false
        };

        try {
            return originalJWSetup.call(this, enhancedConfig);
        } catch (error) {
            console.error("🔧 Setup error, trying fallback:", error);

            // Minimal fallback config
            const fallbackConfig = {
                file: config.file || (config.playlist && config.playlist[0] && config.playlist[0].file),
                width: config.width || '100%',
                height: config.height || '100%',
                autostart: false,
                controls: true
            };

            return originalJWSetup.call(this, fallbackConfig);
        }
    };
}

!function(){ // Start main IIFE scope

    // --- Original Settings Object (defaults used as Chrome Storage is unavailable) ---
    let s={
        autoLowLatency:!0,
        audioLang:"ar", // <--- تم التعديل هنا
        compressorEnabled:!1,
        customWV:!1,
        customWV_data:"",
        customCK:!1,
        customCK_data:{},
        extra:{clearkeys:!1}
    };
    let r=""; // Global variable for the stream URL, potentially used by helper functions
    window.urlCk=!1; // Global variable for ClearKey URL, used by getLicense and potentially l_async fallback

    // --- Original Helper Functions (o, n, c, i) ---
    // These are synchronous parsing functions and should be fine.
    function o(e){e=e.replaceAll("-","+").replaceAll("_","/");let t=atob(e),a=(t=t.split(""),"");return t.forEach(e=>{a+=e.codePointAt(0).toString(16).padStart(2,"0")}),a}
    function n(e){if(!e || typeof e !== 'string') return false; if(/[ {"']*([0-9a-f]{32})[ "']*:[ "']*([0-9a-f]{32})[ }"']*/i.test(e)){var t={};for(const a of e.matchAll(/[ {"']*([0-9a-f]{32})[ "']*:[ "']*([0-9a-f]{32})[ }"']*/gi))t[a[1]]=a[2];return t}return false}
    function c(e){if(!e || typeof e !== 'string') return false; let a={};try{a=JSON.parse(e)}catch(e){return false}if(Array.isArray(a && a.keys)){let t={};a.keys.forEach(e=>{try{t[o(e.kid)]=o(e.k)}catch(parseErr){console.error("Error parsing base64 in key object:", e, parseErr)}});return t}return false}
    function i(e){ // Processes key data string (JSON or key:kid pairs) into DRM config
        var t,a=n(e); // Try parsing as key:kid pairs first
        if(!a){ a=c(e); } // Try parsing as JSON if key:kid failed
        if(a && Object.keys(a).length > 0){ // If parsing succeeded and we have keys
             t=Object.entries(a)[0]; // Get the first key entry
             // Store the full clearKeys map for Shaka configuration later
             // Store the full clearKeys map for Shaka configuration later
             s.extra.clearkeys={drm:{clearKeys:a}};
             console.log("Parsed ClearKeys for Shaka:", JSON.stringify(a));
             // Return true indicating success, keys are stored in s.extra.clearkeys
             return true;
        }
        console.warn("Failed to parse key data string using n() or c().");
        s.extra.clearkeys = false; // Ensure old keys are cleared if parsing fails
        return false; // Indicate failure
    }
    // --- End Original Helper Functions ---

     // --- NEW Async function to fetch key data from URL ---
     function fetchKeyDataAsync(url) {
        console.log("Attempting to fetch key data asynchronously from:", url);
        return new Promise((resolve) => { // Resolve with false on failure, no reject needed here
            var t = new XMLHttpRequest();
            t.open("GET", url, true); // ASYNCHRONOUS request
            t.timeout = 15000; // Set timeout (15 seconds)

            t.onload = function () {
                if (t.status >= 200 && t.status < 300) {
                    // Basic check if response might be valid keys before resolving
                    if (n(t.responseText) !== false || c(t.responseText) !== false) {
                        console.log("Successfully fetched key data from URL.");
                        resolve(t.responseText); // Resolve with the fetched text
                    } else {
                         console.warn("Fetched key data from URL, but failed basic parsing check (n/c). Content:", t.responseText.substring(0, 100));
                         resolve(false); // Resolve with false if parsing check fails
                    }
                } else {
                    console.warn(`Key data request failed with status: ${t.status} for URL: ${url}`);
                    resolve(false); // Resolve with false on non-2xx status
                }
            };
            t.onerror = function () {
                console.error(`Network error during key data request for URL: ${url}`);
                resolve(false); // Resolve with false on network error
            };
             t.ontimeout = function () {
                 console.error(`Key data request timed out for URL: ${url}`);
                 resolve(false); // Resolve with false on timeout
             };

            try {
                 t.send();
            } catch (sendError) {
                 console.error(`Error sending key data request for URL: ${url}`, sendError);
                 resolve(false);
            }
        });
    }

    // --- REVISED l function (now l_async) ---
    // Tries to process keyData: checks if it's a URL to fetch async, otherwise processes directly.
    // Returns true if keys were processed successfully (and stored in s.extra.clearkeys), false otherwise.
    async function l_async(keyDataInput, streamUrl) {
        let keyDataString;
        const trimmedKeyData = typeof keyDataInput === 'string' ? keyDataInput.trim() : '';

        if (!trimmedKeyData) {
            console.log("No key data provided.");
            s.extra.clearkeys = false; // Clear any old keys
            return false;
        }

        // Check if the input is likely a URL
        if (/^https?:\/\//.test(trimmedKeyData)) {
            const keyUrl = trimmedKeyData;
            const fetchedData = await fetchKeyDataAsync(keyUrl); // Await the async fetch

            if (fetchedData === false) {
                 console.warn("Failed to fetch or validate key data from URL:", keyUrl);
                 // Original fallback logic for HLS when sync XHR failed: set urlCk
                 // Mimic this if async fetch fails for an HLS stream
                 if (/m3u8/i.test(streamUrl)) {
                     console.log("Setting window.urlCk for HLS fallback:", keyUrl);
                     window.urlCk = keyUrl; // Used by getLicense
                     // Return placeholder? Original code returned {clearkey:{keyId:"",key:""}} here.
                     // Let's return false for consistency, player might handle HLS key URI directly? Needs testing.
                     s.extra.clearkeys = false;
                     return false; // Let's indicate failure consistently
                 }
                 s.extra.clearkeys = false;
                 return false; // Indicate failure for non-HLS or if HLS fallback doesn't apply
            }
            keyDataString = fetchedData; // Use the fetched data string
        } else {
            // If not a URL, assume it's the key data itself (JSON or key:kid string)
            keyDataString = trimmedKeyData;
        }

        // Process the resulting key data string (either direct input or fetched) using i()
        return i(keyDataString);
    }
    // --- End REVISED l function ---

    // --- Global Debug Function ---
    // Send a debug line to laravel.log
    function debugLogToServer(type, payloadObj) {
        try {
            const payload = typeof payloadObj === 'string' ? payloadObj : JSON.stringify(payloadObj || {});
            const url = `/player/debug-log?type=${encodeURIComponent(type)}&payload=${encodeURIComponent(payload)}`;
            fetch(url, { method: 'GET', credentials: 'omit' }).catch(() => {});
        } catch (_) {}
    }

    // --- Original getLicense function (for URL-based keys, potentially used by HLS fallback) ---
    // This remains synchronous as it might be called directly by the player in some DRM flows.
    window.getLicense=e=>{
         if (!window.urlCk) {
             console.error("getLicense called but window.urlCk is not set.");
             return false;
         }
         console.log("getLicense called for URL:", window.urlCk);
         var t=new XMLHttpRequest;
         t.open("POST",window.urlCk,!1); // Synchronous! Keep as is for potential player integrations.
         try{
             t.send(e);
             console.log("getLicense Sync XHR status:", t.status);
         } catch(err){
             console.error("Error sending sync getLicense request:", err);
             return!1;
         }
         return 200===t.status&&t.responseText
    };

    // --- Event Listeners (hlsdataReady, shakadataReady) ---
    // These should remain largely the same, configuring the player instance *after* it's created.
    document.addEventListener("hlsdataReady",()=>{
        console.log("hlsdataReady event triggered.");
        // Configure HLS.js instance if needed (e.g., widevine)
        // Might need adjustments if widevine license URL comes from async fetch
    });

    document.addEventListener("shakadataReady",()=>{
        console.log("shakadataReady event triggered.");
        // Check if Shaka is available
        if (typeof shaka === 'undefined') {
            console.warn("shakadataReady: Shaka Player library not loaded.");
            return;
        }
        
        // Initialize window.shakadata if needed
        if (typeof window.shakadata === 'undefined') {
            console.log("shakadataReady: Initializing window.shakadata");
            window.shakadata = { shakaPlayer: null };
        }
        
        // Create Shaka Player instance if not exists
        if (!window.shakadata.shakaPlayer) {
            console.log("shakadataReady: Creating new Shaka Player instance");
            try {
                const videoElement = document.querySelector('#player video');
                if (videoElement) {
                    window.shakadata.shakaPlayer = new shaka.Player(videoElement);
                    console.log("shakadataReady: Shaka Player instance created");
                } else {
                    console.warn("shakadataReady: No video element found yet, will retry later");
                    return;
                }
            } catch (error) {
                console.error("shakadataReady: Error creating Shaka Player instance:", error);
                return;
            }
        }
        let shakaPlayerInstance = window.shakadata.shakaPlayer;
        console.log("Shaka Player Version:", shaka.Player.version);

        // Basic configuration (already applied before?)
        try {
            // Re-apply basic config in case instance was recreated
            shakaPlayerInstance.configure({
                preferredAudioLanguage:s.audioLang,
                preferredTextLanguage:s.audioLang,
                streaming:{autoLowLatencyMode:s.autoLowLatency}
            });
             console.log("shakadataReady: Applied basic config.");
        } catch(e) {
             console.error("shakadataReady: Error applying basic config to Shaka:", e);
        }

        // Apply ClearKey config if available from the last call to l_async() -> i()
        // This relies on s.extra.clearkeys being set *before* player.setup() is called.
        if(s.extra.clearkeys && s.extra.clearkeys.drm && s.extra.clearkeys.drm.clearKeys){
            console.log("shakadataReady: Applying ClearKeys:", JSON.stringify(s.extra.clearkeys.drm.clearKeys));
            try {
                // Clean the clearKeys object to remove any invalid keys
                const cleanKeys = {};
                Object.entries(s.extra.clearkeys.drm.clearKeys).forEach(([kid, key]) => {
                    // Only add valid KID:Key pairs (skip null, keyId, etc.)
                    if (kid && key && kid !== 'keyId' && kid !== 'null' && typeof key === 'string') {
                        cleanKeys[kid] = key;
                    }
                });
                
                console.log("shakadataReady: Original ClearKeys:", JSON.stringify(s.extra.clearkeys.drm.clearKeys));
                console.log("shakadataReady: Cleaned ClearKeys:", JSON.stringify(cleanKeys));
                
                shakaPlayerInstance.configure({ drm: { clearKeys: cleanKeys } });
                console.log("shakadataReady: Shaka ClearKeys config applied successfully.");
            } catch (configError) {
                console.error("shakadataReady: Error applying ClearKeys config to Shaka:", configError);
            }
        }
        
        // Apply FairPlay DRM configuration for HLS streams
        if (s.extra.fairplayCertUrl && s.extra.fairplayLicenseUrl) {
            console.log("🍎 shakadataReady: Configuring Shaka Player for FairPlay DRM...");
            console.log("🍎 Certificate URL:", s.extra.fairplayCertUrl);
            console.log("🍎 License URL:", s.extra.fairplayLicenseUrl);
            
            // Log to server
            if (typeof debugLogToServer === 'function') {
                debugLogToServer('fairplay_config_start', {
                    certUrl: s.extra.fairplayCertUrl,
                    licenseUrl: s.extra.fairplayLicenseUrl
                });
            }
            
            // Fetch FairPlay certificate
            fetch(s.extra.fairplayCertUrl)
                .then(response => response.arrayBuffer())
                .then(certificate => {
                    console.log("🔐 Certificate fetched, bytes:", certificate.byteLength);
                    
                    // Log to server
                    if (typeof debugLogToServer === 'function') {
                        debugLogToServer('certificate_fetched', { bytes: certificate.byteLength });
                    }
                    
                    // Configure Shaka Player for FairPlay (try both approaches)
                    let drmConfig;
                    
                    try {
                        // Check if we're on Apple platform (simplified check)
                        const isApple = /iPhone|iPad|iPod|Mac/.test(navigator.userAgent);
                        const hasWebKitMediaKeys = typeof window.WebKitMediaKeys !== 'undefined';
                        
                        console.log("🍎 Platform check - isApple:", isApple, "hasWebKitMediaKeys:", hasWebKitMediaKeys);
                        
                        if (isApple && hasWebKitMediaKeys) {
                            console.log("🍎 Apple platform detected, configuring FairPlay DRM...");
                            
                            // Method 1: Correct Shaka FairPlay config format
                            drmConfig = {
                                servers: {
                                    'com.apple.streamingkeydelivery': s.extra.fairplayLicenseUrl
                                },
                                advanced: {
                                    'com.apple.streamingkeydelivery': {
                                        serverCertificate: new Uint8Array(certificate)
                                    }
                                }
                            };
                            
                            console.log("🍎 Using simplified Shaka config - let JW Player handle FairPlay natively");
                        } else {
                            console.warn("🍎 FairPlay not supported on this platform, falling back to JW Player native FairPlay");
                            console.log("🍎 Platform details - userAgent:", navigator.userAgent);
                            
                            // Log to server
                            if (typeof debugLogToServer === 'function') {
                                debugLogToServer('fairplay_platform_unsupported', { 
                                    userAgent: navigator.userAgent,
                                    isApple: isApple,
                                    hasWebKitMediaKeys: hasWebKitMediaKeys
                                });
                            }
                            
                            // Let JW Player handle FairPlay natively
                            return;
                        }
                    } catch (platformError) {
                        console.warn("🍎 Error checking platform support, using fallback config:", platformError.message);
                        
                        // Fallback: simpler config (using correct keyformat)
                        drmConfig = {
                            servers: {
                                'com.apple.streamingkeydelivery': s.extra.fairplayLicenseUrl
                            }
                        };
                        
                        // Log to server
                        if (typeof debugLogToServer === 'function') {
                            debugLogToServer('fairplay_platform_error', { 
                                error: platformError.message,
                                fallback: 'simple_config'
                            });
                        }
                    }
                    
                    // Add network request filter for license requests
                    shakaPlayerInstance.getNetworkingEngine().registerRequestFilter((type, request) => {
                        if (type === shaka.net.NetworkingEngine.RequestType.LICENSE) {
                            console.log("🌐 FairPlay license request intercepted");
                            console.log("🌐 Request type:", type);
                            console.log("🌐 Request URIs:", request.uris);
                            
                            // Log to server
                            if (typeof debugLogToServer === 'function') {
                                debugLogToServer('license_request_intercepted', {
                                    originalUris: request.uris,
                                    licenseUrl: s.extra.fairplayLicenseUrl
                                });
                            }
                            
                            // Set proper headers for FairPlay license request
                            request.headers['Content-Type'] = 'application/octet-stream';
                            
                            // Add UserToken if available
                            try {
                                const licenseUrl = new URL(s.extra.fairplayLicenseUrl);
                                const userToken = licenseUrl.searchParams.get('UserToken');
                                if (userToken) {
                                    request.headers['X-VUDRM-TOKEN'] = userToken;
                                    console.log("🪪 Added X-VUDRM-TOKEN to license request");
                                }
                            } catch (e) {
                                console.warn("Failed to extract UserToken from license URL:", e.message);
                            }
                            
                            // Use the configured license URL
                            request.uris = [s.extra.fairplayLicenseUrl];
                            console.log("🌐 Updated request URIs to:", request.uris);
                        }
                    });
                    
                    // Apply the DRM configuration
                    try {
                        shakaPlayerInstance.configure({ drm: drmConfig });
                        console.log("✅ FairPlay DRM configured successfully");
                        
                        // Log to server
                        if (typeof debugLogToServer === 'function') {
                            debugLogToServer('fairplay_drm_configured', { 
                                success: true,
                                config: JSON.stringify(drmConfig, null, 2)
                            });
                        }
                    } catch (configError) {
                        console.error("❌ Failed to configure Shaka DRM:", configError.message);
                        console.warn("🍎 Falling back to JW Player native FairPlay handling");
                        
                        // Log to server
                        if (typeof debugLogToServer === 'function') {
                            debugLogToServer('shaka_drm_config_error', { 
                                error: configError.message,
                                fallback: 'jw_native_fairplay'
                            });
                        }
                        
                        // Let JW Player handle FairPlay natively
                        return;
                    }
                    
                })
                .catch(error => {
                    console.error("❌ Failed to fetch FairPlay certificate:", error);
                    
                    // Log to server
                    if (typeof debugLogToServer === 'function') {
                        debugLogToServer('fairplay_cert_error', { error: error.message });
                    }
                });
        } else {
             console.log("shakadataReady: No FairPlay or ClearKeys config found in s.extra to apply.");
             console.log("🍎 Relying on JW Player native FairPlay handling for HLS streams");
             
             // Log to server
             if (typeof debugLogToServer === 'function') {
                 debugLogToServer('fairplay_fallback_jw', { 
                     reason: 'no_shaka_config',
                     jwDrmConfig: 'native_fairplay'
                 });
             }
        }
    });
    // --- End Original Event Listeners ---


    // --- Original Compressor Functions (d, m) ---
    // Kept as is, assuming they work independently.
    function d(e){var videoEl=document.querySelector("video");if(!videoEl) return; if(!0===e){let audioCtx,sourceNode,compressorNode,gainNode;gainNode=videoEl._extension_source&&videoEl._extension_compressor&&videoEl._extension_gain?(audioCtx=videoEl._extension_source.context,sourceNode=videoEl._extension_source,compressorNode=videoEl._extension_compressor,videoEl._extension_gain):(audioCtx=new (window.AudioContext || window.webkitAudioContext)(),sourceNode=audioCtx.createMediaElementSource(videoEl),compressorNode=audioCtx.createDynamicsCompressor(),new window.GainNode(audioCtx,{gain:2}));compressorNode.threshold.value=-50,compressorNode.knee.value=40,compressorNode.ratio.value=12,compressorNode.attack.value=0,compressorNode.release.value=.25,sourceNode.connect(compressorNode),compressorNode.connect(gainNode),gainNode.connect(audioCtx.destination),videoEl._extension_source=sourceNode,videoEl._extension_compressor=compressorNode,videoEl._extension_gain=gainNode}else videoEl._extension_source&&(videoEl._extension_compressor.disconnect(),videoEl._extension_gain.disconnect(),(e=videoEl._extension_source).disconnect(),e.connect(e.context.destination))}
    function m(e){var t=document.createElement("template"),a=(t.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon btn-compressor-off" viewBox="0 0 640 512"><path d="M352 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V480c0 17.7 14.3 32 32 32s32-14.3 32-32V32zM544 96c0-17.7-14.3-32-32-32s-32 14.3-32 32V416c0 17.7 14.3 32 32 32s32-14.3 32-32V96zM256 128c0-17.7-14.3-32-32-32s-32 14.3-32 32V384c0 17.7 14.3 32 32 32s32-14.3 32-32V128zm192 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V352c0 17.7 14.3 32 32 32s32-14.3 32-32V160zM160 224c0-17.7-14.3-32-32-32s-32 14.3-32 32v64c0 17.7 14.3 32 32 32s32-14.3 32-32V224zM0 256a32 32 0 1 0 64 0A32 32 0 1 0 0 256zm576 0a32 32 0 1 0 64 0 32 32 0 1 0 -64 0z"/></svg>',document.createElement("template")),ev=(a.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon btn-compressor-on" viewBox="0 0 640 512"><path d="M320 0c12 0 22.1 8.8 23.8 20.7l42 304.4L424.3 84.2c1.9-11.7 12-20.3 23.9-20.2s21.9 8.9 23.6 20.6l28.2 197.3 20.5-102.6c2.2-10.8 11.3-18.7 22.3-19.3s20.9 6.4 24.2 16.9L593.7 264H616c13.3 0 24 10.7 24 24s-10.7 24-24 24H576c-10.5 0-19.8-6.9-22.9-16.9l-4.1-13.4-29.4 147c-2.3 11.5-12.5 19.6-24.2 19.3s-21.4-9-23.1-20.6L446.7 248.3l-39 243.5c-1.9 11.7-12.1 20.3-24 20.2s-21.9-8.9-23.5-20.7L320 199.6 279.8 491.3c-1.6 11.8-11.6 20.6-23.5 20.7s-22.1-8.5-24-20.2l-39-243.5L167.8 427.4c-1.7 11.6-11.4 20.3-23.1 20.6s-21.9-7.8-24.2-19.3l-29.4-147-4.1 13.4C83.8 305.1 74.5 312 64 312H24c-13.3 0-24-10.7-24-24s10.7-24 24-24H46.3l26.8-87.1c3.2-10.5 13.2-17.5 24.2-16.9s20.2 8.5 22.3 19.3l20.5 102.6L168.2 84.6c1.7-11.7 11.7-20.5 23.6-20.6s22 8.5 23.9 20.2l38.5 240.9 42-304.4C297.9 8.8 308 0 320 0z"/></svg>',e.currentTarget);
        if(ev.querySelector("svg").classList.contains("btn-compressor-off")){
            ev.querySelector("svg").replaceWith(a.content.firstChild);
            ev.querySelector(".jw-text").innerText="Compressor Off"; // Static text
            d(!0);
        } else {
            ev.querySelector("svg").replaceWith(t.content.firstChild);
            ev.querySelector(".jw-text").innerText="Compressor On"; // Static text
            d(!1);
        }
    }
    // --- End Original Compressor Functions ---

    // --- REVISED Initialization Logic ---
    // Now an ASYNC function because it calls l_async
    async function initializePlayer(streamUrl, keyData, streamType = 'dash', licenseUrl = '', title = '') {
        console.log("Async initializePlayer called with stream type:", streamType);

        // Store license URL for later use
        if (licenseUrl) {
            s.extra.licenseUrl = licenseUrl;
            console.log("License URL stored:", licenseUrl);
        }

        // --- 1. Process Key Data Asynchronously (only for DASH) ---
        s.extra.clearkeys = false; // Reset clear keys state
        let keysProcessedSuccessfully = false; // Flag to track if keys were parsed
        
        if (streamType === 'dash' && keyData) {
            console.log("Processing DASH key data...");
            try {
                 keysProcessedSuccessfully = await l_async(keyData, streamUrl); // Await the async key processing
                 if (keysProcessedSuccessfully) {
                      console.log("l_async indicates keys were processed and stored in s.extra.clearkeys.");
                 } else {
                      console.warn("l_async indicates key processing failed or no keys found.");
                      // s.extra.clearkeys should be false from l_async/i failure path
                 }
            } catch (error) {
                 console.error("Error during async key processing (l_async):", error);
                 s.extra.clearkeys = false; // Ensure reset on error
                 keysProcessedSuccessfully = false;
                 // Optionally alert the user or display an error message here
                 // alert("خطأ في معالجة مفتاح التشفير.");
            }
        } else if (streamType === 'hls') {
             console.log("HLS stream detected - no external key processing needed.");
             s.extra.clearkeys = false; // Ensure reset
        } else {
             console.log("No key data provided to initializePlayer.");
             s.extra.clearkeys = false; // Ensure reset
        }

        // --- 2. Prepare Player Configuration ---
        console.log("🎬 Setting up player with title:", title);
        console.log("🎬 Title fallback will be:", title || "Shahid Content");

        // Force proxy for HLS manifest to avoid direct CDN calls
        let originalRemoteHlsUrl = null;
        if (streamType === 'hls' && streamUrl && !streamUrl.includes('/video-proxy/')) {
            originalRemoteHlsUrl = streamUrl;
            streamUrl = window.location.origin + '/video-proxy/manifest?url=' + encodeURIComponent(streamUrl);
            console.log('Using proxied HLS URL:', streamUrl);
        }

        // Default FairPlay test configuration (matching actual SKD from manifest)
        const DEFAULT_FAIRPLAY_TEST = {
            certificateUrl: 'https://shahid.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
            licenseUrl: 'https://shahid.la.drm.cloud/acquire-license/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4&KID=827802f4-e896-4769-ba77-f1e656ed1bd4&IV=1f3a1d7ce87a4cd3846038fcf4d05f82',
            kid: '827802f4-e896-4769-ba77-f1e656ed1bd4',
            iv: '1f3a1d7ce87a4cd3846038fcf4d05f82',
            hls: 'https://mbcvod-enc.edgenextcdn.net/out/v1/b9ab8e5fc3d44bb2a28ab3c9cb8b2ae7/de5f5ce1bd4d43eb9896d42d0de1dab6/75166141e593471998b1061968ca6824/index.m3u8?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264'
        };

        // debugLogToServer is now available globally

        // Helper: extract skd:// from master or first variant playlist via proxy
        async function extractSkdFromHls(hlsUrl) {
            try {
                console.log('🔍 Extracting SKD from HLS URL:', hlsUrl);
                
                // Try original URL first (non-proxied)
                try {
                    const directResp = await fetch(hlsUrl, { 
                        credentials: 'omit',
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15',
                            'Referer': 'https://shahid.mbc.net/',
                            'Accept': '*/*'
                        }
                    });
                    const directTxt = await directResp.text();
                    console.log('🔍 Direct manifest response (first 500 chars):', directTxt.substring(0, 500));
                    
                    let skd = (directTxt.match(/#EXT-X-KEY:[^\n]*URI=\"(skd:[^\"]+)\"/i) || [])[1];
                    if (skd) {
                        console.log('🔓 Found SKD in master manifest:', skd);
                        debugLogToServer('skd_master_direct', { skd, source: 'direct_master' });
                        return skd;
                    }

                    // try first variant from direct
                    const firstVariant = (directTxt.match(/^(?!#).*\.m3u8[^\r\n]*/m) || [])[0];
                    if (firstVariant) {
                        const absolute = resolveAgainst(firstVariant.trim(), hlsUrl);
                        console.log('🔍 Checking first variant:', absolute);
                        
                        const varResp = await fetch(absolute, { 
                            credentials: 'omit',
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15',
                                'Referer': 'https://shahid.mbc.net/',
                                'Accept': '*/*'
                            }
                        });
                        const varTxt = await varResp.text();
                        console.log('🔍 Variant manifest response (first 500 chars):', varTxt.substring(0, 500));
                        
                        skd = (varTxt.match(/#EXT-X-KEY:[^\n]*URI=\"(skd:[^\"]+)\"/i) || [])[1];
                        if (skd) {
                            console.log('🔓 Found SKD in variant manifest:', skd);
                            debugLogToServer('skd_variant_direct', { skd, variant: absolute });
                            return skd;
                        }
                    }
                } catch (directError) {
                    console.warn('🔍 Direct fetch failed, trying proxy:', directError.message);
                }
                
                // Fallback to proxy if direct fails
                const proxied = window.location.origin + '/video-proxy/manifest?url=' + encodeURIComponent(hlsUrl);
                console.log('🔍 Trying proxy URL:', proxied);
                
                const masterResp = await fetch(proxied, { credentials: 'omit' });
                const masterTxt = await masterResp.text();
                console.log('🔍 Proxied manifest response (first 500 chars):', masterTxt.substring(0, 500));
                
                let skd = (masterTxt.match(/#EXT-X-KEY:[^\n]*URI=\"(skd:[^\"]+)\"/i) || [])[1];
                if (skd) {
                    console.log('🔓 Found SKD in proxied master:', skd);
                    debugLogToServer('skd_master_proxy', { skd });
                    return skd;
                }

                // try first variant from proxy
                const firstVariant = (masterTxt.match(/^(?!#).*\.m3u8[^\r\n]*/m) || [])[0];
                if (firstVariant) {
                    const absolute = resolveAgainst(firstVariant.trim(), hlsUrl);
                    const varProxied = window.location.origin + '/video-proxy/manifest?url=' + encodeURIComponent(absolute);
                    console.log('🔍 Checking proxied variant:', varProxied);
                    
                    const varResp = await fetch(varProxied, { credentials: 'omit' });
                    const varTxt = await varResp.text();
                    console.log('🔍 Proxied variant response (first 500 chars):', varTxt.substring(0, 500));
                    
                    skd = (varTxt.match(/#EXT-X-KEY:[^\n]*URI=\"(skd:[^\"]+)\"/i) || [])[1];
                    if (skd) {
                        console.log('🔓 Found SKD in proxied variant:', skd);
                        debugLogToServer('skd_variant_proxy', { skd, variant: absolute });
                        return skd;
                    }
                }
            } catch (e) {
                debugLogToServer('skd_error', { message: e.message });
                console.warn('SKD probe failed:', e.message);
            }
            
            console.warn('🔍 No SKD found in any manifest');
            debugLogToServer('skd_not_found', { hlsUrl });
            return null;
        }

        // Log KID/UserToken if present in license URL
        try {
            if (licenseUrl) {
                const u = new URL(licenseUrl);
                const kid = u.searchParams.get('KID');
                const token = u.searchParams.get('UserToken') || u.searchParams.get('token');
                if (kid) console.log('🔑 Extracted KID from licenseUrl:', kid);
                if (token) console.log('🪪 Extracted Token (UserToken/X-VUDRM-TOKEN):', token);
                debugLogToServer('license_params', { licenseUrl, kid, token });
            }
        } catch(e) {}

        // Helper: resolve relative URLs against a base URL
        const resolveAgainst = (url, base) => {
            try {
                if (/^https?:\/\//i.test(url)) return url;
                return new URL(url, base).href;
            } catch (_) { return url; }
        };

        let lastManifestBase = originalRemoteHlsUrl || '';

        let playerConfig = {
            playlist: [{
                sources: [{ file: streamUrl || DEFAULT_FAIRPLAY_TEST.hls, default: true }],
                // ✅ Add title and description to playlist
                title: title || "Shahid Content",
                description: "Streaming on Shahid Player"
            }],
            width: "100%",
            height: "100%",
            stretching: "uniform",
            autostart: true,
            preload: "auto",
            liveSyncDuration: 10,  // Increased for stability
            liveTimeout: 30,       // Added timeout
            playbackRateControls: true,
            defaultTrack: 1,
            abouttext: "Stream Player",
            // ❌ Disable title display
            displaytitle: false,
            displaydescription: false,
            localization: {
                'liveBroadcast': 'Live broadcast',
                'related': 'Related'
            },
        };

        // Configure stream type and providers based on streamType parameter
        if (streamType === 'hls') {
            playerConfig.playlist[0].sources[0].type = 'hls';
            
            // Use HTML5 provider for HLS streams (better FairPlay support)
            playerConfig.providers = [
                { name: 'html5' }
            ];
            console.log("HLS: Using HTML5 provider for better FairPlay support");
            
            // Add CORS and proxy support for HLS streams
            playerConfig.playlist[0].sources[0].preload = "metadata";
            playerConfig.playlist[0].sources[0].withCredentials = false;  // Disable for proxy
            
            // Add HLS configuration for proxy and CORS support
            playerConfig.hlsConfig = {
                withCredentials: false,  // Important for proxy URLs
                enableWorker: false,     // Disable worker to avoid CSP issues
                backBufferLength: 30,    // Reduced for live streams
                maxBufferLength: 30,     // Reduced for live streams
                maxMaxBufferLength: 60,  // Reduced for live streams
                liveSyncDuration: 3,     // Faster sync for live
                liveMaxLatencyDuration: 8, // Reduced latency
                manifestLoadingTimeOut: 20000, // Reduced timeout
                manifestLoadingMaxRetry: 3,    // Fewer retries for faster recovery
                levelLoadingTimeOut: 15000,    // Reduced timeout
                fragLoadingTimeOut: 15000,     // Reduced timeout
                fragLoadingMaxRetry: 3,        // Fewer retries
                levelLoadingMaxRetry: 2,       // Fewer retries
                startLevel: -1,                // Auto quality selection
                capLevelToPlayerSize: true,    // Optimize for player size
                debug: false,
                // Intercept every HLS request and force it through our proxy
                xhr: {
                    withCredentials: false,
                    beforeRequest: function(options) {
                        try {
                            options.headers = options.headers || {};

                            // Track last manifest base to resolve relatives
                            const url = options.url;
                            const isManifest = /\.m3u8(\?|$)/i.test(url);
                            const isSegment = /(\.ts|\.m4s|\.mp4)(\?|$)/i.test(url) || /init\.mp4(\?|$)/i.test(url);

                            let absoluteUrl = url;
                            if (originalRemoteHlsUrl && !/^https?:\/\//i.test(url)) {
                                absoluteUrl = resolveAgainst(url, lastManifestBase || originalRemoteHlsUrl);
                            }

                            // Update base if this is a manifest request
                            if (isManifest && /^https?:\/\//i.test(absoluteUrl)) {
                                lastManifestBase = absoluteUrl;
                            }

                            const alreadyProxied = absoluteUrl.includes('/video-proxy/');
                            if (!alreadyProxied && /^https?:\/\//i.test(absoluteUrl)) {
                                const origin = window.location.origin;
                                if (isManifest) {
                                    options.url = origin + '/video-proxy/segment/' + btoa(absoluteUrl);
                                } else if (isSegment) {
                                    options.url = origin + '/video-proxy/segment/' + btoa(absoluteUrl);
                                } else {
                                    // Fallback: send through manifest endpoint
                                    options.url = origin + '/video-proxy/manifest?url=' + encodeURIComponent(absoluteUrl);
                                }
                            }

                            // Add auth/geolocation headers if available
                            if (window.shahidToken || s.extra.token) {
                                const token = window.shahidToken || s.extra.token;
                                options.headers['token'] = token;
                            }
                            options.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
                            options.headers['Referer'] = 'https://shahid.mbc.net/';
                            options.headers['x-forwarded-for'] = '***********';
                            options.headers['cf-ipcountry'] = 'AE';
                            options.headers['accept-language'] = 'ar';
                            options.headers['language'] = 'ar';
                        } catch(e) { console.warn('hls.xhr.beforeRequest error:', e.message); }
                        return options;
                    }
                }
            };
            
            console.log("HLS configured for proxy with CORS support");
            
            // Add HLS configuration with token if available (for direct connection)
            if (window.shahidToken || s.extra.token) {
                const token = window.shahidToken || s.extra.token;
                console.log("Adding Shahid token to HLS configuration (direct connection)");
                
                // Keep headers if token is present (merged with proxying above)
            }
            
            console.log("Configured for HLS playback with authentication");
        } else {
            playerConfig.playlist[0].sources[0].type = 'dash';

            // Add CORS support for DASH streams
            playerConfig.playlist[0].sources[0].preload = "metadata";
            playerConfig.playlist[0].sources[0].withCredentials = false;

            // Add headers for DASH authentication
            playerConfig.playlist[0].sources[0].requestHeaders = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/dash+xml,video/mp4,application/mp4,*/*',
                'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
                'x-forwarded-for': '*************',
                'cf-ipcountry': 'SA',
                'language': 'ar',
                'Origin': 'https://shahid.mbc.net',
                'Referer': 'https://shahid.mbc.net/'
            };

            // Try HTML5 provider first for DASH content with DRM
            playerConfig.providers = [
                { name: 'html5' },
                { name: 'shaka' }
            ];
            console.log("Configured for DASH playback with CORS support");
        }

        // Configure DRM if keys are available (only for DASH)
        if (streamType === 'dash' && keysProcessedSuccessfully && s.extra.clearkeys && s.extra.clearkeys.drm && s.extra.clearkeys.drm.clearKeys) {
            // Clean the clearKeys for playlist configuration
            const cleanPlaylistKeys = {};
            Object.entries(s.extra.clearkeys.drm.clearKeys).forEach(([kid, key]) => {
                // Only add valid KID:Key pairs (skip null, keyId, etc.)
                if (kid && key && kid !== 'keyId' && kid !== 'null' && typeof key === 'string') {
                    cleanPlaylistKeys[kid] = key;
                }
            });
            
            console.log("Applying DRM configuration to DASH playlist:", JSON.stringify(cleanPlaylistKeys));
            
            // DRM config for both HTML5 and Shaka providers
            const drmConfig = {
                clearkey: cleanPlaylistKeys
            };

            // Add Widevine config if license URL is available
            if (s.extra.licenseUrl) {
                drmConfig.widevine = {
                    url: s.extra.licenseUrl
                };
                console.log("Added Widevine config with license URL:", s.extra.licenseUrl);
            }
            
            playerConfig.playlist[0].sources[0].drm = drmConfig;
        } else if (streamType === 'hls') {
            console.log("HLS stream - no external DRM configuration needed");
            // Inject FairPlay DRM fields directly into JW Player config with proper format
            try {
                // JW Player FairPlay config format
                playerConfig.playlist[0].sources[0].drm = {
                    fairplay: {
                        certificateUrl: s.extra.fairplayCertUrl,
                        processSpcUrl: s.extra.fairplayLicenseUrl, // JW Player uses processSpcUrl for license
                        licenseRequestHeaders: {
                            'Content-Type': 'application/octet-stream'
                        }
                    }
                };
                
                console.log('📦 Injected FairPlay DRM into JW config:', playerConfig.playlist[0].sources[0].drm);
                debugLogToServer('jw_drm_injected', playerConfig.playlist[0].sources[0].drm);
            } catch (e) { console.warn('Failed to inject JW DRM config:', e.message); }
        }

        // If FairPlay globals provided, store them for Shaka path
        if (typeof window !== 'undefined') {
            if (window.__FAIRPLAY_CERT_URL__) {
                s.extra.fairplayCertUrl = window.__FAIRPLAY_CERT_URL__;
                console.log('FairPlay certificate URL provided:', s.extra.fairplayCertUrl);
            }
            if (window.__FAIRPLAY_LICENSE_URL__) {
                s.extra.fairplayLicenseUrl = window.__FAIRPLAY_LICENSE_URL__;
                console.log('FairPlay license URL provided:', s.extra.fairplayLicenseUrl);
            }
        }

        // Fallback to default test values if not provided
        if (!s.extra.fairplayCertUrl) {
            s.extra.fairplayCertUrl = DEFAULT_FAIRPLAY_TEST.certificateUrl;
            console.log('🧪 Using default FairPlay certificate for testing:', s.extra.fairplayCertUrl);
            debugLogToServer('fairplay_cert_fallback', { certUrl: s.extra.fairplayCertUrl });
        }
        if (!s.extra.fairplayLicenseUrl) {
            s.extra.fairplayLicenseUrl = DEFAULT_FAIRPLAY_TEST.licenseUrl;
            console.log('🧪 Using default FairPlay license for testing:', s.extra.fairplayLicenseUrl);
            debugLogToServer('fairplay_license_fallback', { licenseUrl: s.extra.fairplayLicenseUrl });
        }
        if (!originalRemoteHlsUrl && !streamUrl) {
            originalRemoteHlsUrl = DEFAULT_FAIRPLAY_TEST.hls;
            console.log('🧪 Using default HLS for testing:', originalRemoteHlsUrl);
            debugLogToServer('fairplay_hls_fallback', { hls: originalRemoteHlsUrl });
        }

        // Probe and print SKD if HLS URL is available
        if (streamType === 'hls' && originalRemoteHlsUrl) {
            extractSkdFromHls(originalRemoteHlsUrl).then((skd) => {
                if (skd) {
                    console.log('🔓 Extracted FairPlay SKD URI:', skd);
                } else {
                    console.log('ℹ️ No SKD URI found in playlists.');
                }
            });
        }

        // Ensure JW config contains FairPlay DRM fields with values (after fallback resolution)
        try {
            if (playerConfig.playlist[0].sources[0].type === 'hls') {
                let kidFromUrl = null;
                try {
                    const u = new URL(s.extra.fairplayLicenseUrl);
                    kidFromUrl = u.searchParams.get('KID') || u.searchParams.get('kid');
                } catch (_) {}
                const ivValue = DEFAULT_FAIRPLAY_TEST.iv || null;
                playerConfig.playlist[0].sources[0].drm = {
                    fairplay: {
                        certificateUrl: s.extra.fairplayCertUrl,
                        processSpcUrl: s.extra.fairplayLicenseUrl, // Use processSpcUrl instead of licenseUrl
                        licenseRequestHeaders: {
                            'Content-Type': 'application/octet-stream',
                            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15'
                        }
                    }
                };
                debugLogToServer('jw_drm_final', playerConfig.playlist[0].sources[0].drm);
            }
        } catch (e) { console.warn('Failed to set final JW DRM config:', e.message); }

        // Load skin dynamically
        const playerDiv = document.getElementById('player');
        if (playerDiv && playerDiv.dataset.skinUrl) {
            playerConfig.skin = { name: "custom", url: playerDiv.dataset.skinUrl };
            if (!document.querySelector(`link[href="${playerDiv.dataset.skinUrl}"]`)) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = playerDiv.dataset.skinUrl;
                document.head.appendChild(link);
                console.log("Dynamically loaded skin CSS:", playerDiv.dataset.skinUrl);
                debugLogToServer('player_skin', { skinUrl: playerDiv.dataset.skinUrl });
            }
        } else {
             console.warn("Player skin URL not found in data-skin-url attribute.");
        }

         // --- 3. Pre-setup configuration for DRM providers ---
         if (playerConfig.playlist[0].sources[0].type === 'dash') {
             console.log("DASH content detected - ensuring Shaka Player is ready for DRM");
             
             // Ensure shaka player is loaded and available
             if (typeof shaka === 'undefined') {
                 console.error("Shaka Player library not loaded!");
                 throw new Error("Shaka Player library is required for DASH content but not loaded");
             }
             
             // Initialize shakadata if not exists
             if (typeof window.shakadata === 'undefined') {
                 window.shakadata = {
                     shakaPlayer: null
                 };
             }
             
             // Don't trigger shakadataReady here - we'll do it after JWPlayer is ready
             console.log("DASH detected - DRM will be applied after player setup");
         } else if (playerConfig.playlist[0].sources[0].type === 'hls') {
             console.log("HLS content detected - using JW Player native FairPlay (no Shaka)");
             
             // Check if we're on Windows/non-Apple platform
             const isWindows = /Windows/.test(navigator.userAgent);
             const isApple = /iPhone|iPad|iPod|Mac/.test(navigator.userAgent);
             
             if (isWindows || !isApple) {
                 console.warn("⚠️ FairPlay DRM not supported on this platform");
                 console.log("💡 Platform:", navigator.userAgent);
                 console.log("💡 Suggestion: Use Safari on macOS/iOS for FairPlay content");
                 
                 debugLogToServer('fairplay_platform_warning', {
                     platform: navigator.userAgent,
                     isWindows: isWindows,
                     isApple: isApple,
                     suggestion: 'Use Safari on macOS/iOS'
                 });
             }
         }


        console.log("Final JW Player Config before setup:", JSON.stringify(playerConfig, null, 2));

        // --- 4. Setup the Player Instance ---
        let playerInstance;
        try {
             playerInstance = jwplayer("player"); // Get div
             if (playerInstance && typeof playerInstance.remove === 'function' && playerInstance.getState() !== 'idle') {
                 console.log("Attempting to remove previous player instance.");
                 await playerInstance.remove(); // Use await if remove() is async (check docs)
                 console.log("Previous player instance removed.");
                 playerInstance = jwplayer("player"); // Re-acquire after removal
             }
        } catch (e) {
            console.error("Error getting or removing player instance:", e);
            // Handle error - maybe display message in player div
            const playerElement = document.getElementById('player');
            if (playerElement) {
                playerElement.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Error initializing player container.</div>`;
            }
            return; // Stop initialization
        }

        // Force set base path for JWPlayer files using multiple methods
        // Get current domain
        const currentDomain = window.location.protocol + '//' + window.location.host;
        const basePath = currentDomain + '/player/js/';

        try {
            if (typeof jwplayer !== 'undefined') {
                // Method 1: Set default base path
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.base = basePath;

                // Method 2: Override script loader base path
                if (jwplayer.utils && jwplayer.utils.loaderstatus) {
                    const originalGetScriptPath = jwplayer.utils.loaderstatus.getScriptPath;
                    jwplayer.utils.loaderstatus.getScriptPath = function(script) {
                        if (script === 'jwpsrv' || script === 'jwplayer.core.controls') {
                            return basePath + script + '.js';
                        }
                        return originalGetScriptPath ? originalGetScriptPath.call(this, script) : script;
                    };
                }

                // Method 3: Set in player config
                playerConfig.base = basePath;

                // Method 4: Force script paths
                if (jwplayer.utils && jwplayer.utils.getScriptPath) {
                    const originalGetPath = jwplayer.utils.getScriptPath;
                    jwplayer.utils.getScriptPath = function(script) {
                        if (script.includes('jwpsrv') || script.includes('jwplayer.core.controls')) {
                            return basePath + script.split('/').pop();
                        }
                        return originalGetPath ? originalGetPath.call(this, script) : script;
                    };
                }

                // Method 5: Override any repo settings
                if (jwplayer.utils && jwplayer.utils.repo) {
                    jwplayer.utils.repo = basePath;
                }
            }
            console.log("JWPlayer base path configured to:", basePath);
        } catch (pathError) {
            console.warn("Could not set JWPlayer base path:", pathError);
        }
        
        // Enhanced player configuration with responsive design
        const enhancedConfig = {
            ...playerConfig,
            controls: true,
            sharing: false,
            displaytitle: false,
            displaydescription: false,
            abouttext: "Shahid Player",
            aboutlink: "#",

            // Responsive skin configuration
            skin: {
                name: "netflix",
                url: "/player/tod_skin.css"
            },

            // Enhanced captions
            captions: {
                color: "#FFF",
                fontSize: 16,
                backgroundOpacity: 0,
                edgeStyle: "raised",
                fontFamily: "Arial, sans-serif"
            },

            // Mobile optimizations
            stretching: "uniform",
            aspectratio: "16:9",

            // Performance optimizations
            preload: "metadata",
            autostart: true,
            mute: false,

            // Quality settings
            qualityLabels: {
                "144": "144p",
                "240": "240p", 
                "360": "360p",
                "480": "480p",
                "720": "720p HD",
                "1080": "1080p Full HD"
            },

            // UI customizations
            logo: {
                hide: true
            },

            // Responsive behavior
            width: "100%",
            height: "100%"
        };

        console.log("🎨 Enhanced player config:", enhancedConfig);

        // Setup the player with enhanced configuration and hosting fixes
        try {
            // Add additional error handling for hosting issues
            const setupPromise = playerInstance.setup(enhancedConfig)
            .on("ready", function() {
                console.log("🎬 Player Ready Event Triggered!");

                // Get player container
                const playerContainer = playerInstance.getContainer();

                // Move the timeslider in-line with other controls (like the example)
                const buttonContainer = playerContainer.querySelector(".jw-button-container");
                const spacer = buttonContainer?.querySelector(".jw-spacer");
                const timeSlider = playerContainer.querySelector(".jw-slider-time");
                if (buttonContainer && spacer && timeSlider) {
                    buttonContainer.replaceChild(timeSlider, spacer);
                }

                // Fix live stream layout issues
                const fixLiveStreamLayout = () => {
                    const controlBar = playerContainer.querySelector(".jw-controlbar");
                    const timeSliderElement = playerContainer.querySelector(".jw-slider-time");

                    if (controlBar && timeSliderElement) {
                        // Force show time slider even in live streams
                        timeSliderElement.style.display = "flex";
                        timeSliderElement.style.visibility = "visible";
                        timeSliderElement.style.opacity = "1";
                        timeSliderElement.style.flex = "1";
                        timeSliderElement.style.minWidth = "0";
                        timeSliderElement.style.margin = "0 8px";

                        // Ensure control bar uses flexbox
                        controlBar.style.display = "flex";
                        controlBar.style.alignItems = "center";

                        console.log("✅ Live stream layout fixed");
                    }
                };

                // Apply fix immediately and on state changes
                fixLiveStreamLayout();

                // Monitor for live stream changes
                const observer = new MutationObserver(() => {
                    if (playerContainer.classList.contains('jw-flag-live')) {
                        fixLiveStreamLayout();
                    }
                });

                observer.observe(playerContainer, {
                    attributes: true,
                    attributeFilter: ['class']
                });

                // Add Forward 10 seconds functionality
                const rewindContainer = playerContainer.querySelector(".jw-display-icon-rewind");
                if (rewindContainer) {
                    const forwardContainer = rewindContainer.cloneNode(true);
                    const forwardDisplayButton = forwardContainer.querySelector(".jw-icon-rewind");
                    if (forwardDisplayButton) {
                        forwardDisplayButton.style.transform = "scaleX(-1)";
                        forwardDisplayButton.ariaLabel = "Forward 10 Seconds";

                        const nextContainer = playerContainer.querySelector(".jw-display-icon-next");
                        if (nextContainer && nextContainer.parentNode) {
                            nextContainer.parentNode.insertBefore(forwardContainer, nextContainer);
                            // Hide next button
                            nextContainer.style.display = "none";
                        }
                    }

                    // Add control bar forward button
                    if (buttonContainer) {
                        const rewindControlBarButton = buttonContainer.querySelector(".jw-icon-rewind");
                        if (rewindControlBarButton) {
                            const forwardControlBarButton = rewindControlBarButton.cloneNode(true);
                            forwardControlBarButton.style.transform = "scaleX(-1)";
                            forwardControlBarButton.ariaLabel = "Forward 10 Seconds";
                            rewindControlBarButton.parentNode.insertBefore(
                                forwardControlBarButton,
                                rewindControlBarButton.nextElementSibling
                            );

                            // Add onclick handlers
                            [forwardDisplayButton, forwardControlBarButton].forEach((button) => {
                                if (button) {
                                    button.onclick = () => {
                                        playerInstance.seek(playerInstance.getPosition() + 10);
                                    };
                                }
                            });
                        }
                    }
                }
            })
            .on("setupError",(error)=>{
                console.error("❌ Player Setup Error:", error);

                // Handle specific hosting-related errors
                if (error.code === 101102) {
                    console.log("🔄 Attempting recovery from chunk loading error...");

                    // Try simplified setup after delay
                    setTimeout(() => {
                        try {
                            const simpleConfig = {
                                file: enhancedConfig.file || enhancedConfig.playlist[0].file,
                                width: "100%",
                                height: "100%",
                                autostart: false,
                                controls: true
                            };

                            playerInstance.setup(simpleConfig);
                            console.log("🔄 Recovery setup attempted");
                        } catch (recoveryError) {
                            console.error("❌ Recovery setup failed:", recoveryError);
                        }
                    }, 2000);
                }
            })
            .on("error",(error)=>{
                console.error("❌ Player Error:", error);

                // Auto-retry for network errors
                if (error.code === 100001) {
                    console.log("🔄 Network error detected, retrying in 3 seconds...");
                    setTimeout(() => {
                        try {
                            playerInstance.load(enhancedConfig.playlist || enhancedConfig.file);
                        } catch (retryError) {
                            console.error("❌ Retry failed:", retryError);
                        }
                    }, 3000);
                }
            })
            .on("warning",(warning)=>{
                console.warn("⚠️ Player Warning:", warning);
            })
            .on("ready",()=>{
                console.log("🎬 Player Ready Event Triggered!");
                
                // Debug: check which provider is actually being used
                try {
                    const currentProvider = playerInstance.getProvider();
                    console.log("🔍 Current provider:", currentProvider ? currentProvider.name : 'unknown');
                    debugLogToServer('player_provider', { 
                        provider: currentProvider ? currentProvider.name : 'unknown',
                        streamType: playerConfig.playlist[0].sources[0].type
                    });
                } catch (e) {
                    console.warn("Could not get provider info:", e.message);
                }
                
                // Debug: check quality levels after player is ready
                setTimeout(() => {
                    try {
                        const qualityLevels = playerInstance.getQualityLevels();
                        console.log("🎚️ Available quality levels:", qualityLevels);
                        console.log("🎚️ Current quality:", playerInstance.getCurrentQuality());
                        
                        debugLogToServer('quality_levels_debug', {
                            levels: qualityLevels,
                            currentQuality: playerInstance.getCurrentQuality(),
                            levelsCount: qualityLevels ? qualityLevels.length : 0
                        });
                        
                        if (!qualityLevels || qualityLevels.length <= 1) {
                            console.warn("🎚️ No quality levels available or only one level");
                            
                            // Try to force quality detection
                            setTimeout(() => {
                                try {
                                    const newLevels = playerInstance.getQualityLevels();
                                    console.log("🎚️ Re-checking quality levels:", newLevels);
                                    
                                    if (newLevels && newLevels.length > 1) {
                                        console.log("🎚️ Quality levels now available!");
                                        debugLogToServer('quality_levels_delayed', {
                                            levels: newLevels,
                                            levelsCount: newLevels.length
                                        });
                                    } else {
                                        console.warn("🎚️ Still no quality levels - may be single bitrate stream");
                                    }
                                } catch (e) {
                                    console.warn("Error re-checking quality levels:", e.message);
                                }
                            }, 5000);
                        }
                    } catch (e) {
                        console.warn("Could not get quality levels:", e.message);
                    }
                }, 2000);
                
                // Initialize Shaka Player instance if not exists and we have DASH content
                if (playerConfig.playlist[0].sources[0].type === 'dash') {
                    // Wait for video element to be fully ready
                    setTimeout(() => {
                        try {
                            if (!window.shakadata || !window.shakadata.shakaPlayer) {
                                console.log("Initializing Shaka Player instance after ready...");
                                const videoElement = document.querySelector('#player video');
                                if (videoElement && typeof shaka !== 'undefined') {
                                    window.shakadata = {
                                        shakaPlayer: new shaka.Player(videoElement)
                                    };
                                    console.log("Shaka Player instance created successfully");
                                    
                                    // Apply DRM configuration to Shaka
                                    if (s.extra.clearkeys && s.extra.clearkeys.drm && s.extra.clearkeys.drm.clearKeys) {
                                        window.shakadata.shakaPlayer.configure({
                                            drm: { clearKeys: s.extra.clearkeys.drm.clearKeys }
                                        });
                                        console.log("DRM configuration applied to Shaka Player");
                                    }
                                    
                                    // Trigger shakadataReady now that Shaka is properly initialized
                                    console.log("Triggering shakadataReady after Shaka initialization for", playerConfig.playlist[0].sources[0].type);
                                    debugLogToServer('shaka_ready_trigger', { 
                                        streamType: playerConfig.playlist[0].sources[0].type,
                                        hasFairPlayConfig: !!(s.extra.fairplayCertUrl && s.extra.fairplayLicenseUrl)
                                    });
                                    document.dispatchEvent(new CustomEvent('shakadataReady'));
                                } else {
                                    console.warn("Video element or Shaka still not available");
                                }
                            }
                        } catch (shakaError) {
                            console.error("Error initializing Shaka Player:", shakaError);
                        }
                    }, 1000); // Wait 1 second for video element to be ready
                } else if (playerConfig.playlist[0].sources[0].type === 'hls') {
                    console.log("🍎 HLS stream - using JW Player native FairPlay (no Shaka initialization)");
                    console.log("🍎 FairPlay config in JW Player:", JSON.stringify(playerConfig.playlist[0].sources[0].drm, null, 2));
                }
                
                // Add compressor button (only if not already added)
                if (!document.querySelector(".jw-icon.btn-compressor")) {
                    try {
                         playerInstance.addButton(
                              '<svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon btn-compressor-off" viewBox="0 0 640 512"><path d="M352 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V480c0 17.7 14.3 32 32 32s32-14.3 32-32V32zM544 96c0-17.7-14.3-32-32-32s-32 14.3-32 32V416c0 17.7 14.3 32 32 32s32-14.3 32-32V96zM256 128c0-17.7-14.3-32-32-32s-32 14.3-32 32V384c0 17.7 14.3 32 32 32s32-14.3 32-32V128zm192 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V352c0 17.7 14.3 32 32 32s32-14.3 32-32V160zM160 224c0-17.7-14.3-32-32-32s-32 14.3-32 32v64c0 17.7 14.3 32 32 32s32-14.3 32-32V224zM0 256a32 32 0 1 0 64 0A32 32 0 1 0 0 256zm576 0a32 32 0 1 0 64 0 32 32 0 1 0 -64 0z"/></svg>',
                              "Compressor On", m, "btn-compressor", "btn-compressor"
                         );
                         // Try multiple times to position the button correctly
                         let attempts = 0;
                         const maxAttempts = 10;

                         const positionCompressorButton = () => {
                             attempts++;
                             let captionsBtn = document.querySelector(".jw-icon.jw-icon-cc");
                             let compressorBtn = document.querySelector(".jw-icon.btn-compressor");

                             console.log(`Attempt ${attempts}: captionsBtn=${!!captionsBtn}, compressorBtn=${!!compressorBtn}`);

                             if (compressorBtn) {
                                 if (captionsBtn && captionsBtn.parentElement && compressorBtn.parentElement) {
                                     // Insert compressor button right after captions button
                                     captionsBtn.parentElement.insertAdjacentElement('afterend', compressorBtn.parentElement);
                                     console.log("✅ Compressor button positioned after captions button");

                                     // Auto-enable if setting is on
                                     if(s.compressorEnabled && compressorBtn.querySelector('.btn-compressor-off')){
                                         compressorBtn.click();
                                     }
                                     return; // Success, stop trying
                                 } else {
                                     // Fallback: place after volume button
                                     let volumeBtn = document.querySelector(".jw-icon.jw-icon-volume");
                                     if (volumeBtn && volumeBtn.parentElement && compressorBtn.parentElement) {
                                         volumeBtn.parentElement.after(compressorBtn.parentElement);
                                         console.log("✅ Compressor button positioned after volume button (fallback)");

                                         // Auto-enable if setting is on
                                         if(s.compressorEnabled && compressorBtn.querySelector('.btn-compressor-off')){
                                             compressorBtn.click();
                                         }
                                         return; // Success, stop trying
                                     }
                                 }
                             }

                             // If we haven't succeeded and haven't reached max attempts, try again
                             if (attempts < maxAttempts) {
                                 setTimeout(positionCompressorButton, 200);
                             } else {
                                 console.warn("❌ Failed to position compressor button after", maxAttempts, "attempts");
                             }
                         };

                         // Start positioning attempts
                         setTimeout(positionCompressorButton, 100);
                    } catch (e) { console.error("Error adding compressor button:", e); }
                }
            }).on("error",e=>{
                console.error("JW Player Error Event:", e);

                // Check if this is a stream-related error that might be fixed by refreshing
                if (e.code === 230001 || e.code === 230002 || e.code === 230003) {
                    console.log("🔄 Stream error detected, attempting automatic recovery...");

                    // Try to refresh the stream automatically
                    setTimeout(() => {
                        console.log("🔄 Attempting to reload stream...");
                        try {
                            // Get current playlist
                            const currentPlaylist = playerInstance.getPlaylist();
                            if (currentPlaylist && currentPlaylist.length > 0) {
                                const currentSource = currentPlaylist[0].sources[0];
                                if (currentSource && currentSource.file) {
                                    // Add a cache-busting parameter to force refresh
                                    const refreshedUrl = currentSource.file + (currentSource.file.includes('?') ? '&' : '?') + 'refresh=' + Date.now();

                                    // Update the source
                                    const newPlaylist = [{
                                        sources: [{
                                            file: refreshedUrl,
                                            type: currentSource.type,
                                            default: true
                                        }],
                                        title: currentPlaylist[0].title || 'Live Stream'
                                    }];

                                    console.log("🔄 Loading refreshed stream...");
                                    playerInstance.load(newPlaylist);
                                    playerInstance.play();
                                    return; // Don't show error message if we're trying to recover
                                }
                            }
                        } catch (refreshError) {
                            console.error("❌ Auto-refresh failed:", refreshError);
                        }
                    }, 2000); // Wait 2 seconds before attempting refresh
                }

                // Show error message for non-recoverable errors or if recovery fails
                const playerElement = document.getElementById('player');
                if (playerElement) {
                     playerElement.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Player Error: ${e.message} (Code: ${e.code})<br>Please check stream details and keys.</div>`;
                }
                 // Allow button to be shown again on error - handled by DOMContentLoaded listener
            }).on("levels",r=>{ // Quality level display logic (original, adapted for safety)
                 console.log("🎚️ Levels event triggered:", r);
                 debugLogToServer('player_levels', { 
                     levels: r ? r.levels : null,
                     levelsCount: r && r.levels ? r.levels.length : 0 
                 });
                 
                 try {
                     let playerElement = document.getElementById('player');
                     let qualityMenu = playerElement ? playerElement.querySelector(".jw-settings-submenu-items[role='menu'][aria-label='Quality']") : null;
                     
                     console.log("🎚️ Quality menu found:", !!qualityMenu);
                     if (!qualityMenu) {
                         console.warn("🎚️ Quality menu not found - checking alternative selectors");
                         // Try alternative selectors
                         qualityMenu = playerElement ? playerElement.querySelector(".jw-settings-quality") : null;
                         if (!qualityMenu) {
                             qualityMenu = playerElement ? playerElement.querySelector("[aria-label*='Quality']") : null;
                         }
                         if (!qualityMenu) {
                             console.warn("🎚️ No quality menu found with any selector");
                             return;
                         }
                     }
                     const qualityButtons = qualityMenu.querySelectorAll("button[role='menuitemradio']");
                     console.log("🎚️ Quality buttons found:", qualityButtons.length);
                     
                     qualityButtons.forEach((buttonEl, index) => {
                         console.log(`🎚️ Processing button ${index}:`, buttonEl.textContent);
                         if (index !== 0) {
                             const levelIndex = index -1;
                             if (!r || !r.levels || !r.levels[levelIndex]) { 
                                 console.warn(`🎚️ No level data for index ${levelIndex}`);
                                 return; 
                             }
                             let levelData = r.levels[levelIndex];
                             console.log(`🎚️ Level ${levelIndex} data:`, levelData);
                             let frameRateInfo = "";
                             var providerData;
                             let currentProvider = playerInstance.getProvider();
                             if (currentProvider && currentProvider.name === "shaka" && typeof window.shakadata !== 'undefined' && window.shakadata.shakaPlayer) {
                                  try { let trackId = levelData.shakaId; if (trackId !== undefined) { providerData = window.shakadata.shakaPlayer.getVariantTracks().find(track => track.id === trackId); if (providerData?.frameRate) { frameRateInfo = " " + parseFloat(Number(providerData.frameRate).toFixed(2)) + " fps"; } } } catch (shakaError) { console.warn("Error getting Shaka track info:", shakaError); }
                             } else if (currentProvider && currentProvider.name === "hlsjs" && typeof window.hlsdata !== 'undefined' && window.hlsdata.levelController) {
                                  try { let hlsIndex = levelData.hlsjsIndex; if (hlsIndex !== undefined) { providerData = window.hlsdata.levelController.levels.at(hlsIndex); if (providerData?.attrs["FRAME-RATE"]) { frameRateInfo = " " + parseFloat(Number(providerData.attrs["FRAME-RATE"]).toFixed(2)) + " fps"; } } } catch (hlsError) { console.warn("Error getting HLS level info:", hlsError); }
                             }
                             let height = levelData.height || '?';
                             let bitrate = levelData.bitrate ? Math.floor(levelData.bitrate / 1e3) : '?';
                             buttonEl.innerHTML = height + "p (" + bitrate + " kbps" + frameRateInfo + ")";
                         }
                     });
                 } catch (levelsError) { console.error("Error updating quality levels display:", levelsError); }
            });

            // Return the instance if needed elsewhere
            return playerInstance;

        } catch (setupError) {
             console.error("Fatal error during playerInstance.setup():", setupError);
             const playerElement = document.getElementById('player');
             if (playerElement) {
                  playerElement.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Fatal Player Setup Error: ${setupError.message}<br>Check browser console for more details.</div>`;
             }
             // Ensure UI state (button) is reset if setup fails completely
              const playerSection = document.getElementById('player');
              if (playerSection) playerSection.classList.remove('player-active', 'player-loading');
              const playButton = document.getElementById('playStreamButton');
              if (playButton) playButton.disabled = false; // Re-enable button if setup failed
             throw setupError; // Re-throw error to be caught by the click handler
        }
    } // --- End initializePlayer ---

    // --- REVISED DOMContentLoaded listener ---
    document.addEventListener('DOMContentLoaded', () => {
        const playButton = document.getElementById('playStreamButton');
        const playerSection = document.getElementById('player'); // Use the player div instead

        if (!playerSection) {
             console.error("Critical: Player container (#player) not found!");
             return;
        }

        if (playButton) {
            console.log("Play button (playStreamButton) found.");

            // Check if button should be initially hidden (because it's disabled due to missing data)
            if(playButton.disabled) {
                const buttonContainer = playerSection.querySelector('.play-button-container');
                if (buttonContainer) buttonContainer.style.display = 'none';
                console.log("Play button is disabled (missing data), hiding overlay.");
            } else {
                 // Add click listener only if the button is enabled
                 playButton.addEventListener('click', async () => { // Make listener async to await initializePlayer
                     console.log("Play button clicked.");
                     console.log("Button dataset:", playButton.dataset);
                     const streamType = playButton.dataset.streamType || 'dash';
                     const streamUrl = streamType === 'hls' ? playButton.dataset.hlsUrl : playButton.dataset.mpdUrl;
                     const keyData = playButton.dataset.keyData;
                     const licenseUrl = playButton.dataset.licenseUrl;
                     const certificateUrl = playButton.dataset.certificateUrl; // <--- added
                     const title = playButton.dataset.title || document.title || 'Shahid Content';
                     console.log("Extracted data:", { streamType, streamUrl, keyData, licenseUrl, title });

                     if (!streamUrl) {
                         alert('خطأ: رابط البث مفقود في بيانات الزر.');
                         console.error("Stream URL missing in playStreamButton data attribute.");
                         return;
                     }
                     
                     // Clean the stream URL - remove parameters and fix duplicates
                    let cleanStreamUrl = streamUrl.split(';')[0];

                    // Fix duplicate URL issue
                    if (cleanStreamUrl.includes('&') && cleanStreamUrl.includes('http')) {
                        const parts = cleanStreamUrl.split('&');
                        for (const part of parts) {
                            if (part.startsWith('http') && part.includes('.mpd') && !part.includes('&http')) {
                                cleanStreamUrl = part;
                                break;
                            }
                        }
                        // If no clean part found, use first part
                        if (cleanStreamUrl === streamUrl) {
                            cleanStreamUrl = parts[0];
                        }
                    }

                    console.log("Stream type:", streamType, "Original URL:", streamUrl);
                    if (cleanStreamUrl !== streamUrl) {
                        console.log("Cleaned URL:", cleanStreamUrl);
                    }

                    // Use the cleaned URL for player initialization
                    const finalStreamUrl = cleanStreamUrl;

                     // --- UI Feedback Start ---
                     playButton.disabled = true; // Disable button during loading
                     playerSection.classList.add('player-loading'); // Add loading class (optional: style .player-loading .play-button-container with a spinner)
                     playerSection.classList.remove('player-error'); // Clear previous error state
                     console.log("UI feedback: Button disabled, loading class added.");
                     // ---

                     try {
                         // Expose FairPlay endpoints to the initializer
                         if (certificateUrl) { window.__FAIRPLAY_CERT_URL__ = certificateUrl; }
                         if (licenseUrl) { window.__FAIRPLAY_LICENSE_URL__ = licenseUrl; }
                         // Call the async initializer function and wait for it
                         const playerInstance = await initializePlayer(finalStreamUrl, keyData, streamType, licenseUrl, title);
                         console.log("initializePlayer finished successfully.");

                         // --- UI Feedback Success (Player handles visibility via events) ---
                         // If player setup is successful, the 'play' event handler below
                         // will add 'player-active' which hides the button via CSS.
                         // We just need to remove the loading state here.
                         playerSection.classList.remove('player-loading');

                         // Optional: Attach player event listeners here if needed after setup
                         if (playerInstance) {
                            attachPlayerEventListeners(playerInstance, playerSection, playButton);
                         }
                         // ---

                     } catch (error) {
                         console.error("Error occurred during initializePlayer call:", error);
                         // --- UI Feedback Error ---
                         playerSection.classList.remove('player-loading', 'player-active');
                         playerSection.classList.add('player-error'); // Optional class for error state styling
                         playButton.disabled = false; // Re-enable button on error
                         alert("حدث خطأ أثناء تهيئة المشغل. يرجى مراجعة التفاصيل أو المحاولة مرة أخرى.");
                         // ---
                     }
                 }); // End click listener
                 console.log("Click listener added to enabled playStreamButton.");
            } // End else (button enabled)

        } else {
            console.warn("Play button (playStreamButton) not found in the DOM on load.");
        }

        console.log("Player script loaded and DOM ready.");

    }); // --- End DOMContentLoaded ---


    // --- NEW Function to attach player event listeners ---
    // This helps keep the DOMContentLoaded cleaner
    function attachPlayerEventListeners(playerInstance, playerSection, playButton) {
         console.log("Attaching player event listeners for UI state.");

         // Make sure playerSection and playButton are valid DOM elements
         if (!playerSection || !playButton) {
             console.error("Cannot attach listeners: playerSection or playButton is invalid.");
             return;
         }

         // Function to reset UI (show button)
         const showPlayButton = () => {
              playerSection.classList.remove('player-active', 'player-loading', 'player-error');
              playButton.disabled = false;
              console.log("Player event: Resetting UI, showing play button.");
         };

         // Function to hide UI (hide button)
         const hidePlayButton = () => {
              playerSection.classList.remove('player-loading', 'player-error');
              playerSection.classList.add('player-active'); // This class hides the button via CSS
              playButton.disabled = true; // Keep disabled while active
              console.log("Player event: Hiding play button (adding player-active class).");
         };

         // Attach Listeners
         playerInstance.on('play', hidePlayButton);
         playerInstance.on('buffer', hidePlayButton); // Also hide during buffering after initial play
         playerInstance.on('error', showPlayButton);
         playerInstance.on('complete', showPlayButton);
         playerInstance.on('remove', showPlayButton); // When player is removed

         // Optional: Handle pause? Sometimes users might want the button back on pause.
         // playerInstance.on('pause', showPlayButton);
         // ✅ إضافة النقر المزدوج للشاشة الكاملة - يعمل حتى أثناء التشغيل
         playerInstance.on('ready', function() {
             console.log("🎬 Setting up double-click fullscreen");

             // ✅ تفعيل دعم الشاشة الكاملة
             const playerElement = document.getElementById('player');
             if (playerElement) {
                 playerElement.allowFullscreen = true;
                 playerElement.webkitAllowFullscreen = true;
                 playerElement.mozAllowFullscreen = true;
                 playerElement.setAttribute('allowfullscreen', '');
                 playerElement.setAttribute('webkitallowfullscreen', '');
                 playerElement.setAttribute('mozallowfullscreen', '');
                 console.log("✅ Fullscreen support enabled");
             }

             // انتظار تحميل المشغل بالكامل
             setTimeout(function() {
                 const playerContainer = playerInstance.getContainer();
                 if (playerContainer) {
                     // ❌ Title display disabled
                     console.log('🚫 Title display is disabled');
                     if (false) { // Disabled title display
                         const titleOverlay = document.createElement('div');
                         titleOverlay.className = 'custom-episode-title';
                         titleOverlay.textContent = episodeTitle;
                         titleOverlay.style.cssText = `
                             position: absolute;
                             top: 20px;
                             left: 20px;
                             right: 20px;
                             background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
                             color: white;
                             font-family: Arial, sans-serif;
                             font-size: 18px;
                             font-weight: bold;
                             padding: 15px 20px;
                             border-radius: 8px;
                             text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
                             z-index: 100;
                             pointer-events: none;
                             max-width: 80%;
                             word-wrap: break-word;
                             opacity: 0;
                             transition: opacity 0.3s ease;
                         `;

                         playerContainer.appendChild(titleOverlay);

                         // إظهار العنوان لفترة ثم إخفاؤه
                         setTimeout(() => {
                             titleOverlay.style.opacity = '1';
                         }, 500);

                         setTimeout(() => {
                             titleOverlay.style.opacity = '0';
                         }, 5000);

                         // إظهار العنوان عند pause
                         playerInstance.on('pause', () => {
                             titleOverlay.style.opacity = '1';
                         });

                         playerInstance.on('play', () => {
                             setTimeout(() => {
                                 titleOverlay.style.opacity = '0';
                             }, 2000);
                         });

                         console.log("✅ Custom episode title overlay added:", episodeTitle);
                     }
                     // ✅ نقرة واحدة بسيطة للتشغيل/الإيقاف
                     const handleSingleClick = function(e) {
                         // تجنب الكنترولز
                         if (e.target.closest('.jw-controls') ||
                             e.target.closest('.jw-controlbar') ||
                             e.target.closest('.jw-button') ||
                             e.target.closest('.jw-slider')) {
                             return;
                         }

                         e.preventDefault();
                         e.stopPropagation();

                         // تشغيل/إيقاف مباشر
                         const currentState = playerInstance.getState();
                         console.log("🖱️ Click detected, current state:", currentState);

                         if (currentState === 'playing') {
                             playerInstance.pause();
                             console.log("⏸️ Video paused");
                         } else if (currentState === 'paused' || currentState === 'idle') {
                             playerInstance.play();
                             console.log("▶️ Video playing");
                         }
                     };

                     // إضافة listeners متعددة للتأكد من عمل النقر
                     playerContainer.addEventListener('click', handleSingleClick, true);

                     // ✅ إضافة dblclick event منفصل للشاشة الكاملة
                     playerContainer.addEventListener('dblclick', function(e) {
                         // تجنب الكنترولز
                         if (e.target.closest('.jw-controls') ||
                             e.target.closest('.jw-controlbar') ||
                             e.target.closest('.jw-button') ||
                             e.target.closest('.jw-slider')) {
                             return;
                         }

                         e.preventDefault();
                         e.stopPropagation();

                         console.log("🖱️ Double click event detected - toggling fullscreen");

                         try {
                             const isFullscreen = playerInstance.getFullscreen();
                             if (isFullscreen) {
                                 playerInstance.setFullscreen(false);
                                 console.log("🔲 Exited fullscreen by dblclick");
                             } else {
                                 playerInstance.setFullscreen(true);
                                 console.log("🔳 Entered fullscreen by dblclick");
                             }
                         } catch (err) {
                             console.log("⚠️ JWPlayer fullscreen failed, using native API");
                             const playerElement = document.getElementById('player');
                             if (document.fullscreenElement) {
                                 document.exitFullscreen();
                                 console.log("🔲 Exited native fullscreen");
                             } else {
                                 playerElement.requestFullscreen().then(() => {
                                     console.log("🔳 Entered native fullscreen");
                                 }).catch(e => console.log("❌ Fullscreen failed:", e));
                             }
                         }
                     }, true);

                     // إضافة listener للفيديو مباشرة
                     const videoElement = playerContainer.querySelector('video');
                     if (videoElement) {
                         videoElement.addEventListener('click', handleSingleClick, true);

                         // إضافة dblclick للفيديو أيضاً
                         videoElement.addEventListener('dblclick', function(e) {
                             e.preventDefault();
                             e.stopPropagation();

                             console.log("🖱️ Video double click detected");

                             try {
                                 const isFullscreen = playerInstance.getFullscreen();
                                 if (isFullscreen) {
                                     playerInstance.setFullscreen(false);
                                     console.log("🔲 Exited fullscreen by video dblclick");
                                 } else {
                                     playerInstance.setFullscreen(true);
                                     console.log("🔳 Entered fullscreen by video dblclick");
                                 }
                             } catch (err) {
                                 const playerElement = document.getElementById('player');
                                 if (document.fullscreenElement) {
                                     document.exitFullscreen();
                                 } else {
                                     playerElement.requestFullscreen();
                                 }
                             }
                         }, true);

                         console.log("✅ Video click handlers added");
                     }

                     console.log("✅ Double-click fullscreen setup complete");
                 }
             }, 1000);
         });

         // ✅ إضافة keyboard shortcuts
         document.addEventListener('keydown', function(e) {
             if (playerInstance && playerInstance.getState) {
                 const currentState = playerInstance.getState();

                 // مفتاح المسافة للتشغيل/الإيقاف
                 if (e.code === 'Space' && !e.target.matches('input, textarea, select')) {
                     e.preventDefault();
                     if (currentState === 'playing') {
                         playerInstance.pause();
                         console.log("⏸️ Paused by spacebar");
                     } else if (currentState === 'paused' || currentState === 'idle') {
                         playerInstance.play();
                         console.log("▶️ Playing by spacebar");
                     }
                 }

                 // مفتاح F للشاشة الكاملة
                 if (e.key === 'f' || e.key === 'F') {
                     e.preventDefault();
                     try {
                         const isFullscreen = playerInstance.getFullscreen();
                         if (isFullscreen) {
                             playerInstance.setFullscreen(false);
                             console.log("🔲 Exited fullscreen by F key");
                         } else {
                             playerInstance.setFullscreen(true);
                             console.log("🔳 Entered fullscreen by F key");
                         }
                     } catch (err) {
                         const playerElement = document.getElementById('player');
                         if (document.fullscreenElement) {
                             document.exitFullscreen();
                         } else {
                             playerElement.requestFullscreen();
                         }
                     }
                 }

                 // مفتاح Escape للخروج من الشاشة الكاملة
                 if (e.key === 'Escape') {
                     try {
                         const isFullscreen = playerInstance.getFullscreen();
                         if (isFullscreen) {
                             playerInstance.setFullscreen(false);
                             console.log("🔲 Exited fullscreen by Escape");
                         }
                     } catch (err) {
                         if (document.fullscreenElement) {
                             document.exitFullscreen();
                         }
                     }
                 }
             }
         });

         console.log("✅ All interaction handlers setup complete");

         // Start stream health monitoring for live streams
         if (typeof streamType !== 'undefined' && streamType === 'hls') {
             startStreamHealthMonitoring();
         }
    }

    // Stream health monitoring function
    function startStreamHealthMonitoring() {
        console.log("🔍 Starting stream health monitoring...");

        let consecutiveErrors = 0;
        const maxConsecutiveErrors = 3;

        // Monitor every 2 minutes
        const monitorInterval = setInterval(() => {
            if (!playerInstance) {
                clearInterval(monitorInterval);
                return;
            }

            try {
                const state = playerInstance.getState();
                const position = playerInstance.getPosition();
                const duration = playerInstance.getDuration();

                console.log("🔍 Stream health check:", {
                    state: state,
                    position: position,
                    duration: duration,
                    consecutiveErrors: consecutiveErrors
                });

                // Check if stream is stalled (position not changing for live stream)
                if (state === 'playing' && duration === 0) { // Live stream
                    // For live streams, we can't rely on position changes
                    // Instead, we'll rely on error events
                    consecutiveErrors = 0; // Reset if playing
                } else if (state === 'error' || state === 'idle') {
                    consecutiveErrors++;
                    console.log(`⚠️ Stream issue detected (${consecutiveErrors}/${maxConsecutiveErrors})`);

                    if (consecutiveErrors >= maxConsecutiveErrors) {
                        console.log("🔄 Too many consecutive errors, attempting stream refresh...");
                        attemptStreamRefresh();
                        consecutiveErrors = 0; // Reset after refresh attempt
                    }
                } else {
                    consecutiveErrors = 0; // Reset on successful state
                }

            } catch (error) {
                console.error("❌ Error in stream health monitoring:", error);
            }
        }, 120000); // Check every 2 minutes

        // Store interval ID for cleanup
        window.streamMonitorInterval = monitorInterval;
    }

    // Function to attempt stream refresh
    function attemptStreamRefresh() {
        try {
            console.log("🔄 Attempting automatic stream refresh...");

            const currentPlaylist = playerInstance.getPlaylist();
            if (currentPlaylist && currentPlaylist.length > 0) {
                const currentSource = currentPlaylist[0].sources[0];
                if (currentSource && currentSource.file) {
                    // Add cache-busting parameter
                    const refreshedUrl = currentSource.file + (currentSource.file.includes('?') ? '&' : '?') + 'refresh=' + Date.now();

                    const newPlaylist = [{
                        sources: [{
                            file: refreshedUrl,
                            type: currentSource.type,
                            default: true
                        }],
                        title: currentPlaylist[0].title || 'Live Stream'
                    }];

                    console.log("🔄 Loading refreshed stream URL...");
                    playerInstance.load(newPlaylist);

                    // Auto-play after a short delay
                    setTimeout(() => {
                        playerInstance.play();
                    }, 1000);
                }
            }
        } catch (error) {
            console.error("❌ Failed to refresh stream:", error);
        }
    }

    // Function to add forward/rewind controls
    function addForwardRewindControls(player) {
        try {
            console.log("⏪⏩ Adding forward/rewind controls...");

            const playerContainer = player.getContainer();
            if (!playerContainer) {
                console.warn("Player container not found");
                return;
            }

            // Find the existing rewind button in display controls
            const rewindDisplayIcon = playerContainer.querySelector('.jw-display-icon-rewind');

            if (rewindDisplayIcon) {
                // Create forward button by cloning rewind button
                const forwardDisplayIcon = rewindDisplayIcon.cloneNode(true);
                const forwardIcon = forwardDisplayIcon.querySelector('.jw-icon-rewind');

                if (forwardIcon) {
                    // Flip the icon horizontally for forward
                    forwardIcon.style.transform = 'scaleX(-1)';
                    forwardIcon.setAttribute('aria-label', 'Forward 10 Seconds');

                    // Add click handler for forward
                    forwardDisplayIcon.addEventListener('click', () => {
                        const currentTime = player.getPosition();
                        player.seek(currentTime + 10);
                        console.log("⏩ Forward 10 seconds");
                    });

                    // Find the next button to insert before it
                    const nextButton = playerContainer.querySelector('.jw-display-icon-next');
                    if (nextButton && nextButton.parentNode) {
                        nextButton.parentNode.insertBefore(forwardDisplayIcon, nextButton);
                        // Hide the next button since we don't need it
                        nextButton.style.display = 'none';
                    } else {
                        // If no next button, insert after rewind
                        rewindDisplayIcon.parentNode.insertBefore(forwardDisplayIcon, rewindDisplayIcon.nextSibling);
                    }
                }

                // Add click handler for existing rewind button
                rewindDisplayIcon.addEventListener('click', () => {
                    const currentTime = player.getPosition();
                    player.seek(Math.max(0, currentTime - 10));
                    console.log("⏪ Rewind 10 seconds");
                });
            }

            // Also add to control bar if buttons exist there
            const controlBar = playerContainer.querySelector('.jw-controlbar');
            if (controlBar) {
                const rewindControlButton = controlBar.querySelector('.jw-icon-rewind');

                if (rewindControlButton && rewindControlButton.parentElement) {
                    // Create forward button for control bar
                    const forwardControlButton = rewindControlButton.cloneNode(true);
                    forwardControlButton.style.transform = 'scaleX(-1)';
                    forwardControlButton.setAttribute('aria-label', 'Forward 10 Seconds');

                    // Add click handler
                    forwardControlButton.addEventListener('click', () => {
                        const currentTime = player.getPosition();
                        player.seek(currentTime + 10);
                        console.log("⏩ Forward 10 seconds (control bar)");
                    });

                    // Insert after rewind button
                    rewindControlButton.parentElement.insertBefore(
                        forwardControlButton,
                        rewindControlButton.nextElementSibling
                    );

                    // Add click handler for rewind
                    rewindControlButton.addEventListener('click', () => {
                        const currentTime = player.getPosition();
                        player.seek(Math.max(0, currentTime - 10));
                        console.log("⏪ Rewind 10 seconds (control bar)");
                    });
                }
            }

            console.log("✅ Forward/rewind controls added successfully");
        } catch (error) {
            console.error("❌ Error adding forward/rewind controls:", error);
        }
    }

}(); // End main IIFE
