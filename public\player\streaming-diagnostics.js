/**
 * Streaming Diagnostics Tool
 * Monitors and diagnoses streaming issues on hosting environments
 */

console.log('🔍 Streaming Diagnostics loaded');

// Global diagnostics object
window.StreamingDiagnostics = {
    logs: [],
    errors: [],
    warnings: [],
    tests: {},
    
    // Log function
    log: function(type, message, data = null) {
        const timestamp = new Date().toISOString();
        const entry = { timestamp, type, message, data };
        
        this.logs.push(entry);
        
        if (type === 'error') {
            this.errors.push(entry);
            console.error(`🚨 [${timestamp}] ${message}`, data);
        } else if (type === 'warning') {
            this.warnings.push(entry);
            console.warn(`⚠️ [${timestamp}] ${message}`, data);
        } else {
            console.log(`ℹ️ [${timestamp}] ${message}`, data);
        }
    },
    
    // Test CSP compliance
    testCSP: function() {
        this.log('info', 'Testing CSP compliance...');
        
        try {
            // Test blob URL creation
            const testBlob = new Blob(['test'], { type: 'video/mp4' });
            const blobUrl = URL.createObjectURL(testBlob);
            this.tests.blobUrls = true;
            this.log('info', 'Blob URLs: ✅ Working', { blobUrl });
            URL.revokeObjectURL(blobUrl);
        } catch (error) {
            this.tests.blobUrls = false;
            this.log('error', 'Blob URLs: ❌ Failed', error);
        }
        
        try {
            // Test Worker creation using safe method
            const workerBlob = new Blob(['self.postMessage("test");'], { type: 'application/javascript' });
            const workerUrl = URL.createObjectURL(workerBlob);

            // Use safe Worker creation if available
            const worker = window.CSPFix && window.CSPFix.createSafeWorker
                ? window.CSPFix.createSafeWorker(workerUrl)
                : new (window.Worker.__original__ || Worker)(workerUrl);
            
            worker.onmessage = () => {
                this.tests.workers = true;
                this.log('info', 'Web Workers: ✅ Working');
                worker.terminate();
                URL.revokeObjectURL(workerUrl);
            };
            
            worker.onerror = (error) => {
                this.tests.workers = false;
                this.log('error', 'Web Workers: ❌ Failed', error);
                worker.terminate();
                URL.revokeObjectURL(workerUrl);
            };
            
        } catch (error) {
            this.tests.workers = false;
            this.log('error', 'Web Workers: ❌ Failed', error);
        }
    },
    
    // Test proxy connectivity
    testProxy: function() {
        this.log('info', 'Testing proxy connectivity...');
        
        const proxyUrl = 'https://fast-decoder.com/video-proxy/manifest?url=' + 
                        encodeURIComponent('https://httpbin.org/get');
        
        fetch(proxyUrl, {
            method: 'HEAD',
            headers: {
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
            }
        }).then(response => {
            if (response.ok) {
                this.tests.proxy = true;
                this.log('info', 'Proxy: ✅ Working', { status: response.status });
            } else {
                this.tests.proxy = false;
                this.log('error', 'Proxy: ❌ Failed', { 
                    status: response.status, 
                    statusText: response.statusText 
                });
            }
        }).catch(error => {
            this.tests.proxy = false;
            this.log('error', 'Proxy: ❌ Network Error', error);
        });
    },
    
    // Test CORS headers
    testCORS: function() {
        this.log('info', 'Testing CORS headers...');
        
        fetch(window.location.href, {
            method: 'HEAD'
        }).then(response => {
            const corsHeaders = {
                'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
                'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
                'access-control-allow-headers': response.headers.get('access-control-allow-headers')
            };
            
            this.tests.cors = corsHeaders;
            this.log('info', 'CORS Headers:', corsHeaders);
        }).catch(error => {
            this.log('error', 'CORS Test Failed:', error);
        });
    },
    
    // Test video element creation
    testVideoElement: function() {
        this.log('info', 'Testing video element creation...');
        
        try {
            const video = document.createElement('video');
            video.crossOrigin = 'anonymous';
            video.preload = 'metadata';
            
            // Test blob video source
            const testBlob = new Blob([''], { type: 'video/mp4' });
            const blobUrl = URL.createObjectURL(testBlob);
            
            video.src = blobUrl;
            
            video.addEventListener('loadstart', () => {
                this.tests.videoBlob = true;
                this.log('info', 'Video Blob URLs: ✅ Working');
                URL.revokeObjectURL(blobUrl);
            });
            
            video.addEventListener('error', (e) => {
                this.tests.videoBlob = false;
                this.log('error', 'Video Blob URLs: ❌ Failed', e.target.error);
                URL.revokeObjectURL(blobUrl);
            });
            
        } catch (error) {
            this.tests.videoBlob = false;
            this.log('error', 'Video Element Test Failed:', error);
        }
    },
    
    // Run all tests
    runAllTests: function() {
        this.log('info', '🔍 Starting comprehensive streaming diagnostics...');
        
        this.testCSP();
        this.testProxy();
        this.testCORS();
        this.testVideoElement();
        
        // Generate report after 3 seconds
        setTimeout(() => {
            this.generateReport();
        }, 3000);
    },
    
    // Generate diagnostic report
    generateReport: function() {
        this.log('info', '📊 Generating diagnostic report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            tests: this.tests,
            errors: this.errors,
            warnings: this.warnings,
            recommendations: []
        };
        
        // Add recommendations based on test results
        if (!this.tests.blobUrls) {
            report.recommendations.push('CSP policy needs to allow blob: URLs for video streaming');
        }
        
        if (!this.tests.workers) {
            report.recommendations.push('CSP policy needs to allow worker-src blob: for HLS.js workers');
        }
        
        if (!this.tests.proxy) {
            report.recommendations.push('Proxy server is not accessible - check firewall/hosting restrictions');
        }
        
        if (!this.tests.videoBlob) {
            report.recommendations.push('Video element cannot load blob URLs - CSP media-src issue');
        }
        
        console.group('📊 Streaming Diagnostics Report');
        console.table(this.tests);
        console.log('Recommendations:', report.recommendations);
        console.log('Full Report:', report);
        console.groupEnd();
        
        // Store report globally
        window.STREAMING_DIAGNOSTICS_REPORT = report;
        
        return report;
    },
    
    // Export logs for debugging
    exportLogs: function() {
        const data = {
            logs: this.logs,
            errors: this.errors,
            warnings: this.warnings,
            tests: this.tests,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `streaming-diagnostics-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        this.log('info', 'Diagnostic logs exported');
    }
};

// Auto-run diagnostics when loaded
setTimeout(() => {
    window.StreamingDiagnostics.runAllTests();
}, 1000);

// Export for global access
window.SD = window.StreamingDiagnostics;

console.log('✅ Streaming Diagnostics ready - use window.SD.runAllTests() to test');
