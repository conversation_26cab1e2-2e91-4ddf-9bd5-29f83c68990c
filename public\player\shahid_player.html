<!DOCTYPE html>
<html lang="en">
<head>
    <title>Shahid Player</title>
    <link rel="icon" type="image/png" href="/static/player-shahid/play-on.png">
    <link rel="stylesheet" href="/static/player-shahid/tod_skin.css">
    <style>
        html,body,#player{height:100% !important;overflow:hidden !important}
        body{margin:0 auto;background-color:#000}
        .jw-aspect.jw-reset[style*=padding-top]{padding-top:unset !important}
    </style>
</head>
<body>
    <div id="player"></div>
    <button id="playStreamButton" style="display: none;"></button>
    <!-- Use CDN for JWPlayer -->
    <script src="https://cdn.jwplayer.com/libraries/KB5zFt7A.js"></script>
    <script>
        // Set JWPlayer base path immediately after loading
        if (typeof jwplayer !== 'undefined') {
            try {
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.base = 'https://cdn.jwplayer.com/libraries/';
                console.log('JWPlayer base path set to:', jwplayer.defaults.base);
            } catch(e) {
                console.warn('Could not set JWPlayer base path:', e);
            }
        }
    </script>
    <!-- Use CDN for Shaka Player -->
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <!-- Use local player script -->
    <script src="/player/player_original.js"></script>
    <script>
        // Get URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                title: params.get('title') || '',
                mpd: params.get('mpd') || '',
                hls: params.get('hls') || '',
                keyId: params.get('keyId') || '',
                key: params.get('key') || '',
                type: params.get('type') || 'dash',
                token: params.get('token') || '',  // إضافة token parameter
                licenseUrl: params.get('licenseUrl') || ''
            };
        }

        // Initialize player when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            const streamType = params.type;
            const streamUrl = streamType === 'hls' ? params.hls : params.mpd;
            const keyId = params.keyId;
            const key = params.key;
            const token = params.token;
            const licenseUrl = params.licenseUrl;
            const title = params.title;

            // Update page title if provided
            if (title) {
                document.title = title + ' - Shahid Player';
            }

            console.log('Initializing player with:', { title, streamType, streamUrl, keyId, key, token, licenseUrl });
            console.log('Raw URL params:', window.location.search);
            console.log('Parsed params:', params);

            if (streamUrl) {
                // Set up the button with stream data
                const playButton = document.getElementById('playStreamButton');
                if (playButton) {
                    if (streamType === 'hls') {
                        // 🚀 Smart HLS handling - check if it's already a dynamic proxy URL
                        let finalHlsUrl = streamUrl;
                        
                        if (streamUrl.startsWith('/shahid/stream/')) {
                            // Already a dynamic proxy URL - use directly
                            console.log('✅ Using dynamic proxy URL:', streamUrl);
                            finalHlsUrl = streamUrl;
                        } else {
                            // Use VideoProxyController (same as channels)
                            console.log('⚠️ Using VideoProxyController for non-cached URL');
                            finalHlsUrl = `/video-proxy/manifest?url=${encodeURIComponent(streamUrl)}`;
                        }
                        
                        playButton.dataset.hlsUrl = finalHlsUrl;
                        playButton.dataset.streamType = 'hls';
                        playButton.dataset.keyData = ''; // HLS doesn't use external keys like DASH
                        playButton.dataset.token = token; // إضافة token للـ HLS
                        
                        console.log('🎬 Final HLS URL:', finalHlsUrl);
                        
                        // Set global token for player.js to access
                        if (token) {
                            window.shahidToken = token;
                            console.log('✅ Set global Shahid token for HLS authentication');
                        }
                    } else {
                        playButton.dataset.mpdUrl = streamUrl;
                        playButton.dataset.streamType = 'dash';
                        playButton.dataset.keyData = keyId && key ? `${keyId}:${key}` : '';
                        playButton.dataset.licenseUrl = licenseUrl || '';
                    }

                    // Auto-start playback
                    setTimeout(() => {
                        console.log('Auto-clicking play button');
                        playButton.click();

                        // Unmute audio after a delay
                        setTimeout(() => {
                            try {
                                // Try to unmute video element directly (works with Shaka Player)
                                const video = document.querySelector('video');
                                if (video) {
                                    video.muted = false;
                                    video.volume = 1.0;
                                    console.log('✅ Audio unmuted via video element');
                                }

                                // Try global player if available
                                if (window.player && typeof window.player.setVolume === 'function') {
                                    window.player.setVolume(1.0);
                                    console.log('✅ Audio unmuted via global player');
                                }
                            } catch (e) {
                                console.log('ℹ️ Unmute attempt:', e.message);
                            }
                        }, 2000);
                    }, 500);
                }
            } else {
                console.error('No stream URL provided');
                document.getElementById('player').innerHTML =
                    '<div style="color: white; text-align: center; padding: 20px; font-size: 18px;">No stream URL provided</div>';
            }
        });
    </script>
</body>
</html>
