<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مشاكل البث - Streaming Issues Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-card { margin-bottom: 20px; }
        .test-result { padding: 10px; border-radius: 5px; margin: 5px 0; }
        .test-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .test-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-container { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; }
        .log-entry { margin: 2px 0; font-family: monospace; font-size: 12px; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        .recommendations { background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-broadcast-tower text-primary"></i>
                    اختبار مشاكل البث
                </h1>
                <p class="text-center text-muted">أداة تشخيص شاملة لمشاكل البث على الاستضافة</p>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <button id="runTests" class="btn btn-primary btn-lg me-2">
                    <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                </button>
                <button id="exportLogs" class="btn btn-secondary btn-lg">
                    <i class="fas fa-download"></i> تصدير السجلات
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <!-- CSP Test -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> اختبار Content Security Policy</h5>
                    </div>
                    <div class="card-body">
                        <div id="csp-results">
                            <div class="test-result test-info">
                                <i class="fas fa-clock"></i> في انتظار التشغيل...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proxy Test -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-server"></i> اختبار Smart Proxy</h5>
                    </div>
                    <div class="card-body">
                        <div id="proxy-results">
                            <div class="test-result test-info">
                                <i class="fas fa-clock"></i> في انتظار التشغيل...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CORS Test -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-globe"></i> اختبار CORS Headers</h5>
                    </div>
                    <div class="card-body">
                        <div id="cors-results">
                            <div class="test-result test-info">
                                <i class="fas fa-clock"></i> في انتظار التشغيل...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Test -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-video"></i> اختبار Video Element</h5>
                    </div>
                    <div class="card-body">
                        <div id="video-results">
                            <div class="test-result test-info">
                                <i class="fas fa-clock"></i> في انتظار التشغيل...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb"></i> التوصيات والحلول</h5>
                    </div>
                    <div class="card-body">
                        <div id="recommendations" class="recommendations">
                            <p><i class="fas fa-info-circle"></i> سيتم عرض التوصيات بعد تشغيل الاختبارات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Logs -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-terminal"></i> سجل الأحداث المباشر</h5>
                    </div>
                    <div class="card-body">
                        <div id="live-logs" class="log-container">
                            <div class="log-entry log-info">🔍 جاهز لبدء التشخيص...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info"></i> معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-info">
                            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                            <p><strong>URL:</strong> <span id="current-url"></span></p>
                            <p><strong>Protocol:</strong> <span id="protocol"></span></p>
                            <p><strong>Host:</strong> <span id="host"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/player/csp-fix.js"></script>
    <script src="/player/smart-proxy.js"></script>
    <script src="/player/streaming-diagnostics.js"></script>
    
    <script>
        // Initialize system info
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.host;

        // Live log display
        function addLogEntry(type, message) {
            const logsContainer = document.getElementById('live-logs');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(entry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // Update test results
        function updateTestResult(testId, success, message) {
            const container = document.getElementById(`${testId}-results`);
            const resultClass = success ? 'test-success' : 'test-error';
            const icon = success ? 'fas fa-check-circle' : 'fas fa-times-circle';
            
            container.innerHTML = `
                <div class="test-result ${resultClass}">
                    <i class="${icon}"></i> ${message}
                </div>
            `;
        }

        // Run tests button
        document.getElementById('runTests').addEventListener('click', function() {
            addLogEntry('info', '🔍 بدء تشغيل جميع الاختبارات...');
            
            // Reset results
            ['csp', 'proxy', 'cors', 'video'].forEach(test => {
                updateTestResult(test, null, 'جاري التشغيل...');
            });
            
            // Run diagnostics
            if (window.StreamingDiagnostics) {
                window.StreamingDiagnostics.runAllTests();
                
                // Monitor results
                setTimeout(() => {
                    const tests = window.StreamingDiagnostics.tests;
                    
                    // Update CSP results
                    if (tests.blobUrls !== undefined) {
                        updateTestResult('csp', tests.blobUrls, 
                            tests.blobUrls ? 'Blob URLs تعمل بشكل صحيح' : 'مشكلة في Blob URLs - تحقق من CSP');
                    }
                    
                    // Update Proxy results
                    if (tests.proxy !== undefined) {
                        updateTestResult('proxy', tests.proxy, 
                            tests.proxy ? 'Smart Proxy يعمل بشكل صحيح' : 'مشكلة في Smart Proxy - خطأ 403');
                    }
                    
                    // Update CORS results
                    if (tests.cors !== undefined) {
                        updateTestResult('cors', true, 'CORS Headers تم فحصها');
                    }
                    
                    // Update Video results
                    if (tests.videoBlob !== undefined) {
                        updateTestResult('video', tests.videoBlob, 
                            tests.videoBlob ? 'Video Element يعمل مع Blob URLs' : 'مشكلة في Video Element مع Blob URLs');
                    }
                    
                    // Update recommendations
                    const report = window.STREAMING_DIAGNOSTICS_REPORT;
                    if (report && report.recommendations.length > 0) {
                        const recommendationsHtml = report.recommendations.map(rec => 
                            `<li><i class="fas fa-arrow-right"></i> ${rec}</li>`
                        ).join('');
                        
                        document.getElementById('recommendations').innerHTML = `
                            <h6>التوصيات المقترحة:</h6>
                            <ul>${recommendationsHtml}</ul>
                        `;
                    } else {
                        document.getElementById('recommendations').innerHTML = `
                            <p><i class="fas fa-check-circle text-success"></i> جميع الاختبارات تعمل بشكل صحيح!</p>
                        `;
                    }
                }, 4000);
            }
        });

        // Export logs button
        document.getElementById('exportLogs').addEventListener('click', function() {
            if (window.StreamingDiagnostics) {
                window.StreamingDiagnostics.exportLogs();
                addLogEntry('info', '📁 تم تصدير السجلات');
            }
        });

        // Override console to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            addLogEntry('info', args.join(' '));
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            addLogEntry('error', args.join(' '));
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            addLogEntry('warning', args.join(' '));
            originalWarn.apply(console, args);
        };
    </script>
</body>
</html>
