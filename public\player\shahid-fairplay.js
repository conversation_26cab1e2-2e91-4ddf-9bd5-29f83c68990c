/**
 * Shahid FairPlay DRM Integration for iOS
 * Based on the provided license data
 */

window.ShahidFairPlay = {
    
    // Shahid DRM endpoints
    endpoints: {
        licenseUrl: "https://shahid-ga.la.drm.cloud/acquire-license/fairplay",
        certificateUrl: "https://shahid-ga.la.drm.cloud/certificate/fairplay",
        proxyUrl: "https://api3.shahid.net/proxy/v2.1/playout/new/drm"
    },

    // Brand GUID for Shahid
    brandGuid: "2be49af0-6fbd-4511-8e11-3d6523185bb4",

    // Required headers for Shahid API
    getShahidHeaders: function() {
        return {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en',
            'Authorization': '3ce470dbc332bb0c023a1670099f6b41e54528627bb15340bf6ad1180f9604cb',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Host': 'api3.shahid.net',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
            'Referer': 'https://shahid.mbc.net/'
        };
    },

    // Get DRM license data from Shahid
    getDRMLicense: async function(assetId, country = "EG") {
        console.log('🔐 Getting Shahid DRM license for asset:', assetId);
        
        const url = `${this.endpoints.proxyUrl}?request=%7B%22assetId%22:${assetId}%7D&country=${country}&ts=1755488387456`;
        
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: this.getShahidHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('✅ DRM license data received:', data);
            
            return data;
        } catch (error) {
            console.error('❌ Failed to get DRM license:', error);
            throw error;
        }
    },

    // Setup FairPlay for iOS with Shahid data
    setupShahidFairPlay: function(assetId, userToken) {
        console.log('🍎 Setting up Shahid FairPlay for iOS...');
        
        return {
            drm: {
                fairplay: {
                    // Certificate URL
                    certificateUrl: `${this.endpoints.certificateUrl}?BrandGuid=${this.brandGuid}`,
                    
                    // License URL with parameters
                    licenseUrl: this.buildLicenseUrl(assetId, userToken),
                    
                    // Content ID
                    contentId: assetId,
                    
                    // Certificate request headers
                    certificateRequestHeaders: {
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                        'Accept': 'application/octet-stream',
                        'Origin': 'https://shahid.mbc.net',
                        'Referer': 'https://shahid.mbc.net/'
                    },
                    
                    // License request headers
                    licenseRequestHeaders: {
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                        'Content-Type': 'application/octet-stream',
                        'Accept': 'application/octet-stream',
                        'Origin': 'https://shahid.mbc.net',
                        'Referer': 'https://shahid.mbc.net/'
                    },

                    // Custom license processing
                    licenseRequestFilter: this.processFairPlayLicense.bind(this),
                    
                    // Error handling
                    onLicenseRequest: function(request) {
                        console.log('📝 FairPlay license request:', request);
                    },
                    
                    onLicenseResponse: function(response) {
                        console.log('📨 FairPlay license response received');
                    }
                }
            }
        };
    },

    // Build license URL with KID and user token
    buildLicenseUrl: function(assetId, userToken, kid = null) {
        const baseUrl = this.endpoints.licenseUrl;
        const params = new URLSearchParams({
            'BrandGuid': this.brandGuid
        });
        
        if (kid) {
            params.append('KID', kid);
        }
        
        if (userToken) {
            params.append('UserToken', userToken);
        }
        
        return `${baseUrl}?${params.toString()}`;
    },

    // Process FairPlay license request
    processFairPlayLicense: function(request) {
        console.log('🔄 Processing FairPlay license request...');
        
        // Extract KID from the request if needed
        const kid = this.extractKIDFromRequest(request);
        if (kid) {
            console.log('🔑 Extracted KID:', kid);
        }
        
        // Return the request as-is or modify if needed
        return request;
    },

    // Extract KID from FairPlay request
    extractKIDFromRequest: function(request) {
        try {
            // FairPlay requests contain the KID in the SPC (Server Playback Context)
            // This is a simplified extraction - real implementation may vary
            const uint8Array = new Uint8Array(request);
            
            // Convert to hex string for analysis
            const hexString = Array.from(uint8Array)
                .map(byte => byte.toString(16).padStart(2, '0'))
                .join('');
            
            console.log('📊 FairPlay request hex (first 100 chars):', hexString.substring(0, 100));
            
            // Return null for now - KID extraction requires specific parsing
            return null;
        } catch (error) {
            console.error('❌ Failed to extract KID:', error);
            return null;
        }
    },

    // Create complete player config for Shahid content
    createPlayerConfig: function(hlsUrl, assetId, userToken, options = {}) {
        console.log('🎬 Creating Shahid player config...');
        
        const fairPlayConfig = this.setupShahidFairPlay(assetId, userToken);
        
        const config = {
            // Video source
            file: hlsUrl,
            
            // FairPlay DRM
            ...fairPlayConfig,
            
            // iOS optimizations
            hlsjsdefault: false, // Use native HLS on iOS
            preload: 'metadata',
            autostart: false,
            mute: true,
            playsinline: true,
            controls: true,
            
            // Disable unnecessary features
            sharing: false,
            related: false,
            logo: false,
            abouttext: '',
            aboutlink: '',
            
            // Custom styling
            skin: {
                name: 'shahid-ios',
                active: '#ff6b35',
                inactive: '#ffffff',
                background: '#000000'
            },
            
            // Additional options
            ...options,
            
            // Event handlers
            events: {
                onReady: function() {
                    console.log('✅ Shahid FairPlay player ready');
                },
                onPlay: function() {
                    console.log('▶️ Shahid content playback started');
                },
                onError: function(error) {
                    console.error('❌ Shahid player error:', error);
                    
                    // Handle specific FairPlay errors
                    if (error.code === 224003) {
                        console.log('🔄 FairPlay DRM error, attempting recovery...');
                        // Implement recovery logic
                    }
                },
                onSetupError: function(error) {
                    console.error('❌ Shahid setup error:', error);
                },
                ...options.events
            }
        };
        
        console.log('🎬 Shahid config created:', config);
        return config;
    },

    // Test function with sample data
    testWithSampleData: function() {
        console.log('🧪 Testing Shahid FairPlay with sample data...');
        
        const sampleAssetId = "4992343679206767";
        const sampleUserToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjEuNzU1NDg4Njg3RTksImRybVRva2VuSW5mbyI6eyJleHAiOiIyMDI1LTA4LTE4VDAzOjQ0OjQ3LjAwMDcyOCIsImtpZCI6WyIqIl0sInAiOnsicGVycyI6ZmFsc2UsImVkIjoiMjAyNS0wOC0xOFQwMzo0NDo0Ny4wMDA3MjgiLCJleGMiOnsiV2lkZXZpbmVDYW5SZW5ldyI6ZmFsc2UsIkZhaXJwbGF5UmVudGFsRHVyYXRpb25TZWNvbmRzIjo4NjAwMCwiRmFpcnBsYXlMZWFzZUR1cmF0aW9uU2Vjb25kcyI6ODYwMDB9fSwid2lkZXZpbmUiOnsiZGlzYWJsZV9hbmFsb2ciOnRydWUsImhkY3AiOiJIRENQX1YyXzIiLCJjZ21zIjoiQ09QWV9ORVZFUiJ9LCJmYWlycGxheSI6eyJoZGNwIjp0cnVlfSwicGxheXJlYWR5Ijp7InVuY29tcHJlc3NlZF9kaWdpdGFsX2F1ZGlvX29wbCI6MzAwLCJ1bmNvbXByZXNzZWRfZGlnaXRhbF92aWRlb19vcGwiOjMwMCwiY29tcHJlc3NlZF9kaWdpdGFsX2F1ZGlvX29wbCI6MzAwLCJjb21wcmVzc2VkX2RpZ2l0YWxfdmlkZW9fb3BsIjo1MDB9fX0.P9XXFrD9ua6exgcT3ZwOMk6mmkk9oDES9cFtNY6vTJI";
        const sampleHLS = "https://example.com/video.m3u8"; // Replace with actual HLS URL
        
        return this.createPlayerConfig(sampleHLS, sampleAssetId, sampleUserToken);
    },

    // Initialize Shahid FairPlay support
    init: function() {
        console.log('🍎 Shahid FairPlay support initialized');
        console.log('🔗 Certificate URL:', `${this.endpoints.certificateUrl}?BrandGuid=${this.brandGuid}`);
        console.log('🔗 License URL:', this.endpoints.licenseUrl);
    }
};

// Auto-initialize
document.addEventListener('DOMContentLoaded', function() {
    window.ShahidFairPlay.init();
});

console.log('🍎 Shahid FairPlay integration loaded');
