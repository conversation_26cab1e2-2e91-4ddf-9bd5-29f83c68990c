<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Security Headers for Video Streaming
    <IfModule mod_headers.c>
        # Enhanced Content Security Policy for video streaming
        Header always set Content-Security-Policy "default-src 'self' https: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; style-src 'self' 'unsafe-inline' https:; img-src 'self' https: data: blob:; media-src 'self' https: data: blob:; connect-src 'self' https: wss: ws:; font-src 'self' https: data:; object-src 'none'; base-uri 'self'; frame-ancestors 'self'; worker-src 'self' blob: data:;"

        # CORS Headers for video streaming
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD"
        Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma"
        Header always set Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges"
        Header always set Access-Control-Allow-Credentials "true"

        # Video streaming optimizations
        Header always set X-Content-Type-Options "nosniff"
        Header always set X-Frame-Options "SAMEORIGIN"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"

        # Cache control for video files
        <FilesMatch "\.(mp4|webm|ogg|avi|mov|m4v|3gp|m3u8|ts)$">
            Header set Cache-Control "public, max-age=31536000"
            Header set Accept-Ranges "bytes"
        </FilesMatch>

        # Handle preflight OPTIONS requests
        RewriteCond %{REQUEST_METHOD} OPTIONS
        RewriteRule ^(.*)$ $1 [R=200,L]
    </IfModule>

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>