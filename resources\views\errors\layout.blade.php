@extends('admin.layouts.app')

@section('title', 'Error - ' . ($__env->yieldContent('code') ?: 'Unknown'))

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <div class="error-icon mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #6c757d, #495057); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="@yield('icon', 'fas fa-exclamation-circle') text-white" style="font-size: 60px;"></i>
                        </div>
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-secondary mb-3">@yield('code', 'Error')</h1>
                    
                    <!-- Error Title -->
                    <h2 class="h3 text-dark mb-3">@yield('title', 'Something went wrong')</h2>
                    
                    <!-- Error Message -->
                    <p class="text-muted mb-4 lead">
                        @yield('message', 'An unexpected error occurred. Please try again later.')
                    </p>

                    <!-- Additional Content -->
                    @yield('additional-content')

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <button onclick="history.back()" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary px-4">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                        @yield('additional-buttons')
                    </div>

                    <!-- Footer Info -->
                    <div class="mt-4 pt-4 border-top">
                        <small class="text-muted">
                            @yield('footer-info', 'If this problem persists, please contact support.')
                        </small>
                    </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: @yield('animation', 'pulse') 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}



.btn {
    border-radius: 8px;
    font-weight: 500;
}

@yield('additional-styles')
</style>

@yield('additional-scripts')
@endsection
