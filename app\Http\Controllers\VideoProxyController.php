<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\ProxyService;

class VideoProxyController extends Controller
{
    protected $proxyService;

    public function __construct(ProxyService $proxyService)
    {
        $this->proxyService = $proxyService;
    }

    private $allowedDomains = [
        'mbcvod-enc.edgenextcdn.net',
        'shls-live-enc.edgenextcdn.net',
        'shd-gcp-live.edgenextcdn.net',  // Added for live channels
        'shd-live.edgenextcdn.net',
        'ssc-news-live-enc.edgenextcdn.net',     // Additional live domain
        'edgenextcdn.net',               // General EdgeNext CDN
        'shahid.mbc.net',
        'cdn.shahid.net',
        'mbc.net',
        'cdxaws-ak.akamaized.net',
        'akamaized.net',
        'amazonaws.com',
        'cloudfront.net',
        'httpbin.org'
    ];

    private $shahidHeaders = [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept' => '*/*',
        'Accept-Language' => 'ar,en-US;q=0.9,en;q=0.8',
        'Accept-Encoding' => 'gzip, deflate, br',
        'Origin' => 'https://shahid.mbc.net',
        'Referer' => 'https://shahid.mbc.net/',
        'x-forwarded-for' => '*************',
        'cf-ipcountry' => 'SA',
        'language' => 'ar',
        'Connection' => 'keep-alive',
        'Cache-Control' => 'no-cache'
    ];

    // Cache settings for different content types
    private $cacheSettings = [
        'manifest' => 10,      // 10 seconds for manifests (shorter for live channels)
        'init' => 3600,        // 1 hour for init segments
        'segment' => 300,      // 5 minutes for video segments
        'subtitle' => 1800     // 30 minutes for subtitles
    ];

    /**
     * Proxy manifest files (MPD/M3U8)
     */
    public function manifest(Request $request)
    {
        $url = $request->query('url');

        if (!$url) {
            return response('URL parameter required', 400, $this->getCorsHeaders());
        }

        if (!$this->isAllowedDomain($url)) {
            Log::warning("VideoProxy: Domain not allowed", [
                'url' => $url,
                'parsed_host' => parse_url($url)['host'] ?? 'unknown',
                'allowed_domains' => $this->allowedDomains
            ]);
            return response('Domain not allowed', 403, $this->getCorsHeaders());
        }

        try {
            // Check cache first
            $cacheKey = 'manifest_' . md5($url);
            $cached = Cache::get($cacheKey);

            if ($cached) {
                return response($cached['content'], 200, array_merge([
                    'Content-Type' => $cached['content_type'],
                    'Cache-Control' => 'public, max-age=10'  // Shorter cache for live content
                ], $this->getCorsHeaders()));
            }

            // Make request directly without ProxyService (for manifests)
            $response = Http::withOptions([
                'timeout' => 20,
                'connect_timeout' => 5
            ])->withHeaders($this->shahidHeaders)
                ->retry(2, 100)         // Retry on failure
                ->get($url);

            if (!$response->successful()) {
                Log::error("Manifest proxy failed: {$response->status()}");
                return response('Proxy error', $response->status(), $this->getCorsHeaders());
            }

            $content = $response->body();

            // Transform URLs in manifest to use our proxy
            $content = $this->transformManifestUrls($content, $url);

            $contentType = $response->header('Content-Type') ?: 'application/dash+xml';

            // Cache the manifest for 30 seconds
            Cache::put($cacheKey, [
                'content' => $content,
                'content_type' => $contentType
            ], $this->cacheSettings['manifest']);

            return response($content, 200, array_merge([
                'Content-Type' => $contentType,
                'Cache-Control' => 'public, max-age=10'  // Shorter cache for live content
            ], $this->getCorsHeaders()));

        } catch (\Exception $e) {
            Log::error('Manifest proxy error: ' . $e->getMessage());
            return response('Proxy error: ' . $e->getMessage(), 500, [
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, HEAD, OPTIONS',
                'Access-Control-Allow-Headers' => 'Range, Content-Type, Authorization'
            ]);
        }
    }

    /**
     * Proxy video segments and initialization files
     */
    public function segment(Request $request, $path)
    {
        try {
            // Decode the path with safe validation
            $decodedPath = $this->safeBase64Decode($path);

            // Fallback: handle appended relative paths like /video-proxy/segment/{base64}/index_26_0.m3u8
            if ($decodedPath === false && strpos($path, '/') !== false) {
                [$encodedBase, $extra] = explode('/', $path, 2);
                $baseUrl = $this->safeBase64Decode($encodedBase);
                if ($baseUrl !== false && filter_var($baseUrl, FILTER_VALIDATE_URL)) {
                    // Resolve the extra part relative to the base URL
                    if (preg_match('/^https?:\/\//i', $extra)) {
                        $decodedPath = $extra;
                    } else {
                        $decodedPath = $this->resolveRelativeUrl($baseUrl, $extra);
                    }
                }
            }

            if ($decodedPath === false) {
                Log::error("Invalid base64 path encoding", [
                    'path' => substr($path, 0, 100) . '...',
                    'path_length' => strlen($path),
                    'path_full' => $path,
                    'user_agent' => $request->header('User-Agent'),
                    'referer' => $request->header('Referer')
                ]);
                return response()->json([
                    'error' => 'Invalid base64 encoding',
                    'details' => 'The provided path could not be decoded',
                    'path_length' => strlen($path)
                ], 400, $this->getCorsHeaders());
            }

            // Validate that decoded path is a valid URL
            if (!filter_var($decodedPath, FILTER_VALIDATE_URL)) {
                Log::error("Invalid URL after decoding", [
                    'decodedPath' => substr($decodedPath, 0, 200) . '...',
                    'path' => substr($path, 0, 100) . '...'
                ]);
                return response('Invalid URL format', 400, $this->getCorsHeaders());
            }

            if (!$this->isAllowedDomain($decodedPath)) {
                return response('Domain not allowed', 403, $this->getCorsHeaders());
            }

            // Check cache first for non-range requests
            $cacheKey = 'video_segment_' . md5($decodedPath);
            $rangeHeader = $request->header('Range');

            if (!$rangeHeader && $this->shouldCache($decodedPath)) {
                $cached = Cache::get($cacheKey);
                if ($cached) {
                    return response($cached['body'], 200, array_merge($cached['headers'], $this->getCorsHeaders()));
                }
            }

            // Prepare headers for request
            $headers = $this->shahidHeaders;
            if ($rangeHeader) {
                $headers['Range'] = $rangeHeader;
            }

            Log::info("Making request to segment", [
                'url' => substr($decodedPath, 0, 200) . '...',
                'has_range' => !empty($rangeHeader)
            ]);

            // Make request directly without ProxyService (for video segments)
            try {
                $response = Http::withOptions([
                    'timeout' => 30,
                    'connect_timeout' => 10,
                    'verify' => false  // Disable SSL verification if needed
                ])->withHeaders($headers)
                    ->retry(2, 100)         // Retry on failure
                    ->get($decodedPath);
            } catch (\Exception $httpException) {
                Log::error("HTTP request failed", [
                    'url' => substr($decodedPath, 0, 100) . '...',
                    'error' => $httpException->getMessage(),
                    'type' => get_class($httpException)
                ]);
                return response('Network error: ' . $httpException->getMessage(), 500, $this->getCorsHeaders());
            }

            if (!$response->successful()) {
                Log::error("Segment proxy failed", [
                    'url' => substr($decodedPath, 0, 100) . '...',
                    'status' => $response->status(),
                    'response_body' => substr($response->body(), 0, 200)
                ]);
                return response('Proxy error', $response->status(), $this->getCorsHeaders());
            }

            // Prepare response headers
            $responseHeaders = [
                'Content-Type' => $response->header('Content-Type') ?: 'video/mp4',
                'Accept-Ranges' => 'bytes',
                'Cache-Control' => 'public, max-age=3600',
                'Expires' => gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT'
            ];

            // Forward range response headers
            if ($response->header('Content-Range')) {
                $responseHeaders['Content-Range'] = $response->header('Content-Range');
            }
            if ($response->header('Content-Length')) {
                $responseHeaders['Content-Length'] = $response->header('Content-Length');
            }

            $statusCode = $response->status();
            $body = $response->body();

            // Transform M3U8 content if this is a playlist file
            if ($statusCode === 200 && (strpos($decodedPath, '.m3u8') !== false ||
                strpos($response->header('Content-Type', ''), 'application/vnd.apple.mpegurl') !== false ||
                strpos($response->header('Content-Type', ''), 'application/x-mpegURL') !== false)) {

                Log::info("Transforming M3U8 segment content", ['url' => substr($decodedPath, 0, 100) . '...']);
                $body = $this->transformManifestUrls($body, $decodedPath);
            }

            // Cache successful non-range responses
            if (!$rangeHeader && $statusCode === 200 && $this->shouldCache($decodedPath)) {
                $cacheData = [
                    'body' => $body,
                    'headers' => $responseHeaders
                ];
                Cache::put($cacheKey, $cacheData, $this->getCacheDuration($decodedPath));
            }

            // Add CORS headers
            $responseHeaders = array_merge($responseHeaders, $this->getCorsHeaders());

            return response($body, $statusCode, $responseHeaders);

        } catch (\Exception $e) {
            Log::error('Segment proxy error', [
                'path' => substr($path, 0, 50) . '...',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response('Proxy error: ' . $e->getMessage(), 500, $this->getCorsHeaders());
        }
    }

    /**
     * Get CORS headers
     */
    private function getCorsHeaders()
    {
        return [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, HEAD, OPTIONS',
            'Access-Control-Allow-Headers' => 'Range, Content-Type, Authorization',
            'Access-Control-Expose-Headers' => 'Content-Range, Content-Length, Accept-Ranges'
        ];
    }

    /**
     * Check if content should be cached
     */
    private function shouldCache($url)
    {
        // Cache init segments and small files
        if (strpos($url, 'init') !== false) {
            return true;
        }

        // Cache subtitle files
        if (strpos($url, '.vtt') !== false || strpos($url, '.srt') !== false) {
            return true;
        }

        // Cache small video segments (less than 5MB estimated)
        if (preg_match('/segment_\d+\.m4s$/', $url) || preg_match('/chunk_\d+\.mp4$/', $url)) {
            return true;
        }

        return false;
    }

    /**
     * Get cache duration based on content type
     */
    private function getCacheDuration($url)
    {
        if (strpos($url, 'init') !== false) {
            return $this->cacheSettings['init'];
        }

        if (strpos($url, '.vtt') !== false || strpos($url, '.srt') !== false) {
            return $this->cacheSettings['subtitle'];
        }

        return $this->cacheSettings['segment'];
    }

    /**
     * Clear video cache
     */
    public function clearCache(Request $request)
    {
        try {
            // Clear all video-related cache
            Cache::flush();

            return response()->json([
                'success' => true,
                'message' => 'Video cache cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cache statistics
     */
    public function cacheStats(Request $request)
    {
        try {
            // This is a basic implementation - you might want to use Redis for better stats
            return response()->json([
                'success' => true,
                'cache_enabled' => true,
                'cache_driver' => config('cache.default'),
                'settings' => $this->cacheSettings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get cache stats: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Transform URLs in manifest to use our proxy
     */
    private function transformManifestUrls($content, $baseUrl)
    {
        $baseUrlParts = parse_url($baseUrl);

        if (!$baseUrlParts || !isset($baseUrlParts['scheme']) || !isset($baseUrlParts['host'])) {
            Log::error("Invalid base URL for manifest transformation", [
                'baseUrl' => $baseUrl,
                'parsed' => $baseUrlParts
            ]);
            return $content; // Return unchanged if base URL is invalid
        }

        $baseHost = $baseUrlParts['scheme'] . '://' . $baseUrlParts['host'];
        $basePath = dirname($baseUrlParts['path'] ?? '/');

        Log::info("Transforming manifest URLs", [
            'baseUrl' => $baseUrl,
            'baseHost' => $baseHost,
            'basePath' => $basePath,
            'parsed_parts' => $baseUrlParts
        ]);

        // Transform relative URLs to absolute, then to proxy URLs
        // Handle DASH (MPD) files
        $content = preg_replace_callback(
            '/(?:initialization|media)="([^"]+)"/',
            function ($matches) use ($baseHost, $basePath) {
                $url = $matches[1];
                $originalUrl = $url;

                // Handle template URLs specially
                if (strpos($url, '$') !== false) {
                    Log::info("Processing template URL", ['url' => $url]);

                    // Make absolute URL first
                    $absoluteUrl = $url;
                    if (!preg_match('/^https?:\/\//', $url)) {
                        if (strpos($url, '/') === 0) {
                            $absoluteUrl = $baseHost . $url;
                        } else {
                            $absoluteUrl = $this->resolveRelativeUrl($baseHost . $basePath, $url);
                        }
                    }

                    // Create template proxy URL - ensure clean base URL
                    $cleanBaseUrl = $baseHost . $basePath;

                    // Validate base URL before encoding
                    if (!filter_var($cleanBaseUrl, FILTER_VALIDATE_URL) && !preg_match('/^https?:\/\/[^\/]+/', $cleanBaseUrl)) {
                        Log::error("Invalid base URL for template", [
                            'baseHost' => $baseHost,
                            'basePath' => $basePath,
                            'cleanBaseUrl' => $cleanBaseUrl
                        ]);
                        return $matches[0]; // Return unchanged if invalid
                    }

                    // Use URL-safe base64 encoding for better compatibility
                    $encodedBase = rtrim(strtr(base64_encode($cleanBaseUrl), '+/', '-_'), '=');
                    $encodedTemplate = rtrim(strtr(base64_encode($originalUrl), '+/', '-_'), '=');

                    // Validate base64 encoding with fallback
                    $testBase = $this->safeBase64Decode($encodedBase);
                    $testTemplate = $this->safeBase64Decode($encodedTemplate);

                    if ($testBase === false || $testTemplate === false) {
                        Log::warning("Base64 encoding validation failed, trying standard encoding", [
                            'cleanBaseUrl' => $cleanBaseUrl,
                            'originalUrl' => $originalUrl,
                            'encodedBase' => $encodedBase,
                            'encodedTemplate' => $encodedTemplate
                        ]);

                        // Fallback to standard base64
                        $encodedBase = base64_encode($cleanBaseUrl);
                        $encodedTemplate = base64_encode($originalUrl);

                        if ($this->safeBase64Decode($encodedBase) === false || $this->safeBase64Decode($encodedTemplate) === false) {
                            Log::error("Both base64 encoding methods failed", [
                                'cleanBaseUrl' => $cleanBaseUrl,
                                'originalUrl' => $originalUrl
                            ]);
                            return $matches[0]; // Return unchanged if encoding fails
                        }
                    }

                    $templateUrl = url("/video-proxy/template/{$encodedBase}/{$encodedTemplate}/\$Number\$");

                    Log::info("Template URL transformation", [
                        'original' => $originalUrl,
                        'absolute' => $absoluteUrl,
                        'cleanBaseUrl' => $cleanBaseUrl,
                        'encodedBase' => $encodedBase,
                        'encodedTemplate' => $encodedTemplate,
                        'template' => $templateUrl
                    ]);

                    return str_replace($matches[1], $templateUrl, $matches[0]);
                }

                // Make absolute URL
                if (!preg_match('/^https?:\/\//', $url)) {
                    if (strpos($url, '/') === 0) {
                        // Absolute path
                        $url = $baseHost . $url;
                    } else {
                        // Relative path - resolve properly
                        $url = $this->resolveRelativeUrl($baseHost . $basePath, $url);
                    }
                }

                // Validate URL before encoding
                if (!filter_var($url, FILTER_VALIDATE_URL)) {
                    Log::error("Invalid URL after transformation", [
                        'original' => $originalUrl,
                        'transformed' => $url
                    ]);
                    return $matches[0]; // Return unchanged if invalid
                }

                // Convert to proxy URL
                $encodedUrl = base64_encode($url);

                // Validate encoding
                if ($this->safeBase64Decode($encodedUrl) === false) {
                    Log::error("Base64 encoding validation failed for URL", [
                        'url' => $url,
                        'encoded' => $encodedUrl
                    ]);
                    return $matches[0]; // Return unchanged if encoding fails
                }

                $proxyUrl = url("/video-proxy/segment/{$encodedUrl}");

                Log::info("URL transformation", [
                    'original' => $originalUrl,
                    'absolute' => $url,
                    'proxy' => $proxyUrl
                ]);

                return str_replace($matches[1], $proxyUrl, $matches[0]);
            },
            $content
        );

        // Also handle BaseURL elements
        $content = preg_replace_callback(
            '/<BaseURL>([^<]+)<\/BaseURL>/',
            function ($matches) use ($baseHost, $basePath) {
                $url = $matches[1];
                $originalUrl = $url;

                // Make absolute URL
                if (!preg_match('/^https?:\/\//', $url)) {
                    if (strpos($url, '/') === 0) {
                        // Absolute path
                        $url = $baseHost . $url;
                    } else {
                        // Relative path - resolve properly
                        $url = $this->resolveRelativeUrl($baseHost . $basePath, $url);
                    }
                }

                // Convert to proxy URL
                $encodedUrl = base64_encode($url);

                // Validate encoding
                if ($this->safeBase64Decode($encodedUrl) === false) {
                    Log::error("Base64 encoding validation failed for BaseURL", [
                        'url' => $url,
                        'encoded' => $encodedUrl
                    ]);
                    return $matches[0]; // Return unchanged if encoding fails
                }

                $proxyUrl = url("/video-proxy/segment/{$encodedUrl}");

                Log::info("BaseURL transformation", [
                    'original' => $originalUrl,
                    'absolute' => $url,
                    'proxy' => $proxyUrl
                ]);

                return "<BaseURL>{$proxyUrl}</BaseURL>";
            },
            $content
        );

        // Handle HLS (M3U8) files - transform segment and subtitle URLs
        $content = preg_replace_callback(
            '/^(?!#)([^\r\n]+\.(?:ts|m4s|mp4|m3u8|vtt))(\?[^\r\n]*)?$/m',
            function ($matches) use ($baseHost, $basePath) {
                $url = $matches[1] . ($matches[2] ?? '');
                $originalUrl = $url;

                // Make absolute URL
                if (!preg_match('/^https?:\/\//', $url)) {
                    if (strpos($url, '/') === 0) {
                        // Absolute path
                        $url = $baseHost . $url;
                    } else {
                        // Relative path - resolve properly
                        $url = $this->resolveRelativeUrl($baseHost . $basePath, $url);
                    }
                }

                // Convert to proxy URL
                $encodedUrl = base64_encode($url);

                // Validate encoding
                if ($this->safeBase64Decode($encodedUrl) === false) {
                    Log::error("Base64 encoding validation failed for HLS URL", [
                        'url' => $url,
                        'encoded' => $encodedUrl
                    ]);
                    return $matches[0]; // Return unchanged if encoding fails
                }

                $proxyUrl = url("/video-proxy/segment/{$encodedUrl}");

                Log::info("HLS URL transformation", [
                    'original' => $originalUrl,
                    'absolute' => $url,
                    'proxy' => $proxyUrl
                ]);

                return $proxyUrl;
            },
            $content
        );

        // Handle HLS EXT-X-MAP URIs (init segments)
        $content = preg_replace_callback(
            '/#EXT-X-MAP:([^\n]*?)URI="([^"]+)"/i',
            function ($matches) use ($baseHost, $basePath) {
                $attrs = $matches[1];
                $uri = $matches[2];

                // Make absolute URL
                if (!preg_match('/^https?:\/\//', $uri)) {
                    if (strpos($uri, '/') === 0) {
                        $uri = $baseHost . $uri;
                    } else {
                        $uri = $this->resolveRelativeUrl($baseHost . $basePath, $uri);
                    }
                }

                // Encode and proxy
                $encodedUrl = base64_encode($uri);
                if ($this->safeBase64Decode($encodedUrl) === false) {
                    return $matches[0];
                }
                $proxyUrl = url("/video-proxy/segment/{$encodedUrl}");
                return "#EXT-X-MAP:{$attrs}URI=\"{$proxyUrl}\"";
            },
            $content
        );

        // Handle EXT-X-MEDIA entries with URI (subtitles playlists)
        $content = preg_replace_callback(
            '/#EXT-X-MEDIA:([^\n]*?)URI="([^"]+)"/i',
            function ($matches) use ($baseHost, $basePath) {
                $attrs = $matches[1];
                $uri = $matches[2];

                if (!preg_match('/^https?:\/\//', $uri)) {
                    if (strpos($uri, '/') === 0) {
                        $uri = $baseHost . $uri;
                    } else {
                        $uri = $this->resolveRelativeUrl($baseHost . $basePath, $uri);
                    }
                }

                $encodedUrl = base64_encode($uri);
                if ($this->safeBase64Decode($encodedUrl) === false) {
                    return $matches[0];
                }
                $proxyUrl = url("/video-proxy/segment/{$encodedUrl}");
                return "#EXT-X-MEDIA:{$attrs}URI=\"{$proxyUrl}\"";
            },
            $content
        );

        return $content;
    }

    /**
     * Resolve relative URLs properly
     */
    private function resolveRelativeUrl($base, $relative)
    {
        // Validate inputs first
        if (empty($base) || empty($relative)) {
            Log::error("Empty base or relative URL", ['base' => $base, 'relative' => $relative]);
            return $base;
        }

        // Clean inputs to remove any invalid characters
        $base = filter_var($base, FILTER_SANITIZE_URL);
        $relative = filter_var($relative, FILTER_SANITIZE_URL);

        // Remove query string and fragment from base
        $base = preg_replace('/[?#].*/', '', $base);

        // If relative URL starts with ./ remove it
        $relative = preg_replace('/^\.\//', '', $relative);

        // Handle ../ in relative URL
        $originalRelative = $relative;
        while (strpos($relative, '../') === 0) {
            $relative = substr($relative, 3);
            $base = dirname($base);

            // Prevent going above the domain root
            $baseParts = parse_url($base);
            if (!isset($baseParts['path']) || $baseParts['path'] === '' || $baseParts['path'] === '/') {
                $base = $baseParts['scheme'] . '://' . $baseParts['host'];
                break;
            }
        }

        // Combine base and relative
        $result = rtrim($base, '/') . '/' . ltrim($relative, '/');

        // Validate the result
        if (!filter_var($result, FILTER_VALIDATE_URL)) {
            Log::error("Invalid URL generated during resolution", [
                'base' => $base,
                'relative' => $originalRelative,
                'result' => $result
            ]);

            // Fallback: try a simpler approach
            $baseParts = parse_url($base);
            if (isset($baseParts['scheme']) && isset($baseParts['host'])) {
                $result = $baseParts['scheme'] . '://' . $baseParts['host'] . '/' . ltrim($originalRelative, '/');
            } else {
                Log::error("Cannot parse base URL", ['base' => $base]);
                return $base; // Return original base if we can't parse it
            }
        }

        Log::info("Relative URL resolution", [
            'original_base' => func_get_args()[0],
            'original_relative' => $originalRelative,
            'final_base' => $base,
            'final_relative' => $relative,
            'result' => $result,
            'is_valid' => filter_var($result, FILTER_VALIDATE_URL) !== false
        ]);

        return $result;
    }

    /**
     * Check if domain is allowed
     */
    private function isAllowedDomain($url)
    {
        // Clean the URL first - remove any invalid characters
        $cleanUrl = filter_var($url, FILTER_SANITIZE_URL);

        $parsedUrl = parse_url($cleanUrl);

        if (!isset($parsedUrl['host'])) {
            Log::debug("VideoProxy: No host in URL", [
                'original_url' => substr($url, 0, 200) . '...',
                'clean_url' => substr($cleanUrl, 0, 200) . '...',
                'parsed' => $parsedUrl
            ]);
            return false;
        }

        $host = $parsedUrl['host'];

        foreach ($this->allowedDomains as $domain) {
            if (strpos($host, $domain) !== false) {
                Log::debug("VideoProxy: Domain allowed", ['host' => $host, 'matched_domain' => $domain]);
                return true;
            }
        }

        Log::debug("VideoProxy: Domain not in allowed list", [
            'host' => $host,
            'allowed_domains' => $this->allowedDomains
        ]);
        return false;
    }

    /**
     * Validate and safely decode base64
     */
    private function safeBase64Decode($data)
    {
        // Clean the data first - handle URL-safe base64
        $data = str_replace(['-', '_'], ['+', '/'], $data);

        // Add padding if needed
        $padding = strlen($data) % 4;
        if ($padding) {
            $data .= str_repeat('=', 4 - $padding);
        }

        // Check if the string is valid base64 (more permissive)
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $data)) {
            Log::warning("VideoProxy: Base64 regex validation failed", [
                'data' => substr($data, 0, 100),
                'length' => strlen($data)
            ]);
            return false;
        }

        $decoded = base64_decode($data, true);

        // Additional validation - check if the decoded data makes sense
        if ($decoded === false || empty($decoded)) {
            Log::warning("VideoProxy: Base64 decode failed", [
                'data' => substr($data, 0, 100)
            ]);
            return false;
        }

        // More lenient encoding check - allow common URL characters
        if (!mb_check_encoding($decoded, 'UTF-8') && !preg_match('/^[\x20-\x7E]*$/', $decoded)) {
            Log::warning("VideoProxy: Encoding validation failed", [
                'decoded' => substr($decoded, 0, 100)
            ]);
            return false;
        }

        return $decoded;
    }

    /**
     * Handle OPTIONS requests for CORS
     */
    public function options()
    {
        return response('', 200, [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, HEAD, OPTIONS',
            'Access-Control-Allow-Headers' => 'Range, Content-Type, Authorization',
            'Access-Control-Max-Age' => '86400'
        ]);
    }
}
