/* Landing Page Additional Styles - Dashboard Theme */

/* Dashboard Color Variables Override */
:root {
    /* Dashboard Colors */
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* Dashboard Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(45deg, #007bff, #0056b3);
    --gradient-success: linear-gradient(45deg, #28a745, #20c997);
    --gradient-danger: linear-gradient(45deg, #dc3545, #c82333);
    --gradient-play: linear-gradient(45deg, #ff6b6b, #ee5a24);
    --gradient-dark: linear-gradient(135deg, #343a40 0%, #212529 100%);

    /* Text Colors */
    --text-light: #ffffff;
    --text-gray: #6c757d;
    --text-muted: #868e96;

    /* Background Colors */
    --dark-bg: #212529;
    --card-bg: #ffffff;
    --overlay-bg: rgba(52, 58, 64, 0.9);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
    --shadow-xl: 0 12px 24px rgba(0,0,0,0.25);
    --shadow-hover: 0 15px 35px rgba(0,0,0,0.2);
}

/* Advanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hero Section Enhancements */
.hero-visual {
    animation: float 6s ease-in-out infinite;
}

.hero-card:nth-child(1) {
    animation-delay: 0s;
}

.hero-card:nth-child(2) {
    animation-delay: 0.2s;
}

.hero-card:nth-child(3) {
    animation-delay: 0.4s;
}

.hero-card:nth-child(4) {
    animation-delay: 0.6s;
}

/* Button Enhancements */
.btn-primary {
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Feature Cards Advanced Effects */
.feature-card {
    position: relative;
    overflow: hidden;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(229, 9, 20, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover::after {
    opacity: 1;
}

.feature-icon {
    position: relative;
    z-index: 2;
}

.feature-card:hover .feature-icon {
    animation: pulse 1s ease-in-out;
}

/* Scroll Indicators */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.1s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #b20710;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--dark-bg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-logo {
    font-size: 3rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
    animation: pulse 2s ease-in-out infinite;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(229, 9, 20, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 1rem;
    color: var(--text-gray);
    font-size: 1.1rem;
}

/* Particle Background */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.3;
    animation: particleFloat 10s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Enhanced Mobile Styles */
@media (max-width: 768px) {
    .hero-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-card {
        padding: 1rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Dark Mode Toggle (if needed) */
.theme-toggle {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--card-bg);
    border: none;
    border-radius: 50%;
    color: var(--text-light);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.theme-toggle:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for accessibility */
.btn:focus,
.nav-menu a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-gray: #ffffff;
        --card-bg: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .btn,
    .social-links {
        display: none;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .hero,
    .features,
    .cta {
        page-break-inside: avoid;
    }
}
