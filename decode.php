<?php

$encoded = 'aHR0cHM6Ly9tYmN2b2QtZW5jLmVkZ2VuZXh0Y2RuLm5ldC9vdXQvdjEvNmM1NWI4Zjc1YzcyNDRhMWIxMDEwOGY1ODc1YmM3ZTgvMjIxNjYzMzNkNTFhNGQ1Mjg0NzgxNDRjYzMwZmMwNDgvNjVhZWUxNmIyNzc1NDI5ZThhZGQzOWM1NGY3YjY5OTUvLi4vYmU4ODhkZjA3ZTc0NGM0NTk0NjY5Mzg4MmJkYjM1NGYvMDdmZjk4MzVjNDI0NGI3YTk4OTcwN2UyYzhjNmE1YTUvaW5kZXhfYXVkaW9fOV8wXyROdW1iZXIkLm1wNA==';

$decoded = base64_decode($encoded);

echo "Encoded: " . $encoded . "\n";
echo "Decoded: " . $decoded . "\n";
echo "Valid URL: " . (filter_var($decoded, FILTER_VALIDATE_URL) ? 'Yes' : 'No') . "\n";

// Test resolving relative URL
function resolveRelativeUrl($base, $relative) {
    // Remove query string and fragment from base
    $base = preg_replace('/[?#].*/', '', $base);
    
    // If relative URL starts with ./ remove it
    $relative = preg_replace('/^\.\//', '', $relative);
    
    // Handle ../ in relative URL
    while (strpos($relative, '../') === 0) {
        $relative = substr($relative, 3);
        $base = dirname($base);
    }
    
    // Combine base and relative
    $result = rtrim($base, '/') . '/' . ltrim($relative, '/');
    
    return $result;
}

// Test with the problematic URL
$baseUrl = 'https://mbcvod-enc.edgenextcdn.net/out/v1/6c55b8f75c7244a1b10108f5875bc7e8/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd';
$relativeUrl = '../be888df07e744c45946693882bdb354f/07ff9835c4244b7a989707e2c8c6a5a5/index_audio_9_0_$Number$.mp4';

$resolved = resolveRelativeUrl(dirname($baseUrl), $relativeUrl);

echo "\nBase URL: " . $baseUrl . "\n";
echo "Base Dir: " . dirname($baseUrl) . "\n";
echo "Relative: " . $relativeUrl . "\n";
echo "Resolved: " . $resolved . "\n";
