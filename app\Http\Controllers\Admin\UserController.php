<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
        // Allow both web and api middleware groups
        $this->middleware('web')->only(['index', 'store', 'show', 'update', 'destroy', 'toggleStatus', 'resetPassword', 'statistics']);
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): JsonResponse
    {
        // Check if user has permission to view users
        if (!auth('admin')->user()->hasPermission('view users')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to view users'
            ], 403);
        }
        $query = User::where('user_type', 'user') // Only show regular users, not admins
                    ->where('is_admin', false);   // Exclude admin users

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('country', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->get('status') === 'active');
        }

        // Filter by country
        if ($request->filled('country')) {
            $query->where('country', $request->get('country'));
        }

        // Filter by subscription status
        if ($request->has('subscription_status')) {
            $status = $request->get('subscription_status');
            switch ($status) {
                case 'active':
                    $query->whereHas('activeSubscription');
                    break;
                case 'expired':
                    $query->where('subscription_expiry', '<=', now());
                    break;
                case 'none':
                    $query->whereDoesntHave('subscriptions');
                    break;
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $users = $query->with(['activeSubscription.subscriptionPlan', 'subscriptions.subscriptionPlan'])
                       ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $users,
            'message' => 'Users retrieved successfully'
        ]);
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request): JsonResponse
    {
        // Check if user has permission to create users
        if (!auth('admin')->user()->hasPermission('create users')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to create users'
            ], 403);
        }
        // Convert checkbox value to boolean
        $requestData = $request->all();
        $requestData['is_active'] = $request->has('is_active') ? true : false;

        try {
            $validated = validator($requestData, [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users',
                'password' => 'required|string|min:6',
                'phone' => 'required|string|max:20',
                'country' => 'required|string|size:2',
                'subscription_plan_id' => 'required|exists:subscription_plans,id',
                'subscription_duration' => 'required|integer|min:1|max:365',
                'is_active' => 'boolean',
            ])->validate();
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        // Hash password
        $validated['password'] = Hash::make($validated['password']);
        $validated['password_changed_at'] = now();

        // Set default values
        $validated['user_type'] = 'user';
        $validated['application'] = 'SHAHID';
        $validated['is_admin'] = false;

        $user = User::create($validated);

        // Create subscription if plan is selected
        if ($request->subscription_plan_id) {
            $subscriptionPlan = \App\Models\SubscriptionPlan::find($request->subscription_plan_id);

            \App\Models\UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $request->subscription_plan_id,
                'starts_at' => now(),
                'expires_at' => now()->addDays($request->subscription_duration),
                'status' => 'active',
                'payment_status' => 'paid',
                'amount' => $subscriptionPlan->price,
                'currency' => $subscriptionPlan->currency,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $user->load('activeSubscription.subscriptionPlan'),
            'message' => 'User registered successfully with subscription'
        ], 201);
    }



    /**
     * Display the specified user.
     */
    public function show(User $user): JsonResponse
    {
        try {
            // Check if user has permission to view users
            if (!auth('admin')->user()->hasPermission('view users')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view users'
                ], 403);
            }

            // Load subscriptions safely
            try {
                // Only load subscriptions if the table exists and has the relationship
                if (\Schema::hasTable('user_subscriptions')) {
                    $user->load([
                        'subscriptions' => function ($query) {
                            $query->with('subscriptionPlan:id,name,price,currency')
                                  ->latest()
                                  ->limit(10);
                        }
                    ]);

                    // Try to load active subscription if the relationship exists
                    if (method_exists($user, 'activeSubscription')) {
                        $user->load('activeSubscription.subscriptionPlan:id,name,price,currency');
                    }

                    $user->loadCount(['subscriptions']);
                }
            } catch (\Exception $e) {
                // If there's an error loading relationships, continue without them
                \Log::warning('Error loading user relationships: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => $user,
                'message' => 'User retrieved successfully'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in UserController@show: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving user data'
            ], 500);
        }
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user): JsonResponse
    {
        try {
            \Log::info('User update request received', [
                'user_id' => $user->id,
                'request_data' => $request->all(),
                'auth_user' => auth('admin')->user()->id
            ]);

            // Check if user has permission to update users
            if (!auth('admin')->user()->hasPermission('update users')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to update users'
                ], 403);
            }
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'nullable',
                'email',
                Rule::unique('users')->ignore($user->id)
            ],
            'password' => 'nullable|string|min:6',
            'application' => 'required|string',
            'subscription_plan_id' => 'nullable|exists:subscription_plans,id',
            'subscription_expiry' => 'nullable|date',
            'remaining_days' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_admin' => 'boolean',
            'permissions' => 'nullable|array',
        ]);

        // Handle password update
        if (isset($validated['password']) && $validated['password']) {
            $validated['password'] = Hash::make($validated['password']);
            $validated['password_changed_at'] = now();
        } else {
            unset($validated['password']);
        }

        // Calculate remaining days if subscription_expiry is provided
        if (isset($validated['subscription_expiry']) && $validated['subscription_expiry']) {
            $expiryDate = \Carbon\Carbon::parse($validated['subscription_expiry']);
            $remainingDays = now()->diffInDays($expiryDate, false);
            $validated['remaining_days'] = max(0, $remainingDays);
        } elseif (isset($validated['subscription_expiry']) && !$validated['subscription_expiry']) {
            // If subscription_expiry is cleared, reset remaining_days
            $validated['remaining_days'] = 0;
        }

        // Force user_type to 'user' since we're managing regular users only
        $validated['user_type'] = 'user';
        $validated['is_admin'] = false;

        // Handle subscription plan update
        if (isset($validated['subscription_plan_id']) && $validated['subscription_plan_id']) {
            $this->updateUserSubscription($user, $validated['subscription_plan_id'], $validated);
        }

        // Remove subscription_plan_id from user update data
        unset($validated['subscription_plan_id']);

        $user->update($validated);

            return response()->json([
                'success' => true,
                'data' => $user,
                'message' => 'User updated successfully'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('User update validation error', [
                'user_id' => $user->id,
                'errors' => $e->errors()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('User update error: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the user'
            ], 500);
        }
    }

    /**
     * Update user subscription
     */
    private function updateUserSubscription($user, $planId, $validated)
    {
        try {
            // Get the subscription plan
            $plan = \App\Models\SubscriptionPlan::find($planId);
            if (!$plan) {
                return;
            }

            // Deactivate existing subscriptions
            \App\Models\UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->update(['status' => 'cancelled']);

            // Calculate dates
            $startsAt = now();
            $expiresAt = isset($validated['subscription_expiry']) && $validated['subscription_expiry']
                ? \Carbon\Carbon::parse($validated['subscription_expiry'])
                : now()->addDays($plan->duration_days);

            // Create new subscription
            \App\Models\UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'starts_at' => $startsAt,
                'expires_at' => $expiresAt,
                'status' => 'active',
                'allowed_applications' => $plan->allowed_applications,
                'max_devices' => $plan->max_devices,
                'amount_paid' => $plan->price,
                'payment_method' => 'admin_assigned',
                'payment_reference' => 'ADMIN_' . uniqid(),
                'notes' => 'Subscription assigned by admin',
                'activated_at' => $startsAt,
            ]);

            // Update user info
            $userInfo = json_decode($user->user_info ?? '{}', true);
            $userInfo['subscription_plan'] = $plan->name;
            $userInfo['plan'] = $plan->name;

            // Update user fields
            $user->update([
                'user_info' => json_encode($userInfo),
                'subscription_expiry' => $expiresAt,
                'remaining_days' => $expiresAt->diffInDays(now()),
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating user subscription: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user): JsonResponse
    {
        // Check if user has permission to delete users
        if (!auth('admin')->user()->hasPermission('delete users')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete users'
            ], 403);
        }
        // Check if user has active subscriptions
        if ($user->activeSubscription) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete user with active subscription'
            ], 422);
        }

        // Prevent deleting admin users
        if ($user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete admin users'
            ], 422);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Toggle user status.
     */
    public function toggleStatus(User $user): JsonResponse
    {
        $user->update([
            'is_active' => !$user->is_active
        ]);

        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => 'User status updated successfully'
        ]);
    }

    /**
     * Reset user password.
     */
    public function resetPassword(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'password' => 'required|string|min:6|confirmed',
        ]);

        $user->update([
            'password' => Hash::make($validated['password']),
            'password_changed_at' => now(),
            'force_password_change' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password reset successfully'
        ]);
    }

    /**
     * Get user statistics.
     */
    public function statistics(): JsonResponse
    {
        // Only count regular users, not admins
        $userQuery = User::where('user_type', 'user')->where('is_admin', false);

        $stats = [
            'total_users' => $userQuery->count(),
            'active_users' => $userQuery->where('is_active', true)->count(),
            'inactive_users' => $userQuery->where('is_active', false)->count(),
            'users_with_subscriptions' => $userQuery->whereHas('activeSubscription')->count(),
            'new_users_this_month' => $userQuery->whereMonth('created_at', now()->month)
                                               ->whereYear('created_at', now()->year)
                                               ->count(),
            'users_by_country' => $userQuery->select('country', \DB::raw('count(*) as total'))
                                           ->groupBy('country')
                                           ->pluck('total', 'country')
                                           ->toArray(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Statistics retrieved successfully'
        ]);
    }
}
