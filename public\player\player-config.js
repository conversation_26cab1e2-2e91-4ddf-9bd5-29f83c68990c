/**
 * Player Configuration for CSP Compliance
 * Ensures proper configuration for Content Security Policy
 */

console.log('🔧 Player Config Script loaded');

// Global player configuration
window.PlayerConfig = {
    // CSP-compliant configuration
    csp: {
        allowBlobUrls: true,
        allowDataUrls: true,
        allowHttpsUrls: true,
        allowHttpUrls: true
    },
    
    // JWPlayer specific configuration
    jwplayer: {
        key: 'XSuP4qMl+9tK17QNb+4+th2Pm9AWgMO/cYH8CI0HGGr24=',
        basePath: null, // Will be set dynamically
        providers: [
            { name: 'html5' },
            { name: 'shaka' }
        ]
    },
    
    // Shaka Player configuration
    shaka: {
        drm: {
            clearKeys: {}
        },
        streaming: {
            bufferingGoal: 10,
            rebufferingGoal: 2,
            bufferBehind: 30
        },
        manifest: {
            retryParameters: {
                timeout: 30000,
                maxAttempts: 3,
                baseDelay: 1000,
                backoffFactor: 2,
                fuzzFactor: 0.5
            }
        }
    },
    
    // Network configuration
    network: {
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
            'Accept': 'application/dash+xml,video/mp4,application/mp4,*/*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Origin': 'https://shahid.mbc.net',
            'Referer': 'https://shahid.mbc.net/'
        },
        timeout: 30000,
        withCredentials: false
    },

    // Proxy configuration
    proxy: {
        enabled: true,
        baseUrl: 'https://fast-decoder.com/video-proxy'
    },

    // HLS.js configuration for better streaming
    hlsConfig: {
        debug: false,
        enableWorker: false, // Disabled to prevent recursion issues with CSP fix
        lowLatencyMode: true,
        backBufferLength: 90,
        maxBufferLength: 30,
        maxMaxBufferLength: 600,
        maxBufferSize: 60 * 1000 * 1000, // 60MB
        maxBufferHole: 0.5,
        highBufferWatchdogPeriod: 2,
        nudgeOffset: 0.1,
        nudgeMaxRetry: 3,
        maxFragLookUpTolerance: 0.25,
        liveSyncDurationCount: 3,
        liveMaxLatencyDurationCount: 10,
        liveDurationInfinity: false,
        enableSoftwareAES: true,
        manifestLoadingTimeOut: 10000,
        manifestLoadingMaxRetry: 1,
        manifestLoadingRetryDelay: 1000,
        levelLoadingTimeOut: 10000,
        levelLoadingMaxRetry: 4,
        levelLoadingRetryDelay: 1000,
        fragLoadingTimeOut: 20000,
        fragLoadingMaxRetry: 6,
        fragLoadingRetryDelay: 1000,
        startFragPrefetch: true,
        testBandwidth: true,
        progressive: false,

        // Enhanced XHR setup for better streaming
        xhrSetup: function(xhr, url) {
            xhr.setRequestHeader('Accept', '*/*');
            xhr.setRequestHeader('Cache-Control', 'no-cache');
            xhr.setRequestHeader('Pragma', 'no-cache');

            // Handle CORS for streaming
            if (url.includes('fast-decoder.com') || url.includes('shahid.net')) {
                xhr.withCredentials = false;
            }
        },

        // Enhanced fetch setup for better streaming
        fetchSetup: function(context, initParams) {
            initParams.headers = initParams.headers || {};
            initParams.headers['Accept'] = '*/*';
            initParams.headers['Cache-Control'] = 'no-cache';
            initParams.headers['Pragma'] = 'no-cache';

            // Handle CORS for streaming
            if (context.url.includes('fast-decoder.com') || context.url.includes('shahid.net')) {
                initParams.mode = 'cors';
                initParams.credentials = 'omit';
            }

            return initParams;
        }
    }
};

// Initialize player configuration
function initializePlayerConfig() {
    // Set dynamic base path
    const currentDomain = window.location.protocol + '//' + window.location.host;
    window.PlayerConfig.jwplayer.basePath = currentDomain + '/player/js/';
    
    console.log('✅ Player base path set to:', window.PlayerConfig.jwplayer.basePath);
    
    // Configure JWPlayer if available
    if (typeof jwplayer !== 'undefined') {
        jwplayer.defaults = jwplayer.defaults || {};
        jwplayer.defaults.base = window.PlayerConfig.jwplayer.basePath;
        jwplayer.key = window.PlayerConfig.jwplayer.key;
        
        console.log('✅ JWPlayer configured');
    }
    
    // Configure Shaka Player if available
    if (typeof shaka !== 'undefined') {
        // Install polyfills
        shaka.polyfill.installAll();
        
        // Check browser support
        if (shaka.Player.isBrowserSupported()) {
            console.log('✅ Shaka Player supported');
        } else {
            console.warn('⚠️ Shaka Player not supported');
        }
    }
}

// Error handling for player initialization
function handlePlayerError(error) {
    console.error('❌ Player Error:', error);
    
    // Specific error handling
    if (error.code === 241403) {
        console.error('DRM Error: Access to protected content denied');
        return 'DRM_ERROR';
    } else if (error.code === 1001) {
        console.error('Network Error: Failed to load manifest');
        return 'NETWORK_ERROR';
    } else if (error.message && error.message.includes('CSP')) {
        console.error('CSP Error: Content Security Policy violation');
        return 'CSP_ERROR';
    }
    
    return 'UNKNOWN_ERROR';
}

// Utility function to validate URLs
function validateUrl(url) {
    try {
        const parsedUrl = new URL(url);
        const allowedProtocols = ['http:', 'https:', 'blob:', 'data:'];
        
        if (!allowedProtocols.includes(parsedUrl.protocol)) {
            console.warn('⚠️ Unsupported URL protocol:', parsedUrl.protocol);
            return false;
        }
        
        return true;
    } catch (error) {
        console.error('❌ Invalid URL:', url);
        return false;
    }
}

// Export configuration and utilities
window.PlayerConfig.initialize = initializePlayerConfig;
window.PlayerConfig.handleError = handlePlayerError;
window.PlayerConfig.validateUrl = validateUrl;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePlayerConfig);
} else {
    initializePlayerConfig();
}

console.log('✅ Player Config Script ready');
