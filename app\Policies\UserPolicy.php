<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        return $user->isSuperAdmin() || 
               $user->hasPermissionTo('view users') || 
               $user->hasRole(['admin', 'user-manager']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        // Super admin can view anyone
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can view themselves
        if ($user->id === $model->id) {
            return true;
        }

        // Admins can view non-admin users
        if ($user->isAdmin() && !$model->isAdmin()) {
            return $user->hasPermissionTo('view users');
        }

        // Admin managers can view other admins (but not super admins)
        if ($user->canManageAdmins() && $model->isAdmin() && !$model->isSuperAdmin()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create users.
     */
    public function create(User $user): bool
    {
        return $user->isSuperAdmin() || 
               $user->hasPermissionTo('create users') || 
               $user->hasRole(['admin', 'user-manager']);
    }

    /**
     * Determine whether the user can create admin users.
     */
    public function createAdmin(User $user): bool
    {
        return $user->isSuperAdmin() || 
               ($user->canManageAdmins() && $user->hasPermissionTo('create admins'));
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // Super admin can update anyone except other super admins
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can update themselves (limited fields)
        if ($user->id === $model->id) {
            return true;
        }

        // Prevent non-super-admins from updating super admins
        if ($model->isSuperAdmin()) {
            return false;
        }

        // Admins can update non-admin users
        if ($user->isAdmin() && !$model->isAdmin()) {
            return $user->hasPermissionTo('update users');
        }

        // Admin managers can update other admins
        if ($user->canManageAdmins() && $model->isAdmin()) {
            return $user->hasPermissionTo('update admins');
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        // Cannot delete yourself
        if ($user->id === $model->id) {
            return false;
        }

        // Cannot delete super admins
        if ($model->isSuperAdmin()) {
            return false;
        }

        // Super admin can delete anyone (except other super admins)
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Admins can delete non-admin users
        if ($user->isAdmin() && !$model->isAdmin()) {
            return $user->hasPermissionTo('delete users');
        }

        // Admin managers can delete other admins
        if ($user->canManageAdmins() && $model->isAdmin()) {
            return $user->hasPermissionTo('delete admins');
        }

        return false;
    }

    /**
     * Determine whether the user can suspend the model.
     */
    public function suspend(User $user, User $model): bool
    {
        // Cannot suspend yourself
        if ($user->id === $model->id) {
            return false;
        }

        // Cannot suspend super admins
        if ($model->isSuperAdmin()) {
            return false;
        }

        // Super admin can suspend anyone
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Admins can suspend non-admin users
        if ($user->isAdmin() && !$model->isAdmin()) {
            return $user->hasPermissionTo('suspend users');
        }

        // Admin managers can suspend other admins
        if ($user->canManageAdmins() && $model->isAdmin()) {
            return $user->hasPermissionTo('suspend admins');
        }

        return false;
    }

    /**
     * Determine whether the user can manage roles for the model.
     */
    public function manageRoles(User $user, User $model): bool
    {
        // Cannot manage your own roles
        if ($user->id === $model->id) {
            return false;
        }

        // Cannot manage super admin roles
        if ($model->isSuperAdmin()) {
            return false;
        }

        // Super admin can manage anyone's roles
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Admin managers can manage other admin roles
        if ($user->canManageAdmins() && $model->isAdmin()) {
            return $user->hasPermissionTo('manage roles');
        }

        // Regular admins can manage non-admin user roles
        if ($user->isAdmin() && !$model->isAdmin()) {
            return $user->hasPermissionTo('manage user roles');
        }

        return false;
    }

    /**
     * Determine whether the user can reset password for the model.
     */
    public function resetPassword(User $user, User $model): bool
    {
        // Super admin can reset anyone's password
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can reset their own password
        if ($user->id === $model->id) {
            return true;
        }

        // Cannot reset super admin passwords
        if ($model->isSuperAdmin()) {
            return false;
        }

        // Admins can reset non-admin passwords
        if ($user->isAdmin() && !$model->isAdmin()) {
            return $user->hasPermissionTo('reset user passwords');
        }

        // Admin managers can reset other admin passwords
        if ($user->canManageAdmins() && $model->isAdmin()) {
            return $user->hasPermissionTo('reset admin passwords');
        }

        return false;
    }

    /**
     * Determine whether the user can view admin panel.
     */
    public function viewAdminPanel(User $user): bool
    {
        return $user->isAdmin() && $user->isActiveAdmin();
    }

    /**
     * Determine whether the user can access admin management.
     */
    public function manageAdmins(User $user): bool
    {
        return $user->isSuperAdmin() || 
               ($user->canManageAdmins() && $user->hasPermissionTo('manage admins'));
    }
}
