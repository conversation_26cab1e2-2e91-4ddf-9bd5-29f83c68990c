<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>🎬 Shahid Player - FairPlay</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        html, body {
            height: 100vh;
            height: 100dvh; /* Dynamic viewport height for modern browsers */
            margin: 0;
            padding: 0;
            background: #000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            position: fixed;
            width: 100%;
        }

        /* Player Container - Full Screen */
        #player {
            width: 100vw;
            height: 100vh;
            height: 100dvh;
            position: relative;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* iPhone Safe Area Support */
        @supports (padding: max(0px)) {
            #player {
                padding-left: max(0px, env(safe-area-inset-left));
                padding-right: max(0px, env(safe-area-inset-right));
                padding-top: max(0px, env(safe-area-inset-top));
                padding-bottom: max(0px, env(safe-area-inset-bottom));
            }
        }

        /* Video Element Styling */
        video {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain;
            background: #000;
        }

        /* JW Player Styling */
        .jwplayer {
            width: 100% !important;
            height: 100% !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
        }

        /* Loading Spinner */
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }



        /* Error Messages */
        .error-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            padding: 20px;
        }

        .error-message {
            background: rgba(220, 38, 38, 0.9);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            max-width: 90%;
            font-size: 14px;
            line-height: 1.5;
        }



        /* Status Bar */
        .status-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: env(safe-area-inset-top, 20px);
            background: rgba(0, 0, 0, 0.8);
            z-index: 9998;
        }
    </style>
</head>
<body>
    <!-- Status Bar for iPhone -->
    <div class="status-bar"></div>
    
    <!-- Main Player Container -->
    <div id="player" data-skin-url="/player/tod_skin.css">
        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner"></div>
    </div>
    
    <!-- Hidden Play Button -->
    <button id="playStreamButton" style="display:none"></button>
    


    <script src="/player/js/jwplayer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <script>
        // JW Player initialization for FairPlay
        if (typeof jwplayer !== 'undefined') {
            try {
                jwplayer.defaults = jwplayer.defaults || {};
                jwplayer.defaults.base = window.location.origin + '/player/js/';
                if (jwplayer.utils && jwplayer.utils.repo) {
                    jwplayer.utils.repo = window.location.origin + '/player/js/';
                }
                // Use a valid JW Player key (this is a demo key, replace with your own)
                jwplayer.key = 'Z8lq0BAJBEu//qi4oQ7e5kmmCB4pOlIsjYLVL95r9jE=';
                jwplayer.defaults.analytics = false;
                jwplayer.defaults.advertising = false;
                jwplayer.defaults.related = false;
                jwplayer.defaults.sharing = false;
    
            } catch(e) { 
                // Silent fail - JW Player init
            }
        }
    </script>
    <script>
        // قراءة البيانات من URL parameters أو sessionStorage (إذا تم تمريرها من صفحة الاختبار)
        function getTestData() {
            // جرب قراءة من URL parameters أولاً
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('hls')) {
                return {
                    title: urlParams.get('title') || 'Shahid Asset (من الاختبار)',
                    hls: urlParams.get('hls'),
                    certificateUrl: urlParams.get('certificateUrl') || 'https://shahid.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
                    licenseUrl: urlParams.get('licenseUrl') || '',
                    kid: urlParams.get('keyId') || urlParams.get('kid') || '827802f4-e896-4769-ba77-f1e656ed1bd4',
                    iv: urlParams.get('iv') || '1f3a1d7ce87a4cd3846038fcf4d05f82'
                };
            }
            
            // جرب قراءة من sessionStorage
            try {
                const stored = sessionStorage.getItem('fairplay_extracted_data');
                if (stored) {
                    const parsed = JSON.parse(stored);

                    return parsed;
                }
            } catch (e) {
                // Silent fail - use default data
            }
            
            // البيانات الافتراضية (ثابتة للاختبار)
            return {
                title: 'Shahid Asset 49923436792142',
                hls: 'https://mbcvod-enc.edgenextcdn.net/out/v1/b9ab8e5fc3d44bb2a28ab3c9cb8b2ae7/de5f5ce1bd4d43eb9896d42d0de1dab6/75166141e593471998b1061968ca6824/index.m3u8?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264',
                certificateUrl: 'https://shahid.la.drm.cloud/certificate/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
                licenseUrl: 'https://shahid.la.drm.cloud/acquire-license/fairplay?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4&KID=827802f4-e896-4769-ba77-f1e656ed1bd4&IV=1f3a1d7ce87a4cd3846038fcf4d05f82&UserToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjEuNzU1NzM2MzNFOSwiZHJtVG9rZW5JbmZvIjp7ImV4cCI6IjIwMjUtMDgtMjFUMDA6MzI6MTAuMDAwMTU2Iiwia2lkIjpbIioiXSwicCI6eyJwZXJzIjpmYWxzZSwiZWQiOiIyMDI1LTA4LTIxVDAwOjMyOjEwLjAwMDE1NiIsImV4YyI6eyJXaWRldmluZUNhblJlbmV3IjpmYWxzZSwiRmFpcnBsYXlSZW50YWxEdXJhdGlvblNlY29uZHMiOjg2MDAwLCJGYWlycGxheUxlYXNlRHVyYXRpb25TZWNvbmRzIjo4NjAwMH19LCJ3aWRldmluZSI6eyJkaXNhYmxlX2FuYWxvZyI6dHJ1ZSwiaGRjcCI6IkhEQ1BfVjJfMiIsImNnbXMiOiJDT1BZX05FVkVSIn0sImZhaXJwbGF5Ijp7ImhkY3AiOnRydWV9LCJwbGF5cmVhZHkiOnsidW5jb21wcmVzc2VkX2RpZ2l0YWxfYXVkaW9fb3BsIjozMDAsInVuY29tcHJlc3NlZF9kaWdpdGFsX3ZpZGVvX29wbCI6MzAwLCJjb21wcmVzc2VkX2RpZ2l0YWxfYXVkaW9fb3BsIjozMDAsImNvbXByZXNzZWRfZGlnaXRhbF92aWRlb19vcGwiOjUwMH19fQ.IMAd3ixOnctasxnd8UVzP5JQO-XjoHS1y0ZegbYspvY',
                kid: '827802f4-e896-4769-ba77-f1e656ed1bd4',
                iv: '1f3a1d7ce87a4cd3846038fcf4d05f82'
            };
        }
        
        const STATIC_TEST = getTestData();
        


        // إعداد المتغيرات العامة قبل تحميل المشغل
        window.__FAIRPLAY_CERT_URL__ = STATIC_TEST.certificateUrl;
        window.__FAIRPLAY_LICENSE_URL__ = STATIC_TEST.licenseUrl;





        // Loading Management Functions
        window.showLoading = function() {
            const spinner = document.getElementById('loadingSpinner');
            if (spinner) {
                spinner.style.display = 'block';
            }
        };

        window.hideLoading = function() {
            const spinner = document.getElementById('loadingSpinner');
            if (spinner) {
                spinner.style.display = 'none';
            }
        };

        // iPhone Specific Functions
        window.enterFullscreen = function() {
            const player = document.getElementById('player');
            if (player.requestFullscreen) {
                player.requestFullscreen();
            } else if (player.webkitRequestFullscreen) {
                player.webkitRequestFullscreen();
            } else if (player.msRequestFullscreen) {
                player.msRequestFullscreen();
            }
        };

        // Orientation Change Handler
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                // Adjust player size after orientation change
                const player = document.getElementById('player');
                if (player) {
                    player.style.height = '100vh';
                    player.style.height = '100dvh';
                }

            }, 100);
        });

        // Prevent iOS Safari bounce effect
        document.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });

        // اضبط زِر التشغيل بالبيانات وثم شغّل تلقائياً
        document.addEventListener('DOMContentLoaded', function(){
            const btn = document.getElementById('playStreamButton');
            btn.dataset.streamType = 'hls';
            btn.dataset.hlsUrl = STATIC_TEST.hls;
            btn.dataset.title = STATIC_TEST.title;
            btn.dataset.licenseUrl = STATIC_TEST.licenseUrl;
            btn.dataset.certificateUrl = STATIC_TEST.certificateUrl;
            btn.dataset.keyData = ''; // Empty for HLS/FairPlay
            
            // Show loading initially
            showLoading();

            // شغّل تلقائياً بعد تأكد من تحميل المشغل
            setTimeout(() => {
                btn.click();
                
                // Hide loading after player starts
                setTimeout(() => {
                    hideLoading();
                }, 2000);
            }, 500);
        });
    </script>
    <script src="/player/fairplay_player.js"></script>
</body>
</html>

