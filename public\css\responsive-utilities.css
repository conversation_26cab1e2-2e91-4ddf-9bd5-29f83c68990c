/* Responsive Utilities for StreamVibe Landing Page - Dashboard Theme */

/* Dashboard Color Variables */
:root {
    /* Dashboard Colors */
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* Dashboard Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(45deg, #007bff, #0056b3);
    --gradient-success: linear-gradient(45deg, #28a745, #20c997);
    --gradient-danger: linear-gradient(45deg, #dc3545, #c82333);
    --gradient-play: linear-gradient(45deg, #ff6b6b, #ee5a24);
    --gradient-dark: linear-gradient(135deg, #343a40 0%, #212529 100%);

    /* Text Colors */
    --text-light: #ffffff;
    --text-gray: #6c757d;
    --text-muted: #868e96;

    /* Background Colors */
    --dark-bg: #212529;
    --card-bg: #ffffff;
    --overlay-bg: rgba(52, 58, 64, 0.9);
}

/* ===== UTILITY CLASSES ===== */

/* Display Utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

/* Flex Utilities */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

/* Text Utilities */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }
.font-weight-light { font-weight: 300 !important; }
.font-weight-normal { font-weight: 400 !important; }
.font-weight-medium { font-weight: 500 !important; }
.font-weight-semibold { font-weight: 600 !important; }
.font-weight-bold { font-weight: 700 !important; }
.font-weight-extrabold { font-weight: 800 !important; }

/* Spacing Utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }
.m-auto { margin: auto !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 0.25rem !important; }
.ml-2 { margin-left: 0.5rem !important; }
.ml-3 { margin-left: 1rem !important; }
.ml-4 { margin-left: 1.5rem !important; }
.ml-5 { margin-left: 3rem !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 1rem !important; }
.mr-4 { margin-right: 1.5rem !important; }
.mr-5 { margin-right: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 1rem !important; }
.pt-4 { padding-top: 1.5rem !important; }
.pt-5 { padding-top: 3rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 1rem !important; }
.pb-4 { padding-bottom: 1.5rem !important; }
.pb-5 { padding-bottom: 3rem !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 0.25rem !important; }
.pl-2 { padding-left: 0.5rem !important; }
.pl-3 { padding-left: 1rem !important; }
.pl-4 { padding-left: 1.5rem !important; }
.pl-5 { padding-left: 3rem !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 0.25rem !important; }
.pr-2 { padding-right: 0.5rem !important; }
.pr-3 { padding-right: 1rem !important; }
.pr-4 { padding-right: 1.5rem !important; }
.pr-5 { padding-right: 3rem !important; }

/* Width & Height Utilities */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* Position Utilities */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Border Utilities */
.border { border: 1px solid rgba(255, 255, 255, 0.1) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid rgba(255, 255, 255, 0.1) !important; }
.border-bottom { border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important; }
.border-left { border-left: 1px solid rgba(255, 255, 255, 0.1) !important; }
.border-right { border-right: 1px solid rgba(255, 255, 255, 0.1) !important; }

.rounded { border-radius: 0.375rem !important; }
.rounded-sm { border-radius: 0.125rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }
.rounded-2xl { border-radius: 1rem !important; }
.rounded-full { border-radius: 9999px !important; }
.rounded-none { border-radius: 0 !important; }

/* Shadow Utilities */
.shadow-sm { box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06) !important; }
.shadow-md { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important; }
.shadow-xl { box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04) !important; }
.shadow-2xl { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important; }
.shadow-none { box-shadow: none !important; }

/* Color Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-light { color: var(--text-light) !important; }
.text-gray { color: var(--text-gray) !important; }
.text-dark { color: var(--dark-bg) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-accent { background-color: var(--accent-color) !important; }
.bg-light { background-color: var(--text-light) !important; }
.bg-gray { background-color: var(--text-gray) !important; }
.bg-dark { background-color: var(--dark-bg) !important; }
.bg-card { background-color: var(--card-bg) !important; }
.bg-transparent { background-color: transparent !important; }

.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-dark { background: var(--gradient-dark) !important; }

/* Overflow Utilities */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }
.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }

/* Z-index Utilities */
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }
.z-auto { z-index: auto !important; }

/* ===== RESPONSIVE UTILITIES ===== */

/* Mobile First Responsive Classes */
@media (min-width: 576px) {
    .sm\:d-none { display: none !important; }
    .sm\:d-block { display: block !important; }
    .sm\:d-flex { display: flex !important; }
    .sm\:text-left { text-align: left !important; }
    .sm\:text-center { text-align: center !important; }
    .sm\:text-right { text-align: right !important; }
}

@media (min-width: 768px) {
    .md\:d-none { display: none !important; }
    .md\:d-block { display: block !important; }
    .md\:d-flex { display: flex !important; }
    .md\:text-left { text-align: left !important; }
    .md\:text-center { text-align: center !important; }
    .md\:text-right { text-align: right !important; }
    .md\:flex-row { flex-direction: row !important; }
    .md\:flex-column { flex-direction: column !important; }
}

@media (min-width: 992px) {
    .lg\:d-none { display: none !important; }
    .lg\:d-block { display: block !important; }
    .lg\:d-flex { display: flex !important; }
    .lg\:text-left { text-align: left !important; }
    .lg\:text-center { text-align: center !important; }
    .lg\:text-right { text-align: right !important; }
}

@media (min-width: 1200px) {
    .xl\:d-none { display: none !important; }
    .xl\:d-block { display: block !important; }
    .xl\:d-flex { display: flex !important; }
    .xl\:text-left { text-align: left !important; }
    .xl\:text-center { text-align: center !important; }
    .xl\:text-right { text-align: right !important; }
}

/* ===== ANIMATION UTILITIES ===== */

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.5s ease-out;
}

.animate-slide-left {
    animation: slideLeft 0.5s ease-out;
}

.animate-slide-right {
    animation: slideRight 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-30px,0); }
    70% { transform: translate3d(0,-15px,0); }
    90% { transform: translate3d(0,-4px,0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== TRANSITION UTILITIES ===== */

.transition-all { transition: all 0.3s ease !important; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease !important; }
.transition-opacity { transition: opacity 0.3s ease !important; }
.transition-transform { transition: transform 0.3s ease !important; }
.transition-none { transition: none !important; }

.duration-75 { transition-duration: 75ms !important; }
.duration-100 { transition-duration: 100ms !important; }
.duration-150 { transition-duration: 150ms !important; }
.duration-200 { transition-duration: 200ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.duration-500 { transition-duration: 500ms !important; }
.duration-700 { transition-duration: 700ms !important; }
.duration-1000 { transition-duration: 1000ms !important; }

.ease-linear { transition-timing-function: linear !important; }
.ease-in { transition-timing-function: ease-in !important; }
.ease-out { transition-timing-function: ease-out !important; }
.ease-in-out { transition-timing-function: ease-in-out !important; }

/* ===== TRANSFORM UTILITIES ===== */

.transform { transform: translateZ(0) !important; }
.transform-none { transform: none !important; }
.scale-0 { transform: scale(0) !important; }
.scale-50 { transform: scale(0.5) !important; }
.scale-75 { transform: scale(0.75) !important; }
.scale-90 { transform: scale(0.9) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }
.scale-110 { transform: scale(1.1) !important; }
.scale-125 { transform: scale(1.25) !important; }
.scale-150 { transform: scale(1.5) !important; }

.rotate-0 { transform: rotate(0deg) !important; }
.rotate-45 { transform: rotate(45deg) !important; }
.rotate-90 { transform: rotate(90deg) !important; }
.rotate-180 { transform: rotate(180deg) !important; }
.rotate-270 { transform: rotate(270deg) !important; }

.translate-x-0 { transform: translateX(0) !important; }
.translate-x-1 { transform: translateX(0.25rem) !important; }
.translate-x-2 { transform: translateX(0.5rem) !important; }
.translate-x-3 { transform: translateX(0.75rem) !important; }
.translate-x-4 { transform: translateX(1rem) !important; }

.translate-y-0 { transform: translateY(0) !important; }
.translate-y-1 { transform: translateY(0.25rem) !important; }
.translate-y-2 { transform: translateY(0.5rem) !important; }
.translate-y-3 { transform: translateY(0.75rem) !important; }
.translate-y-4 { transform: translateY(1rem) !important; }
