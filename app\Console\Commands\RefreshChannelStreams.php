<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\RefreshChannelStreamsJob;
use Illuminate\Support\Facades\Log;

class RefreshChannelStreams extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'channels:refresh {channel_id?} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh channel stream URLs to keep them active';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $channelId = $this->argument('channel_id');
        $force = $this->option('force');

        $this->info('🔄 Starting channel stream refresh...');

        try {
            if ($channelId) {
                $this->info("Refreshing specific channel: {$channelId}");
                RefreshChannelStreamsJob::dispatch($channelId);
            } else {
                $this->info("Refreshing all active channels");
                RefreshChannelStreamsJob::dispatch();
            }

            $this->info('✅ Channel refresh job dispatched successfully');
            
            if ($force) {
                $this->info('🚀 Running job immediately...');
                // Run the job synchronously when --force is used
                $job = new RefreshChannelStreamsJob($channelId);
                $job->handle();
                $this->info('✅ Job completed');
            }

        } catch (\Exception $e) {
            $this->error("❌ Failed to refresh channels: " . $e->getMessage());
            Log::error("Channel refresh command failed: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
