<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\ProxyService;

/**
 * Shahid Series API Service
 * Handles all series-related functionality
 */
class ShahidSeriesAPI extends ShahidBaseAPI
{
    protected $proxyService;

    public function __construct(ProxyService $proxyService = null)
    {
        parent::__construct();
        $this->proxyService = $proxyService ?: app(ProxyService::class);
    }

    /**
     * Override makeRequest to use ProxyService
     */
    protected function makeRequest($method, $url, $options = [])
    {
        try {
            $headers = $this->getHeaders($options['auth'] ?? false);

            // Use ProxyService for HTTP client
            $httpClient = $this->proxyService->getHttpClient([
                'timeout' => 30
            ])->withHeaders($headers);

            switch (strtoupper($method)) {
                case 'GET':
                    $response = $httpClient->get($url, $options['data'] ?? []);
                    break;
                case 'POST':
                    $response = $httpClient->post($url, $options['data'] ?? []);
                    break;
                case 'PUT':
                    $response = $httpClient->put($url, $options['data'] ?? []);
                    break;
                case 'DELETE':
                    $response = $httpClient->delete($url);
                    break;
                default:
                    throw new \Exception('Unsupported HTTP method: ' . $method);
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                $responseBody = $response->body();
                $responseData = $response->json();

                // Handle specific Shahid API errors
                $errorMessage = 'HTTP ' . $response->status();

                if (isset($responseData['faults']) && is_array($responseData['faults'])) {
                    $fault = $responseData['faults'][0];
                    if (isset($fault['code']) && $fault['code'] == 5004) {
                        $errorMessage = 'This content requires Shahid VIP subscription';
                    } else {
                        $errorMessage = $fault['userMessage'] ?? $fault['internalMessage'] ?? $errorMessage;
                    }
                } elseif (isset($responseData['message'])) {
                    $errorMessage = $responseData['message'];
                }

                Log::warning('API request failed', [
                    'url' => $url,
                    'status' => $response->status(),
                    'error' => $errorMessage,
                    'response_body' => $responseBody
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'status_code' => $response->status(),
                    'response_data' => $responseData
                ];
            }

        } catch (\Exception $e) {
            Log::error('HTTP request exception', [
                'url' => $url,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get series from Shahid (no token required for browsing)
     */
    public function getSeries($country = 'EG', $limit = 50, $offset = 0, $fetchAll = false)
    {
        try {
            // Increase execution time limit
            set_time_limit(120);

            Log::info("Fetching TV series for country: {$country}");

            $headers = [
                'accept' => 'application/json, text/plain, */*',
                'accept-language' => 'en',
                'browser_name' => 'CHROME',
                'browser_version' => '131.0.0.0',
                'cache-control' => 'no-cache',
                'language' => 'EN',
                'mparticleid' => '-8068094556643926163',
                'origin' => 'https://shahid.mbc.net',
                'os_version' => 'NT 10.0',
                'pragma' => 'no-cache',
                'priority' => 'u=1, i',
                'profile' => '{"id":"5fd56d50-ada0-11ef-8f21-014b67b685e0","ageRestriction":false,"master":true}',
                'profile-key' => '{"ageRestriction":false,"isAdult":true}',
                'referer' => 'https://shahid.mbc.net/',
                'sec-ch-ua' => '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Windows"',
                'sec-fetch-dest' => 'empty',
                'sec-fetch-mode' => 'cors',
                'sec-fetch-site' => 'cross-site',
                'sec-gpc' => '1',
                'shahid_os' => 'WINDOWS',
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            ];

            // Get series page carousels
            $seriesPageUrl = "https://api3.shahid.net/proxy/v2.1/editorial/page?request=%7B%22pageAlias%22:%22series%22,%22profileFolder%22:%22WW%22%7D&country={$country}";

            Log::info("Requesting series page URL: {$seriesPageUrl}");

            // Use ProxyService for HTTP client
            $httpClient = $this->proxyService->getHttpClient([
                'timeout' => 60,
                'retry' => [3, 1000]
            ])->withHeaders($headers);

            Log::info("Making request to series page...");
            $response = $httpClient->get($seriesPageUrl);
            Log::info("Response received with status: " . $response->status());

            if (!$response->successful()) {
                Log::error("Failed to get series page for {$country}: " . $response->status() . " - " . $response->body());
                return ['error' => 'Failed to fetch series page: HTTP ' . $response->status()];
            }

            $pageData = $response->json();
            $seriesLinks = [];

            // Extract carousel links
            $carousels = $pageData['carousels'] ?? [];
            if (empty($carousels)) {
                Log::warning("No carousels found for {$country}");
                return ['series' => [], 'total' => 0];
            }

            Log::info("Found " . count($carousels) . " carousels for {$country}");

            // Limit carousels to process (to avoid timeout)
            if ($fetchAll) {
                Log::info("Fetching ALL series - processing all carousels");
                $carouselsToProcess = $carousels; // Process all carousels
            } else {
                $maxCarousels = 15; // Increased from 5 to 15 to get more series (was causing only 270 instead of 390)
                $carouselsToProcess = array_slice($carousels, 0, $maxCarousels);
                Log::info("Fetching limited series - processing {$maxCarousels} carousels");
            }

            foreach ($carouselsToProcess as $carousel) {
                try {
                    $encoded = str_replace('/', '%2F', $carousel['id']);
                    $carouselUrl = "https://api3.shahid.net/proxy/v2.1/editorial/carousel?request=%7B%22displayedItems%22:0,%22id%22:%22{$encoded}%22,%22itemsRequestedStatic%22:true,%22pageNumber%22:0,%22pageSize%22:999,%22totalItems%22:999%7D&country={$country}";
                    $seriesLinks[] = $carouselUrl;
                } catch (\Exception $e) {
                    Log::error("Error processing carousel for {$country}: " . $e->getMessage());
                    continue;
                }
            }

            Log::info("Found " . count($seriesLinks) . " series carousels for {$country}");

            $allSeries = [];

            // Process each carousel
            foreach ($seriesLinks as $url) {
                try {
                    Log::info("Processing series carousel URL: {$url}");
                    $carouselResponse = $httpClient->get($url);

                    if (!$carouselResponse->successful()) {
                        if ($carouselResponse->status() === 404) {
                            Log::info("Series carousel not found (404), skipping: {$url}");
                        } else {
                            Log::warning("Failed to get carousel: " . $carouselResponse->status());
                        }
                        continue;
                    }

                    $items = $carouselResponse->json()['editorialItems'] ?? [];
                    Log::info("Found " . count($items) . " items in carousel");

                    foreach ($items as $item) {
                        try {
                            $itemData = $item['item'] ?? [];

                            // Extract series details
                            $title = $itemData['title'] ?? 'Unknown Series';
                            $seriesId = $itemData['id'] ?? null;

                            if (!$seriesId) {
                                Log::warning("No ID found for series: {$title}");
                                continue;
                            }

                            // Get series URL - try multiple patterns
                            $seriesUrl = "";
                            foreach ($itemData['productUrls'] ?? [] as $productUrl) {
                                $url = $productUrl['url'] ?? '';
                                // Try multiple URL patterns: /en/series/, /ar/series/, or any /series/
                                if (strpos($url, '/en/series/') !== false ||
                                    strpos($url, '/ar/series/') !== false ||
                                    strpos($url, '/series/') !== false) {
                                    $seriesUrl = $url;
                                    break;
                                }
                            }

                            // If no series URL found, try to construct one from the ID
                            if (empty($seriesUrl)) {
                                // Try to construct URL from series ID
                                $seriesUrl = "https://shahid.mbc.net/en/series/{$seriesId}";
                                Log::info("Constructed URL for series: {$title} (ID: {$seriesId}) -> {$seriesUrl}");
                            }

                            // Extract poster image
                            $posterUrl = $this->extractPosterUrl($itemData);

                            // Extract year and seasons count
                            $year = null;
                            $seasonsCount = 0;

                            if (isset($itemData['releaseDate'])) {
                                $year = substr($itemData['releaseDate'], 0, 4);
                            }

                            // Try to get seasons count
                            if (isset($itemData['seasons']) && is_array($itemData['seasons'])) {
                                $seasonsCount = count($itemData['seasons']);
                            }

                            $seriesInfo = [
                                'id' => $seriesId,
                                'title' => $title,
                                'poster_url' => $posterUrl,
                                'series_url' => $seriesUrl,
                                'year' => $year,
                                'seasons_count' => $seasonsCount,
                                'country' => $country
                            ];

                            $allSeries[] = $seriesInfo;
                            Log::info("Added series to collection: {$title}");

                        } catch (\Exception $e) {
                            Log::warning("Error processing series item: " . $e->getMessage());
                            continue;
                        }
                    }

                } catch (\Exception $e) {
                    Log::warning("Error processing carousel: " . $e->getMessage());
                    continue;
                }
            }

            // Remove duplicates based on series ID (more reliable than URL)
            $uniqueSeries = [];
            $seenIds = [];

            foreach ($allSeries as $series) {
                $seriesId = $series['id'] ?? '';
                if (!empty($seriesId) && !in_array($seriesId, $seenIds)) {
                    $uniqueSeries[] = $series;
                    $seenIds[] = $seriesId;
                }
            }

            // Apply limit and offset only if not fetching all
            $total = count($uniqueSeries);

            if ($fetchAll) {
                $series = $uniqueSeries; // Return all series when fetchAll is true
                Log::info("{$country}: Total {$total} unique series found, returning ALL {$total} series");
            } else {
                // For regular fetch, return more series (up to 500) instead of limiting to the small limit parameter
                $maxRegularLimit = 500; // Increased limit for regular fetch to get more series
                $effectiveLimit = min($maxRegularLimit, $total); // Don't exceed total available
                $series = array_slice($uniqueSeries, $offset, $effectiveLimit);
                Log::info("{$country}: Total {$total} unique series found, returning " . count($series) . " (max regular limit: {$maxRegularLimit})");
            }

            return [
                'series' => $series,
                'total' => $total,
                'country' => $country
            ];

        } catch (\Exception $e) {
            Log::error("Error in getSeries: " . $e->getMessage());
            return ['error' => 'Failed to fetch series: ' . $e->getMessage()];
        }
    }

    /**
     * Extract poster URL from series data
     */
    private function extractPosterUrl($seriesData)
    {
        $posterUrl = null;

        if (isset($seriesData['image']) && $seriesData['image']) {
            $imageData = $seriesData['image'];

            // Try different image types in order of preference
            $imageTypes = ['posterImage', 'posterClean', 'posterHero', 'thumbnailImage', 'heroSliderImage', 'landscapeClean'];
            foreach ($imageTypes as $imageType) {
                if (isset($imageData[$imageType]) && $imageData[$imageType]) {
                    $posterUrl = $imageData[$imageType];
                    break;
                }
            }
        }

        // Fallback: try direct posterImage field
        if (!$posterUrl && isset($seriesData['posterImage']) && $seriesData['posterImage']) {
            $posterUrl = $seriesData['posterImage'];
        }

        // Fallback: try thumbnailImage field
        if (!$posterUrl && isset($seriesData['thumbnailImage']) && $seriesData['thumbnailImage']) {
            $posterUrl = $seriesData['thumbnailImage'];
        }

        // Clean and optimize poster URL
        if ($posterUrl) {
            // Replace template variables with actual values
            if (strpos($posterUrl, '{height}') !== false || strpos($posterUrl, '{width}') !== false) {
                $posterUrl = str_replace(['{height}', '{width}', '{croppingPoint}'], ['600', '400', 'center'], $posterUrl);
            }

            // Clean URL by removing unwanted parameters
            $posterUrl = $this->cleanPosterUrl($posterUrl);

            // Additional optimization for mediaObject URLs
            if (strpos($posterUrl, 'mediaObject/') !== false && strpos($posterUrl, '?') === false) {
                $posterUrl .= "?width=400&height=600";
            }
        }

        return $posterUrl;
    }

    /**
     * Clean poster URL
     */
    private function cleanPosterUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // Remove unwanted parameters but keep essential ones
        $parsedUrl = parse_url($url);
        if (!$parsedUrl) {
            return $url;
        }

        $cleanUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }

        // Add clean parameters for image optimization
        if (strpos($url, 'mediaObject/') !== false) {
            $cleanUrl .= '?width=400&height=600';
        }

        return $cleanUrl;
    }

    /**
     * Get series seasons
     */
    public function getSeriesSeasons($seriesId)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        try {
            // Use api3.shahid.net like Python code
            $url = "https://api3.shahid.net/proxy/v2.1/product/id";
            $params = [
                'request' => '{"id":"' . $seriesId . '"}',
                'country' => 'EG'
            ];

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'uuid' => 'web'
            ];

            Log::info("=== GETTING SERIES SEASONS ===", [
                'series_id' => $seriesId,
                'url' => $url
            ]);

            $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

            if (!$result['success']) {
                return ['error' => $result['error'] ?? 'Failed to get series details'];
            }

            $data = $result['data'];
            $seasons = [];

            // Print full API response for debugging
            Log::info("=== FULL SERIES SEASONS API RESPONSE ===", [
                'response' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            ]);

            // Log the response structure for debugging
            Log::info("Series seasons API response structure", [
                'has_productModel' => isset($data['productModel']),
                'has_seasons' => isset($data['productModel']['seasons']),
                'seasons_count' => isset($data['productModel']['seasons']) ? count($data['productModel']['seasons']) : 0,
                'first_season_keys' => isset($data['productModel']['seasons'][0]) ? array_keys($data['productModel']['seasons'][0]) : []
            ]);

            // Get planned episodes from main series data (outside the loop)
            $plannedEpisodes = $data['productModel']['numberOfEpisodes'] ?? 0;

            Log::info("=== MAIN PRODUCT MODEL KEYS ===", [
                'productModel_keys' => array_keys($data['productModel']),
                'has_numberOfEpisodes' => isset($data['productModel']['numberOfEpisodes']),
                'numberOfEpisodes_value' => $data['productModel']['numberOfEpisodes'] ?? 'not_found'
            ]);

            Log::info("=== PLANNED EPISODES FROM MAIN SERIES ===", [
                'numberOfEpisodes' => $data['productModel']['numberOfEpisodes'] ?? 'not_found',
                'planned_episodes' => $plannedEpisodes
            ]);

            if (isset($data['productModel']['seasons']) && is_array($data['productModel']['seasons'])) {
                foreach ($data['productModel']['seasons'] as $index => $seasonData) {
                    // Log season data structure for debugging
                    Log::info("Season {$index} data structure", [
                        'season_keys' => array_keys($seasonData),
                        'has_episodesCount' => isset($seasonData['episodesCount']),
                        'has_numberOfEpisodes' => isset($seasonData['numberOfEpisodes']),
                        'has_numberOfAVODEpisodes' => isset($seasonData['numberOfAVODEpisodes']),
                        'has_playlists' => isset($seasonData['playlists']),
                        'title' => $seasonData['title'] ?? 'No title'
                    ]);

                    // Get episode count exactly like Python code
                    $episodeCount = $seasonData['numberOfAVODEpisodes'] ?? 1;

                    Log::info("=== SEASON EPISODE COUNT ANALYSIS ===", [
                        'season_title' => $seasonData['title'] ?? 'No title',
                        'numberOfAVODEpisodes' => $seasonData['numberOfAVODEpisodes'] ?? 'not_found',
                        'has_playlists' => isset($seasonData['playlists']),
                        'initial_count' => $episodeCount
                    ]);

                    // Check playlists for more accurate episode count (Python logic)
                    if (isset($seasonData['playlists']) && is_array($seasonData['playlists'])) {
                        foreach ($seasonData['playlists'] as $playlist) {
                            if (isset($playlist['type']) && $playlist['type'] === 'EPISODE') {
                                $playlistCount = $playlist['count'] ?? 0;
                                if ($playlistCount > $episodeCount) {
                                    Log::info("=== UPDATED EPISODE COUNT FROM PLAYLIST ===", [
                                        'season_title' => $seasonData['title'] ?? 'No title',
                                        'old_count' => $episodeCount,
                                        'new_count' => $playlistCount,
                                        'playlist_id' => $playlist['id'] ?? 'no_id'
                                    ]);
                                    $episodeCount = $playlistCount;
                                }
                            }
                        }
                    }

                    // If no playlists in series data, get actual season details to get correct count
                    if (!isset($seasonData['playlists']) && isset($seasonData['id'])) {
                        Log::info("=== GETTING SEASON DETAILS FOR ACCURATE COUNT ===", [
                            'season_id' => $seasonData['id'],
                            'season_title' => $seasonData['title'] ?? 'No title'
                        ]);

                        $seasonDetails = $this->getSeasonEpisodes($seasonData['id']);
                        if (isset($seasonDetails['data']) && is_array($seasonDetails['data'])) {
                            $actualCount = count($seasonDetails['data']);
                            if ($actualCount > $episodeCount) {
                                Log::info("=== UPDATED EPISODE COUNT FROM SEASON DETAILS ===", [
                                    'season_title' => $seasonData['title'] ?? 'No title',
                                    'old_count' => $episodeCount,
                                    'new_count' => $actualCount
                                ]);
                                $episodeCount = $actualCount;
                            }
                        }

                        // Also get planned episodes from season details
                        if (isset($seasonDetails['season_info']['numberOfEpisodes'])) {
                            $plannedEpisodes = $seasonDetails['season_info']['numberOfEpisodes'];
                            Log::info("=== UPDATED PLANNED EPISODES FROM SEASON DETAILS ===", [
                                'season_title' => $seasonData['title'] ?? 'No title',
                                'planned_episodes' => $plannedEpisodes
                            ]);
                        }
                    }

                    $episodesCount = $episodeCount;

                    // Method 5: If still 0, try to get actual episodes count by calling season details
                    if ($episodesCount === 0 && isset($seasonData['id'])) {
                        $seasonDetails = $this->getSeasonEpisodes($seasonData['id']);
                        if (isset($seasonDetails['data']) && is_array($seasonDetails['data'])) {
                            $episodesCount = count($seasonDetails['data']);
                        }
                    }

                    // Get season number
                    $seasonNumber = $seasonData['seasonNumber'] ?? $seasonData['numberOfSeason'] ?? $seasonData['number'] ?? 0;

                    $seasonInfo = [
                        'id' => $seasonData['id'] ?? '',
                        'title' => $seasonData['title'] ?? '',
                        'season_number' => $seasonNumber,
                        'episodes_count' => $episodesCount,
                        'planned_episodes' => $plannedEpisodes, // Add planned episodes
                        'description' => $seasonData['description'] ?? '',
                        'poster_url' => $this->processImageUrl($seasonData['image']['posterClean'] ?? ($seasonData['image']['poster'] ?? '')),
                        // Add series details from the first season
                        'series_details' => $index === 0 ? $this->extractSeriesDetails($data['productModel']) : null
                    ];

                    // Log final season info
                    Log::info("Final season info", [
                        'season_title' => $seasonInfo['title'],
                        'episodes_count' => $seasonInfo['episodes_count'],
                        'planned_episodes' => $seasonInfo['planned_episodes'],
                        'season_number' => $seasonInfo['season_number']
                    ]);

                    $seasons[] = $seasonInfo;
                }
            }

            return ['success' => true, 'data' => $seasons];

        } catch (\Exception $e) {
            Log::error("Error getting series seasons: " . $e->getMessage());
            return ['error' => 'Failed to get series seasons: ' . $e->getMessage()];
        }
    }

    /**
     * Get season episodes - Use the working method from original file and process images
     */
    public function getSeasonEpisodes($seasonId)
    {
        // Just call the original working method from ShahidAPI
        $originalAPI = new \App\Services\ShahidAPI();
        $result = $originalAPI->getSeasonEpisodes($seasonId);

        // Process images if episodes found
        if (isset($result['success']) && $result['success'] && isset($result['data'])) {
            Log::info("=== PROCESSING EPISODE IMAGES ===", [
                'episodes_count' => count($result['data']),
                'first_episode_keys' => isset($result['data'][0]) ? array_keys($result['data'][0]) : 'no_episodes'
            ]);

            foreach ($result['data'] as &$episode) {
                // Log episode structure for debugging
                Log::info("=== EPISODE STRUCTURE ===", [
                    'episode_keys' => array_keys($episode),
                    'has_thumbnail' => isset($episode['thumbnail']),
                    'has_poster_url' => isset($episode['poster_url']),
                    'thumbnail_value' => $episode['thumbnail'] ?? 'not_found',
                    'poster_url_value' => $episode['poster_url'] ?? 'not_found'
                ]);

                // Process thumbnail image
                if (isset($episode['thumbnail']) && !empty($episode['thumbnail'])) {
                    $episode['thumbnail'] = $this->processImageUrl($episode['thumbnail']);
                }

                // Process poster image
                if (isset($episode['poster_url']) && !empty($episode['poster_url'])) {
                    $episode['poster_url'] = $this->processImageUrl($episode['poster_url']);
                }

                Log::info("=== PROCESSED EPISODE IMAGE ===", [
                    'episode_id' => $episode['id'] ?? 'unknown',
                    'thumbnail_processed' => $episode['thumbnail'] ?? 'empty'
                ]);
            }
        }

        return $result;
    }

    /**
     * Get episode playout URL
     */
    public function getEpisodePlayoutUrl($episodeId, $country = 'SA')
    {
        try {
            Log::info("📡 Getting episode playout URL for: {$episodeId} (Country: {$country})");

            if (!$this->hasValidToken()) {
                return ['error' => 'No valid token found'];
            }

            // Country to IP mapping for geo-bypass
            $countryIpMap = [
                'SA' => '*************',  // Saudi Arabia
                'AE' => '*********',      // UAE
                'EG' => '***********',    // Egypt
                'KW' => '**********',     // Kuwait
                'QA' => '**********',     // Qatar
                'BH' => '************',   // Bahrain
                'OM' => '*********',      // Oman
                'JO' => '*************',  // Jordan
                'LB' => '***********',    // Lebanon
                'IQ' => '**********',     // Iraq
            ];

            // Try multiple countries for better success rate
            $countriesToTry = [$country, 'SA', 'AE', 'EG', 'KW', 'QA'];

            foreach ($countriesToTry as $tryCountry) {
                $forwardedIp = $countryIpMap[$tryCountry] ?? '*************';

                $headers = [
                    'authority' => 'api2.shahid.net',
                    'accept' => 'application/json',
                    'accept-language' => 'ar',
                    'content-type' => 'application/json',
                    'language' => 'ar',
                    'origin' => 'https://shahid.mbc.net',
                    'referer' => 'https://shahid.mbc.net/',
                    'token' => $this->token,
                    'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'uuid' => 'web',
                    'x-forwarded-for' => $forwardedIp,
                    'cf-ipcountry' => $tryCountry,
                ];

                $playoutUrl = "https://api3.shahid.net/proxy/v2.1/playout/new/url/{$episodeId}?outputParameter=vmap&country={$tryCountry}";

                Log::info("🌍 Trying playout request for country: {$tryCountry} (IP: {$forwardedIp})");

                try {
                    $response = Http::withHeaders($headers)
                        ->timeout(15)
                        ->get($playoutUrl);

                    if ($response->successful()) {
                        $data = $response->json();

                        Log::info("📋 Playout response structure: " . json_encode(array_keys($data)));
                        if (isset($data['playout'])) {
                            Log::info("📋 Playout keys: " . json_encode(array_keys($data['playout'])));
                            if (isset($data['playout']['url'])) {
                                Log::info("🔗 Raw playout URL: " . $data['playout']['url']);
                            }
                        }

                        if (isset($data['playout']['url'])) {
                            Log::info("✅ Successfully got playout URL for country: {$tryCountry}");
                            return [
                                'success' => true,
                                'playout' => $data['playout'],
                                'country' => $tryCountry
                            ];
                        }
                    }

                    Log::warning("⚠️ Playout request failed for country: {$tryCountry} - Status: {$response->status()}");

                } catch (\Exception $e) {
                    Log::warning("⚠️ Exception for country {$tryCountry}: " . $e->getMessage());
                    continue;
                }
            }

            Log::error("❌ All playout requests failed for episode: {$episodeId}");
            return ['error' => 'Failed to get playout URL for all countries'];

        } catch (\Exception $e) {
            Log::error("💥 Exception in getEpisodePlayoutUrl: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get episode details (title, series, season info)
     */
    public function getEpisodeDetails($episodeId)
    {
        try {
            Log::info("📋 Getting episode details for: {$episodeId}");

            // Try to find the episode in our cached data or API
            $episodeData = $this->findEpisodeInSeasons($episodeId);

            if ($episodeData) {
                return $episodeData;
            }

            // Fallback: try direct API call
            return $this->getEpisodeDetailsFromAPI($episodeId);

        } catch (\Exception $e) {
            Log::error("❌ Error getting episode details: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Find episode in seasons data
     */
    private function findEpisodeInSeasons($episodeId)
    {
        // This would require searching through seasons/episodes
        // For now, return null to use fallback
        return null;
    }

    /**
     * Get episode details from API (fallback)
     */
    private function getEpisodeDetailsFromAPI($episodeId)
    {
        try {
            // Try to get episode info from Shahid API
            $originalAPI = new \App\Services\ShahidAPI();
            $episodeDetails = $originalAPI->getContentDetails($episodeId);

            if (isset($episodeDetails['data']['productModel'])) {
                $episode = $episodeDetails['data']['productModel'];

                // ✅ Debug episode data structure
                Log::info("📋 Episode productModel keys: " . json_encode(array_keys($episode)));
                Log::info("📋 Episode title field: " . json_encode($episode['title'] ?? 'NOT_FOUND'));
                Log::info("📋 Episode description field: " . json_encode($episode['description'] ?? 'NOT_FOUND'));
                Log::info("📋 Episode shortDescription field: " . json_encode($episode['shortDescription'] ?? 'NOT_FOUND'));
                Log::info("📋 Episode number field: " . json_encode($episode['number'] ?? 'NOT_FOUND'));

                // ✅ Try multiple fields for episode title
                $episodeTitle = $episode['title'] ?? $episode['shortDescription'] ?? $episode['description'] ?? 'Episode';

                $seriesName = '';
                $seasonNumber = 0;
                $episodeNumber = 0;

                // ✅ Get episode number first
                if (isset($episode['episodeNumber'])) {
                    $episodeNumber = (int)$episode['episodeNumber'];
                } elseif (isset($episode['number'])) {
                    $episodeNumber = (int)$episode['number'];
                }

                // ✅ If title is empty, try to create one from episode number
                if (empty($episodeTitle) || $episodeTitle === '') {
                    $episodeTitle = $episodeNumber > 0 ? "Episode {$episodeNumber}" : 'Episode';
                }

                // Get series name from parent or series reference
                if (isset($episode['series']['title'])) {
                    $seriesName = $episode['series']['title'];
                } elseif (isset($episode['parentTitle'])) {
                    $seriesName = $episode['parentTitle'];
                } elseif (isset($episode['seriesTitle'])) {
                    $seriesName = $episode['seriesTitle'];
                }

                // Get season number
                if (isset($episode['seasonNumber'])) {
                    $seasonNumber = (int)$episode['seasonNumber'];
                } elseif (isset($episode['season']['seasonNumber'])) {
                    $seasonNumber = (int)$episode['season']['seasonNumber'];
                }

                // Get episode number
                if (isset($episode['episodeNumber'])) {
                    $episodeNumber = (int)$episode['episodeNumber'];
                } elseif (isset($episode['number'])) {
                    $episodeNumber = (int)$episode['number'];
                }

                return [
                    'title' => $episodeTitle,
                    'series_title' => $seriesName ?: 'Unknown Series',
                    'season_number' => $seasonNumber,
                    'episode_number' => $episodeNumber
                ];
            }

            // Fallback if no data found
            return [
                'title' => 'Episode',
                'series_title' => 'Unknown Series',
                'season_number' => 0,
                'episode_number' => 0
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error getting episode details from API: " . $e->getMessage());
            return [
                'title' => 'Unknown Episode',
                'series_title' => 'Unknown Series',
                'season_number' => 0,
                'episode_number' => 0
            ];
        }
    }

    /**
     * Extract series details from product model
     */
    private function extractSeriesDetails($productModel)
    {
        if (!$productModel) {
            return null;
        }

        // Extract cast from persons array - keep original structure
        $cast = [];
        if (isset($productModel['persons']) && is_array($productModel['persons'])) {
            foreach ($productModel['persons'] as $person) {
                if (isset($person['fullName']) && !empty($person['fullName'])) {
                    $cast[] = [
                        'name' => $person['fullName'],
                        'role' => $person['role'] ?? 'Actor'
                    ];
                }
            }
        }

        // Extract genres
        $genres = [];
        if (isset($productModel['genres']) && is_array($productModel['genres'])) {
            foreach ($productModel['genres'] as $genre) {
                if (isset($genre['title'])) {
                    $genres[] = $genre['title'];
                }
            }
        }

        return [
            'title' => $productModel['title'] ?? '',
            'description' => $productModel['description'] ?? '',
            'image' => $productModel['image'] ?? null,
            'persons' => $cast,
            'genres' => $genres,
            'dialect' => $productModel['dialect'] ?? null,
            'rating' => $productModel['rating'] ?? null,
            'duration' => $productModel['duration'] ?? null,
            'releaseDate' => $productModel['releaseDate'] ?? null,
            'productionDate' => $productModel['productionDate'] ?? null,
            'createdDate' => $productModel['createdDate'] ?? null,
            'totalNumberOfEpisodes' => $productModel['totalNumberOfEpisodes'] ?? 0,
            'numberOfEpisodes' => $productModel['numberOfEpisodes'] ?? 0,
            'plannedEpisodes' => $productModel['plannedEpisodes'] ?? 0
        ];
    }

    /**
     * Process season data with proper episode counting logic from original ShahidAPI
     */
    private function processSeasonData($season, $seriesDetails = null)
    {
        if (!$season || !isset($season['id'])) {
            return null;
        }

        Log::info("=== PROCESSING SEASON DATA ===", [
            'season_id' => $season['id'],
            'season_title' => $season['title'] ?? 'Unknown',
            'raw_season_data' => $season
        ]);

        // Initialize episode counts
        $episodesCount = 0;
        $plannedEpisodes = 0;

        // Method 1: Try numberOfAVODEpisodes first (most accurate)
        if (isset($season['numberOfAVODEpisodes'])) {
            $episodesCount = $season['numberOfAVODEpisodes'];
            Log::info("=== METHOD 1: Using numberOfAVODEpisodes ===", [
                'season_id' => $season['id'],
                'count' => $episodesCount
            ]);
        }

        // Method 2: Try episodes array count
        if ($episodesCount === 0 && isset($season['episodes']) && is_array($season['episodes'])) {
            $episodesCount = count($season['episodes']);
            Log::info("=== METHOD 2: Using episodes array count ===", [
                'season_id' => $season['id'],
                'count' => $episodesCount
            ]);
        }

        // Method 3: Check playlists for more accurate episode count (Python logic)
        if (isset($season['playlists']) && is_array($season['playlists'])) {
            foreach ($season['playlists'] as $playlist) {
                if (isset($playlist['type']) && $playlist['type'] === 'EPISODE') {
                    $playlistCount = $playlist['count'] ?? 0;
                    if ($playlistCount > $episodesCount) {
                        Log::info("=== METHOD 3: Updated from playlist ===", [
                            'season_id' => $season['id'],
                            'old_count' => $episodesCount,
                            'new_count' => $playlistCount,
                            'playlist_id' => $playlist['id'] ?? 'no_id'
                        ]);
                        $episodesCount = $playlistCount;
                    }
                }
            }
        }

        // Method 4: If still 0, try to get actual episodes count by calling season details
        if ($episodesCount === 0 && isset($season['id'])) {
            Log::info("=== METHOD 4: Getting actual season details ===", [
                'season_id' => $season['id']
            ]);
            $seasonDetails = $this->getSeasonEpisodes($season['id']);
            if (isset($seasonDetails['episodes']) && is_array($seasonDetails['episodes'])) {
                $episodesCount = count($seasonDetails['episodes']);
                Log::info("=== METHOD 4: Updated from season details ===", [
                    'season_id' => $season['id'],
                    'count' => $episodesCount
                ]);
            } elseif (isset($seasonDetails['success']) && $seasonDetails['success'] && isset($seasonDetails['episodes'])) {
                // Handle the case where getSeasonEpisodes returns ['success' => true, 'episodes' => [...]]
                $episodesCount = count($seasonDetails['episodes']);
                Log::info("=== METHOD 4: Updated from season details (success format) ===", [
                    'season_id' => $season['id'],
                    'count' => $episodesCount
                ]);
            }
        }

        // Get planned episodes
        if (isset($season['numberOfEpisodes'])) {
            $plannedEpisodes = $season['numberOfEpisodes'];
        } elseif (isset($season['plannedEpisodes'])) {
            $plannedEpisodes = $season['plannedEpisodes'];
        }

        // Get season number with multiple fallbacks
        $seasonNumber = $season['seasonNumber'] ?? $season['numberOfSeason'] ?? $season['number'] ?? 1;

        $result = [
            'id' => $season['id'],
            'title' => $season['title'] ?? 'Unknown Season',
            'season_number' => $seasonNumber,
            'episodes_count' => $episodesCount,
            'planned_episodes' => $plannedEpisodes,
            'description' => $season['description'] ?? '',
            'poster_url' => $this->extractPosterUrl($season),
            'series_details' => $seriesDetails
        ];

        Log::info("=== FINAL SEASON RESULT ===", [
            'season_id' => $season['id'],
            'episodes_count' => $episodesCount,
            'planned_episodes' => $plannedEpisodes,
            'season_number' => $seasonNumber
        ]);

        return $result;
    }

    /**
     * Process episode data
     */
    private function processEpisodeData($episode)
    {
        if (!$episode || !isset($episode['id'])) {
            return null;
        }

        return [
            'id' => $episode['id'],
            'title' => $episode['title'] ?? 'Unknown Episode',
            'episode_number' => $episode['episodeNumber'] ?? 1,
            'description' => $episode['description'] ?? '',
            'duration' => $episode['duration'] ?? null,
            'poster_url' => $this->processImageUrl($episode['image']['posterClean'] ?? ($episode['image']['poster'] ?? '')),
            'thumbnail_url' => $this->processImageUrl($episode['image']['thumbnailImage'] ?? ''),
            'release_date' => $episode['releaseDate'] ?? null,
            'is_coming_soon' => $episode['isComingSoon'] ?? false
        ];
    }

    /**
     * Search content by query with enhanced search capabilities
     */
    public function searchContent($query, $limit = 20, $type = null)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        try {
            Log::info("=== SEARCHING CONTENT ===", [
                'query' => $query,
                'limit' => $limit,
                'type' => $type
            ]);

            $allResults = [];

            // Check if query is numeric (ID search)
            $isIdSearch = is_numeric($query);

            if ($isIdSearch) {
                Log::info("=== ID SEARCH DETECTED ===", ['id' => $query]);

                // For ID search, try to get content details directly
                $contentDetails = $this->getContentDetails($query);
                if ($contentDetails && !isset($contentDetails['error'])) {
                    $formatted = $this->formatSearchResultFromDetails($contentDetails, $query);
                    if ($formatted) {
                        $allResults[$query] = $formatted;
                    }
                }
            }

            // Search in multiple sources
            $searchSources = [
                // Primary search API
                [
                    'url' => 'https://api3.shahid.net/proxy/v2.1/t-search',
                    'countries' => ['EG', 'SA', 'AE', 'KW', 'QA', 'BH', 'OM']
                ],
                // Alternative search API
                [
                    'url' => 'https://api2.shahid.net/proxy/v2.1/t-search',
                    'countries' => ['EG', 'SA']
                ]
            ];

            foreach ($searchSources as $sourceIndex => $source) {
                foreach ($source['countries'] as $country) {
                    // Use basic search parameters (API only accepts: pageNumber, name, pageSize, language)
                    $searchRequest = [
                        'name' => $query,
                        'pageNumber' => 0,
                        'pageSize' => $isIdSearch ? 10 : $limit, // Smaller page size for ID search
                    ];

                    $params = [
                        'request' => json_encode($searchRequest),
                        'exactMatch' => $isIdSearch ? 'true' : 'false',
                        'country' => $country
                    ];

                    Log::info("=== SEARCHING IN SOURCE ===", [
                        'source' => $sourceIndex + 1,
                        'country' => $country,
                        'query' => $query,
                        'exact_match' => $isIdSearch,
                        'url' => $source['url']
                    ]);

                    $result = $this->makeRequest('GET', $source['url'], ['data' => $params, 'auth' => true]);

                    if ($result['success'] && isset($result['data']['productList']['products'])) {
                        $products = $result['data']['productList']['products'];

                        foreach ($products as $product) {
                            try {
                                $productId = $product['id'] ?? '';
                                $productType = strtolower($product['productSubType'] ?? '');

                                Log::debug("Processing product", [
                                    'id' => $productId,
                                    'title' => $product['title'] ?? '',
                                    'type' => $productType,
                                    'available' => $product['available'] ?? 'not_set'
                                ]);

                                // Filter by type if specified
                                if ($type && $productType !== strtolower($type)) {
                                    Log::debug("Skipping due to type filter", [
                                        'product_type' => $productType,
                                        'required_type' => strtolower($type)
                                    ]);
                                    continue;
                                }

                                // For series search, accept both 'series' and 'show' types
                                if ($type && strtolower($type) === 'series') {
                                    if (!in_array($productType, ['series', 'show'])) {
                                        continue;
                                    }
                                }

                                // Filter out unavailable content (but be lenient)
                                if (isset($product['available']) && $product['available'] === false) {
                                    Log::debug("Skipping unavailable content", ['id' => $productId]);
                                    continue;
                                }

                                // For ID search, check if this product matches the query
                                if ($isIdSearch) {
                                    // Convert both to strings for comparison
                                    $productIdStr = (string)$productId;
                                    $queryStr = (string)$query;

                                    // Check for exact match OR if the product contains the query ID
                                    $isMatch = ($productIdStr === $queryStr) ||
                                              (strpos($productIdStr, $queryStr) !== false) ||
                                              (strpos($queryStr, $productIdStr) !== false);

                                    if (!$isMatch) {
                                        Log::debug("Skipping non-matching ID", [
                                            'product_id' => $productIdStr,
                                            'query' => $queryStr,
                                            'product_title' => $product['title'] ?? ''
                                        ]);
                                        continue;
                                    } else {
                                        Log::info("Found ID match!", [
                                            'product_id' => $productIdStr,
                                            'query' => $queryStr,
                                            'title' => $product['title'] ?? '',
                                            'match_type' => $productIdStr === $queryStr ? 'exact' : 'partial'
                                        ]);
                                    }
                                }

                                // Avoid duplicates
                                if (!isset($allResults[$productId])) {
                                    $formatted = $this->formatSearchResult($product);
                                    if ($formatted) {
                                        $allResults[$productId] = $formatted;
                                        Log::debug("Added result", [
                                            'id' => $productId,
                                            'title' => $formatted['title']
                                        ]);
                                    }
                                }
                            } catch (\Exception $e) {
                                Log::error("Error formatting search result: " . $e->getMessage(), [
                                    'product_id' => $product['id'] ?? 'unknown',
                                    'product' => $product
                                ]);
                                continue; // Skip this product and continue with others
                            }
                        }

                        Log::info("=== SOURCE SEARCH RESULT ===", [
                            'source' => $sourceIndex + 1,
                            'country' => $country,
                            'found' => count($products),
                            'total_unique' => count($allResults)
                        ]);
                    }

                    // For ID search, if we found exact match, break early
                    if ($isIdSearch && count($allResults) > 0) {
                        break 2; // Break both loops
                    }

                    // Break early if we have enough results
                    if (count($allResults) >= $limit * 2) {
                        break 2; // Break both loops
                    }
                }

                // If we found results in first source, don't try second source for text search
                if (!$isIdSearch && count($allResults) > 0) {
                    break;
                }
            }

            // Sort results by relevance for text searches
            $finalResults = array_values($allResults);

            Log::info("=== SEARCH COMPLETE ===", [
                'query' => $query,
                'is_id_search' => $isIdSearch,
                'total_results' => count($finalResults)
            ]);

            return ['success' => true, 'data' => $finalResults];

        } catch (\Exception $e) {
            Log::error("Error searching content: " . $e->getMessage());
            return ['error' => 'Search failed: ' . $e->getMessage()];
        }
    }

    /**
     * Format search result for consistent output
     */
    private function formatSearchResult($product)
    {
        // Safe year extraction
        $year = null;
        if (isset($product['productionDate']) && $product['productionDate']) {
            $year = date('Y', strtotime($product['productionDate']));
        } elseif (isset($product['releaseDate']) && $product['releaseDate']) {
            $year = date('Y', strtotime($product['releaseDate']));
        }

        // Extract poster URL using the proper method
        $posterUrl = $this->extractPosterUrl($product);

        return [
            'id' => $product['id'] ?? '',
            'title' => $product['title'] ?? 'Unknown Title',
            'type' => $product['productSubType'] ?? 'unknown',
            'year' => $year,
            'description' => $product['description'] ?? '',
            'poster_url' => $posterUrl,
            'thumbnail_url' => $product['image']['thumbnailImage'] ?? '',
            'available' => $product['available'] ?? true,
            'dialect' => $product['dialect']['title'] ?? null,
            'genres' => array_map(function($genre) {
                return $genre['title'] ?? '';
            }, $product['genres'] ?? [])
        ];
    }

    /**
     * Format content details as search result
     */
    private function formatSearchResultFromDetails($details, $id)
    {
        if (!$details || isset($details['error'])) {
            return null;
        }

        $data = $details['data'] ?? $details;
        $productModel = $data['productModel'] ?? $data;

        if (!$productModel) {
            return null;
        }

        // Safe year extraction
        $year = null;
        if (isset($productModel['productionDate']) && $productModel['productionDate']) {
            $year = date('Y', strtotime($productModel['productionDate']));
        } elseif (isset($productModel['releaseDate']) && $productModel['releaseDate']) {
            $year = date('Y', strtotime($productModel['releaseDate']));
        }

        // Extract poster URL using the proper method
        $posterUrl = $this->extractPosterUrl($productModel);

        return [
            'id' => $productModel['id'] ?? $id,
            'title' => $productModel['title'] ?? 'Unknown Title',
            'type' => $productModel['productSubType'] ?? 'unknown',
            'year' => $year,
            'description' => $productModel['description'] ?? '',
            'poster_url' => $posterUrl,
            'thumbnail_url' => $productModel['image']['thumbnailImage'] ?? '',
            'available' => $productModel['available'] ?? true,
            'dialect' => $productModel['dialect']['title'] ?? null,
            'genres' => array_map(function($genre) {
                return $genre['title'] ?? '';
            }, $productModel['genres'] ?? [])
        ];
    }

    /**
     * Get content details by ID (override base method with correct implementation)
     */
    public function getContentDetails($contentId)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        try {
            Log::info("=== GETTING CONTENT DETAILS ===", ['content_id' => $contentId]);

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'uuid' => 'web',
            ];

            // Try different endpoints and product types
            $endpoints = [
                [
                    'url' => 'https://api3.shahid.net/proxy/v2.1/product/id',
                    'params' => [
                        'request' => '{"id":"' . $contentId . '","productType":"SERIES"}',
                        'country' => 'EG',
                    ]
                ],
                [
                    'url' => 'https://api3.shahid.net/proxy/v2.1/product/id',
                    'params' => [
                        'request' => '{"id":"' . $contentId . '","productType":"MOVIE"}',
                        'country' => 'EG',
                    ]
                ],
                [
                    'url' => 'https://api2.shahid.net/proxy/v2.1/product/id',
                    'params' => [
                        'request' => '{"id":"' . $contentId . '","productType":"SERIES"}',
                        'country' => 'EG',
                    ]
                ]
            ];

            foreach ($endpoints as $index => $endpoint) {
                Log::info("=== TRYING ENDPOINT ===", [
                    'endpoint' => $index + 1,
                    'url' => $endpoint['url'],
                    'params' => $endpoint['params']
                ]);

                $httpClient = $this->proxyService->getHttpClient(['timeout' => 30])->withHeaders($headers);
                $response = $httpClient->get($endpoint['url'], $endpoint['params']);

                if ($response->successful()) {
                    $data = $response->json();

                    Log::info("=== CONTENT DETAILS SUCCESS ===", [
                        'endpoint' => $index + 1,
                        'has_data' => !empty($data),
                        'data_keys' => array_keys($data ?? [])
                    ]);

                    if (!empty($data)) {
                        return [
                            'success' => true,
                            'type' => strpos($endpoint['params']['request'], 'SERIES') !== false ? 'series' : 'movie',
                            'id' => $contentId,
                            'data' => $data
                        ];
                    }
                } else {
                    Log::warning("Endpoint failed", [
                        'endpoint' => $index + 1,
                        'status' => $response->status(),
                        'body' => $response->body()
                    ]);
                }
            }

            Log::error("All content detail endpoints failed", ['content_id' => $contentId]);
            return ['error' => 'Content not found'];

        } catch (\Exception $e) {
            Log::error("Exception in getContentDetails", [
                'content_id' => $contentId,
                'error' => $e->getMessage()
            ]);
            return ['error' => 'Failed to get content details: ' . $e->getMessage()];
        }
    }


    /**
     * Make HTTP request with custom headers
     */
    private function makeRequestWithCustomHeaders($method, $url, $params = [], $customHeaders = [])
    {
        try {
            $client = new \GuzzleHttp\Client([
                'timeout' => 30,
                'verify' => false,
            ]);

            $options = [
                'headers' => $customHeaders,
            ];

            if ($method === 'GET') {
                $options['query'] = $params;
            } else {
                $options['json'] = $params;
            }

            $response = $client->request($method, $url, $options);
            $data = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error("HTTP request failed", [
                'method' => $method,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get episodes from a playlist using pagination
     */
    private function getPlaylistEpisodes($playlistId)
    {
        try {
            Log::info("=== GETTING PLAYLIST EPISODES ===", ['playlist_id' => $playlistId]);

            $allEpisodes = [];
            $page = 0;
            $pageSize = 50; // Reasonable page size

            while (true) {
                $url = "https://api2.shahid.net/proxy/v2.1/editorial/playlist";
                $params = [
                    'request' => json_encode([
                        'id' => $playlistId,
                        'pageNumber' => $page,
                        'pageSize' => $pageSize
                    ]),
                    'country' => 'EG'
                ];

                $headers = [
                    'authority' => 'api2.shahid.net',
                    'accept' => 'application/json',
                    'accept-language' => 'en',
                    'content-type' => 'application/json',
                    'language' => 'en',
                    'origin' => 'https://shahid.mbc.net',
                    'referer' => 'https://shahid.mbc.net/',
                    'token' => $this->token,
                    'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'uuid' => 'web',
                ];

                Log::info("=== PLAYLIST PAGE REQUEST ===", [
                    'playlist_id' => $playlistId,
                    'page' => $page,
                    'page_size' => $pageSize
                ]);

                $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

                if (!$result['success']) {
                    Log::error("Failed to get playlist page {$page}: " . ($result['error'] ?? 'Unknown error'));
                    break;
                }

                $data = $result['data'];

                // Check if we have products in the response
                if (!isset($data['productList']['products']) || !is_array($data['productList']['products'])) {
                    Log::warning("No products found in playlist response", ['page' => $page]);
                    break;
                }

                $currentPageEpisodes = $data['productList']['products'];
                $episodeCount = count($currentPageEpisodes);

                Log::info("=== PAGE RESULT ===", [
                    'page' => $page,
                    'episodes_found' => $episodeCount,
                    'total_so_far' => count($allEpisodes)
                ]);

                // If no episodes in this page, we're done
                if ($episodeCount === 0) {
                    Log::info("No episodes found in page {$page}, stopping pagination");
                    break;
                }

                // Add episodes from this page to our collection
                $allEpisodes = array_merge($allEpisodes, $currentPageEpisodes);

                // If we got less than pageSize episodes, we're done (last page)
                if ($episodeCount < $pageSize) {
                    Log::info("Got {$episodeCount} episodes (less than page size {$pageSize}), stopping pagination");
                    break;
                }

                // Move to next page
                $page++;

                // Safety limit to prevent infinite loops
                if ($page > 20) {
                    Log::warning("Reached page limit (20), stopping pagination");
                    break;
                }
            }

            Log::info("=== PAGINATION COMPLETE ===", [
                'playlist_id' => $playlistId,
                'total_episodes' => count($allEpisodes),
                'total_pages' => $page + 1
            ]);

            return $allEpisodes;

        } catch (\Exception $e) {
            Log::error("Error getting playlist episodes: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Search for episodes by season ID using search API
     */
    private function searchEpisodesBySeason($seasonId)
    {
        try {
            Log::info("=== SEARCH API: Starting search for episodes ===", ['season_id' => $seasonId]);

            // Try different search approaches
            $searchQueries = [
                $seasonId, // Direct season ID
                "season:" . $seasonId, // Season prefix
                "seasonId:" . $seasonId // SeasonId prefix
            ];

            foreach ($searchQueries as $query) {
                Log::info("=== SEARCH API: Trying query ===", ['query' => $query]);

                $url = "https://api2.shahid.net/proxy/v2.1/product/filter";
                $params = [
                    'filter' => json_encode([
                        'pageNumber' => 0,
                        'pageSize' => 50,
                        'productType' => 'EPISODE',
                        'query' => $query
                    ]),
                    'country' => 'EG'
                ];

                $headers = [
                    'authority' => 'api2.shahid.net',
                    'accept' => 'application/json',
                    'accept-language' => 'en',
                    'content-type' => 'application/json',
                    'language' => 'en',
                    'origin' => 'https://shahid.mbc.net',
                    'referer' => 'https://shahid.mbc.net/',
                    'token' => $this->token,
                    'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'uuid' => 'web'
                ];

                $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

                if ($result['success'] && isset($result['data']['products']) && is_array($result['data']['products'])) {
                    $episodes = $result['data']['products'];
                    Log::info("=== SEARCH API: Found episodes ===", ['query' => $query, 'count' => count($episodes)]);

                    if (count($episodes) > 0) {
                        return $episodes;
                    }
                } else {
                    Log::info("=== SEARCH API: No episodes found ===", ['query' => $query]);
                }
            }

            return [];

        } catch (\Exception $e) {
            Log::error("=== SEARCH API: Error ===", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Process image URL - replace placeholders and optimize
     */
    protected function processImageUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // If it's a mediaObject URL, rebuild with exact format
        if (strpos($url, 'mediaObject/') !== false) {
            // Extract mediaObject ID
            if (preg_match('/mediaObject\/([^?]+)/', $url, $matches)) {
                $mediaObjectId = $matches[1];
                // Rebuild URL with exact format
                return "https://shahid.mbc.net/mediaObject/{$mediaObjectId}?width=300&version=1&type=avif&q=80";
            }
        }

        // Fallback: Replace placeholders with actual values
        $url = str_replace('{height}', '150', $url);
        $url = str_replace('{width}', '150', $url);
        $url = str_replace('{croppingPoint}', '', $url);

        return $url;
    }

}
