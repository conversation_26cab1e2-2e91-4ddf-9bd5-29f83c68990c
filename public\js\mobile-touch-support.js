/**
 * Mobile Touch Support for Episode Cards
 * This script provides touch support for episode play buttons on mobile devices
 */

(function($) {
    'use strict';

    // Touch support configuration
    const TouchSupport = {
        isTouchDevice: false,
        isMobile: false,
        touchStartTime: 0,
        touchMoved: false,
        
        init: function() {
            this.detectDevice();
            this.addTouchSupport();
            this.handleResize();
        },
        
        detectDevice: function() {
            // Detect if device supports touch
            this.isTouchDevice = 'ontouchstart' in window || 
                               navigator.maxTouchPoints > 0 || 
                               navigator.msMaxTouchPoints > 0;
            
            this.isMobile = window.innerWidth <= 768;
            
            // Add classes to body for CSS targeting
            if (this.isTouchDevice) {
                $('body').addClass('touch-device');
            }
            
            if (this.isMobile) {
                $('body').addClass('mobile-device');
            }
            
            console.log('Touch Support - Touch Device:', this.isTouchDevice, 'Mobile:', this.isMobile);
        },
        
        addTouchSupport: function() {
            const self = this;
            
            // Remove existing event listeners to prevent duplicates
            $('.episode-card').off('touchstart.touchSupport touchend.touchSupport touchcancel.touchSupport touchmove.touchSupport');
            $('.episode-card').off('mouseenter.touchSupport mouseleave.touchSupport');
            
            $('.episode-card').each(function() {
                const $card = $(this);
                const $overlay = $card.find('.episode-play-overlay');
                
                // For mobile devices, show play button by default
                if (self.isMobile) {
                    $overlay.css('opacity', '0.9');
                }
                
                // Touch events
                if (self.isTouchDevice) {
                    // Touch start
                    $card.on('touchstart.touchSupport', function(e) {
                        self.touchStartTime = Date.now();
                        self.touchMoved = false;
                        
                        $card.addClass('touch-active');
                        $overlay.css({
                            'opacity': '1',
                            'transform': 'translate(-50%, -50%) scale(1.05)'
                        });
                    });
                    
                    // Touch move - detect if user is scrolling
                    $card.on('touchmove.touchSupport', function(e) {
                        self.touchMoved = true;
                    });
                    
                    // Touch end
                    $card.on('touchend.touchSupport', function(e) {
                        const touchDuration = Date.now() - self.touchStartTime;
                        
                        // Only trigger click if it's a quick tap (not a long press or scroll)
                        if (!self.touchMoved && touchDuration < 500) {
                            e.preventDefault();
                            
                            // Add visual feedback
                            $overlay.css('transform', 'translate(-50%, -50%) scale(1.1)');
                            
                            // Trigger click after a short delay for visual feedback
                            setTimeout(() => {
                                // Find the clickable element (could be the card or a link inside)
                                const clickTarget = $card.find('a').length > 0 ? $card.find('a').first() : $card;
                                clickTarget.trigger('click');
                            }, 100);
                        }
                        
                        // Reset state
                        setTimeout(() => {
                            $card.removeClass('touch-active');
                            $overlay.css('transform', 'translate(-50%, -50%) scale(1)');
                            
                            // Keep visible on mobile, hide on desktop
                            if (!self.isMobile) {
                                $overlay.css('opacity', '0');
                            }
                        }, 200);
                    });
                    
                    // Touch cancel
                    $card.on('touchcancel.touchSupport', function(e) {
                        $card.removeClass('touch-active');
                        $overlay.css('transform', 'translate(-50%, -50%) scale(1)');
                        
                        if (!self.isMobile) {
                            $overlay.css('opacity', '0');
                        }
                    });
                } else {
                    // Desktop hover events
                    $card.on('mouseenter.touchSupport', function() {
                        $overlay.css('opacity', '1');
                    });
                    
                    $card.on('mouseleave.touchSupport', function() {
                        $overlay.css('opacity', '0');
                    });
                }
            });
        },
        
        handleResize: function() {
            const self = this;
            let resizeTimer;
            
            $(window).on('resize.touchSupport', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(() => {
                    const newIsMobile = window.innerWidth <= 768;
                    
                    if (newIsMobile !== self.isMobile) {
                        self.isMobile = newIsMobile;
                        
                        // Update body classes
                        if (self.isMobile) {
                            $('body').addClass('mobile-device');
                        } else {
                            $('body').removeClass('mobile-device');
                        }
                        
                        // Refresh touch support
                        self.addTouchSupport();
                    }
                }, 250);
            });
        },
        
        // Public method to refresh touch support (useful when new episodes are loaded)
        refresh: function() {
            this.detectDevice();
            this.addTouchSupport();
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        TouchSupport.init();
    });
    
    // Make TouchSupport available globally for manual refresh
    window.TouchSupport = TouchSupport;
    
    // Auto-refresh when new content is added (MutationObserver)
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            let shouldRefresh = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // Check if new episode cards were added
                    $(mutation.addedNodes).each(function() {
                        if ($(this).hasClass('episode-card') || $(this).find('.episode-card').length > 0) {
                            shouldRefresh = true;
                            return false; // Break the loop
                        }
                    });
                }
            });
            
            if (shouldRefresh) {
                setTimeout(() => {
                    TouchSupport.refresh();
                }, 100);
            }
        });
        
        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

})(jQuery);
