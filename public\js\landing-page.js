/**
 * Landing Page Interactive Features
 */

(function() {
    'use strict';

    // DOM Elements
    const header = document.getElementById('header');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu');
    const heroSection = document.querySelector('.hero');
    
    // Initialize all features
    document.addEventListener('DOMContentLoaded', function() {
        initScrollEffects();
        initAnimations();
        initMobileMenu();
        initParticles();
        initLoadingScreen();
        initScrollProgress();
        initHeroTypingEffect();
    });

    // Scroll Effects
    function initScrollEffects() {
        let ticking = false;

        function updateScrollEffects() {
            const scrollY = window.pageYOffset;

            // Header scroll effect
            if (header) {
                if (scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }

            // Parallax effect for hero
            if (heroSection) {
                heroSection.style.transform = `translateY(${scrollY * 0.3}px)`;
            }

            ticking = false;
        }
        
        window.addEventListener('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        });
    }

    // Intersection Observer for Animations
    function initAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Add staggered animation for feature cards
                    if (entry.target.classList.contains('feature-card')) {
                        const cards = document.querySelectorAll('.feature-card');
                        cards.forEach((card, index) => {
                            setTimeout(() => {
                                card.style.animationDelay = `${index * 0.1}s`;
                                card.classList.add('slide-in-up');
                            }, index * 100);
                        });
                    }
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.fade-in-up, .feature-card, .hero-card').forEach(el => {
            observer.observe(el);
        });
    }

    // Mobile Menu
    function initMobileMenu() {
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', function() {
                navMenu.classList.toggle('active');
                
                // Animate hamburger icon
                const icon = this.querySelector('i');
                if (navMenu.classList.contains('active')) {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                } else {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!navMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                    navMenu.classList.remove('active');
                    const icon = mobileMenuBtn.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });
        }
    }

    // Particle System
    function initParticles() {
        if (!heroSection) return;

        const particleContainer = document.createElement('div');
        particleContainer.className = 'particles';
        heroSection.appendChild(particleContainer);

        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random position and animation
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 10 + 5) + 's';
            particle.style.animationDelay = Math.random() * 5 + 's';
            
            particleContainer.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 15000);
        }

        // Create particles periodically
        setInterval(createParticle, 2000);
    }

    // Loading Screen
    function initLoadingScreen() {
        const loadingScreen = document.createElement('div');
        loadingScreen.className = 'loading-screen';
        loadingScreen.innerHTML = `
            <div class="loading-logo">شاهد</div>
            <div class="loading-spinner"></div>
            <div class="loading-text">جاري تحميل المحتوى...</div>
        `;
        
        document.body.appendChild(loadingScreen);
        
        // Hide loading screen when page is loaded
        window.addEventListener('load', function() {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                document.body.style.opacity = '1';
                
                setTimeout(() => {
                    loadingScreen.remove();
                }, 500);
            }, 1000);
        });
    }

    // Scroll Progress Indicator
    function initScrollProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-indicator';
        progressBar.innerHTML = '<div class="scroll-progress"></div>';
        document.body.appendChild(progressBar);
        
        const progress = progressBar.querySelector('.scroll-progress');
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            progress.style.width = scrollPercent + '%';
        });
    }



    // Smooth Scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            
            if (target) {
                const headerHeight = header.offsetHeight;
                const targetPosition = target.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Button Click Effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Keyboard Navigation
    document.addEventListener('keydown', function(e) {
        // ESC key closes mobile menu
        if (e.key === 'Escape' && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            const icon = mobileMenuBtn.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        }
    });

    // Performance Optimization
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Optimized resize handler
    const handleResize = debounce(function() {
        // Recalculate any size-dependent features
        if (window.innerWidth > 768 && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            const icon = mobileMenuBtn.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        }
    }, 250);

    window.addEventListener('resize', handleResize);

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .animate-in {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
        
        .slide-in-up {
            animation: slideInUp 0.6s ease-out forwards;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: rippleEffect 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);

    // Hero Title Typing Effect
    function initHeroTypingEffect() {
        const heroTitle = document.getElementById('hero-title');
        const heroDescription = document.getElementById('hero-description');

        if (!heroTitle) return;

        // النص للكتابة
        const text = 'Teefa Live';

        // إخفاء العنوان فقط في البداية، النص الوصفي يبقى ظاهر
        heroTitle.style.opacity = '0';
        if (heroDescription) {
            heroDescription.style.opacity = '1';
            heroDescription.style.visibility = 'visible';
        }

        // إظهار مؤشر الكتابة على الشمال
        heroTitle.style.setProperty('--show-cursor', 'inline');

        let isTyping = true;
        let charIndex = 0;

        function typeWriter() {
            if (isTyping) {
                // مرحلة الكتابة
                if (charIndex < text.length) {
                    const char = text.charAt(charIndex);
                    if (char === ' ') {
                        heroTitle.innerHTML += '&nbsp;';
                    } else {
                        heroTitle.innerHTML += `<span style="color: white;">${char}</span>`;
                    }
                    charIndex++;
                    setTimeout(typeWriter, 150); // سرعة الكتابة
                } else {
                    // انتهت الكتابة، انتظر ثم ابدأ المسح
                    isTyping = false;
                    setTimeout(typeWriter, 2000); // انتظار قبل المسح
                }
            } else {
                // مرحلة المسح
                if (charIndex > 0) {
                    charIndex--;
                    heroTitle.innerHTML = '';
                    for (let i = 0; i < charIndex; i++) {
                        const char = text.charAt(i);
                        if (char === ' ') {
                            heroTitle.innerHTML += '&nbsp;';
                        } else {
                            heroTitle.innerHTML += `<span style="color: white;">${char}</span>`;
                        }
                    }
                    setTimeout(typeWriter, 100); // سرعة المسح
                } else {
                    // انتهى المسح، ابدأ الكتابة مرة أخرى
                    isTyping = true;
                    setTimeout(typeWriter, 500); // انتظار قبل الكتابة مرة أخرى
                }
            }
        }

        // بدء التأثير بعد تحميل الصفحة
        setTimeout(() => {
            heroTitle.style.opacity = '1';
            heroTitle.innerHTML = '';
            typeWriter();
        }, 1500);
    }

})();
