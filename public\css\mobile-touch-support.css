/* Mobile Touch Support for Episode Cards */
/* This CSS file provides touch support for episode play buttons on mobile devices */

/* Base styles for episode cards */
.episode-card {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.episode-play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
    pointer-events: auto;
}

/* Hover states for desktop */
.episode-card:hover .episode-play-overlay,
.episode-card.touch-active .episode-play-overlay {
    opacity: 1;
}

/* Mobile touch support - always show play button on mobile */
@media (max-width: 768px) {
    .episode-play-overlay {
        opacity: 0.9 !important;
        background: rgba(0,0,0,0.8) !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
    }
    
    .episode-card:active .episode-play-overlay,
    .episode-card.touch-active .episode-play-overlay {
        opacity: 1 !important;
        transform: translate(-50%, -50%) scale(1.1) !important;
        background: rgba(102, 126, 234, 0.9) !important;
    }
    
    /* Make episode cards more touch-friendly */
    .episode-card {
        margin-bottom: 15px !important;
        min-height: 200px;
    }
    
    .episode-thumbnail {
        height: 180px !important;
    }
    
    .episode-play-overlay {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.2rem !important;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .episode-play-overlay {
        width: 45px !important;
        height: 45px !important;
        font-size: 1rem !important;
    }
    
    .episode-thumbnail {
        height: 160px !important;
    }
    
    .episode-card {
        margin-bottom: 12px !important;
    }
}

/* Ensure play button is always visible and clickable on touch devices */
.touch-device .episode-play-overlay {
    opacity: 0.8 !important;
    pointer-events: auto !important;
}

/* Animation for touch feedback */
@keyframes touchPulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
    100% { transform: translate(-50%, -50%) scale(1); }
}

.episode-card.touch-active .episode-play-overlay {
    animation: touchPulse 0.3s ease-in-out;
}

/* Additional touch improvements */
@media (hover: none) and (pointer: coarse) {
    /* This targets touch devices specifically */
    .episode-play-overlay {
        opacity: 0.85 !important;
        background: rgba(0,0,0,0.75) !important;
    }
    
    .episode-card:active {
        transform: scale(0.98);
    }
}

/* High DPI displays (Retina) */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .episode-play-overlay {
        border: 2px solid rgba(255,255,255,0.3);
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .episode-thumbnail {
        height: 140px !important;
    }
    
    .episode-play-overlay {
        width: 40px !important;
        height: 40px !important;
        font-size: 0.9rem !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .episode-play-overlay {
        background: rgba(255,255,255,0.15) !important;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .episode-card.touch-active .episode-play-overlay {
        background: rgba(102, 126, 234, 0.8) !important;
    }
}

/* Accessibility improvements */
.episode-play-overlay:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    .episode-play-overlay,
    .episode-card {
        transition: none;
    }
    
    .episode-card.touch-active .episode-play-overlay {
        animation: none;
    }
}
