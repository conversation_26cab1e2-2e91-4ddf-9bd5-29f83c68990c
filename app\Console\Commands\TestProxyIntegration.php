<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProxySettings;
use App\Services\ProxyService;
use App\Services\ShahidMoviesAPI;
use Illuminate\Support\Facades\Log;

class TestProxyIntegration extends Command
{
    protected $signature = 'proxy:test-integration {proxy_url?}';
    protected $description = 'Test proxy integration with Shahid API';

    public function handle()
    {
        $proxyUrl = $this->argument('proxy_url');
        
        if ($proxyUrl) {
            $this->info("🔧 Testing with provided proxy: {$proxyUrl}");
            $this->testWithProxyUrl($proxyUrl);
        } else {
            $this->info("🔍 Testing current proxy configuration...");
            $this->testCurrentProxy();
        }
    }

    private function testWithProxyUrl($proxyUrl)
    {
        // Parse proxy URL (format: *****************************:port)
        $parsed = parse_url($proxyUrl);
        
        if (!$parsed) {
            $this->error("❌ Invalid proxy URL format");
            return;
        }

        $host = $parsed['host'] ?? null;
        $port = $parsed['port'] ?? null;
        $username = $parsed['user'] ?? null;
        $password = $parsed['pass'] ?? null;
        $scheme = $parsed['scheme'] ?? 'http';

        if (!$host || !$port) {
            $this->error("❌ Missing host or port in proxy URL");
            return;
        }

        $this->info("📋 Proxy Details:");
        $this->line("  Host: {$host}");
        $this->line("  Port: {$port}");
        $this->line("  Username: " . ($username ? $username : 'None'));
        $this->line("  Type: {$scheme}");

        // Create temporary proxy settings
        $proxyName = "Test-" . time();
        
        try {
            $proxy = ProxySettings::create([
                'name' => $proxyName,
                'host' => $host,
                'port' => $port,
                'username' => $username,
                'password' => $password,
                'type' => $scheme,
                'is_active' => false,
                'is_default' => false,
                'description' => 'Temporary test proxy'
            ]);

            $this->info("✅ Proxy settings created temporarily");

            // Test the proxy
            $this->info("🔍 Testing proxy connection...");
            $testResult = $proxy->testConnection();

            if ($testResult['status'] === 'success') {
                $this->info("✅ Proxy test successful!");
                $this->line("  Response time: {$testResult['response_time']}ms");
                
                // Now test with Shahid API
                $this->info("🎬 Testing Shahid API with proxy...");
                
                // Activate the proxy temporarily
                $proxy->setAsDefault();
                
                // Test Shahid API
                $this->testShahidAPI();
                
            } else {
                $this->error("❌ Proxy test failed: " . $testResult['error']);
            }

            // Clean up
            $this->info("🧹 Cleaning up test proxy...");
            $proxy->delete();

        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
        }
    }

    private function testCurrentProxy()
    {
        $proxyService = app(ProxyService::class);
        
        if (!$proxyService->isProxyEnabled()) {
            $this->warn("⚠️ No proxy is currently active");
            $this->info("💡 Use: php artisan proxy:test-integration '*****************************:port'");
            return;
        }

        $activeProxy = $proxyService->getActiveProxy();
        $this->info("🌐 Active proxy: {$activeProxy->name}");
        
        // Test current proxy
        $this->info("🔍 Testing current proxy...");
        $testResult = $proxyService->testCurrentProxy();
        
        if ($testResult['success']) {
            $this->info("✅ Current proxy test successful!");
            if (isset($testResult['response_time'])) {
                $this->line("  Response time: {$testResult['response_time']}ms");
            }
            
            // Test Shahid API
            $this->info("🎬 Testing Shahid API with current proxy...");
            $this->testShahidAPI();
            
        } else {
            $this->error("❌ Current proxy test failed: " . $testResult['message']);
        }
    }

    private function testShahidAPI()
    {
        try {
            $shahidAPI = app(ShahidMoviesAPI::class);
            
            $this->info("📡 Fetching movies from Shahid...");
            $startTime = microtime(true);
            
            $result = $shahidAPI->getMovies('EG', 5, 0, false);
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            if ($result['success'] && !empty($result['movies'])) {
                $this->info("✅ Shahid API test successful!");
                $this->line("  Movies fetched: " . count($result['movies']));
                $this->line("  Response time: {$duration}ms");
                $this->line("  First movie: " . ($result['movies'][0]['title'] ?? 'Unknown'));
                
                // Log successful proxy usage
                Log::info("🎯 Proxy integration test successful", [
                    'movies_count' => count($result['movies']),
                    'response_time' => $duration,
                    'proxy_used' => true
                ]);
                
            } else {
                $this->error("❌ Shahid API test failed");
                $this->line("  Error: " . ($result['error'] ?? 'Unknown error'));
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Shahid API test error: " . $e->getMessage());
        }
    }
}
