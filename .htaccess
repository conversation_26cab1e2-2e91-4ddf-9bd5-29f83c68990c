<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_flag html_errors Off
</IfModule>

<IfModule mod_php8.c>
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_flag html_errors Off
</IfModule>

<IfModule mod_deflate.c>
    # Enable Gzip Compression
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json application/xml application/rss+xml application/atom+xml application/xhtml+xml application/font-woff application/font-woff2 application/vnd.ms-fontobject font/opentype font/ttf font/eot font/otf image/svg+xml
</IfModule>

# Allow access to assets
<FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$">
    Allow from all
</FilesMatch>
