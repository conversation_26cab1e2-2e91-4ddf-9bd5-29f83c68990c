<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define roles and their permissions
        $rolesConfig = [
            'Super Admin' => 'all', // Gets all permissions
            'Admin' => [
                'view users', 'create users', 'edit users', 'update users', 'delete users', 'suspend users', 'reset user passwords', 'manage user roles',
                'view subscriptions', 'create subscriptions', 'edit subscriptions', 'update subscriptions', 'delete subscriptions', 'manage subscription plans',
                'view content', 'create content', 'edit content', 'delete content', 'manage content',
                'view admin panel', 'view reports', 'view analytics', 'view activity logs',
            ],
            'Content Manager' => [
                'view content', 'create content', 'edit content', 'delete content', 'manage content',
                'view admin panel', 'view reports',
            ],
            'User Manager' => [
                'view users', 'create users', 'edit users', 'update users', 'suspend users', 'reset user passwords', 'manage user roles',
                'view admin panel', 'view activity logs',
            ],
            'Subscription Manager' => [
                'view subscriptions', 'create subscriptions', 'edit subscriptions', 'update subscriptions', 'delete subscriptions', 'manage subscription plans',
                'view admin panel', 'view reports',
            ],
            'Report Viewer' => [
                'view admin panel', 'view reports', 'view analytics', 'view activity logs',
            ],
            'User' => [
                'view admin panel', // Basic permission for regular users
            ],
        ];

        // Create roles for both guards
        foreach ($rolesConfig as $roleName => $permissions) {
            // Create roles for web guard (User model)
            $webRole = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web'
            ]);

            // Create roles for admin guard (Admin model)
            $adminRole = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'admin'
            ]);

            // Assign permissions
            if ($permissions === 'all') {
                // Super Admin gets all permissions
                $webRole->givePermissionTo(Permission::where('guard_name', 'web')->get());
                $adminRole->givePermissionTo(Permission::where('guard_name', 'admin')->get());
            } else {
                // Assign specific permissions
                foreach ($permissions as $permission) {
                    $webPermission = Permission::where('name', $permission)->where('guard_name', 'web')->first();
                    $adminPermission = Permission::where('name', $permission)->where('guard_name', 'admin')->first();
                    
                    if ($webPermission) {
                        $webRole->givePermissionTo($webPermission);
                    }
                    if ($adminPermission) {
                        $adminRole->givePermissionTo($adminPermission);
                    }
                }
            }
        }

        $this->command->info('Roles created successfully!');
        $this->command->info('Total roles per guard: ' . count($rolesConfig));
    }
}
