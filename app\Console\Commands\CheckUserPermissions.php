<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Admin;

class CheckUserPermissions extends Command
{
    protected $signature = 'admin:check-permissions {user_id}';
    protected $description = 'Check permissions for a specific admin user';

    public function handle()
    {
        $userId = $this->argument('user_id');
        $admin = Admin::find($userId);

        if (!$admin) {
            $this->error("Admin with ID {$userId} not found");
            return;
        }

        $this->info("Checking permissions for: {$admin->name} ({$admin->email})");
        $this->info("Is Super Admin (attribute): " . ($admin->is_super_admin ? 'Yes' : 'No'));
        $this->info("Is Super Admin (raw): " . (isset($admin->attributes['is_super_admin']) && $admin->attributes['is_super_admin'] ? 'Yes' : 'No'));
        
        $this->info("Roles:");
        foreach ($admin->roles as $role) {
            $this->line("  - {$role->name}");
        }

        // Test specific permissions
        $permissions = [
            'view users',
            'create users', 
            'edit users',
            'delete users',
            'view admin panel',
            'view dashboard',
            'manage admins'
        ];

        $this->info("\nPermission checks:");
        foreach ($permissions as $permission) {
            $hasPermission = $admin->hasPermission($permission);
            $status = $hasPermission ? '✅' : '❌';
            $this->line("  {$status} {$permission}");
        }

        // Debug the hasPermission method
        $this->info("\nDebug info:");
        $this->line("  Admin ID: {$admin->id}");
        $this->line("  Attributes set: " . (isset($admin->attributes) ? 'Yes' : 'No'));
        $this->line("  is_super_admin in attributes: " . (isset($admin->attributes['is_super_admin']) ? 'Yes' : 'No'));
        if (isset($admin->attributes['is_super_admin'])) {
            $this->line("  is_super_admin value: " . var_export($admin->attributes['is_super_admin'], true));
        }
    }
}
