<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class WatchChannelLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:watch-channels {--filter=CHANNEL}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Watch channel-related logs in real time';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $filter = $this->option('filter');
        $logFile = storage_path('logs/laravel.log');
        
        if (!File::exists($logFile)) {
            $this->error('Log file not found: ' . $logFile);
            return 1;
        }

        $this->info('🎬 Watching channel logs... (Press Ctrl+C to stop)');
        $this->info('📁 Log file: ' . $logFile);
        
        if ($filter) {
            $this->info('🔍 Filter: ' . $filter);
        }
        
        $this->line('');

        // Get initial file size
        $lastSize = filesize($logFile);
        
        while (true) {
            clearstatcache();
            $currentSize = filesize($logFile);
            
            if ($currentSize > $lastSize) {
                // Read new content
                $handle = fopen($logFile, 'r');
                fseek($handle, $lastSize);
                $newContent = fread($handle, $currentSize - $lastSize);
                fclose($handle);
                
                // Filter and display relevant lines
                $lines = explode("\n", $newContent);
                foreach ($lines as $line) {
                    if (empty(trim($line))) continue;
                    
                    // Check if line contains channel-related keywords
                    $isChannelLog = str_contains($line, 'CHANNEL') || 
                                   str_contains($line, 'STREAM') || 
                                   str_contains($line, '🎬') || 
                                   str_contains($line, '📺') || 
                                   str_contains($line, '🌐') ||
                                   str_contains($line, 'shahid/channels') ||
                                   str_contains($line, 'extract-drm');
                    
                    if ($isChannelLog) {
                        if (!$filter || str_contains($line, $filter)) {
                            // Color code different types of logs
                            if (str_contains($line, 'ERROR')) {
                                $this->error($line);
                            } elseif (str_contains($line, 'WARNING')) {
                                $this->warn($line);
                            } elseif (str_contains($line, 'CLICKED') || str_contains($line, 'OPENING')) {
                                $this->info('🎯 ' . $line);
                            } elseif (str_contains($line, 'STREAM URL')) {
                                $this->line('<fg=green>📡 ' . $line . '</>');
                            } else {
                                $this->line($line);
                            }
                        }
                    }
                }
                
                $lastSize = $currentSize;
            }
            
            // Sleep for a short time before checking again
            usleep(500000); // 0.5 seconds
        }
        
        return 0;
    }
}
