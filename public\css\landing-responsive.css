/* Landing Responsive Overrides - Organized breakpoints for all devices */

/* Base variables/extensions */
:root {
  --header-height: 72px;
}

/* General layout adjustments */
.container,
.nav-container,
.hero-container,
.features-container,
.cta-container,
.footer-container {
  max-width: 1400px;
  margin-inline: auto;
  padding-inline: 2rem;
}

/* Ensure fixed header space on all viewports */
.hero {
  padding-top: calc(var(--header-height) + 16px);
}

/* Header + Navigation */
.header {
  width: 100%;
  z-index: 1000;
}

/* Desktop defaults */
.mobile-menu-btn { display: none; }

/* Tablet and down */
@media (max-width: 992px) {
  :root { --header-height: 64px; }

  /* Force hide desktop nav and show mobile button */
  .nav-menu { display: none !important; }
  .mobile-menu-btn { display: inline-flex !important; align-items: center; gap: .5rem; }

  /* Features grid for tablet - 3 columns */
  .features-grid { grid-template-columns: repeat(3, 1fr) !important; gap: 1.25rem !important; }

  /* Mobile dropdown panel - override inline styles */
  .nav-menu.active {
    display: flex !important;
    position: fixed !important;
    top: var(--header-height) !important;
    left: 0 !important;
    right: 0 !important;
    background: var(--secondary-color) !important;
    flex-direction: column !important;
    padding: 1.5rem 1.25rem !important;
    max-height: calc(100vh - var(--header-height)) !important;
    overflow-y: auto !important;
    transform: translateY(0) !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    transition: transform .25s ease, opacity .25s ease !important;
    box-shadow: var(--shadow-lg) !important;
    z-index: 999 !important;
  }

  .nav-menu.active a {
    padding: 1rem 0 !important;
    border-bottom: 1px solid rgba(255,255,255,.08) !important;
    text-align: center !important;
    display: block !important;
  }
  .nav-menu.active a:last-child { border-bottom: 0 !important; }

  /* Hide desktop auth buttons in header on mobile */
  .auth-buttons { display: none !important; }
}

/* Mobile (large phones) */
@media (max-width: 768px) {
  .nav-container { padding-inline: 1rem; }

  /* Move hero content down more on mobile */
  .hero {
    padding-top: calc(var(--header-height) + 60px) !important;
  }

  .hero-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
    padding-top: 2rem;
  }

  .hero-content h1 {
    font-size: clamp(1.8rem, 6vw, 2.8rem);
    line-height: 1.25;
  }
  .hero-content p { font-size: clamp(.95rem, 2.8vw, 1.05rem); }

  .hero-buttons { flex-direction: column; align-items: center; gap: .75rem; }
  .btn-large { width: 100%; max-width: 320px; }

  .features-grid { grid-template-columns: 1fr !important; gap: 1rem !important; }
}

/* Small phones (≤ 480px, iPhone SE etc.) */
@media (max-width: 480px) {
  :root { --header-height: 60px; }

  /* Extra space for small phones */
  .hero {
    padding-top: calc(var(--header-height) + 80px) !important;
  }

  .hero-container {
    padding-top: 2.5rem !important;
  }

  .nav-container,
  .hero-container,
  .features-container,
  .cta-container,
  .footer-container { padding-inline: 1rem; }

  .logo { font-size: 1.6rem !important; }

  .feature-card { padding: 1.25rem !important; }
}

/* Very small phones (≤ 375px) */
@media (max-width: 375px) {
  .btn { padding: .55rem .9rem; font-size: .95rem; }
  .hero-content h1 { font-size: clamp(1.5rem, 7vw, 2.2rem); }
}

/* Medium desktops (≤ 1200px) - slightly tighter layout */
@media (max-width: 1200px) {
  .container,
  .nav-container,
  .hero-container,
  .features-container,
  .cta-container,
  .footer-container { max-width: 1140px; }
}

/* Utilities */
.hide-mobile { display: initial; }
@media (max-width: 992px) { .hide-mobile { display: none !important; } }

.show-mobile { display: none; }
@media (max-width: 992px) { .show-mobile { display: block !important; } }

