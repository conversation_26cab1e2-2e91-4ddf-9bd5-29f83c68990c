<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use App\Services\ProxyService;

/**
 * Shahid API Service - Laravel Port of Python shahid_api.py
 * This service handles all API interactions with Shahid platform
 */
class ShahidAPI
{
    private $token;
    private $baseUrl = 'https://api3.shahid.net';
    private $tokenFile = 'shahid_token.txt';
    private $proxies = null;
    
    // API Endpoints
    private $endpoints = [
        'movies' => '/api/v2/movies',
        'series' => '/api/v2/series',
        'channels' => '/api/v2/channels', 
        'search' => '/api/v2/search',
        'content' => '/api/v2/content',
        'stream' => '/api/v2/stream',
        'validate' => '/api/v2/validate'
    ];

    // Headers for API requests
    private $defaultHeaders = [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'Accept-Language' => 'ar,en;q=0.9',
        'Accept-Encoding' => 'gzip, deflate, br',
        'Connection' => 'keep-alive',
        'Upgrade-Insecure-Requests' => '1'
    ];

    public function __construct($token = null)
    {
        $this->token = $token;
        
        // Load token from file if not provided
        if (!$this->token) {
            $this->token = $this->loadTokenFromFile();
        }
        
        // Load proxy settings if available
        $this->loadProxySettings();
    }

    /**
     * Load Shahid token from storage
     */
    private function loadTokenFromFile()
    {
        try {
            if (Storage::exists($this->tokenFile)) {
                $token = trim(Storage::get($this->tokenFile));
                Log::info('Shahid token loaded from file');
                return $token;
            }
        } catch (\Exception $e) {
            Log::error('Error loading Shahid token: ' . $e->getMessage());
        }
        return null;
    }

    /**
     * Save token to storage
     */
    public function saveToken($token)
    {
        try {
            $this->token = trim($token);
            Storage::put($this->tokenFile, $this->token);
            Log::info('Shahid token saved successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Error saving Shahid token: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Load proxy settings from ProxyService
     */
    private function loadProxySettings()
    {
        $proxyService = app(ProxyService::class);

        if ($proxyService->isProxyEnabled()) {
            $proxyConfig = $proxyService->getProxyConfig();
            if ($proxyConfig) {
                $this->proxies = [
                    'http' => $proxyConfig['url'],
                    'https' => $proxyConfig['url']
                ];
                Log::info("🌐 ShahidAPI using proxy: {$proxyConfig['host']}:{$proxyConfig['port']}");
            }
        } else {
            Log::info("🔗 ShahidAPI using direct connection (no proxy)");
        }
    }

    /**
     * Get headers for API requests
     */
    private function getHeaders($additionalHeaders = [])
    {
        $headers = $this->defaultHeaders;

        // Add Shahid-specific headers
        $headers['browser_name'] = 'CHROME';
        $headers['browser_version'] = '*********';
        $headers['shahid_os'] = 'WINDOWS';
        $headers['language'] = 'EN';
        $headers['origin'] = 'https://shahid.mbc.net';
        $headers['referer'] = 'https://shahid.mbc.net/';

        if ($this->token) {
            // Use 'token' header instead of Authorization Bearer
            $headers['token'] = $this->token;
        }

        return array_merge($headers, $additionalHeaders);
    }

    /**
     * Make HTTP request with error handling
     */
    private function makeRequest($method, $url, $data = null, $additionalHeaders = [])
    {
        try {
            $headers = $this->getHeaders($additionalHeaders);
            
            $httpClient = Http::withHeaders($headers);
            
            // Add proxy if configured
            if ($this->proxies) {
                $httpClient = $httpClient->withOptions([
                    'proxy' => $this->proxies
                ]);
            }
            
            // Set timeout
            $httpClient = $httpClient->timeout(30);
            
            switch (strtoupper($method)) {
                case 'GET':
                    $response = $httpClient->get($url, $data ?: []);
                    break;
                case 'POST':
                    $response = $httpClient->post($url, $data ?: []);
                    break;
                case 'PUT':
                    $response = $httpClient->put($url, $data ?: []);
                    break;
                case 'DELETE':
                    $response = $httpClient->delete($url);
                    break;
                default:
                    throw new \Exception('Unsupported HTTP method: ' . $method);
            }
            
            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                $responseBody = $response->body();
                $responseData = $response->json();

                // Handle specific Shahid API errors
                $errorMessage = 'HTTP ' . $response->status();

                if (isset($responseData['faults']) && is_array($responseData['faults'])) {
                    $fault = $responseData['faults'][0];
                    if (isset($fault['code']) && $fault['code'] == 5004) {
                        $errorMessage = 'This content requires Shahid VIP subscription';
                    } elseif (isset($fault['userMessage'])) {
                        $errorMessage = $fault['userMessage'];
                    } elseif (isset($fault['internalMessage'])) {
                        $errorMessage = $fault['internalMessage'];
                    }
                }

                Log::warning('Shahid API request failed', [
                    'url' => $url,
                    'status' => $response->status(),
                    'response' => $responseBody
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'status_code' => $response->status(),
                    'response' => $responseBody,
                    'requires_vip' => isset($responseData['faults'][0]['code']) && $responseData['faults'][0]['code'] == 5004
                ];
            }
            
        } catch (\Exception $e) {
            Log::error('Shahid API request exception', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => 0
            ];
        }
    }

    /**
     * Check if token is valid
     */
    public function hasValidToken()
    {
        return !empty($this->token);
    }

    /**
     * Validate token with Shahid API
     */
    public function validateToken()
    {
        if (!$this->hasValidToken()) {
            return false;
        }
        
        $url = $this->baseUrl . $this->endpoints['validate'];
        $result = $this->makeRequest('GET', $url);
        
        return $result['success'] ?? false;
    }

    /**
     * Get movies from Shahid (no token required for browsing)
     */
    public function getMovies($country = 'EG', $limit = 50, $offset = 0, $fetchAll = false)
    {
        try {
            // Increase execution time limit
            set_time_limit(120);

            Log::info("Fetching movies for country: {$country}");

            $headers = [
                'accept' => 'application/json, text/plain, */*',
                'accept-language' => 'en',
                'browser_name' => 'CHROME',
                'browser_version' => '131.0.0.0',
                'cache-control' => 'no-cache',
                'language' => 'EN',
                'mparticleid' => '-8068094556643926163',
                'origin' => 'https://shahid.mbc.net',
                'os_version' => 'NT 10.0',
                'pragma' => 'no-cache',
                'priority' => 'u=1, i',
                'profile' => '{"id":"5fd56d50-ada0-11ef-8f21-014b67b685e0","ageRestriction":false,"master":true}',
                'profile-key' => '{"ageRestriction":false,"isAdult":true}',
                'referer' => 'https://shahid.mbc.net/',
                'sec-ch-ua' => '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Windows"',
                'sec-fetch-dest' => 'empty',
                'sec-fetch-mode' => 'cors',
                'sec-fetch-site' => 'cross-site',
                'sec-gpc' => '1',
                'shahid_os' => 'WINDOWS',
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            ];

            // Get movies page carousels
            $moviesPageUrl = "https://api3.shahid.net/proxy/v2.1/editorial/page?request=%7B%22pageAlias%22:%22movies%22,%22profileFolder%22:%22WW%22%7D&country={$country}";

            $httpClient = Http::withHeaders($headers)->timeout(60)->retry(3, 1000);
            if ($this->proxies) {
                $httpClient = $httpClient->withOptions(['proxy' => $this->proxies]);
            }

            $response = $httpClient->get($moviesPageUrl);

            if (!$response->successful()) {
                Log::error("Failed to get movies page for {$country}: " . $response->status());
                return ['error' => 'Failed to fetch movies page'];
            }

            $pageData = $response->json();
            $moviesLinks = [];

            // Extract carousel links
            $carousels = $pageData['carousels'] ?? [];
            if (empty($carousels)) {
                Log::warning("No carousels found for {$country}");
                return ['movies' => [], 'total' => 0];
            }

            // Limit carousels to process (to avoid timeout)
            if ($fetchAll) {
                Log::info("Fetching ALL movies - processing all carousels");
                $carouselsToProcess = $carousels; // Process all carousels
            } else {
                $maxCarousels = 5; // Process only first 5 carousels for faster response
                $carouselsToProcess = array_slice($carousels, 0, $maxCarousels);
                Log::info("Fetching limited movies - processing {$maxCarousels} carousels");
            }

            foreach ($carouselsToProcess as $carousel) {
                try {
                    $encoded = str_replace('/', '%2F', $carousel['id']);
                    $carouselUrl = "https://api3.shahid.net/proxy/v2.1/editorial/carousel?request=%7B%22displayedItems%22:0,%22id%22:%22{$encoded}%22,%22itemsRequestedStatic%22:true,%22pageNumber%22:0,%22pageSize%22:999,%22totalItems%22:999%7D&country={$country}";
                    $moviesLinks[] = $carouselUrl;
                } catch (\Exception $e) {
                    Log::error("Error processing carousel for {$country}: " . $e->getMessage());
                    continue;
                }
            }

            Log::info("Found " . count($moviesLinks) . " movie carousels for {$country}");

            $allMovies = [];

            // Process each carousel
            foreach ($moviesLinks as $url) {
                try {
                    Log::info("Processing movies carousel URL: {$url}");
                    $carouselResponse = $httpClient->get($url);

                    if (!$carouselResponse->successful()) {
                        if ($carouselResponse->status() === 404) {
                            Log::info("Movies carousel not found (404), skipping: {$url}");
                        } else {
                            Log::warning("Failed to get carousel: " . $carouselResponse->status());
                        }
                        continue;
                    }

                    $items = $carouselResponse->json()['editorialItems'] ?? [];
                    Log::info("Found " . count($items) . " items in carousel");

                    foreach ($items as $item) {
                        try {
                            $movieData = $item['item'] ?? [];

                            // Extract movie details
                            $title = $movieData['title'] ?? 'Unknown Title';

                            // Get movie URL
                            $movieUrl = "";
                            foreach ($movieData['productUrls'] ?? [] as $productUrl) {
                                if (strpos($productUrl['url'] ?? '', '/en/player/') !== false) {
                                    $movieUrl = $productUrl['url'];
                                    break;
                                }
                            }

                            if (empty($movieUrl)) {
                                continue;
                            }

                            // Extract poster image
                            $posterUrl = $this->extractPosterUrl($movieData);

                            // Extract year and duration
                            $year = null;
                            $duration = null;

                            if (isset($movieData['releaseDate'])) {
                                $year = substr($movieData['releaseDate'], 0, 4);
                            }

                            if (isset($movieData['runtime'])) {
                                $duration = $movieData['runtime'];
                            } elseif (isset($movieData['duration'])) {
                                $duration = $movieData['duration'];
                            }

                            $movieInfo = [
                                'id' => $movieData['id'] ?? null,
                                'title' => $title,
                                'poster_url' => $posterUrl,
                                'movie_url' => $movieUrl,
                                'year' => $year,
                                'duration' => $duration,
                                'country' => $country
                            ];

                            $allMovies[] = $movieInfo;

                        } catch (\Exception $e) {
                            Log::warning("Error processing movie item: " . $e->getMessage());
                            continue;
                        }
                    }

                } catch (\Exception $e) {
                    Log::warning("Error processing carousel: " . $e->getMessage());
                    continue;
                }
            }

            // Remove duplicates based on movie URL
            $uniqueMovies = [];
            $seenUrls = [];

            foreach ($allMovies as $movie) {
                if (!in_array($movie['movie_url'], $seenUrls)) {
                    $uniqueMovies[] = $movie;
                    $seenUrls[] = $movie['movie_url'];
                }
            }

            // Apply limit and offset only if not fetching all
            $total = count($uniqueMovies);

            if ($fetchAll) {
                $movies = $uniqueMovies; // Return all movies when fetchAll is true
                Log::info("{$country}: Total {$total} unique movies found, returning ALL {$total} movies");
            } else {
                $movies = array_slice($uniqueMovies, $offset, $limit);
                Log::info("{$country}: Total {$total} unique movies found, returning " . count($movies));
            }

            return [
                'movies' => $movies,
                'total' => $total,
                'country' => $country
            ];

        } catch (\Exception $e) {
            Log::error("Error in getMovies: " . $e->getMessage());
            return ['error' => 'Failed to fetch movies: ' . $e->getMessage()];
        }
    }

    /**
     * Get series from Shahid (no token required for browsing)
     */
    public function getSeries($country = 'EG', $limit = 50, $offset = 0, $fetchAll = false)
    {
        try {
            // Increase execution time limit
            set_time_limit(120);

            Log::info("Fetching TV series for country: {$country}");

            $headers = [
                'accept' => 'application/json, text/plain, */*',
                'accept-language' => 'en',
                'browser_name' => 'CHROME',
                'browser_version' => '131.0.0.0',
                'cache-control' => 'no-cache',
                'language' => 'EN',
                'mparticleid' => '-8068094556643926163',
                'origin' => 'https://shahid.mbc.net',
                'os_version' => 'NT 10.0',
                'pragma' => 'no-cache',
                'priority' => 'u=1, i',
                'profile' => '{"id":"5fd56d50-ada0-11ef-8f21-014b67b685e0","ageRestriction":false,"master":true}',
                'profile-key' => '{"ageRestriction":false,"isAdult":true}',
                'referer' => 'https://shahid.mbc.net/',
                'sec-ch-ua' => '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Windows"',
                'sec-fetch-dest' => 'empty',
                'sec-fetch-mode' => 'cors',
                'sec-fetch-site' => 'cross-site',
                'sec-gpc' => '1',
                'shahid_os' => 'WINDOWS',
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            ];

            // Get series page carousels
            $seriesPageUrl = "https://api3.shahid.net/proxy/v2.1/editorial/page?request=%7B%22pageAlias%22:%22series%22,%22profileFolder%22:%22WW%22%7D&country={$country}";

            Log::info("Requesting series page URL: {$seriesPageUrl}");

            $httpClient = Http::withHeaders($headers)->timeout(60)->retry(3, 1000);
            if ($this->proxies) {
                $httpClient = $httpClient->withOptions(['proxy' => $this->proxies]);
                Log::info("Using proxy configuration");
            }

            Log::info("Making request to series page...");
            $response = $httpClient->get($seriesPageUrl);
            Log::info("Response received with status: " . $response->status());

            if (!$response->successful()) {
                Log::error("Failed to get series page for {$country}: " . $response->status() . " - " . $response->body());
                return ['error' => 'Failed to fetch series page: HTTP ' . $response->status()];
            }

            $pageData = $response->json();
            $seriesLinks = [];

            // Extract carousel links
            $carousels = $pageData['carousels'] ?? [];
            if (empty($carousels)) {
                Log::warning("No carousels found for {$country}");
                return ['series' => [], 'total' => 0];
            }

            Log::info("Found " . count($carousels) . " carousels for series");

            // Limit carousels to process (to avoid timeout)
            if ($fetchAll) {
                Log::info("Fetching ALL series - processing all carousels");
                $carouselsToProcess = $carousels; // Process all carousels
            } else {
                $maxCarousels = 5; // Process only first 5 carousels for faster response
                $carouselsToProcess = array_slice($carousels, 0, $maxCarousels);
                Log::info("Fetching limited series - processing {$maxCarousels} carousels");
            }

            foreach ($carouselsToProcess as $carousel) {
                try {
                    $carouselId = str_replace('/', '%2F', $carousel['id']);
                    $carouselUrl = "https://api3.shahid.net/proxy/v2.1/editorial/carousel?request=%7B%22displayedItems%22:0,%22id%22:%22{$carouselId}%22,%22itemsRequestedStatic%22:true,%22pageNumber%22:0,%22pageSize%22:999,%22totalItems%22:999%7D&country={$country}";
                    $seriesLinks[] = $carouselUrl;
                } catch (\Exception $e) {
                    Log::warning("Error processing carousel {$carousel}: " . $e->getMessage());
                    continue;
                }
            }

            Log::info("Generated " . count($seriesLinks) . " carousel URLs");

            // Collect all series
            $allSeries = [];
            $processedIds = [];

            foreach ($seriesLinks as $url) {
                try {
                    Log::info("Processing carousel URL: {$url}");
                    $response = $httpClient->get($url);

                    if (!$response->successful()) {
                        if ($response->status() === 404) {
                            Log::info("Carousel not found (404), skipping: {$url}");
                        } else {
                            Log::warning("Failed to fetch carousel: " . $response->status());
                        }
                        continue;
                    }

                    $carouselData = $response->json();
                    $items = $carouselData['editorialItems'] ?? [];

                    Log::info("Found " . count($items) . " items in carousel");

                    foreach ($items as $item) {
                        try {
                            $itemData = $item['item'] ?? [];

                            // Extract series details
                            $title = $itemData['title'] ?? 'Unknown Title';
                            $seriesId = $itemData['id'] ?? null;

                            if (!$seriesId || in_array($seriesId, $processedIds)) {
                                continue;
                            }

                            $processedIds[] = $seriesId;
                            Log::info("Processing series: {$title} (ID: {$seriesId})");

                            // Get series URL - try multiple patterns
                            $seriesUrl = "";
                            foreach ($itemData['productUrls'] ?? [] as $productUrl) {
                                $url = $productUrl['url'] ?? '';
                                if (strpos($url, '/en/player/') !== false ||
                                    strpos($url, '/ar/player/') !== false ||
                                    strpos($url, '/series/') !== false ||
                                    strpos($url, '/show/') !== false) {
                                    $seriesUrl = $url;
                                    break;
                                }
                            }

                            // If no URL found, create a default one
                            if (empty($seriesUrl) && $seriesId) {
                                $seriesUrl = "https://shahid.mbc.net/en/series/{$seriesId}";
                            }

                            if (empty($seriesUrl)) {
                                Log::warning("No URL found for series: {$title} (ID: {$seriesId})");
                                continue;
                            }

                            // Extract poster image
                            $posterUrl = $this->extractPosterUrl($itemData);

                            // Extract year and seasons count
                            $year = null;
                            $seasonsCount = 0;

                            if (isset($itemData['releaseDate'])) {
                                $year = substr($itemData['releaseDate'], 0, 4);
                            }

                            // Try to get seasons count
                            if (isset($itemData['seasons']) && is_array($itemData['seasons'])) {
                                $seasonsCount = count($itemData['seasons']);
                            }

                            $seriesInfo = [
                                'id' => $seriesId,
                                'title' => $title,
                                'poster_url' => $posterUrl,
                                'series_url' => $seriesUrl,
                                'year' => $year,
                                'seasons_count' => $seasonsCount,
                                'country' => $country
                            ];

                            $allSeries[] = $seriesInfo;
                            Log::info("Added series to collection: {$title}");

                        } catch (\Exception $e) {
                            Log::warning("Error processing series item: " . $e->getMessage());
                            continue;
                        }
                    }

                } catch (\Exception $e) {
                    Log::warning("Error processing carousel: " . $e->getMessage());
                    continue;
                }
            }

            // Apply limit and offset only if not fetching all
            $total = count($allSeries);

            if ($fetchAll) {
                $series = $allSeries; // Return all series when fetchAll is true
                Log::info("{$country}: Total {$total} unique series found, returning ALL {$total} series");
            } else {
                $series = array_slice($allSeries, $offset, $limit);
                Log::info("{$country}: Total {$total} unique series found, returning " . count($series));
            }

            return [
                'series' => $series,
                'total' => $total,
                'country' => $country
            ];

        } catch (\Exception $e) {
            Log::error("Error in getSeries: " . $e->getMessage());
            return ['error' => 'Failed to fetch series: ' . $e->getMessage()];
        }
    }

    /**
     * Get live channels from Shahid (requires token)
     * @deprecated Use ShahidChannelsAPI instead
     */
    public function getLiveChannels()
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found for live channels'];
        }

        try {
            Log::info("Fetching Shahid live channels");

            $allChannels = [];
            $countries = ['EG', 'SA']; // Egypt and Saudi Arabia

            foreach ($countries as $country) {
                Log::info("Fetching channels for country: {$country}");
                $page = 0;

                while (true) {
                    try {
                        $params = [
                            'filter' => json_encode([
                                'pageNumber' => $page,
                                'pageSize' => 24,
                                'productType' => 'LIVESTREAM',
                                'productSubType' => 'LIVE_CHANNEL'
                            ]),
                            'country' => $country
                        ];

                        $headers = [
                            'authority' => 'api2.shahid.net',
                            'accept' => 'application/json',
                            'accept-language' => 'en',
                            'content-type' => 'application/json',
                            'language' => 'en',
                            'origin' => 'https://shahid.mbc.net',
                            'referer' => 'https://shahid.mbc.net/',
                            'token' => $this->token,
                            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'browser_name' => 'CHROME',
                            'browser_version' => '*********',
                            'shahid_os' => 'WINDOWS'
                        ];

                        $httpClient = Http::withHeaders($headers)->timeout(15);
                        if ($this->proxies) {
                            $httpClient = $httpClient->withOptions(['proxy' => $this->proxies]);
                        }

                        $response = $httpClient->get('https://api2.shahid.net/proxy/v2.1/product/filter', $params);

                        if ($response->successful()) {
                            $data = $response->json();

                            if (!isset($data['productList']) || empty($data['productList']['products'])) {
                                break;
                            }

                            $pageChannels = $data['productList']['products'];

                            foreach ($pageChannels as $channel) {
                                $channel['country'] = $country;
                                $channel['country_name'] = $this->getCountryName($country);

                                // Process channel logo/image URLs
                                $channel = $this->processChannelImages($channel);

                                $allChannels[] = $channel;
                            }

                            $page++;

                            // Limit to prevent infinite loops
                            if ($page > 10) {
                                break;
                            }
                        } else {
                            Log::warning("Failed to fetch channels page {$page} for {$country}: " . $response->status());
                            break;
                        }

                    } catch (\Exception $e) {
                        Log::error("Error fetching channels page {$page} for {$country}: " . $e->getMessage());
                        break;
                    }
                }
            }

            // Remove duplicates based on channel ID
            $uniqueChannels = [];
            $seenIds = [];

            foreach ($allChannels as $channel) {
                $channelId = $channel['id'] ?? null;
                if ($channelId && !in_array($channelId, $seenIds)) {
                    $uniqueChannels[] = $channel;
                    $seenIds[] = $channelId;
                }
            }

            Log::info("Total channels collected: " . count($allChannels));
            Log::info("Unique channels after deduplication: " . count($uniqueChannels));

            return [
                'channels' => $uniqueChannels,
                'total' => count($uniqueChannels)
            ];

        } catch (\Exception $e) {
            Log::error("Error fetching live channels: " . $e->getMessage());
            return ['error' => 'Failed to fetch live channels: ' . $e->getMessage()];
        }
    }

    /**
     * Search content by query with enhanced search capabilities
     */
    public function searchContent($query, $limit = 20, $type = null)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        try {
            Log::info("=== SEARCHING CONTENT ===", [
                'query' => $query,
                'limit' => $limit,
                'type' => $type
            ]);

            $allResults = [];

            // Check if query is numeric (ID search)
            $isIdSearch = is_numeric($query);

            if ($isIdSearch) {
                Log::info("=== ID SEARCH DETECTED ===", ['id' => $query]);

                // For ID search, try to get content details directly
                $contentDetails = $this->getContentDetails($query);
                if ($contentDetails && !isset($contentDetails['error'])) {
                    $formatted = $this->formatSearchResultFromDetails($contentDetails, $query);
                    if ($formatted) {
                        $allResults[$query] = $formatted;
                    }
                }
            }

            // Search in multiple sources
            $searchSources = [
                // Primary search API
                [
                    'url' => 'https://api3.shahid.net/proxy/v2.1/t-search',
                    'countries' => ['EG', 'SA', 'AE', 'KW', 'QA', 'BH', 'OM']
                ],
                // Alternative search API
                [
                    'url' => 'https://api2.shahid.net/proxy/v2.1/t-search',
                    'countries' => ['EG', 'SA']
                ]
            ];

            foreach ($searchSources as $sourceIndex => $source) {
                foreach ($source['countries'] as $country) {
                    // Use basic search parameters (API only accepts: pageNumber, name, pageSize, language)
                    $searchRequest = [
                        'name' => $query,
                        'pageNumber' => 0,
                        'pageSize' => $isIdSearch ? 10 : $limit, // Smaller page size for ID search
                    ];

                    $params = [
                        'request' => json_encode($searchRequest),
                        'exactMatch' => $isIdSearch ? 'true' : 'false',
                        'country' => $country
                    ];

                    Log::info("=== SEARCHING IN SOURCE ===", [
                        'source' => $sourceIndex + 1,
                        'country' => $country,
                        'query' => $query,
                        'exact_match' => $isIdSearch,
                        'url' => $source['url']
                    ]);

                    $result = $this->makeRequest('GET', $source['url'], $params);

                    if ($result['success'] && isset($result['data']['productList']['products'])) {
                        $products = $result['data']['productList']['products'];

                        foreach ($products as $product) {
                            try {
                                // Filter by type if specified (SERIES only for better precision)
                                if ($type && isset($product['productSubType']) &&
                                    strtolower($product['productSubType']) !== strtolower($type)) {
                                    continue;
                                }

                                // Additional filtering for SERIES only (like Shahid website)
                                if (!isset($product['productSubType']) ||
                                    strtolower($product['productSubType']) !== 'series') {
                                    continue;
                                }

                                // Filter out unavailable content
                                if (isset($product['available']) && !$product['available']) {
                                    continue;
                                }

                                // For ID search, prioritize exact ID matches
                                $productId = $product['id'] ?? '';
                                if ($isIdSearch && $productId !== $query) {
                                    continue; // Skip non-exact matches for ID search
                                }

                                // For text search, apply strict relevance filtering like Shahid website
                                if (!$isIdSearch) {
                                    $title = $product['title'] ?? '';
                                    $relevanceScore = $this->calculateRelevanceScore($query, $title);

                                    // Use stricter filtering like Shahid website
                                    $minRelevance = $this->getMinimumRelevanceThreshold($query, $title);

                                    if ($relevanceScore < $minRelevance) {
                                        Log::debug("Skipping low relevance result", [
                                            'title' => $title,
                                            'query' => $query,
                                            'score' => $relevanceScore,
                                            'min_required' => $minRelevance
                                        ]);
                                        continue;
                                    }

                                    // Add relevance score to product for sorting
                                    $product['_relevance_score'] = $relevanceScore;
                                }

                                // Avoid duplicates
                                if (!isset($allResults[$productId])) {
                                    $formatted = $this->formatSearchResult($product);
                                    if ($formatted) {
                                        $allResults[$productId] = $formatted;
                                    }
                                }
                            } catch (\Exception $e) {
                                Log::error("Error formatting search result: " . $e->getMessage(), [
                                    'product_id' => $product['id'] ?? 'unknown',
                                    'product' => $product
                                ]);
                                continue; // Skip this product and continue with others
                            }
                        }

                        Log::info("=== SOURCE SEARCH RESULT ===", [
                            'source' => $sourceIndex + 1,
                            'country' => $country,
                            'found' => count($products),
                            'total_unique' => count($allResults)
                        ]);
                    }

                    // For ID search, if we found exact match, break early
                    if ($isIdSearch && count($allResults) > 0) {
                        break 2; // Break both loops
                    }

                    // Break early if we have enough results
                    if (count($allResults) >= $limit * 2) {
                        break 2; // Break both loops
                    }
                }

                // If we found results in first source, don't try second source for text search
                if (!$isIdSearch && count($allResults) > 0) {
                    break;
                }
            }

            // If no results found, try alternative search methods
            if (empty($allResults)) {
                Log::info("=== NO RESULTS FOUND - TRYING ALTERNATIVE SEARCH ===", [
                    'query' => $query,
                    'is_id_search' => $isIdSearch
                ]);

                // Try broader search without exact match
                if ($isIdSearch) {
                    $alternativeResults = $this->searchWithAlternativeMethod($query, $limit);
                    if (!empty($alternativeResults)) {
                        $allResults = $alternativeResults;
                    }
                }

                // Try partial match search for text queries
                if (!$isIdSearch && strlen($query) >= 3) {
                    $partialResults = $this->searchPartialMatch($query, $limit);
                    if (!empty($partialResults)) {
                        $allResults = $partialResults;
                    }
                }
            }

            // Sort results by relevance for text searches
            $finalResults = array_values($allResults);
            if (!$isIdSearch && !empty($finalResults)) {
                $finalResults = $this->sortByRelevance($finalResults, $query);
            }

            // Log search quality metrics
            $qualityMetrics = $this->calculateSearchQuality($finalResults, $query, $isIdSearch);

            Log::info("=== SEARCH COMPLETE ===", [
                'query' => $query,
                'is_id_search' => $isIdSearch,
                'total_results' => count($finalResults),
                'quality_metrics' => $qualityMetrics
            ]);

            return ['success' => true, 'data' => $finalResults];

        } catch (\Exception $e) {
            Log::error("Error searching content: " . $e->getMessage());
            return ['error' => 'Search failed: ' . $e->getMessage()];
        }
    }

    /**
     * Alternative search method for IDs
     */
    private function searchWithAlternativeMethod($query, $limit)
    {
        $results = [];

        try {
            Log::info("=== ALTERNATIVE SEARCH METHOD ===", ['query' => $query]);

            // First try getContentDetails which has multiple endpoints
            $contentDetails = $this->getContentDetails($query);
            if ($contentDetails && !isset($contentDetails['error']) && isset($contentDetails['data'])) {
                Log::info("=== CONTENT DETAILS SUCCESS ===", ['query' => $query]);
                $formatted = $this->formatSearchResultFromDetails($contentDetails, $query);
                if ($formatted) {
                    $results[$query] = $formatted;
                    return $results;
                }
            }

            // Try direct API endpoints as fallback
            $endpoints = [
                [
                    'url' => "https://api3.shahid.net/proxy/v2.1/content/{$query}",
                    'params' => ['country' => 'EG']
                ],
                [
                    'url' => "https://api2.shahid.net/proxy/v2.1/content/{$query}",
                    'params' => ['country' => 'EG']
                ],
                [
                    'url' => "https://api3.shahid.net/proxy/v2.1/product/{$query}",
                    'params' => ['country' => 'EG']
                ]
            ];

            foreach ($endpoints as $endpoint) {
                Log::info("=== TRYING DIRECT ENDPOINT ===", [
                    'url' => $endpoint['url'],
                    'query' => $query
                ]);

                $result = $this->makeRequest('GET', $endpoint['url'], $endpoint['params']);

                if ($result['success'] && isset($result['data'])) {
                    $content = $result['data'];
                    $formatted = $this->formatSearchResultFromDetails(['data' => $content], $query);
                    if ($formatted) {
                        $results[$query] = $formatted;
                        break; // Found result, stop searching
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("Alternative search failed: " . $e->getMessage());
        }

        Log::info("=== ALTERNATIVE SEARCH COMPLETE ===", [
            'query' => $query,
            'results_count' => count($results)
        ]);

        return $results;
    }

    /**
     * Partial match search for text queries with relevance filtering
     */
    private function searchPartialMatch($query, $limit)
    {
        $results = [];

        try {
            // Try search with basic parameters (API only accepts: pageNumber, name, pageSize, language)
            $url = "https://api3.shahid.net/proxy/v2.1/t-search";
            $searchRequest = [
                'name' => $query,
                'pageNumber' => 0,
                'pageSize' => $limit * 2, // Get more results for filtering
            ];

            $params = [
                'request' => json_encode($searchRequest),
                'exactMatch' => 'false',
                'country' => 'EG' // Use main country
            ];

            Log::info("=== PARTIAL MATCH SEARCH ===", [
                'query' => $query,
                'params' => $params
            ]);

            $result = $this->makeRequest('GET', $url, $params);

            if ($result['success'] && isset($result['data']['productList']['products'])) {
                $products = $result['data']['productList']['products'];

                foreach ($products as $product) {
                    $productId = $product['id'] ?? '';
                    $title = $product['title'] ?? '';

                    // Calculate relevance score
                    $relevanceScore = $this->calculateRelevanceScore($query, $title);

                    // Use dynamic threshold based on query length (like Shahid website)
                    $minRelevance = $this->getMinimumRelevanceThreshold($query, $title);
                    $partialSearchThreshold = max($minRelevance * 0.7, 0.25); // Slightly lower for partial search

                    // Only include results with reasonable relevance
                    if ($relevanceScore >= $partialSearchThreshold && !isset($results[$productId])) {
                        $formatted = $this->formatSearchResult($product);
                        if ($formatted) {
                            $formatted['_relevance_score'] = $relevanceScore;
                            $results[$productId] = $formatted;
                        }
                    }
                }

                // Sort by relevance and limit results
                $sortedResults = $this->sortByRelevance(array_values($results), $query);
                $results = array_slice($sortedResults, 0, $limit);

                // Convert back to associative array
                $finalResults = [];
                foreach ($results as $result) {
                    $finalResults[$result['id']] = $result;
                }
                $results = $finalResults;
            }
        } catch (\Exception $e) {
            Log::error("Partial match search failed: " . $e->getMessage());
        }

        Log::info("=== PARTIAL MATCH RESULTS ===", [
            'query' => $query,
            'results_count' => count($results)
        ]);

        return $results;
    }

    /**
     * Calculate search quality metrics
     */
    private function calculateSearchQuality($results, $query, $isIdSearch)
    {
        if (empty($results)) {
            return ['avg_relevance' => 0, 'high_relevance_count' => 0, 'quality_score' => 0];
        }

        $totalRelevance = 0;
        $highRelevanceCount = 0;
        $resultCount = count($results);

        foreach ($results as $result) {
            $title = $result['title'] ?? '';
            $relevance = $this->calculateRelevanceScore($query, $title);
            $totalRelevance += $relevance;

            if ($relevance >= 0.7) {
                $highRelevanceCount++;
            }
        }

        $avgRelevance = $totalRelevance / $resultCount;
        $highRelevanceRatio = $highRelevanceCount / $resultCount;
        $qualityScore = ($avgRelevance * 0.7) + ($highRelevanceRatio * 0.3);

        return [
            'avg_relevance' => round($avgRelevance, 3),
            'high_relevance_count' => $highRelevanceCount,
            'high_relevance_ratio' => round($highRelevanceRatio, 3),
            'quality_score' => round($qualityScore, 3)
        ];
    }

    /**
     * Get minimum relevance threshold based on query characteristics
     */
    private function getMinimumRelevanceThreshold($query, $title)
    {
        $queryLength = strlen(trim($query));
        $titleLength = strlen(trim($title));

        // For very short queries (1-2 chars), require high relevance
        if ($queryLength <= 2) {
            return 0.8;
        }

        // For short queries (3-4 chars), require good relevance
        if ($queryLength <= 4) {
            return 0.6;
        }

        // For medium queries (5-10 chars), moderate relevance
        if ($queryLength <= 10) {
            return 0.4;
        }

        // For longer queries, lower threshold but still meaningful
        return 0.3;
    }

    /**
     * Calculate relevance score between query and title
     */
    private function calculateRelevanceScore($query, $title)
    {
        if (empty($query) || empty($title)) {
            return 0;
        }

        $query = strtolower(trim($query));
        $title = strtolower(trim($title));

        // Exact match gets highest score
        if ($query === $title) {
            return 1.0;
        }

        // Check if query is at the beginning of title (very important for Shahid-like behavior)
        if (strpos($title, $query) === 0) {
            return 0.95;
        }

        // Check if title contains the exact query as a whole word
        if (preg_match('/\b' . preg_quote($query, '/') . '\b/', $title)) {
            return 0.9;
        }

        // Check if title contains the exact query (substring)
        if (strpos($title, $query) !== false) {
            // Calculate position-based score (earlier = better)
            $position = strpos($title, $query);
            $positionScore = 1 - ($position / strlen($title));
            return 0.7 + ($positionScore * 0.2); // 0.7 to 0.9 based on position
        }

        // Calculate word-based similarity
        $queryWords = explode(' ', $query);
        $titleWords = explode(' ', $title);

        $matchingWords = 0;
        $totalQueryWords = count($queryWords);

        foreach ($queryWords as $queryWord) {
            if (strlen($queryWord) < 2) continue; // Skip very short words

            foreach ($titleWords as $titleWord) {
                // Exact word match
                if ($queryWord === $titleWord) {
                    $matchingWords++;
                    break;
                }
                // Partial word match (for Arabic/English variations)
                if (strlen($queryWord) >= 3 && strpos($titleWord, $queryWord) !== false) {
                    $matchingWords += 0.7;
                    break;
                }
            }
        }

        $wordScore = $totalQueryWords > 0 ? $matchingWords / $totalQueryWords : 0;

        // Boost score if query length is significant portion of title
        $lengthRatio = strlen($query) / strlen($title);
        if ($lengthRatio > 0.5) {
            $wordScore *= 1.2;
        }

        return min($wordScore, 1.0);
    }

    /**
     * Sort results by relevance score
     */
    private function sortByRelevance($results, $query)
    {
        // Calculate relevance for each result if not already calculated
        foreach ($results as &$result) {
            if (!isset($result['_relevance_score'])) {
                $result['_relevance_score'] = $this->calculateRelevanceScore($query, $result['title'] ?? '');
            }
        }

        // Sort by relevance score (highest first)
        usort($results, function($a, $b) {
            $scoreA = $a['_relevance_score'] ?? 0;
            $scoreB = $b['_relevance_score'] ?? 0;
            return $scoreB <=> $scoreA;
        });

        // Remove relevance score from final results
        foreach ($results as &$result) {
            unset($result['_relevance_score']);
        }

        return $results;
    }

    /**
     * Format search result for consistent output
     */
    private function formatSearchResult($product)
    {
        // Safe year extraction
        $year = null;
        if (isset($product['productionDate']) && $product['productionDate']) {
            $year = date('Y', strtotime($product['productionDate']));
        } elseif (isset($product['year']) && $product['year']) {
            $year = $product['year'];
        } elseif (isset($product['releaseYear']) && $product['releaseYear']) {
            $year = $product['releaseYear'];
        }

        return [
            'id' => $product['id'] ?? '',
            'title' => $product['title'] ?? 'Unknown Title',
            'description' => $product['description'] ?? '',
            'type' => $product['productSubType'] ?? 'UNKNOWN',
            'year' => $year,
            'image' => $this->getImageUrl($product['image'] ?? ''),
            'thumbnail' => $this->getImageUrl($product['image'] ?? $product['thumbnailImage'] ?? ''),
            'available' => $product['available'] ?? true,
            'country' => $product['country'] ?? 'Unknown'
        ];
    }

    /**
     * Format content details as search result
     */
    private function formatSearchResultFromDetails($details, $id)
    {
        if (!$details || isset($details['error'])) {
            return null;
        }

        Log::info("=== FORMATTING DETAILS ===", [
            'id' => $id,
            'details_keys' => array_keys($details),
            'has_data' => isset($details['data'])
        ]);

        // Extract main content info
        $content = $details;
        if (isset($details['data'])) {
            $content = $details['data'];
        }

        // Check if data is in productModel (common in Shahid API)
        if (isset($content['productModel'])) {
            Log::info("=== PRODUCT MODEL FOUND ===", [
                'productModel_keys' => array_keys($content['productModel']),
                'full_productModel' => $content['productModel']
            ]);
            $content = $content['productModel'];
        }

        // Log content structure for debugging
        Log::info("=== CONTENT STRUCTURE ===", [
            'content_keys' => array_keys($content ?? []),
            'title_fields' => [
                'title' => $content['title'] ?? null,
                'name' => $content['name'] ?? null,
                'productTitle' => $content['productTitle'] ?? null,
                'seriesTitle' => $content['seriesTitle'] ?? null
            ],
            'image_fields' => [
                'image' => $content['image'] ?? null,
                'poster' => $content['poster'] ?? null,
                'posterImage' => $content['posterImage'] ?? null,
                'thumbnailImage' => $content['thumbnailImage'] ?? null
            ]
        ]);

        // Safe year extraction
        $year = null;
        if (isset($content['year']) && $content['year']) {
            $year = $content['year'];
        } elseif (isset($content['releaseYear']) && $content['releaseYear']) {
            $year = $content['releaseYear'];
        } elseif (isset($content['productionDate']) && $content['productionDate']) {
            $year = date('Y', strtotime($content['productionDate']));
        }

        // Extract title with multiple fallbacks
        $title = $content['title'] ??
                 $content['name'] ??
                 $content['productTitle'] ??
                 $content['seriesTitle'] ??
                 'Unknown Title';

        // Extract description with multiple fallbacks
        $description = $content['description'] ??
                      $content['synopsis'] ??
                      $content['summary'] ??
                      '';

        // Extract image with multiple fallbacks
        $imageUrl = '';

        // First try the image object structure
        if (isset($content['image']) && is_array($content['image'])) {
            $imageObj = $content['image'];
            $imagePriorities = ['posterImage', 'thumbnailImage', 'heroSliderImage', 'landscapeClean'];

            foreach ($imagePriorities as $imgField) {
                if (isset($imageObj[$imgField]) && !empty($imageObj[$imgField])) {
                    $imageUrl = $this->getImageUrl($imageObj[$imgField]);
                    if (!empty($imageUrl)) {
                        break;
                    }
                }
            }
        }

        // If no image found, try direct fields
        if (empty($imageUrl)) {
            $imageFields = ['posterImage', 'thumbnailImage', 'image', 'poster'];
            foreach ($imageFields as $field) {
                if (isset($content[$field]) && !empty($content[$field])) {
                    $imageUrl = $this->getImageUrl($content[$field]);
                    if (!empty($imageUrl)) {
                        break;
                    }
                }
            }
        }

        $result = [
            'id' => $id,
            'title' => $title,
            'description' => $description,
            'type' => $content['productSubType'] ?? $content['type'] ?? 'series',
            'year' => $year,
            'image' => $imageUrl,
            'thumbnail' => $imageUrl, // Use same image for thumbnail
            'available' => $content['available'] ?? true,
            'country' => $content['country'] ?? 'Unknown'
        ];

        Log::info("=== FORMATTED RESULT ===", [
            'id' => $result['id'],
            'title' => $result['title'],
            'has_image' => !empty($result['image']),
            'image_url' => $result['image']
        ]);

        return $result;
    }

    /**
     * Get properly formatted image URL using Shahid's media object system
     */
    private function getImageUrl($imageData)
    {
        if (empty($imageData)) {
            return '';
        }

        // If it's already a full URL, return as is
        if (is_string($imageData) && (strpos($imageData, 'http') === 0)) {
            return $imageData;
        }

        // Handle Shahid media object format
        if (is_string($imageData)) {
            // Check if it's already a Shahid media object URL with placeholders
            if (strpos($imageData, 'shahid.mbc.net/mediaObject/') !== false) {
                // Replace placeholders with actual values
                $imageData = str_replace('{height}', '600', $imageData);
                $imageData = str_replace('{width}', '400', $imageData);
                $imageData = str_replace('{croppingPoint}', 'mc', $imageData);
                return $imageData;
            }

            // Check if it's a media object ID (UUID format)
            if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $imageData)) {
                // Use Shahid's media object URL format
                return "https://shahid.mbc.net/mediaObject/{$imageData}?height=600&width=400&croppingPoint=mc&version=1";
            }

            // If it's a path, prepend base URL
            if (strpos($imageData, '/') === 0) {
                return 'https://api2.shahid.net' . $imageData;
            }

            return $imageData;
        }

        // If it's an array/object, try to get the best image
        if (is_array($imageData) || is_object($imageData)) {
            $imageArray = (array) $imageData;

            // Priority order for image selection
            $priorities = ['posterImage', 'thumbnailImage', 'image', 'poster'];

            foreach ($priorities as $key) {
                if (isset($imageArray[$key]) && !empty($imageArray[$key])) {
                    $url = $imageArray[$key];

                    // Handle Shahid media object URL with placeholders
                    if (is_string($url) && strpos($url, 'shahid.mbc.net/mediaObject/') !== false) {
                        $url = str_replace('{height}', '600', $url);
                        $url = str_replace('{width}', '400', $url);
                        $url = str_replace('{croppingPoint}', 'mc', $url);
                        return $url;
                    }

                    // Handle media object ID
                    if (is_string($url) && preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $url)) {
                        return "https://shahid.mbc.net/mediaObject/{$url}?height=600&width=400&croppingPoint=mc&version=1";
                    }

                    // Handle full URLs
                    if (is_string($url) && strpos($url, 'http') === 0) {
                        return $url;
                    }

                    // Handle paths
                    if (is_string($url) && strpos($url, '/') === 0) {
                        return 'https://api2.shahid.net' . $url;
                    }
                }
            }
        }

        return '';
    }

    /**
     * Get content details by ID
     */
    public function getContentDetails($contentId)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        try {
            Log::info("=== GETTING CONTENT DETAILS ===", ['content_id' => $contentId]);

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid' => 'web',
            ];

            // Try different endpoints and product types
            $endpoints = [
                [
                    'url' => 'https://api3.shahid.net/proxy/v2.1/product/id',
                    'params' => [
                        'request' => '{"id":"' . $contentId . '","productType":"SERIES"}',
                        'country' => 'EG',
                    ]
                ],
                [
                    'url' => 'https://api3.shahid.net/proxy/v2.1/product/id',
                    'params' => [
                        'request' => '{"id":"' . $contentId . '","productType":"MOVIE"}',
                        'country' => 'EG',
                    ]
                ],
                [
                    'url' => 'https://api2.shahid.net/proxy/v2.1/product/id',
                    'params' => [
                        'request' => '{"id":"' . $contentId . '","productType":"SERIES"}',
                        'country' => 'EG',
                    ]
                ]
            ];

            foreach ($endpoints as $index => $endpoint) {
                Log::info("=== TRYING ENDPOINT ===", [
                    'endpoint' => $index + 1,
                    'url' => $endpoint['url'],
                    'params' => $endpoint['params']
                ]);

                $httpClient = Http::withHeaders($headers)->timeout(30);
                $response = $httpClient->get($endpoint['url'], $endpoint['params']);

                if ($response->successful()) {
                    $data = $response->json();

                    Log::info("=== CONTENT DETAILS SUCCESS ===", [
                        'endpoint' => $index + 1,
                        'has_data' => !empty($data),
                        'data_keys' => array_keys($data ?? [])
                    ]);

                    if (!empty($data)) {
                        return [
                            'success' => true,
                            'type' => strpos($endpoint['params']['request'], 'SERIES') !== false ? 'series' : 'movie',
                            'id' => $contentId,
                            'data' => $data
                        ];
                    }
                } else {
                    Log::warning("Endpoint failed", [
                        'endpoint' => $index + 1,
                        'status' => $response->status(),
                        'body' => $response->body()
                    ]);
                }
            }

            Log::error("All endpoints failed for content ID: " . $contentId);
            return ['error' => 'Failed to get content details from all endpoints'];

        } catch (\Exception $e) {
            Log::error("Error getting content details: " . $e->getMessage());
            return ['error' => 'Error getting content details: ' . $e->getMessage()];
        }
    }

    /**
     * Get streaming information for content (using playout endpoint)
     */
    public function getStreamingInfo($contentId)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        // Use the correct playout endpoint
        $url = $this->baseUrl . "/proxy/v2.1/playout/new/url/{$contentId}?outputParameter=vmap&country=EG";
        $result = $this->makeRequest('GET', $url);

        if ($result['success']) {
            return $result['data'];
        } else {
            return ['error' => $result['error'] ?? 'Failed to get streaming info'];
        }
    }

    /**
     * Get movie playout URL
     */
    public function getMoviePlayoutUrl($movieId, $country = 'EG')
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        $url = $this->baseUrl . "/proxy/v2.1/playout/new/url/{$movieId}?outputParameter=vmap&country={$country}";
        $result = $this->makeRequest('GET', $url);

        if ($result['success']) {
            return $result['data'];
        } else {
            return ['error' => $result['error'] ?? 'Failed to get movie playout'];
        }
    }

    /**
     * Get episode playout URL
     */
    public function getEpisodePlayoutUrl($episodeId, $country = 'SA')
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        $url = $this->baseUrl . "/proxy/v2.1/playout/new/url/{$episodeId}?outputParameter=vmap&country={$country}";
        $result = $this->makeRequest('GET', $url);

        if ($result['success']) {
            return $result['data'];
        } else {
            return ['error' => $result['error'] ?? 'Failed to get episode playout'];
        }
    }

    /**
     * Extract content ID from URL
     */
    public function extractContentId($input)
    {
        // If it's already an ID, return as is
        if (is_numeric($input)) {
            return $input;
        }
        
        // Extract ID from Shahid URL patterns
        $patterns = [
            '/\/movie\/(\d+)/',
            '/\/series\/(\d+)/',
            '/\/episode\/(\d+)/',
            '/contentId=(\d+)/',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input, $matches)) {
                return $matches[1];
            }
        }
        
        return $input; // Return as is if no pattern matches
    }

    /**
     * Extract poster URL from movie data
     */
    private function extractPosterUrl($movieData)
    {
        $posterUrl = null;

        if (isset($movieData['image']) && $movieData['image']) {
            $imageData = $movieData['image'];

            // Try different image types in order of preference
            $imageTypes = ['posterImage', 'posterClean', 'posterHero', 'thumbnailImage', 'heroSliderImage', 'landscapeClean'];
            foreach ($imageTypes as $imageType) {
                if (isset($imageData[$imageType]) && $imageData[$imageType]) {
                    $posterUrl = $imageData[$imageType];
                    break;
                }
            }
        }

        // Fallback: try direct posterImage field
        if (!$posterUrl && isset($movieData['posterImage']) && $movieData['posterImage']) {
            $posterUrl = $movieData['posterImage'];
        }

        // Fallback: try thumbnailImage field
        if (!$posterUrl && isset($movieData['thumbnailImage']) && $movieData['thumbnailImage']) {
            $posterUrl = $movieData['thumbnailImage'];
        }

        // Clean and optimize poster URL
        if ($posterUrl) {
            // Replace template variables with actual values
            if (strpos($posterUrl, '{height}') !== false || strpos($posterUrl, '{width}') !== false) {
                $posterUrl = str_replace(['{height}', '{width}', '{croppingPoint}'], ['600', '400', 'center'], $posterUrl);
            }

            // Clean URL by removing unwanted parameters
            $posterUrl = $this->cleanPosterUrl($posterUrl);

            // Additional optimization for mediaObject URLs
            if (strpos($posterUrl, 'mediaObject/') !== false && strpos($posterUrl, '?') === false) {
                $posterUrl .= "?width=400&height=600";
            }
        }

        return $posterUrl;
    }

    /**
     * Process channel images and logos
     */
    private function processChannelImages($channel)
    {
        // Initialize logo_url
        $channel['logo_url'] = null;

        // Check for logoTitleImage (main logo)
        if (!empty($channel['logoTitleImage'])) {
            $channel['logo_url'] = $this->processImageUrl($channel['logoTitleImage']);
        }

        // Check for image object
        if (!empty($channel['image'])) {
            $image = $channel['image'];

            // Try different image types
            $imageTypes = ['posterClean', 'posterImage', 'posterHero', 'landscape'];

            foreach ($imageTypes as $type) {
                if (!empty($image[$type])) {
                    if (!$channel['logo_url']) {
                        $channel['logo_url'] = $this->processImageUrl($image[$type]);
                    }
                    $channel['image'][$type] = $this->processImageUrl($image[$type]);
                }
            }
        }

        // Check for thumbnailImage
        if (!empty($channel['thumbnailImage'])) {
            if (!$channel['logo_url']) {
                $channel['logo_url'] = $this->processImageUrl($channel['thumbnailImage']);
            }
            $channel['thumbnailImage'] = $this->processImageUrl($channel['thumbnailImage']);
        }

        return $channel;
    }

    /**
     * Process image URL - replace placeholders and optimize
     */
    private function processImageUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // If it's a mediaObject URL, rebuild with exact format
        if (strpos($url, 'mediaObject/') !== false) {
            // Extract mediaObject ID
            if (preg_match('/mediaObject\/([^?]+)/', $url, $matches)) {
                $mediaObjectId = $matches[1];
                // Rebuild URL with exact format
                return "https://shahid.mbc.net/mediaObject/{$mediaObjectId}?height=auto&width=288&croppingPoint=&version=1&type=webp";
            }
        }

        // Fallback: Replace placeholders with actual values
        $url = str_replace('{height}', 'auto', $url);
        $url = str_replace('{width}', '288', $url);
        $url = str_replace('{croppingPoint}', '', $url);

        return $url;
    }

    /**
     * Clean poster URL
     */
    private function cleanPosterUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // Remove unwanted parameters but keep essential ones
        $parsedUrl = parse_url($url);
        if (!$parsedUrl) {
            return $url;
        }

        $cleanUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }

        // Add clean parameters for image optimization
        if (strpos($url, 'mediaObject/') !== false) {
            $cleanUrl .= '?width=400&height=600';
        }

        return $cleanUrl;
    }

    /**
     * Get country name from code
     */
    private function getCountryName($countryCode)
    {
        $countries = [
            'EG' => 'مصر',
            'SA' => 'السعودية',
            'AE' => 'الإمارات',
            'KW' => 'الكويت',
            'QA' => 'قطر',
            'BH' => 'البحرين',
            'OM' => 'عمان',
            'JO' => 'الأردن',
            'LB' => 'لبنان',
            'IQ' => 'العراق'
        ];

        return $countries[$countryCode] ?? $countryCode;
    }

    /**
     * Get token
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Get series seasons
     */
    public function getSeriesSeasons($seriesId)
    {
        if (!$this->hasValidToken()) {
            return ['error' => 'No valid token found'];
        }

        try {
            // Use api3.shahid.net like Python code
            $url = "https://api3.shahid.net/proxy/v2.1/product/id";
            $params = [
                'request' => '{"id":"' . $seriesId . '"}',
                'country' => 'EG'
            ];

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid' => 'web'
            ];

            Log::info("=== GETTING SERIES SEASONS ===", [
                'series_id' => $seriesId,
                'url' => $url
            ]);

            $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

            if (!$result['success']) {
                return ['error' => $result['error'] ?? 'Failed to get series details'];
            }

            $data = $result['data'];
            $seasons = [];

            // Print full API response for debugging
            Log::info("=== FULL SERIES SEASONS API RESPONSE ===", [
                'response' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            ]);

            // Log the response structure for debugging
            Log::info("Series seasons API response structure", [
                'has_productModel' => isset($data['productModel']),
                'has_seasons' => isset($data['productModel']['seasons']),
                'seasons_count' => isset($data['productModel']['seasons']) ? count($data['productModel']['seasons']) : 0,
                'first_season_keys' => isset($data['productModel']['seasons'][0]) ? array_keys($data['productModel']['seasons'][0]) : []
            ]);

            // Get planned episodes from main series data (outside the loop)
            $plannedEpisodes = $data['productModel']['numberOfEpisodes'] ?? 0;

            Log::info("=== MAIN PRODUCT MODEL KEYS ===", [
                'productModel_keys' => array_keys($data['productModel']),
                'has_numberOfEpisodes' => isset($data['productModel']['numberOfEpisodes']),
                'numberOfEpisodes_value' => $data['productModel']['numberOfEpisodes'] ?? 'not_found'
            ]);

            Log::info("=== PLANNED EPISODES FROM MAIN SERIES ===", [
                'numberOfEpisodes' => $data['productModel']['numberOfEpisodes'] ?? 'not_found',
                'planned_episodes' => $plannedEpisodes
            ]);

            if (isset($data['productModel']['seasons']) && is_array($data['productModel']['seasons'])) {
                foreach ($data['productModel']['seasons'] as $index => $seasonData) {
                    // Log season data structure for debugging
                    Log::info("Season {$index} data structure", [
                        'season_keys' => array_keys($seasonData),
                        'has_episodesCount' => isset($seasonData['episodesCount']),
                        'has_numberOfEpisodes' => isset($seasonData['numberOfEpisodes']),
                        'has_numberOfAVODEpisodes' => isset($seasonData['numberOfAVODEpisodes']),
                        'has_playlists' => isset($seasonData['playlists']),
                        'title' => $seasonData['title'] ?? 'No title'
                    ]);

                    // Get episode count exactly like Python code
                    $episodeCount = $seasonData['numberOfAVODEpisodes'] ?? 1;

                    Log::info("=== SEASON EPISODE COUNT ANALYSIS ===", [
                        'season_title' => $seasonData['title'] ?? 'No title',
                        'numberOfAVODEpisodes' => $seasonData['numberOfAVODEpisodes'] ?? 'not_found',
                        'has_playlists' => isset($seasonData['playlists']),
                        'initial_count' => $episodeCount
                    ]);

                    // Check playlists for more accurate episode count (Python logic)
                    if (isset($seasonData['playlists']) && is_array($seasonData['playlists'])) {
                        foreach ($seasonData['playlists'] as $playlist) {
                            if (isset($playlist['type']) && $playlist['type'] === 'EPISODE') {
                                $playlistCount = $playlist['count'] ?? 0;
                                if ($playlistCount > $episodeCount) {
                                    Log::info("=== UPDATED EPISODE COUNT FROM PLAYLIST ===", [
                                        'season_title' => $seasonData['title'] ?? 'No title',
                                        'old_count' => $episodeCount,
                                        'new_count' => $playlistCount,
                                        'playlist_id' => $playlist['id'] ?? 'no_id'
                                    ]);
                                    $episodeCount = $playlistCount;
                                }
                            }
                        }
                    }

                    // If no playlists in series data, get actual season details to get correct count
                    if (!isset($seasonData['playlists']) && isset($seasonData['id'])) {
                        Log::info("=== GETTING SEASON DETAILS FOR ACCURATE COUNT ===", [
                            'season_id' => $seasonData['id'],
                            'season_title' => $seasonData['title'] ?? 'No title'
                        ]);

                        $seasonDetails = $this->getSeasonEpisodes($seasonData['id']);
                        if (isset($seasonDetails['data']) && is_array($seasonDetails['data'])) {
                            $actualCount = count($seasonDetails['data']);
                            if ($actualCount > $episodeCount) {
                                Log::info("=== UPDATED EPISODE COUNT FROM SEASON DETAILS ===", [
                                    'season_title' => $seasonData['title'] ?? 'No title',
                                    'old_count' => $episodeCount,
                                    'new_count' => $actualCount
                                ]);
                                $episodeCount = $actualCount;
                            }
                        }

                        // Also get planned episodes from season details
                        if (isset($seasonDetails['season_info']['numberOfEpisodes'])) {
                            $plannedEpisodes = $seasonDetails['season_info']['numberOfEpisodes'];
                            Log::info("=== UPDATED PLANNED EPISODES FROM SEASON DETAILS ===", [
                                'season_title' => $seasonData['title'] ?? 'No title',
                                'planned_episodes' => $plannedEpisodes
                            ]);
                        }
                    }

                    $episodesCount = $episodeCount;

                    // Method 5: If still 0, try to get actual episodes count by calling season details
                    if ($episodesCount === 0 && isset($seasonData['id'])) {
                        $seasonDetails = $this->getSeasonEpisodes($seasonData['id']);
                        if (isset($seasonDetails['data']) && is_array($seasonDetails['data'])) {
                            $episodesCount = count($seasonDetails['data']);
                        }
                    }

                    // Get season number
                    $seasonNumber = $seasonData['seasonNumber'] ?? $seasonData['numberOfSeason'] ?? $seasonData['number'] ?? 0;



                    $seasonInfo = [
                        'id' => $seasonData['id'] ?? '',
                        'title' => $seasonData['title'] ?? '',
                        'season_number' => $seasonNumber,
                        'episodes_count' => $episodesCount,
                        'planned_episodes' => $plannedEpisodes, // Add planned episodes
                        'description' => $seasonData['description'] ?? '',
                        'poster_url' => $seasonData['image']['posterClean'] ?? ($seasonData['image']['poster'] ?? ''),
                        // Add series details from the first season
                        'series_details' => $index === 0 ? $this->extractSeriesDetails($data['productModel']) : null
                    ];

                    // Log final season info
                    Log::info("Final season info", [
                        'season_title' => $seasonInfo['title'],
                        'episodes_count' => $seasonInfo['episodes_count'],
                        'planned_episodes' => $seasonInfo['planned_episodes'],
                        'season_number' => $seasonInfo['season_number']
                    ]);

                    $seasons[] = $seasonInfo;
                }
            }

            return ['success' => true, 'data' => $seasons];

        } catch (\Exception $e) {
            Log::error("Error getting series seasons: " . $e->getMessage());
            return ['error' => 'Failed to get series seasons: ' . $e->getMessage()];
        }
    }

    /**
     * Extract series details from product model
     */
    private function extractSeriesDetails($productModel)
    {
        if (!$productModel) {
            return null;
        }

        // Extract cast from persons array - keep original structure
        $cast = [];
        if (isset($productModel['persons']) && is_array($productModel['persons'])) {
            foreach ($productModel['persons'] as $person) {
                if (isset($person['fullName']) && !empty($person['fullName'])) {
                    $cast[] = [
                        'id' => $person['id'] ?? null,
                        'firstName' => $person['firstName'] ?? '',
                        'lastName' => $person['lastName'] ?? '',
                        'fullName' => $person['fullName'],
                        'rank' => $person['rank'] ?? 999,
                        'role' => $person['role'] ?? 'Actor'
                    ];
                }
            }
        }

        // Extract genres
        $genres = [];
        if (isset($productModel['genres']) && is_array($productModel['genres'])) {
            foreach ($productModel['genres'] as $genre) {
                if (isset($genre['title']) && !empty($genre['title'])) {
                    $genres[] = [
                        'id' => $genre['id'] ?? '',
                        'title' => $genre['title']
                    ];
                }
            }
        }

        // Extract dialect
        $dialect = null;
        if (isset($productModel['dialect']) && is_array($productModel['dialect'])) {
            $dialect = [
                'id' => $productModel['dialect']['id'] ?? '',
                'title' => $productModel['dialect']['title'] ?? ''
            ];
        }

        // Use the correct episode count - prioritize actual available episodes
        $actualEpisodeCount = $productModel['totalNumberOfEpisodes'] ?? 0;
        $plannedEpisodeCount = $productModel['numberOfEpisodes'] ?? 0;

        Log::info("=== MAIN SERIES EPISODE COUNT ANALYSIS ===", [
            'totalNumberOfEpisodes' => $productModel['totalNumberOfEpisodes'] ?? 'not_found',
            'numberOfEpisodes' => $productModel['numberOfEpisodes'] ?? 'not_found',
            'using_actual_count' => $actualEpisodeCount,
            'planned_count' => $plannedEpisodeCount
        ]);

        return [
            'title' => $productModel['title'] ?? '',
            'description' => $productModel['description'] ?? '',
            'productionDate' => $productModel['productionDate'] ?? null,
            'releaseDate' => $productModel['releaseDate'] ?? null,
            'createdDate' => $productModel['createdDate'] ?? null,
            'totalNumberOfEpisodes' => $actualEpisodeCount, // Use actual available episodes
            'numberOfEpisodes' => $actualEpisodeCount, // Use actual available episodes
            'plannedEpisodes' => $plannedEpisodeCount, // Keep planned episodes for reference
            'persons' => $cast,
            'genres' => $genres,
            'dialect' => $dialect,
            'image' => $productModel['image'] ?? null,
            'rating' => $productModel['rating'] ?? null,
            'duration' => $productModel['duration'] ?? null
        ];
    }

    /**
     * Get season episodes
     */
    public function getSeasonEpisodes($seasonId)
    {
        Log::info("=== STARTING getSeasonEpisodes ===", ['season_id' => $seasonId]);

        if (!$this->hasValidToken()) {
            Log::error("No valid token found for getSeasonEpisodes");
            return ['error' => 'No valid token found'];
        }

        Log::info("Token validation passed", ['token_length' => strlen($this->token)]);

        try {
            // First, get season details to know how many episodes we should have
            $url = "https://api2.shahid.net/proxy/v2.1/product/id";
            $params = [
                'request' => json_encode(['id' => (string)$seasonId]),
                'country' => 'EG'
            ];

            $headers = [
                'authority' => 'api2.shahid.net',
                'accept' => 'application/json',
                'accept-language' => 'en',
                'content-type' => 'application/json',
                'language' => 'en',
                'origin' => 'https://shahid.mbc.net',
                'referer' => 'https://shahid.mbc.net/',
                'token' => $this->token,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid' => 'web'
            ];

            Log::info("=== STEP 1: Making API request ===", [
                'url' => $url,
                'params' => $params,
                'headers_count' => count($headers)
            ]);

            $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

            Log::info("=== STEP 2: API request completed ===", [
                'success' => $result['success'],
                'has_data' => isset($result['data']),
                'error' => $result['error'] ?? 'none'
            ]);

            if (!$result['success']) {
                Log::error("=== API REQUEST FAILED ===", ['error' => $result['error']]);
                return ['error' => $result['error'] ?? 'Failed to get season details'];
            }

            $data = $result['data'];

            // Print full API response for debugging
            Log::info("=== FULL SEASON EPISODES API RESPONSE ===", [
                'response' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            ]);

            $data = $result['data'];
            $episodes = [];

            Log::info("=== STEP 3: Analyzing response data ===", [
                'data_type' => gettype($data),
                'is_array' => is_array($data),
                'data_keys' => is_array($data) ? array_keys($data) : 'not_array'
            ]);

            if (!is_array($data)) {
                Log::error("=== DATA IS NOT ARRAY ===", ['data' => $data]);
                return ['error' => 'Invalid response format'];
            }

            // Log the response structure for debugging
            Log::info("=== STEP 4: Response structure analysis ===", [
                'season_id' => $seasonId,
                'response_keys' => array_keys($data),
                'has_productModel' => isset($data['productModel']),
                'has_products' => isset($data['products']),
                'has_items' => isset($data['items']),
                'products_count' => isset($data['products']) ? count($data['products']) : 0
            ]);

            if (isset($data['productModel'])) {
                Log::info("=== STEP 5: ProductModel found ===", [
                    'productModel_keys' => array_keys($data['productModel']),
                    'has_episodes' => isset($data['productModel']['episodes']),
                    'has_playlists' => isset($data['productModel']['playlists']),
                    'has_allPlaylist' => isset($data['productModel']['allPlaylist']),
                    'has_numberOfEpisodes' => isset($data['productModel']['numberOfEpisodes']),
                    'has_numberOfAVODEpisodes' => isset($data['productModel']['numberOfAVODEpisodes'])
                ]);
            }

            // Initialize episodes data array
            $episodesData = [];

            Log::info("=== STEP 6: Starting episodes search ===");

            // Method 1: Try to get episodes from products (filter API response)
            if (isset($data['products']) && is_array($data['products'])) {
                $episodesData = $data['products'];
                Log::info("=== METHOD 1 SUCCESS: Found episodes in products ===", ['count' => count($episodesData)]);
            } else {
                Log::info("=== METHOD 1 FAILED: No products found ===");
            }

            // Method 2: Try direct episodes in productModel
            if (empty($episodesData) && isset($data['productModel']['episodes']) && is_array($data['productModel']['episodes'])) {
                $episodesData = $data['productModel']['episodes'];
                Log::info("=== METHOD 2 SUCCESS: Found episodes in productModel.episodes ===", ['count' => count($episodesData)]);
            } else if (empty($episodesData)) {
                Log::info("=== METHOD 2 FAILED: No episodes in productModel.episodes ===");
            }

            // Method 3: Try playlists
            if (empty($episodesData) && isset($data['productModel']['playlists']) && is_array($data['productModel']['playlists'])) {
                Log::info("=== METHOD 3: Checking playlists ===", ['playlists_count' => count($data['productModel']['playlists'])]);

                foreach ($data['productModel']['playlists'] as $index => $playlist) {
                    Log::info("=== PLAYLIST {$index} ANALYSIS ===", [
                        'playlist_keys' => array_keys($playlist),
                        'type' => $playlist['type'] ?? 'no_type',
                        'count' => $playlist['count'] ?? 0,
                        'has_items' => isset($playlist['items']),
                        'items_count' => isset($playlist['items']) ? count($playlist['items']) : 0,
                        'playlist_id' => $playlist['id'] ?? 'no_id'
                    ]);

                    if ($playlist['type'] === 'EPISODE') {
                        // First try to get episodes from items if available
                        if (isset($playlist['items']) && is_array($playlist['items']) && count($playlist['items']) > 0) {
                            $episodesData = $playlist['items'];
                            Log::info("=== METHOD 3A SUCCESS: Found episodes in playlist items ===", ['count' => count($episodesData)]);
                            break;
                        }

                        // If no items but has count and ID, try to fetch from playlist API
                        if (isset($playlist['id']) && $playlist['count'] > 0) {
                            Log::info("=== METHOD 3B: Fetching episodes from playlist API ===", ['playlist_id' => $playlist['id']]);
                            $playlistEpisodes = $this->getPlaylistEpisodes($playlist['id']);
                            if (!empty($playlistEpisodes)) {
                                $episodesData = $playlistEpisodes;
                                Log::info("=== METHOD 3B SUCCESS: Found episodes from playlist API ===", ['count' => count($playlistEpisodes)]);
                                break;
                            }
                        }
                    }
                }

                if (empty($episodesData)) {
                    Log::info("=== METHOD 3 FAILED: No episodes found in playlists ===");
                }
            }

            // Method 4: Try allPlaylist
            if (empty($episodesData)) {
                Log::info("=== METHOD 4: Checking allPlaylist ===");
                if (isset($data['productModel']['allPlaylist'])) {
                    Log::info("=== allPlaylist found ===", [
                        'type' => gettype($data['productModel']['allPlaylist']),
                        'is_array' => is_array($data['productModel']['allPlaylist']),
                        'count' => is_array($data['productModel']['allPlaylist']) ? count($data['productModel']['allPlaylist']) : 'not_array'
                    ]);

                    if (is_array($data['productModel']['allPlaylist']) && count($data['productModel']['allPlaylist']) > 0) {
                        $episodesData = $data['productModel']['allPlaylist'];
                        Log::info("=== METHOD 4 SUCCESS: Found episodes in allPlaylist ===", ['count' => count($episodesData)]);
                    } else {
                        Log::info("=== METHOD 4 FAILED: allPlaylist is empty or not array ===");
                    }
                } else {
                    Log::info("=== METHOD 4 FAILED: allPlaylist not found ===");
                }
            }

            // Method 5: Try search API to find episodes
            if (empty($episodesData)) {
                Log::info("=== METHOD 5: Trying search API ===");
                $searchEpisodes = $this->searchEpisodesBySeason($seasonId);
                if (!empty($searchEpisodes)) {
                    $episodesData = $searchEpisodes;
                    Log::info("=== METHOD 5 SUCCESS: Found episodes via search API ===", ['count' => count($episodesData)]);
                } else {
                    Log::info("=== METHOD 5 FAILED: No episodes found via search API ===");
                }
            }

            // Method 6: Generate episodes based on playlist count (more accurate)
            if (empty($episodesData)) {
                Log::info("=== METHOD 6: Using playlist count for episodes ===");

                // Find the EPISODE playlist with the highest count
                $maxEpisodeCount = 0;
                if (isset($data['productModel']['playlists']) && is_array($data['productModel']['playlists'])) {
                    foreach ($data['productModel']['playlists'] as $playlist) {
                        if ($playlist['type'] === 'EPISODE' && isset($playlist['count'])) {
                            $maxEpisodeCount = max($maxEpisodeCount, $playlist['count']);
                        }
                    }
                }

                // Fallback to productModel counts if no playlist count found
                if ($maxEpisodeCount === 0) {
                    $maxEpisodeCount = $data['productModel']['numberOfAVODEpisodes'] ?? $data['productModel']['numberOfEpisodes'] ?? 0;
                }

                Log::info("=== Episode count determination ===", [
                    'playlist_max_count' => $maxEpisodeCount,
                    'numberOfAVODEpisodes' => $data['productModel']['numberOfAVODEpisodes'] ?? 'not_found',
                    'numberOfEpisodes' => $data['productModel']['numberOfEpisodes'] ?? 'not_found',
                    'final_count' => $maxEpisodeCount
                ]);

                if ($maxEpisodeCount > 0) {
                    for ($i = 1; $i <= $maxEpisodeCount; $i++) {
                        $episodesData[] = [
                            'id' => $seasonId . '_episode_' . $i,
                            'title' => 'Episode ' . $i,
                            'episodeNumber' => $i,
                            'description' => 'Episode ' . $i . ' description',
                            'duration' => 2700, // 45 minutes default
                            'image' => ['thumbnail' => ''],
                            'releaseDate' => ''
                        ];
                    }
                    Log::info("=== METHOD 6 SUCCESS: Generated episodes based on playlist count ===", ['count' => count($episodesData)]);
                } else {
                    Log::info("=== METHOD 6 FAILED: No episode count found ===");
                }
            }

            // Process episodes data if found
            if (!empty($episodesData)) {
                Log::info("=== PROCESSING EPISODES ===", ['count' => count($episodesData)]);

                foreach ($episodesData as $index => $episodeData) {
                    // Determine episode availability status
                    $streamInfo = $episodeData['streamInfo'] ?? [];
                    $streamState = $streamInfo['streamState'] ?? 'VOD';
                    $startDate = $streamInfo['startDate'] ?? null;
                    $endDate = $streamInfo['endDate'] ?? null;

                    $episodeNumber = $episodeData['number'] ?? $episodeData['episodeNumber'] ?? ($index + 1);

                    // Calculate availability status
                    $availabilityStatus = $this->calculateEpisodeAvailability($streamState, $startDate, $endDate);

                    $episodes[] = [
                        'id' => $episodeData['id'] ?? $seasonId . '_episode_' . ($index + 1),
                        'title' => $episodeData['title'] ?? 'Episode ' . ($index + 1),
                        'episode_number' => $episodeData['number'] ?? $episodeData['episodeNumber'] ?? ($index + 1),
                        'description' => $episodeData['description'] ?? 'No description available',
                        'duration' => $episodeData['duration'] ?? 2700,
                        'thumbnail' => $episodeData['image']['thumbnail'] ?? $episodeData['thumbnailImage'] ?? '',
                        'poster_url' => $episodeData['image']['posterClean'] ?? $episodeData['image']['posterImage'] ?? '',
                        'release_date' => $episodeData['releaseDate'] ?? '',
                        'stream_state' => $streamState,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'availability_status' => $availabilityStatus['status'],
                        'availability_message' => $availabilityStatus['message'],
                        'coming_soon_time' => $availabilityStatus['coming_soon_time'] ?? null
                    ];
                }
            }



            // Extract season info for planned episodes
            $seasonInfo = [];
            if (isset($data['productModel'])) {
                $seasonInfo = [
                    'numberOfEpisodes' => $data['productModel']['numberOfEpisodes'] ?? 0,
                    'numberOfAVODEpisodes' => $data['productModel']['numberOfAVODEpisodes'] ?? 0,
                    'title' => $data['productModel']['title'] ?? ''
                ];
            }

            Log::info("=== FINAL RESULT ===", ['episodes_count' => count($episodes)]);
            return [
                'success' => true,
                'data' => $episodes,
                'season_info' => $seasonInfo
            ];

        } catch (\Exception $e) {
            Log::error("Error getting season episodes: " . $e->getMessage());
            return ['error' => 'Failed to get season episodes: ' . $e->getMessage()];
        }
    }

    /**
     * Generate automatic coming soon date for episodes
     */
    private function generateComingSoonDate($episodeNumber)
    {
        // Base date: August 13, 2025 at 11:59 PM
        $baseDate = new \DateTime('2025-08-13 23:59:00', new \DateTimeZone('UTC'));

        // Add days based on episode number (episode 8 = Aug 14, episode 9 = Aug 15, etc.)
        $daysToAdd = $episodeNumber - 8; // Episode 8 starts on Aug 14
        if ($daysToAdd < 0) $daysToAdd = 0;

        $baseDate->add(new \DateInterval('P' . $daysToAdd . 'D'));

        return $baseDate->format('Y-m-d\TH:i:s.000\Z');
    }



    /**
     * Calculate episode availability status based on stream info
     */
    private function calculateEpisodeAvailability($streamState, $startDate, $endDate)
    {
        $now = new \DateTime();

        Log::info("=== CALCULATING EPISODE AVAILABILITY ===", [
            'streamState' => $streamState,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'current_time' => $now->format('Y-m-d H:i:s')
        ]);

        switch ($streamState) {
            case 'COMING_SOON':
                // For COMING_SOON episodes, check the end_date (availability date)
                if ($endDate) {
                    $availabilityDate = new \DateTime($endDate);
                    if ($availabilityDate > $now) {
                        // Episode is coming soon (availability date hasn't arrived yet)
                        $diff = $availabilityDate->diff($now);
                        $comingSoonTime = $this->formatComingSoonTime($diff, $availabilityDate);

                        return [
                            'status' => 'coming_soon',
                            'message' => $comingSoonTime['message'],
                            'coming_soon_time' => $comingSoonTime['time']
                        ];
                    } else {
                        // Availability date has passed, episode should be available now
                        return [
                            'status' => 'available',
                            'message' => 'Available now'
                        ];
                    }
                }

                // If no end_date but has start_date (upload date), check if it's recent
                if ($startDate) {
                    $uploadDate = new \DateTime($startDate);
                    $diff = $now->diff($uploadDate);
                    if ($diff->days <= 7) {
                        return [
                            'status' => 'coming_soon_uploaded',
                            'message' => 'Coming Soon (Recently Uploaded)'
                        ];
                    }
                }

                // Default coming soon status
                return [
                    'status' => 'coming_soon',
                    'message' => 'Coming Soon'
                ];

            case 'VOD':
                // Check if episode has expired
                if ($endDate) {
                    $end = new \DateTime($endDate);
                    if ($end < $now) {
                        return [
                            'status' => 'expired',
                            'message' => 'No longer available'
                        ];
                    }
                }
                return [
                    'status' => 'available',
                    'message' => 'Available now'
                ];

            case 'LIVE':
                return [
                    'status' => 'live',
                    'message' => 'Live now'
                ];

            default:
                return [
                    'status' => 'available',
                    'message' => 'Available now'
                ];
        }
    }

    /**
     * Format coming soon time message
     */
    private function formatComingSoonTime($diff, $startDateTime)
    {
        // Convert to Egypt timezone (UTC+2 in winter, UTC+3 in summer)
        $egyptTime = clone $startDateTime;
        $egyptTime->setTimezone(new \DateTimeZone('Africa/Cairo'));

        // Get current time in Egypt timezone
        $nowEgypt = new \DateTime('now', new \DateTimeZone('Africa/Cairo'));

        // Calculate difference in Egypt timezone
        $diffEgypt = $egyptTime->diff($nowEgypt);
        $totalHours = ($diffEgypt->days * 24) + $diffEgypt->h;

        Log::info("=== COMING SOON TIME CALCULATION ===", [
            'egyptTime' => $egyptTime->format('Y-m-d H:i:s T'),
            'nowEgypt' => $nowEgypt->format('Y-m-d H:i:s T'),
            'diffEgypt_days' => $diffEgypt->days,
            'diffEgypt_h' => $diffEgypt->h,
            'diffEgypt_i' => $diffEgypt->i,
            'totalHours' => $totalHours
        ]);

        // Check if it's the same calendar day or next day
        $nowDate = $nowEgypt->format('Y-m-d');
        $episodeDate = $egyptTime->format('Y-m-d');

        if ($totalHours < 1) {
            // Less than 1 hour - show minutes
            $message = "Coming in {$diffEgypt->i} minutes";
            $time = $egyptTime->format('H:i');
        } elseif ($nowDate === $episodeDate) {
            // Same calendar day - show "Coming Tonight" with Egypt time
            $message = "Coming Tonight " . $egyptTime->format('g:i A');
            $time = $egyptTime->format('H:i');
        } elseif ($egyptTime->format('Y-m-d') === date('Y-m-d', strtotime($nowDate . ' +1 day'))) {
            // Next calendar day - show "Coming Tomorrow" with Egypt time
            $message = "Coming Tomorrow " . $egyptTime->format('g:i A');
            $time = $egyptTime->format('H:i');
        } else {
            // Multiple days - show date and time in Egypt timezone
            $message = $egyptTime->format('M j, g:i A');
            $time = $egyptTime->format('Y-m-d H:i:s');
        }

        return [
            'message' => $message,
            'time' => $time
        ];
    }

    /**
     * Search for episodes by season ID using search API
     */
    private function searchEpisodesBySeason($seasonId)
    {
        try {
            Log::info("=== SEARCH API: Starting search for episodes ===", ['season_id' => $seasonId]);

            // Try different search approaches
            $searchQueries = [
                $seasonId, // Direct season ID
                "season:" . $seasonId, // Season prefix
                "seasonId:" . $seasonId // SeasonId prefix
            ];

            foreach ($searchQueries as $query) {
                Log::info("=== SEARCH API: Trying query ===", ['query' => $query]);

                $url = "https://api2.shahid.net/proxy/v2.1/product/filter";
                $params = [
                    'filter' => json_encode([
                        'pageNumber' => 0,
                        'pageSize' => 50,
                        'productType' => 'EPISODE',
                        'query' => $query
                    ]),
                    'country' => 'EG'
                ];

                $headers = [
                    'authority' => 'api2.shahid.net',
                    'accept' => 'application/json',
                    'accept-language' => 'en',
                    'content-type' => 'application/json',
                    'language' => 'en',
                    'origin' => 'https://shahid.mbc.net',
                    'referer' => 'https://shahid.mbc.net/',
                    'token' => $this->token,
                    'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                    'uuid' => 'web'
                ];

                $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

                if ($result['success'] && isset($result['data']['products']) && is_array($result['data']['products'])) {
                    $episodes = $result['data']['products'];
                    Log::info("=== SEARCH API: Found episodes ===", ['query' => $query, 'count' => count($episodes)]);

                    if (count($episodes) > 0) {
                        return $episodes;
                    }
                } else {
                    Log::info("=== SEARCH API: No episodes found ===", ['query' => $query]);
                }
            }

            return [];

        } catch (\Exception $e) {
            Log::error("=== SEARCH API: Error ===", ['error' => $e->getMessage()]);
            return [];
        }
    }



    /**
     * Get episodes from a specific playlist (using Python method)
     */
    private function getPlaylistEpisodes($playlistId)
    {
        try {
            $allEpisodes = [];
            $page = 0;
            $pageSize = 30; // Keep original page size like Python code

            Log::info("=== STARTING PAGINATION FOR PLAYLIST ===", ['playlist_id' => $playlistId]);

            while (true) {
                $url = "https://api3.shahid.net/proxy/v2.1/product/playlist";
                $params = [
                    'request' => json_encode([
                        'pageNumber' => $page,
                        'pageSize' => $pageSize,
                        'playListId' => (string)$playlistId,
                        'sorts' => [['order' => 'DESC', 'type' => 'SORTDATE']]
                    ]),
                    'country' => 'EG'
                ];

                $headers = [
                    'authority' => 'api2.shahid.net',
                    'accept' => 'application/json',
                    'accept-language' => 'en',
                    'content-type' => 'application/json',
                    'language' => 'en',
                    'origin' => 'https://shahid.mbc.net',
                    'referer' => 'https://shahid.mbc.net/',
                    'token' => $this->token,
                    'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                    'uuid' => 'web'
                ];

                Log::info("=== FETCHING PAGE ===", [
                    'playlist_id' => $playlistId,
                    'page' => $page,
                    'page_size' => $pageSize
                ]);

                $result = $this->makeRequestWithCustomHeaders('GET', $url, $params, $headers);

                if (!$result['success']) {
                    Log::error("Failed to get playlist episodes page", [
                        'page' => $page,
                        'error' => $result['error']
                    ]);
                    break;
                }

                $data = $result['data'];

                if (!isset($data['productList']['products'])) {
                    Log::warning("No products found in playlist response", ['page' => $page]);
                    break;
                }

                $currentPageEpisodes = $data['productList']['products'];
                $episodeCount = count($currentPageEpisodes);

                Log::info("=== PAGE RESULT ===", [
                    'page' => $page,
                    'episodes_found' => $episodeCount,
                    'total_so_far' => count($allEpisodes)
                ]);

                // If no episodes in this page, we're done
                if ($episodeCount === 0) {
                    Log::info("No episodes found in page {$page}, stopping pagination");
                    break;
                }

                // Add episodes from this page to our collection
                $allEpisodes = array_merge($allEpisodes, $currentPageEpisodes);

                // If we got less than pageSize episodes, we're done (last page)
                if ($episodeCount < $pageSize) {
                    Log::info("Got {$episodeCount} episodes (less than page size {$pageSize}), stopping pagination");
                    break;
                }

                // Move to next page
                $page++;

                // Safety limit to prevent infinite loops
                if ($page > 20) {
                    Log::warning("Reached page limit (20), stopping pagination");
                    break;
                }
            }

            Log::info("=== PAGINATION COMPLETE ===", [
                'playlist_id' => $playlistId,
                'total_episodes' => count($allEpisodes),
                'total_pages' => $page + 1
            ]);

            return $allEpisodes;

        } catch (\Exception $e) {
            Log::error("Error getting playlist episodes: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Make HTTP request with custom headers
     */
    private function makeRequestWithCustomHeaders($method, $url, $params = [], $customHeaders = [])
    {
        try {
            $client = new \GuzzleHttp\Client([
                'timeout' => 30,
                'verify' => false,
                'http_errors' => false
            ]);

            $options = [
                'headers' => $customHeaders
            ];

            if ($method === 'GET' && !empty($params)) {
                $options['query'] = $params;
            } elseif ($method === 'POST') {
                $options['json'] = $params;
            }

            $response = $client->request($method, $url, $options);
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            Log::info("Custom API Request", [
                'method' => $method,
                'url' => $url,
                'status' => $statusCode,
                'response_length' => strlen($body)
            ]);

            if ($statusCode === 200) {
                $data = json_decode($body, true);
                return ['success' => true, 'data' => $data];
            } else {
                return ['success' => false, 'error' => "HTTP {$statusCode}: {$body}"];
            }

        } catch (\Exception $e) {
            Log::error("Custom API Request failed: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get stream URL for content (movie or episode)
     */
    public function getStreamUrl($contentId)
    {
        try {
            Log::info("📡 Getting stream URL for content: {$contentId}");

            // Get content details first
            $contentDetails = $this->getContentDetails($contentId);
            if (!$contentDetails || !isset($contentDetails['data'])) {
                return [
                    'success' => false,
                    'error' => 'Failed to get content details'
                ];
            }

            // Look for stream URL in content data
            $contentData = $contentDetails['data'];

            // Check for direct stream URL
            if (isset($contentData['stream_url'])) {
                return [
                    'success' => true,
                    'stream_url' => $contentData['stream_url']
                ];
            }

            // Check for manifest URL in productModel
            if (isset($contentData['productModel']['manifestUrl'])) {
                return [
                    'success' => true,
                    'stream_url' => $contentData['productModel']['manifestUrl']
                ];
            }

            // Try to get stream URL from API
            $streamEndpoint = $this->baseUrl . $this->endpoints['stream'] . '/' . $contentId;
            $response = $this->makeRequest('GET', $streamEndpoint);

            if ($response && isset($response['data']['stream_url'])) {
                return [
                    'success' => true,
                    'stream_url' => $response['data']['stream_url']
                ];
            }

            // Fallback: construct stream URL based on content type
            $streamUrl = $this->constructStreamUrl($contentId, $contentData);
            if ($streamUrl) {
                return [
                    'success' => true,
                    'stream_url' => $streamUrl
                ];
            }

            return [
                'success' => false,
                'error' => 'No stream URL found for content'
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error getting stream URL: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Construct stream URL based on content data
     */
    private function constructStreamUrl($contentId, $contentData)
    {
        try {
            // Common Shahid stream URL patterns
            $baseStreamUrl = 'https://mbcvod-enc.edgenextcdn.net';

            // Try to extract stream path from content data
            if (isset($contentData['productModel']['id'])) {
                $productId = $contentData['productModel']['id'];
                return "{$baseStreamUrl}/out/v1/{$productId}/index.mpd";
            }

            // Fallback to content ID
            return "{$baseStreamUrl}/out/v1/{$contentId}/index.mpd";

        } catch (\Exception $e) {
            Log::error("❌ Error constructing stream URL: " . $e->getMessage());
            return null;
        }
    }

}
