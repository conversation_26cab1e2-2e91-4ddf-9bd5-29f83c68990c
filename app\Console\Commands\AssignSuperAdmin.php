<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Admin;
use <PERSON>tie\Permission\Models\Role;

class AssignSuperAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:assign-super {admin_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign Super Admin role to an admin user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $adminId = $this->argument('admin_id');

        // Find admin user
        $admin = Admin::find($adminId);

        if (!$admin) {
            $this->error("Admin with ID {$adminId} not found");
            return 1;
        }

        $this->info("Found admin: {$admin->name} ({$admin->email})");

        // Check if Super Admin role exists
        $superAdminRole = Role::where('name', 'Super Admin')
                             ->where('guard_name', 'admin')
                             ->first();

        if (!$superAdminRole) {
            $this->info("Creating Super Admin role...");
            $superAdminRole = Role::create([
                'name' => 'Super Admin',
                'guard_name' => 'admin'
            ]);
        }

        // Assign the role
        if (!$admin->hasRole('Super Admin')) {
            $admin->assignRole('Super Admin');
            $this->info("✅ Super Admin role assigned to {$admin->email}");
        } else {
            $this->info("✅ {$admin->email} already has Super Admin role");
        }

        // Also set is_super_admin flag
        if (!$admin->is_super_admin) {
            $admin->update(['is_super_admin' => true]);
            $this->info("✅ is_super_admin flag set to true");
        }

        $this->info("\nCurrent roles for {$admin->email}:");
        foreach ($admin->roles as $role) {
            $this->line("- {$role->name}");
        }

        $this->info("\nSuper admin status: " . ($admin->is_super_admin ? 'Yes' : 'No'));

        return 0;
    }
}
