<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create Admin Users (Admin model with admin guard)
        $this->createAdminUsers();

        // Create Regular Users (User model with web guard)
        $this->createRegularUsers();

        $this->command->info('All users created successfully!');
    }

    /**
     * Create Admin users using Admin model
     */
    private function createAdminUsers(): void
    {
        // Super Admin
        $superAdmin = Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'username' => 'admin',
                'password' => Hash::make('admin123'),
                'is_super_admin' => true,
                'is_active' => true,
            ]
        );

        $superAdminRole = Role::where('name', 'Super Admin')->where('guard_name', 'admin')->first();
        if ($superAdminRole) {
            $superAdmin->assignRole($superAdminRole);
        }

        // Content Manager
        $contentManager = Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Content Manager',
                'username' => 'manager',
                'password' => Hash::make('manager123'),
                'is_super_admin' => false,
                'is_active' => true,
            ]
        );

        $contentManagerRole = Role::where('name', 'Content Manager')->where('guard_name', 'admin')->first();
        if ($contentManagerRole) {
            $contentManager->assignRole($contentManagerRole);
        }

        // User Manager
        $userManager = Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'User Manager',
                'username' => 'usermanager',
                'password' => Hash::make('usermanager123'),
                'is_super_admin' => false,
                'is_active' => true,
            ]
        );

        $userManagerRole = Role::where('name', 'User Manager')->where('guard_name', 'admin')->first();
        if ($userManagerRole) {
            $userManager->assignRole($userManagerRole);
        }

        $this->command->info('Admin users created:');
        $this->command->info('- Super Admin: <EMAIL> / admin123');
        $this->command->info('- Content Manager: <EMAIL> / manager123');
        $this->command->info('- User Manager: <EMAIL> / usermanager123');
    }

    /**
     * Create regular users using User model
     */
    private function createRegularUsers(): void
    {
        // Super Admin User (for web interface)
        $superAdminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'password' => Hash::make('SuperAdmin@123'),
                'user_type' => 'admin',
                'is_admin' => true,
                'is_super_admin' => true,
                'can_manage_admins' => true,
                'is_active' => true,
                'email_verified' => true,
                'department' => 'IT',
                'position' => 'Super Administrator',
                'password_changed_at' => now(),
            ]
        );

        $superAdminRole = Role::where('name', 'Super Admin')->where('guard_name', 'web')->first();
        if ($superAdminRole) {
            $superAdminUser->assignRole($superAdminRole);
        }

        // Regular Admin User
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('Admin@123'),
                'user_type' => 'admin',
                'is_admin' => true,
                'is_super_admin' => false,
                'can_manage_admins' => false,
                'is_active' => true,
                'email_verified' => true,
                'department' => 'IT',
                'position' => 'System Administrator',
                'created_by_admin' => $superAdminUser->id,
                'password_changed_at' => now(),
            ]
        );

        $adminRole = Role::where('name', 'Admin')->where('guard_name', 'web')->first();
        if ($adminRole) {
            $adminUser->assignRole($adminRole);
        }

        // Regular User
        $regularUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'password' => Hash::make('password'),
                'user_type' => 'user',
                'is_admin' => false,
                'is_active' => true,
                'email_verified' => true,
            ]
        );

        $userRole = Role::where('name', 'User')->where('guard_name', 'web')->first();
        if ($userRole) {
            $regularUser->assignRole($userRole);
        }

        $this->command->info('Regular users created:');
        $this->command->info('- Super Admin: <EMAIL> / SuperAdmin@123');
        $this->command->info('- Admin: <EMAIL> / Admin@123');
        $this->command->info('- User: <EMAIL> / password');
    }
}
