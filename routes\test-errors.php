<?php

use Illuminate\Support\Facades\Route;

// Test routes for error pages (only in development)
if (app()->environment(['local', 'development'])) {
    Route::prefix('admin/test-errors')->middleware(['auth', 'admin'])->group(function () {
        
        Route::get('/403', function () {
            abort(403, 'Access denied for testing purposes');
        })->name('test.error.403');
        
        Route::get('/404', function () {
            abort(404, 'Page not found for testing purposes');
        })->name('test.error.404');
        
        Route::get('/419', function () {
            abort(419, 'Session expired for testing purposes');
        })->name('test.error.419');
        
        Route::get('/500', function () {
            throw new \Exception('Internal server error for testing purposes');
        })->name('test.error.500');
        
        Route::get('/503', function () {
            abort(503, 'Service unavailable for testing purposes');
        })->name('test.error.503');
        
        // Test custom error
        Route::get('/custom/{code}', function ($code) {
            abort((int)$code, "Custom error {$code} for testing purposes");
        })->name('test.error.custom');
        
        // Error page preview (without throwing actual errors)
        Route::get('/preview/{code}', function ($code) {
            $errorView = "errors.{$code}";
            
            if (view()->exists($errorView)) {
                return view($errorView, [
                    'exception' => new \Exception("Preview of error {$code}"),
                    'statusCode' => (int)$code
                ]);
            }
            
            return view('errors.layout', [
                'exception' => new \Exception("Preview of error {$code}"),
                'statusCode' => (int)$code,
                'code' => $code,
                'title' => "Error {$code}",
                'message' => "This is a preview of error page {$code}"
            ]);
        })->name('test.error.preview');
        
        // List all available error tests
        Route::get('/', function () {
            return view('admin.test-errors.index');
        })->name('test.errors.index');
    });
}
