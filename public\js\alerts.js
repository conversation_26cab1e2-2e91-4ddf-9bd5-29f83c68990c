/**
 * Unified Toast Alert System using SweetAlert2
 * All error messages in English
 * Simple, clean, and beautiful notifications
 */

// Check if Toast is available (defined in main layout)
if (typeof Toast === 'undefined') {
    console.warn('Toast is not defined. Make sure this script loads after the main layout script.');
}

// Common error messages
const ERROR_MESSAGES = {
    // Network errors
    NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    TIMEOUT_ERROR: 'Request timeout. Please try again.',
    
    // Authentication errors
    TOKEN_REQUIRED: 'Authentication token is required.',
    TOKEN_INVALID: 'Invalid or expired token. Please update your token.',
    TOKEN_MISSING: 'Please enter the token.',
    
    // Data errors
    NO_DATA_FOUND: 'No data found.',
    INVALID_DATA: 'Invalid data received.',
    DATA_LOAD_ERROR: 'Failed to load data.',
    
    // Movie/Series specific
    MOVIE_NOT_FOUND: 'Movie not found.',
    SERIES_NOT_FOUND: 'Series not found.',
    CHANNEL_NOT_FOUND: 'Channel not found.',
    PLAYBACK_ERROR: 'Failed to get required playback data.',
    EXTRACTION_ERROR: 'Error occurred while extracting data.',
    
    // General
    UNKNOWN_ERROR: 'An unknown error occurred.',
    OPERATION_FAILED: 'Operation failed. Please try again.',
    COPY_ERROR: 'Error copying data to clipboard.',
    
    // Validation
    REQUIRED_FIELD: 'This field is required.',
    INVALID_FORMAT: 'Invalid format.',
    
    // Success messages
    SUCCESS_SAVE: 'Data saved successfully.',
    SUCCESS_UPDATE: 'Updated successfully.',
    SUCCESS_DELETE: 'Deleted successfully.',
    SUCCESS_COPY: 'Data copied to clipboard.',
    
    // Loading messages
    LOADING_DATA: 'Loading data...',
    LOADING_MOVIES: 'Loading movies...',
    LOADING_SERIES: 'Loading series...',
    LOADING_CHANNELS: 'Loading channels...',
    PROCESSING: 'Processing...',
};

// Quick alert functions with predefined messages using custom icons
window.AlertSystem = {
    // Network errors
    networkError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.NETWORK_ERROR }),
    serverError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.SERVER_ERROR }),
    timeoutError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.TIMEOUT_ERROR }),

    // Authentication
    tokenRequired: () => Toast.fire({ iconHtml: '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>', title: ERROR_MESSAGES.TOKEN_REQUIRED }),
    tokenInvalid: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.TOKEN_INVALID }),
    tokenMissing: () => Toast.fire({ iconHtml: '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>', title: ERROR_MESSAGES.TOKEN_MISSING }),

    // Data
    noDataFound: () => Toast.fire({ iconHtml: '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>', title: ERROR_MESSAGES.NO_DATA_FOUND }),
    invalidData: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.INVALID_DATA }),
    dataLoadError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.DATA_LOAD_ERROR }),

    // Content specific
    movieNotFound: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.MOVIE_NOT_FOUND }),
    seriesNotFound: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.SERIES_NOT_FOUND }),
    channelNotFound: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.CHANNEL_NOT_FOUND }),
    playbackError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.PLAYBACK_ERROR }),
    extractionError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.EXTRACTION_ERROR }),

    // General
    unknownError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.UNKNOWN_ERROR }),
    operationFailed: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.OPERATION_FAILED }),
    copyError: () => Toast.fire({ iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>', title: ERROR_MESSAGES.COPY_ERROR }),

    // Success
    successSave: () => Toast.fire({ iconHtml: '<i class="fas fa-check-circle" style="color: #28a745;"></i>', title: ERROR_MESSAGES.SUCCESS_SAVE }),
    successUpdate: () => Toast.fire({ iconHtml: '<i class="fas fa-check-circle" style="color: #28a745;"></i>', title: ERROR_MESSAGES.SUCCESS_UPDATE }),
    successDelete: () => Toast.fire({ iconHtml: '<i class="fas fa-check-circle" style="color: #28a745;"></i>', title: ERROR_MESSAGES.SUCCESS_DELETE }),
    successCopy: () => Toast.fire({ iconHtml: '<i class="fas fa-check-circle" style="color: #28a745;"></i>', title: ERROR_MESSAGES.SUCCESS_COPY }),

    // Loading (using Swal.fire for loading, not toast)
    loadingData: () => showLoading(ERROR_MESSAGES.LOADING_DATA),
    loadingMovies: () => showLoading(ERROR_MESSAGES.LOADING_MOVIES),
    loadingSeries: () => showLoading(ERROR_MESSAGES.LOADING_SERIES),
    loadingChannels: () => showLoading(ERROR_MESSAGES.LOADING_CHANNELS),
    processing: () => showLoading(ERROR_MESSAGES.PROCESSING),
};

// Handle AJAX errors automatically
window.handleAjaxError = function(xhr, textStatus, errorThrown) {
    console.error('AJAX Error:', { xhr, textStatus, errorThrown });
    
    let errorMessage = ERROR_MESSAGES.UNKNOWN_ERROR;
    let title = 'Error';
    
    if (xhr.status === 0) {
        errorMessage = ERROR_MESSAGES.NETWORK_ERROR;
        title = 'Network Error';
    } else if (xhr.status === 401) {
        errorMessage = ERROR_MESSAGES.TOKEN_INVALID;
        title = 'Authentication Error';
    } else if (xhr.status === 403) {
        errorMessage = 'Access denied. You do not have permission to perform this action.';
        title = 'Access Denied';
    } else if (xhr.status === 404) {
        errorMessage = 'The requested resource was not found.';
        title = 'Not Found';
    } else if (xhr.status === 422) {
        // Validation errors
        if (xhr.responseJSON && xhr.responseJSON.errors) {
            showValidationErrors(xhr.responseJSON.errors);
            return;
        }
        errorMessage = 'Validation error occurred.';
        title = 'Validation Error';
    } else if (xhr.status === 429) {
        errorMessage = 'Too many requests. Please wait a moment and try again.';
        title = 'Rate Limit';
    } else if (xhr.status >= 500) {
        errorMessage = ERROR_MESSAGES.SERVER_ERROR;
        title = 'Server Error';
    } else if (textStatus === 'timeout') {
        errorMessage = ERROR_MESSAGES.TIMEOUT_ERROR;
        title = 'Timeout Error';
    } else if (xhr.responseJSON && xhr.responseJSON.message) {
        errorMessage = xhr.responseJSON.message;
    }
    
    showError(title, errorMessage);
};

// Enhanced confirmation dialog - slide from top
window.confirmAction = function(title, message, confirmText = 'Yes', cancelText = 'Cancel') {
    return new Promise((resolve) => {
        Swal.fire({
            icon: 'question',
            title: title,
            text: message,
            position: 'top',
            showCancelButton: true,
            confirmButtonText: confirmText,
            cancelButtonText: cancelText,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            reverseButtons: true,
            focusCancel: true,
            showClass: {
                popup: 'animate__animated animate__slideInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__slideOutUp'
            },
            backdrop: 'rgba(0, 0, 0, 0.4)',
            allowOutsideClick: true
        }).then((result) => {
            resolve(result.isConfirmed);
        });
    });
};

// ===== MAIN ALERT FUNCTIONS =====
// These functions use the global Toast defined in layout

// Success messages
window.showSuccess = function(title, message = '') {
    Toast.fire({
        icon: 'success',
        title: title || 'Success!',
        text: message
    });
};

// Error messages
window.showError = function(title, message = '') {
    Toast.fire({
        icon: 'error',
        title: title || 'Error!',
        text: message,
        timer: 4000
    });
};

// Warning messages
window.showWarning = function(title, message = '') {
    Toast.fire({
        icon: 'warning',
        title: title || 'Warning!',
        text: message,
        timer: 3500
    });
};

// Info messages
window.showInfo = function(title, message = '') {
    Toast.fire({
        icon: 'info',
        title: title || 'Information',
        text: message
    });
};

// Simple toast function using the global Toast
window.showToast = function(icon, title, timer = 3000) {
    Toast.fire({
        icon: icon,
        title: title,
        timer: timer
    });
};

// Loading messages (center modal, not toast)
window.showLoading = function(title = 'Loading...') {
    Swal.fire({
        title: title,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
};

// Close loading
window.closeLoading = function() {
    Swal.close();
};

// Progress dialog for long operations
window.showProgress = function(title = 'Processing...') {
    let timerInterval;
    
    Swal.fire({
        title: title,
        html: 'Progress: <b>0%</b>',
        timer: 30000,
        timerProgressBar: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
            const b = Swal.getHtmlContainer().querySelector('b');
            timerInterval = setInterval(() => {
                const timeLeft = Swal.getTimerLeft();
                const progress = Math.round(((30000 - timeLeft) / 30000) * 100);
                b.textContent = progress + '%';
            }, 100);
        },
        willClose: () => {
            clearInterval(timerInterval);
        }
    });
    
    return {
        update: (progress, text) => {
            const b = Swal.getHtmlContainer().querySelector('b');
            if (b) b.textContent = progress + '%';
            if (text) Swal.getTitle().textContent = text;
        },
        close: () => Swal.close()
    };
};

// Check if everything is loaded correctly
if (typeof Toast !== 'undefined' && typeof Swal !== 'undefined') {
    console.log('✅ Alert System loaded successfully');
} else {
    console.error('❌ Alert System failed to load. Missing dependencies:', {
        Toast: typeof Toast,
        Swal: typeof Swal
    });
}
