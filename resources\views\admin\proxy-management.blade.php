@extends('admin.layouts.app')

@section('title', 'Proxy Management - Shahid Admin')

@section('styles')
<style>
.proxy-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.proxy-card:hover {
    transform: translateY(-5px);
}

.proxy-card.active {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    box-shadow: 0 10px 30px rgba(86, 171, 47, 0.3);
}

.proxy-card.inactive {
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
}

.proxy-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background: #28a745;
    color: white;
}

.status-inactive {
    background: #6c757d;
    color: white;
}

.status-testing {
    background: #ffc107;
    color: #212529;
}

.status-error {
    background: #dc3545;
    color: white;
}

.btn-proxy {
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    margin: 2px;
    transition: all 0.3s ease;
}

.btn-test {
    background: #17a2b8;
    color: white;
}

.btn-activate {
    background: #28a745;
    color: white;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-proxy:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.add-proxy-form {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.proxy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.proxy-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.proxy-details {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 15px;
}

.proxy-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.response-time {
    background: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
}

@media (max-width: 768px) {
    .proxy-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🌐 Proxy Management</h2>
                    <p class="text-muted mb-0">Manage proxy servers for accessing geo-restricted content</p>
                </div>
                <div>
                    <button class="btn btn-danger" onclick="deactivateAllProxies()">
                        <i class="fas fa-power-off me-2"></i>Disable All
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Proxy Form -->
    <div class="add-proxy-form">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">➕ Add New Proxy</h5>
            <div>
                <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="toggleBulkImport()">
                    <i class="fas fa-list me-1"></i>Bulk Import
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleSingleForm()">
                    <i class="fas fa-plus me-1"></i>Single Proxy
                </button>
            </div>
        </div>

        <!-- Bulk Import Form -->
        <div id="bulkImportForm" style="display: none;">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Bulk Import Format:</strong> Enter one proxy per line in format:
                <code>*****************************:port</code>
                <br>
                <small>Example: <code>*******************************************************</code></small>
            </div>
            <form id="bulkProxyForm">
                <div class="mb-3">
                    <label class="form-label">Proxy List</label>
                    <textarea class="form-control" name="proxy_list" rows="8"
                              placeholder="***********************************&#10;http://user2:pass2@***********:8080&#10;socks5://user3:pass3@***********:1080"
                              required></textarea>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Default Type (if not specified)</label>
                        <select class="form-control" name="default_type">
                            <option value="http">HTTP</option>
                            <option value="https">HTTPS</option>
                            <option value="socks4">SOCKS4</option>
                            <option value="socks5">SOCKS5</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Name Prefix</label>
                        <input type="text" class="form-control" name="name_prefix" placeholder="Proxy" value="Imported Proxy">
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Import Proxies
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="toggleSingleForm()">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                </div>
            </form>
        </div>

        <!-- Single Proxy Form -->
        <div id="singleProxyForm">
            <form id="addProxyForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Proxy Name</label>
                    <input type="text" class="form-control" name="name" placeholder="e.g., US Proxy 1" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Host/IP</label>
                    <input type="text" class="form-control" name="host" placeholder="************" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Port</label>
                    <input type="number" class="form-control" name="port" placeholder="49178" min="1" max="65535" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" name="username" placeholder="CepCU8cCVa">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" name="password" placeholder="yl55JedZrq">
                </div>
                <div class="col-md-1">
                    <label class="form-label">Type</label>
                    <select class="form-control" name="type" required>
                        <option value="http">HTTP</option>
                        <option value="https">HTTPS</option>
                        <option value="socks4">SOCKS4</option>
                        <option value="socks5">SOCKS5</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-8">
                    <label class="form-label">Description (Optional)</label>
                    <input type="text" class="form-control" name="description" placeholder="e.g., US server for accessing US content">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>Add Proxy
                    </button>
                </div>
            </div>
        </form>
        </div>
    </div>

    <!-- Current Active Proxy -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info" id="activeProxyAlert">
                <i class="fas fa-info-circle me-2"></i>
                <span id="activeProxyText">No proxy is currently active. All requests will use direct connection.</span>
            </div>
        </div>
    </div>

    <!-- Proxy List -->
    <div class="proxy-grid" id="proxyGrid">
        @foreach($proxies as $proxy)
        <div class="proxy-card {{ $proxy->is_active ? 'active' : 'inactive' }}" data-proxy-id="{{ $proxy->id }}">
            <div class="proxy-info">
                <div>
                    <h6 class="mb-0">{{ $proxy->name }}</h6>
                    <span class="proxy-status {{ $proxy->is_active ? 'status-active' : 'status-inactive' }}">
                        {{ $proxy->is_active ? 'Active' : 'Inactive' }}
                    </span>
                    @if($proxy->test_results && isset($proxy->test_results['response_time']))
                    <span class="response-time">{{ $proxy->test_results['response_time'] }}ms</span>
                    @endif
                </div>
                <div>
                    @if($proxy->is_default)
                    <i class="fas fa-star text-warning" title="Default Proxy"></i>
                    @endif
                </div>
            </div>
            
            <div class="proxy-details">
                <div><strong>Address:</strong> {{ $proxy->host }}:{{ $proxy->port }}</div>
                <div><strong>Type:</strong> {{ strtoupper($proxy->type) }}</div>
                @if($proxy->username)
                <div><strong>Auth:</strong> {{ $proxy->username }}:***</div>
                @endif
                @if($proxy->description)
                <div><strong>Description:</strong> {{ $proxy->description }}</div>
                @endif
                @if($proxy->last_tested_at)
                <div><strong>Last Tested:</strong> {{ $proxy->last_tested_at->diffForHumans() }}</div>
                @endif
            </div>

            <div class="proxy-actions">
                <button class="btn btn-proxy btn-test" onclick="testProxy({{ $proxy->id }})">
                    <i class="fas fa-vial"></i> Test
                </button>
                @if(!$proxy->is_active)
                <button class="btn btn-proxy btn-activate" onclick="activateProxy({{ $proxy->id }})">
                    <i class="fas fa-play"></i> Activate
                </button>
                @endif
                <button class="btn btn-proxy btn-edit" onclick="editProxy({{ $proxy->id }})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-proxy btn-delete" onclick="deleteProxy({{ $proxy->id }})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
        @endforeach
    </div>

    @if($proxies->isEmpty())
    <div class="text-center py-5">
        <i class="fas fa-network-wired fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No proxies configured</h5>
        <p class="text-muted">Add your first proxy using the form above</p>
    </div>
    @endif
</div>

<!-- Edit Proxy Modal -->
<div class="modal fade" id="editProxyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Proxy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProxyForm">
                    <input type="hidden" id="editProxyId">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Proxy Name</label>
                            <input type="text" class="form-control" id="editName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Type</label>
                            <select class="form-control" id="editType" required>
                                <option value="http">HTTP</option>
                                <option value="https">HTTPS</option>
                                <option value="socks4">SOCKS4</option>
                                <option value="socks5">SOCKS5</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">Host/IP</label>
                            <input type="text" class="form-control" id="editHost" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Port</label>
                            <input type="number" class="form-control" id="editPort" min="1" max="65535" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-control" id="editUsername">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Password</label>
                            <input type="password" class="form-control" id="editPassword">
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Description</label>
                        <input type="text" class="form-control" id="editDescription">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateProxy()">Update Proxy</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    updateActiveProxyDisplay();

    // Add proxy form submission
    $('#addProxyForm').on('submit', function(e) {
        e.preventDefault();
        addProxy();
    });

    // Bulk import form submission
    $('#bulkProxyForm').on('submit', function(e) {
        e.preventDefault();
        bulkImportProxies();
    });
});

function addProxy() {
    const formData = new FormData(document.getElementById('addProxyForm'));
    const data = Object.fromEntries(formData);

    $.ajax({
        url: '/admin/proxy-management',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                $('#addProxyForm')[0].reset();
                location.reload(); // Reload to show new proxy
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMsg = 'Validation errors:\n';
                Object.keys(errors).forEach(key => {
                    errorMsg += `${key}: ${errors[key][0]}\n`;
                });
                showAlert('danger', errorMsg);
            } else {
                showAlert('danger', 'Error adding proxy');
            }
        }
    });
}

function testProxy(proxyId) {
    const card = $(`.proxy-card[data-proxy-id="${proxyId}"]`);
    const testBtn = card.find('.btn-test');
    const originalText = testBtn.html();

    testBtn.html('<i class="fas fa-spinner fa-spin"></i> Testing...').prop('disabled', true);

    $.ajax({
        url: `/admin/proxy-management/${proxyId}/test`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success && response.results.status === 'success') {
                showAlert('success', `Proxy test successful! Response time: ${response.results.response_time}ms`);
                // Update response time display
                const responseTimeSpan = card.find('.response-time');
                if (responseTimeSpan.length) {
                    responseTimeSpan.text(`${response.results.response_time}ms`);
                } else {
                    card.find('.proxy-status').after(`<span class="response-time">${response.results.response_time}ms</span>`);
                }
            } else {
                showAlert('danger', `Proxy test failed: ${response.results?.error || 'Unknown error'}`);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error testing proxy');
        },
        complete: function() {
            testBtn.html(originalText).prop('disabled', false);
        }
    });
}

function activateProxy(proxyId) {
    if (!confirm('Are you sure you want to activate this proxy? This will deactivate all other proxies.')) {
        return;
    }

    $.ajax({
        url: `/admin/proxy-management/${proxyId}/activate`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                location.reload(); // Reload to update UI
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error activating proxy');
        }
    });
}

function deactivateAllProxies() {
    if (!confirm('Are you sure you want to deactivate all proxies? This will use direct connection.')) {
        return;
    }

    $.ajax({
        url: '/admin/proxy-management/deactivate-all',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                location.reload();
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error deactivating proxies');
        }
    });
}

function editProxy(proxyId) {
    // Get proxy data and populate modal
    const card = $(`.proxy-card[data-proxy-id="${proxyId}"]`);

    // This is a simplified version - in a real app you'd fetch the data via AJAX
    $('#editProxyModal').modal('show');
    $('#editProxyId').val(proxyId);
}

function updateProxy() {
    const proxyId = $('#editProxyId').val();
    const data = {
        name: $('#editName').val(),
        host: $('#editHost').val(),
        port: $('#editPort').val(),
        username: $('#editUsername').val(),
        password: $('#editPassword').val(),
        type: $('#editType').val(),
        description: $('#editDescription').val()
    };

    $.ajax({
        url: `/admin/proxy-management/${proxyId}`,
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                $('#editProxyModal').modal('hide');
                location.reload();
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error updating proxy');
        }
    });
}

function deleteProxy(proxyId) {
    if (!confirm('Are you sure you want to delete this proxy? This action cannot be undone.')) {
        return;
    }

    $.ajax({
        url: `/admin/proxy-management/${proxyId}`,
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                $(`.proxy-card[data-proxy-id="${proxyId}"]`).fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            showAlert('danger', 'Error deleting proxy');
        }
    });
}

function updateActiveProxyDisplay() {
    const activeCard = $('.proxy-card.active');
    const alertText = $('#activeProxyText');

    if (activeCard.length > 0) {
        const proxyName = activeCard.find('h6').text();
        const proxyAddress = activeCard.find('.proxy-details div:first').text().replace('Address: ', '');
        alertText.html(`<strong>Active Proxy:</strong> ${proxyName} (${proxyAddress})`);
        $('#activeProxyAlert').removeClass('alert-info').addClass('alert-success');
    } else {
        alertText.text('No proxy is currently active. All requests will use direct connection.');
        $('#activeProxyAlert').removeClass('alert-success').addClass('alert-info');
    }
}

function toggleBulkImport() {
    console.log('Toggling to bulk import...');
    $('#bulkImportForm').show();
    $('#singleProxyForm').hide();
    console.log('Bulk import form shown');
}

function toggleSingleForm() {
    console.log('Toggling to single form...');
    $('#bulkImportForm').hide();
    $('#singleProxyForm').show();
    console.log('Single form shown');
}

function bulkImportProxies() {
    const formData = new FormData(document.getElementById('bulkProxyForm'));
    const data = Object.fromEntries(formData);

    const submitBtn = $('#bulkProxyForm button[type="submit"]');
    const originalText = submitBtn.html();
    submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Importing...').prop('disabled', true);

    $.ajax({
        url: '/admin/proxy-management/bulk-import',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                showAlert('success', `Successfully imported ${response.imported_count} proxies. ${response.failed_count} failed.`);
                $('#bulkProxyForm')[0].reset();
                toggleSingleForm();
                location.reload();
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMsg = 'Validation errors:\n';
                Object.keys(errors).forEach(key => {
                    errorMsg += `${key}: ${errors[key][0]}\n`;
                });
                showAlert('danger', errorMsg);
            } else {
                showAlert('danger', 'Error importing proxies');
            }
        },
        complete: function() {
            submitBtn.html(originalText).prop('disabled', false);
        }
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
