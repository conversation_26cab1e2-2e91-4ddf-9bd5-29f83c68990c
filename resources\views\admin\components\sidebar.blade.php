<!-- Admin Sidebar - Matching Original Design -->
<div class="sidebar" style="position: fixed !important; top: 0 !important; left: 0 !important; width: 250px !important; height: 100vh !important; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; z-index: 1000 !important; overflow-y: auto !important; display: block !important;">
    <div class="sidebar-header" style="padding: 20px; text-align: center; border-bottom: 1px solid rgba(255,255,255,0.1);">
        <div style="color: white;">
            <i class="fas fa-crown fa-2x mb-2" style="color: #ffd700;"></i>
            <h4 style="margin: 0; font-weight: 700; color: white;">Admin Panel</h4>
            <small style="opacity: 0.8;">Shahid Play Management</small>
        </div>
    </div>
    
    <div class="sidebar-menu" style="padding: 20px 0;">
        <ul style="list-style: none; padding: 0; margin: 0;">
            <!-- Dashboard -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.dashboard') }}" 
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.dashboard') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.dashboard') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.dashboard') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.dashboard') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-tachometer-alt" style="width: 20px; margin-right: 12px;"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            
            @if(auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('view users'))
            <!-- Users -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.users.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.users.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.users.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.users.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.users.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-users" style="width: 20px; margin-right: 12px;"></i>
                    <span>Users</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('manage proxies'))
            <!-- Proxy Management -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.proxy.management') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.proxy.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.proxy.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.proxy.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.proxy.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-server" style="width: 20px; margin-right: 12px;"></i>
                    <span>Proxy Management</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user() && auth('admin')->user()->canManageAdmins())
            <!-- Admin Management -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.admin-management.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.admin-management.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.admin-management.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.admin-management.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.admin-management.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-users-cog" style="width: 20px; margin-right: 12px;"></i>
                    <span>Admin Management</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user() && (auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('view roles')))
            <!-- Role Management -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.roles.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.roles.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.roles.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.roles.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.roles.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-user-tag" style="width: 20px; margin-right: 12px;"></i>
                    <span>Roles</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('view permissions'))
            <!-- Permission Management -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.permissions.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.permissions.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.permissions.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.permissions.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.permissions.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-key" style="width: 20px; margin-right: 12px;"></i>
                    <span>Permissions</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user() && (auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('view subscriptions')))
            <!-- Subscription Plans -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.subscription-plans.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.subscription-plans.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.subscription-plans.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.subscription-plans.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.subscription-plans.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-tags" style="width: 20px; margin-right: 12px;"></i>
                    <span>Subscription Plans</span>
                </a>
            </li>

            <!-- User Subscriptions -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.user-subscriptions.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.user-subscriptions.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.user-subscriptions.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.user-subscriptions.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.user-subscriptions.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-credit-card" style="width: 20px; margin-right: 12px;"></i>
                    <span>User Subscriptions</span>
                </a>
            </li>
            @endif
            
            @if(auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('view reports'))
            <!-- Reports -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.reports.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.reports.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.reports.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.reports.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.reports.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-chart-bar" style="width: 20px; margin-right: 12px;"></i>
                    <span>Reports</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user() && (auth('admin')->user()->isSuperAdmin() || auth('admin')->user()->hasPermission('view settings')))
            <!-- Settings -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.settings.index') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.settings.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.settings.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.settings.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.settings.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-cog" style="width: 20px; margin-right: 12px;"></i>
                    <span>Settings</span>
                </a>
            </li>
            @endif

            @if(auth('admin')->user()->isSuperAdmin())
            <!-- Cache Management -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('admin.cache.management') }}"
                   style="display: flex; align-items: center; padding: 12px 20px; color: {{ request()->routeIs('admin.cache.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.cache.*') ? 'background: rgba(255,255,255,0.1); border-left: 3px solid #ffd700;' : '' }}"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='{{ request()->routeIs('admin.cache.*') ? 'rgba(255,255,255,0.1)' : 'transparent' }}'; this.style.color='{{ request()->routeIs('admin.cache.*') ? '#fff' : 'rgba(255,255,255,0.8)' }}';">
                    <i class="fas fa-broom" style="width: 20px; margin-right: 12px;"></i>
                    <span>Cache Management</span>
                </a>
            </li>
            @endif
            
            <!-- Divider -->
            <li style="margin: 20px 0;">
                <hr style="border-color: rgba(255,255,255,0.2); margin: 0 20px;">
            </li>
            
            <!-- User Dashboard Link -->
            <li style="margin-bottom: 5px;">
                <a href="{{ route('dashboard') }}" 
                   style="display: flex; align-items: center; padding: 12px 20px; color: rgba(255,255,255,0.8); text-decoration: none; transition: all 0.3s ease;"
                   onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                   onmouseout="this.style.background='transparent'; this.style.color='rgba(255,255,255,0.8)';">
                    <i class="fas fa-external-link-alt" style="width: 20px; margin-right: 12px;"></i>
                    <span>User Dashboard</span>
                </a>
            </li>
            
            <!-- Logout -->
            <li style="margin-bottom: 5px;">
                <form action="{{ route('admin.logout') }}" method="POST" style="margin: 0;">
                    @csrf
                    <button type="submit" 
                            style="display: flex; align-items: center; padding: 12px 20px; color: rgba(255,255,255,0.8); background: none; border: none; width: 100%; text-align: left; cursor: pointer; transition: all 0.3s ease;"
                            onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.color='#fff';"
                            onmouseout="this.style.background='transparent'; this.style.color='rgba(255,255,255,0.8)';">
                        <i class="fas fa-sign-out-alt" style="width: 20px; margin-right: 12px;"></i>
                        <span>Logout</span>
                    </button>
                </form>
            </li>
        </ul>
    </div>
    
    <!-- User Info at Bottom -->
    <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 15px 20px; background: rgba(0,0,0,0.2); border-top: 1px solid rgba(255,255,255,0.1);">
        <div style="color: white; text-align: center;">
            <i class="fas fa-user-circle" style="font-size: 1.2rem; margin-right: 8px;"></i>
            <small>{{ Auth::guard('admin')->user()->name }}</small>
            <br>
            <small style="opacity: 0.7;">Administrator</small>
        </div>
    </div>
</div>

<!-- Overlay for mobile -->
<div class="sidebar-overlay d-md-none" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999;" onclick="toggleMobileSidebar()"></div>

<script>
function toggleMobileSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (sidebar.style.transform === 'translateX(-100%)') {
        sidebar.style.transform = 'translateX(0)';
        overlay.style.display = 'block';
    } else {
        sidebar.style.transform = 'translateX(-100%)';
        overlay.style.display = 'none';
    }
}

// Hide sidebar on mobile by default
if (window.innerWidth <= 767) {
    document.querySelector('.sidebar').style.transform = 'translateX(-100%)';
}

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (window.innerWidth > 767) {
        sidebar.style.transform = 'translateX(0)';
        overlay.style.display = 'none';
    } else {
        sidebar.style.transform = 'translateX(-100%)';
        overlay.style.display = 'none';
    }
});
</script>
