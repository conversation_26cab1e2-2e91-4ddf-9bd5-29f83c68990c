@extends('admin.layouts.app')

@section('title', 'Subscription Plans')
@section('page-title', 'Subscription Plans')

@section('breadcrumb')
<li class="breadcrumb-item">
    <a href="{{ route('admin.subscription-plans.index') }}" style="color: #667eea; text-decoration: none;">Plans</a>
</li>
<li class="breadcrumb-item active">Management</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Create and manage subscription plans with different pricing and features.</p>
    </div>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPlanModal">
        <i class="fas fa-plus me-2"></i>Create New Plan
    </button>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchInput" placeholder="Search plans...">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="sortBy">
                    <option value="sort_order">Sort Order</option>
                    <option value="name">Name</option>
                    <option value="price">Price</option>
                    <option value="duration_days">Duration</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-secondary" id="refreshBtn" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-outline-warning" id="clearFilters" title="Clear Filters">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="btn btn-outline-secondary" id="exportBtn" title="Export">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Plans Grid -->
<div class="row" id="plansGrid">
    <div class="col-12 text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

<!-- Pagination -->
<nav aria-label="Plans pagination" class="mt-4">
    <ul class="pagination justify-content-center" id="pagination">
        <!-- Pagination will be loaded here -->
    </ul>
</nav>

<!-- Create Plan Modal -->
<div class="modal fade" id="createPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Subscription Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createPlanForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="planName" class="form-label">Plan Name *</label>
                                <input type="text" class="form-control" id="planName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="planPrice" class="form-label">Price *</label>
                                <input type="number" step="0.01" class="form-control" id="planPrice" name="price" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="planCurrency" class="form-label">Currency *</label>
                                <select class="form-select" id="planCurrency" name="currency" required>
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="GBP">GBP</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="planDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="planDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="planDuration" class="form-label">Duration (Days) *</label>
                                <input type="number" class="form-control" id="planDuration" name="duration_days" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="planMaxDevices" class="form-label">Max Devices *</label>
                                <input type="number" class="form-control" id="planMaxDevices" name="max_devices" value="1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="planSortOrder" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="planSortOrder" name="sort_order" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Allowed Applications</label>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="appAll" name="allowed_applications[]" value="ALL" checked>
                                    <label class="form-check-label" for="appAll">All Applications</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="appShahid" name="allowed_applications[]" value="SHAHID">
                                    <label class="form-check-label" for="appShahid">Shahid</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="appNetflix" name="allowed_applications[]" value="NETFLIX">
                                    <label class="form-check-label" for="appNetflix">Netflix</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="planFeatures" class="form-label">Features (one per line)</label>
                        <textarea class="form-control" id="planFeatures" name="features_text" rows="4" 
                                  placeholder="HD Quality Streaming&#10;Multiple Devices&#10;Download for Offline"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="planActive" name="is_active" checked>
                        <label class="form-check-label" for="planActive">
                            Active Plan
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Plan Modal -->
<div class="modal fade" id="editPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Subscription Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPlanForm">
                <div class="modal-body">
                    <input type="hidden" id="editPlanId" name="plan_id">
                    <!-- Same form fields as create modal but with edit prefix -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPlanName" class="form-label">Plan Name *</label>
                                <input type="text" class="form-control" id="editPlanName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="editPlanPrice" class="form-label">Price *</label>
                                <input type="number" step="0.01" class="form-control" id="editPlanPrice" name="price" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="editPlanCurrency" class="form-label">Currency *</label>
                                <select class="form-select" id="editPlanCurrency" name="currency" required>
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="GBP">GBP</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editPlanDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editPlanDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editPlanDuration" class="form-label">Duration (Days) *</label>
                                <input type="number" class="form-control" id="editPlanDuration" name="duration_days" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editPlanMaxDevices" class="form-label">Max Devices *</label>
                                <input type="number" class="form-control" id="editPlanMaxDevices" name="max_devices" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editPlanSortOrder" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="editPlanSortOrder" name="sort_order">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Allowed Applications</label>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editAppAll" name="allowed_applications[]" value="ALL">
                                    <label class="form-check-label" for="editAppAll">All Applications</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editAppShahid" name="allowed_applications[]" value="SHAHID">
                                    <label class="form-check-label" for="editAppShahid">Shahid</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editAppNetflix" name="allowed_applications[]" value="NETFLIX">
                                    <label class="form-check-label" for="editAppNetflix">Netflix</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editPlanFeatures" class="form-label">Features (one per line)</label>
                        <textarea class="form-control" id="editPlanFeatures" name="features_text" rows="4"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editPlanActive" name="is_active">
                        <label class="form-check-label" for="editPlanActive">
                            Active Plan
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentPage = 1;
let currentFilters = {};

$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    loadPlans();
    
    // Search and filter events
    $('#searchInput').on('input', debounce(function() {
        currentPage = 1;
        loadPlans();
    }, 500));
    
    $('#statusFilter, #sortBy').on('change', function() {
        currentPage = 1;
        loadPlans();
    });
    
    $('#refreshBtn').on('click', function() {
        loadPlans();
    });

    // Clear filters button
    $('#clearFilters').on('click', function() {
        $('#searchInput').val('');
        $('#statusFilter').val('');
        $('#sortBy').val('sort_order');
        currentPage = 1;
        loadPlans();
    });
    
    // Form submissions
    $('#createPlanForm').on('submit', function(e) {
        e.preventDefault();
        createPlan();
    });
    
    $('#editPlanForm').on('submit', function(e) {
        e.preventDefault();
        updatePlan();
    });
});

function loadPlans() {
    const filters = {
        page: currentPage
    };

    // Only add non-empty filters
    const search = $('#searchInput').val();
    if (search && search.trim() !== '') {
        filters.search = search.trim();
    }

    const status = $('#statusFilter').val();
    if (status && status !== '') {
        filters.status = status;
    }

    const sortBy = $('#sortBy').val();
    if (sortBy && sortBy !== '') {
        filters.sort_by = sortBy;
    }

    currentFilters = filters;

    // Show loading state
    $('#plansGrid').html(`
        <div class="col-12 text-center py-4">
            <div class="loading-spinner me-2"></div>
            Loading plans...
        </div>
    `);

    $.get('/admin/ajax/subscription-plans', filters)
        .done(function(response) {
            if (response.success) {
                renderPlansGrid(response.data.data);
                renderPagination(response.data);
            }
        })
        .fail(function(xhr) {
            const errorMessage = xhr.responseJSON?.message || 'Failed to load plans';
            $('#plansGrid').html(`
                <div class="col-12 text-center">
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        <h5 class="text-danger">Error Loading Data</h5>
                        <p class="text-muted">${errorMessage}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadPlans()">
                            <i class="fas fa-refresh me-2"></i>Try Again
                        </button>
                    </div>
                </div>
            `);
        });
}

function renderPlansGrid(plans) {
    let html = '';
    
    if (plans.length === 0) {
        html = '<div class="col-12 text-center text-muted">No subscription plans found</div>';
    } else {
        plans.forEach(plan => {
            const statusBadge = plan.is_active ? 
                '<span class="badge bg-success">Active</span>' : 
                '<span class="badge bg-secondary">Inactive</span>';
            
            const features = plan.features ? plan.features.map(f => `<li>${f}</li>`).join('') : '';
            const applications = plan.allowed_applications ? plan.allowed_applications.join(', ') : 'All';
            
            html += `
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">${plan.name}</h5>
                            ${statusBadge}
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="h2 text-primary">${plan.currency} ${plan.price}</div>
                                <div class="text-muted">${plan.duration_text}</div>
                            </div>
                            
                            ${plan.description ? `<p class="text-muted">${plan.description}</p>` : ''}
                            
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Max ${plan.max_devices} device(s)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Apps: ${applications}</li>
                                ${features}
                            </ul>
                            
                            <div class="small text-muted">
                                <div>Subscribers: ${plan.active_user_subscriptions_count || 0}</div>
                                <div>Created: ${new Date(plan.created_at).toLocaleDateString()}</div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <button class="btn btn-outline-primary btn-sm" onclick="editPlan(${plan.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-outline-${plan.is_active ? 'warning' : 'success'} btn-sm" 
                                        onclick="togglePlanStatus(${plan.id})">
                                    <i class="fas fa-${plan.is_active ? 'pause' : 'play'}"></i> 
                                    ${plan.is_active ? 'Deactivate' : 'Activate'}
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deletePlan(${plan.id})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#plansGrid').html(html);
}

function renderPagination(data) {
    let html = '';
    
    if (data.last_page > 1) {
        // Previous button
        html += `<li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.current_page - 1})">Previous</a>
                 </li>`;
        
        // Page numbers
        for (let i = 1; i <= data.last_page; i++) {
            if (i === data.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else if (i === 1 || i === data.last_page || (i >= data.current_page - 2 && i <= data.current_page + 2)) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
            } else if (i === data.current_page - 3 || i === data.current_page + 3) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        // Next button
        html += `<li class="page-item ${data.current_page === data.last_page ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.current_page + 1})">Next</a>
                 </li>`;
    }
    
    $('#pagination').html(html);
}

function changePage(page) {
    currentPage = page;
    loadPlans();
}

function createPlan() {
    const formData = new FormData($('#createPlanForm')[0]);

    // Convert features text to array
    const featuresText = $('#planFeatures').val();
    if (featuresText) {
        const features = featuresText.split('\n').filter(f => f.trim());
        formData.delete('features_text');
        features.forEach((feature, index) => {
            formData.append(`features[${index}]`, feature.trim());
        });
    }

    // Convert is_active checkbox to boolean
    const isActive = $('#planActive').is(':checked');
    formData.set('is_active', isActive ? '1' : '0');

    // Convert allowed_applications checkboxes to array
    const allowedApps = [];
    $('#createPlanForm input[name="allowed_applications[]"]:checked').each(function() {
        allowedApps.push($(this).val());
    });
    formData.delete('allowed_applications[]');
    allowedApps.forEach((app, index) => {
        formData.append(`allowed_applications[${index}]`, app);
    });

    // Add CSRF token
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

    // Show loading state
    const submitBtn = $('#createPlanForm button[type="submit"]');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Creating...');

    $.ajax({
        url: '/admin/ajax/subscription-plans',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#createPlanModal').modal('hide');
                $('#createPlanForm')[0].reset();
                loadPlans();
                showAlert('success', response.message || 'Subscription plan created successfully!');
            } else {
                showAlert('error', response.message || 'Failed to create subscription plan');
            }
        },
        error: function(xhr) {
            console.error('Error creating plan:', xhr);

            let errorMessage = 'Failed to create subscription plan';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                if (xhr.responseJSON.errors) {
                    showFormErrors('createPlanForm', xhr.responseJSON.errors);
                    return;
                }
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred. Please try again.';
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to create subscription plans.';
            } else if (xhr.status === 422) {
                errorMessage = 'Please check your input data.';
            }

            showAlert('error', errorMessage);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function editPlan(planId) {
    $.get(`/admin/ajax/subscription-plans/${planId}`)
        .done(function(response) {
            if (response.success) {
                const plan = response.data;
                $('#editPlanId').val(plan.id);
                $('#editPlanName').val(plan.name);
                $('#editPlanDescription').val(plan.description);
                $('#editPlanPrice').val(plan.price);
                $('#editPlanCurrency').val(plan.currency);
                $('#editPlanDuration').val(plan.duration_days);
                $('#editPlanMaxDevices').val(plan.max_devices);
                $('#editPlanSortOrder').val(plan.sort_order);
                $('#editPlanActive').prop('checked', plan.is_active);
                
                // Set allowed applications
                $('#editPlanForm input[name="allowed_applications[]"]').prop('checked', false);
                if (plan.allowed_applications) {
                    plan.allowed_applications.forEach(app => {
                        $(`#editPlanForm input[value="${app}"]`).prop('checked', true);
                    });
                }
                
                // Set features
                if (plan.features) {
                    $('#editPlanFeatures').val(plan.features.join('\n'));
                }
                
                $('#editPlanModal').modal('show');
            }
        })
        .fail(function() {
            showAlert('error', 'Failed to load plan data');
        });
}

function updatePlan() {
    const planId = $('#editPlanId').val();
    const formData = new FormData($('#editPlanForm')[0]);

    // Convert features text to array
    const featuresText = $('#editPlanFeatures').val();
    if (featuresText) {
        const features = featuresText.split('\n').filter(f => f.trim());
        formData.delete('features_text');
        features.forEach((feature, index) => {
            formData.append(`features[${index}]`, feature.trim());
        });
    }

    // Convert is_active checkbox to boolean
    const isActive = $('#editPlanActive').is(':checked');
    formData.set('is_active', isActive ? '1' : '0');

    // Convert allowed_applications checkboxes to array
    const allowedApps = [];
    $('#editPlanForm input[name="allowed_applications[]"]:checked').each(function() {
        allowedApps.push($(this).val());
    });
    formData.delete('allowed_applications[]');
    allowedApps.forEach((app, index) => {
        formData.append(`allowed_applications[${index}]`, app);
    });
    
    $.ajax({
        url: `/admin/ajax/subscription-plans/${planId}`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-HTTP-Method-Override': 'PUT'
        },
        headers: {
            'X-HTTP-Method-Override': 'PUT',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#editPlanModal').modal('hide');
                loadPlans();
                showAlert('success', response.message || 'Subscription plan updated successfully!');
            } else {
                showAlert('error', response.message || 'Failed to update subscription plan');
            }
        },
        error: function(xhr) {
            console.error('Error updating plan:', xhr);

            let errorMessage = 'Failed to update subscription plan';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                if (xhr.responseJSON.errors) {
                    showFormErrors('editPlanForm', xhr.responseJSON.errors);
                    return;
                }
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred. Please try again.';
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to update subscription plans.';
            } else if (xhr.status === 422) {
                errorMessage = 'Please check your input data.';
            }

            showAlert('error', errorMessage);
        }
    });
}

function togglePlanStatus(planId) {
    if (confirm('Are you sure you want to change this plan\'s status?')) {
        $.post(`/admin/ajax/subscription-plans/${planId}/toggle-status`)
            .done(function(response) {
                if (response.success) {
                    loadPlans();
                    showAlert('success', response.message);
                }
            })
            .fail(function() {
                showAlert('error', 'Failed to update plan status');
            });
    }
}

function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
        $.ajax({
            url: `/admin/ajax/subscription-plans/${planId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    loadPlans();
                    showAlert('success', 'Subscription plan deleted successfully!');
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to delete plan';
                showAlert('error', message);
            }
        });
    }
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.main-content').prepend(alert);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

function showFormErrors(formId, errors) {
    // Clear previous errors
    $(`#${formId} .is-invalid`).removeClass('is-invalid');
    $(`#${formId} .invalid-feedback`).remove();
    
    // Show new errors
    Object.keys(errors).forEach(field => {
        const input = $(`#${formId} [name="${field}"]`);
        input.addClass('is-invalid');
        input.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
@endsection

@push('styles')
<style>
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.plan-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.btn-group-sm > .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 4px;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
    border-radius: 0 0 12px 12px;
}

@media (max-width: 768px) {
    .plan-card {
        margin-bottom: 1rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-flex.justify-content-between .btn {
        align-self: stretch;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state i {
        font-size: 2rem;
    }
}
</style>
@endpush
