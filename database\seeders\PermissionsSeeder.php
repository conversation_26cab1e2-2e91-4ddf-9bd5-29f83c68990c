<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        $this->command->info('Starting permissions seeder...');

        // Define all permissions
        $permissions = [
            // User Management
            'view users',
            'create users',
            'edit users',
            'update users',
            'delete users',
            'suspend users',
            'reset user passwords',
            'manage user roles',

            // Admin Management
            'view admins',
            'create admins',
            'edit admins',
            'delete admins',
            'manage admins',
            'manage admin permissions',

            // Role & Permission Management
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
            'manage roles',
            'view permissions',
            'manage permissions',
            'manage role permissions',

            // Subscription Management
            'view subscriptions',
            'create subscriptions',
            'edit subscriptions',
            'update subscriptions',
            'delete subscriptions',
            'manage subscription plans',

            // Content Management
            'view content',
            'create content',
            'edit content',
            'delete content',
            'manage content',

            // Reports & Analytics
            'view reports',
            'view analytics',
            'view activity logs',

            // System Management
            'view admin panel',
            'manage settings',
            'manage proxies',
            'view system info',

            // Special Permissions
            'super admin access',
        ];

        // Create permissions for both guards
        $this->command->info('Creating ' . count($permissions) . ' permissions for both guards...');

        foreach ($permissions as $permission) {
            // Create for web guard (User model)
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);

            // Create for admin guard (Admin model)
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'admin'
            ]);
        }

        $webCount = Permission::where('guard_name', 'web')->count();
        $adminCount = Permission::where('guard_name', 'admin')->count();

        $this->command->info('Permissions created successfully!');
        $this->command->info("Web guard permissions: {$webCount}");
        $this->command->info("Admin guard permissions: {$adminCount}");
    }
}
