<?php

namespace App\Services;

/**
 * ShahidDRM - DRM service for Shahid Movies only
 *
 * This service handles DRM operations for movies.
 * For TV series episodes, use ShahidSeriesDRM instead.
 */

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\Setting;
use App\Helpers\SettingsHelper;
use App\Services\ProxyService;

/**
 * Shahid DRM Service - Laravel Port of Python shahid_drm.py
 * This service handles DRM operations for Shahid content
 */
class ShahidDRM
{
    private $deviceFile = 'device.wvd';
    private $drmEndpoint = 'https://drm.shahid.net';
    protected $proxyService;

    public function __construct(ProxyService $proxyService = null)
    {
        // Initialize DRM service
        $this->proxyService = $proxyService ?: app(ProxyService::class);
    }

    /**
     * Check if device file exists
     */
    public function hasDeviceFile()
    {
        return Storage::exists($this->deviceFile);
    }

    /**
     * Get DRM license for content
     */
    public function getLicense($contentId, $pssh = null)
    {
        try {
            if (!$this->hasDeviceFile()) {
                return [
                    'success' => false,
                    'error' => 'Device file not found'
                ];
            }

            // This is a simplified version
            // In a real implementation, you would need to:
            // 1. Load the device file
            // 2. Create a CDM session
            // 3. Generate license request
            // 4. Send to DRM server
            // 5. Process response

            Log::info('DRM license requested for content: ' . $contentId);

            return [
                'success' => true,
                'license' => 'mock_license_data',
                'keys' => []
            ];

        } catch (\Exception $e) {
            Log::error('DRM license error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract decryption key using device.wvd file directly
     * Can accept either PSSH or content ID
     */
    public function extractKey($psshhOrContentId, $licenseUrl = null)
    {
        try {
            Log::info('=== STARTING KEY EXTRACTION PROCESS ===');

            // Check if input is content ID (numeric) or PSSH (base64)
            if (is_numeric($psshhOrContentId) || (strlen($psshhOrContentId) < 50 && !preg_match('/[A-Za-z0-9+\/=]/', $psshhOrContentId))) {
                // It's a content ID, extract complete data using API
                Log::info("🎬 Content ID detected: {$psshhOrContentId}");
                return $this->extractContentKeyUsingApi($psshhOrContentId);
            }

            // It's a PSSH, proceed with normal extraction
            $pssh = $psshhOrContentId;
            Log::info('PSSH length: ' . strlen($pssh));
            Log::info('PSSH preview: ' . substr($pssh, 0, 100) . '...');
            Log::info('License URL: ' . ($licenseUrl ?? 'Not provided'));

            if (!$this->hasDeviceFile()) {
                Log::error('STEP 1 FAILED: Device file not found');
                return [
                    'success' => false,
                    'error' => 'Device file not found'
                ];
            }
            Log::info('STEP 1 SUCCESS: Device file exists');

            if (!$pssh) {
                Log::error('STEP 2 FAILED: PSSH is required');
                return [
                    'success' => false,
                    'error' => 'PSSH is required'
                ];
            }
            Log::info('STEP 2 SUCCESS: PSSH provided');

            Log::info('STEP 3: Attempting direct device.wvd parsing...');
            // Try direct device.wvd parsing first (fastest method)
            $directResult = $this->extractKeyFromDeviceFile($pssh, $licenseUrl);

            if ($directResult['success']) {
                Log::info('STEP 3 SUCCESS: Direct parsing succeeded');
                Log::info('Direct result: ' . json_encode($directResult, JSON_PRETTY_PRINT));
                return $directResult;
            } else {
                Log::warning('STEP 3 FAILED: Direct parsing failed - ' . ($directResult['error'] ?? 'Unknown error'));
            }

            Log::info('STEP 4: Falling back to script-based extraction...');
            // Fallback to external script if direct parsing fails
            $scriptResult = $this->extractKeyWithScript($pssh, $licenseUrl);

            if ($scriptResult['success']) {
                Log::info('STEP 4 SUCCESS: Script extraction succeeded');
            } else {
                Log::error('STEP 4 FAILED: Script extraction failed - ' . ($scriptResult['error'] ?? 'Unknown error'));
            }

            Log::info('=== KEY EXTRACTION PROCESS COMPLETED ===');
            return $scriptResult;

        } catch (\Exception $e) {
            Log::error('CRITICAL ERROR in key extraction: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract key directly from device.wvd file (PHP implementation)
     */
    private function extractKeyFromDeviceFile($pssh, $licenseUrl = null)
    {
        try {
            Log::info('--- DIRECT DEVICE FILE EXTRACTION ---');

            $devicePath = storage_path('app/' . $this->deviceFile);
            Log::info('Device file path: ' . $devicePath);

            if (!file_exists($devicePath)) {
                Log::error('Device file does not exist at: ' . $devicePath);
                return [
                    'success' => false,
                    'error' => 'Device file not found at: ' . $devicePath
                ];
            }
            Log::info('✓ Device file exists');

            // Check file permissions
            if (!is_readable($devicePath)) {
                Log::error('Device file is not readable');
                return [
                    'success' => false,
                    'error' => 'Device file is not readable'
                ];
            }
            Log::info('✓ Device file is readable');

            // Read device.wvd file
            $deviceData = file_get_contents($devicePath);
            if ($deviceData === false) {
                Log::error('Failed to read device file content');
                return [
                    'success' => false,
                    'error' => 'Failed to read device file content'
                ];
            }

            $deviceSize = strlen($deviceData);
            Log::info('✓ Device file loaded successfully');
            Log::info('Device file size: ' . $deviceSize . ' bytes');
            Log::info('Device file first 50 bytes (hex): ' . bin2hex(substr($deviceData, 0, 50)));

            // Parse PSSH to extract KID
            Log::info('Attempting to extract KID from PSSH...');
            $kid = $this->extractKIDFromPSSH($pssh);

            if (!$kid) {
                Log::error('Failed to extract KID from PSSH');
                Log::info('PSSH base64 length: ' . strlen($pssh));
                Log::info('PSSH first 100 chars: ' . substr($pssh, 0, 100));

                return [
                    'success' => false,
                    'error' => 'Failed to extract KID from PSSH'
                ];
            }

            Log::info('✓ KID extracted successfully: ' . $kid);

            // Try to extract more info from device file
            Log::info('Parsing device file for additional info...');
            $deviceInfo = $this->parseDeviceFile($deviceData);

            if ($deviceInfo) {
                Log::info('✓ Device info extracted: ' . json_encode($deviceInfo));
            } else {
                Log::warning('Could not extract additional device info');
            }

            // For actual key extraction, we need to implement Widevine protocol
            // This is where the real key extraction would happen
            Log::info('Attempting actual key extraction...');

            $keyExtractionResult = $this->attemptKeyExtraction($deviceData, $kid, $pssh, $licenseUrl);

            // Extract key and correct KID from the extraction result
            $extractedKey = null;
            $correctKid = $kid; // Default to PSSH KID

            if ($keyExtractionResult && is_array($keyExtractionResult)) {
                $extractedKey = $keyExtractionResult['key'] ?? null;
                $correctKid = $keyExtractionResult['kid'] ?? $kid;
            } elseif ($keyExtractionResult && is_string($keyExtractionResult)) {
                $extractedKey = $keyExtractionResult;
                // Keep the original KID if only key is returned
            }

            $result = [
                'success' => true,
                'keys' => [
                    [
                        'kid' => $correctKid, // Use the correct KID from Python API
                        'key' => $extractedKey, // This will be null unless we implement full CDM
                        'type' => 'CONTENT'
                    ]
                ],
                'pssh' => $pssh,
                'device_info' => array_merge([
                    'file_size' => $deviceSize,
                    'available' => true,
                    'path' => $devicePath
                ], $deviceInfo ?? []),
                'message' => $extractedKey ? 'Key extracted successfully!' : 'KID extracted successfully. Key extraction requires full Widevine CDM implementation.',
                'extraction_method' => 'direct_php'
            ];

            Log::info('Direct extraction result: ' . json_encode($result, JSON_PRETTY_PRINT));
            return $result;

        } catch (\Exception $e) {
            Log::error('EXCEPTION in direct device file extraction: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());
            return [
                'success' => false,
                'error' => 'Direct extraction failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Attempt actual key extraction from device file
     */
    private function attemptKeyExtraction($deviceData, $kid, $pssh, $licenseUrl = null)
    {
        try {
            Log::info('--- ATTEMPTING ACTUAL KEY EXTRACTION ---');
            Log::info('KID for extraction: ' . $kid);
            Log::info('Device data size: ' . strlen($deviceData) . ' bytes');

            // This is where we would implement the actual Widevine key extraction
            // For now, this is a placeholder that shows the process

            Log::info('Step 1: Analyzing device file structure...');

            // Check if device file has the expected Widevine structure
            $deviceHex = bin2hex($deviceData);
            Log::info('Device file starts with: ' . substr($deviceHex, 0, 32));

            // Look for Widevine signatures
            $widevineSignatures = ['widevine', 'WIDEVINE', 'WVD'];
            $foundSignature = false;

            foreach ($widevineSignatures as $sig) {
                if (strpos($deviceData, $sig) !== false) {
                    Log::info('✓ Found Widevine signature: ' . $sig);
                    $foundSignature = true;
                    break;
                }
            }

            if (!$foundSignature) {
                Log::warning('No Widevine signature found in device file');
            }

            Log::info('Step 2: Checking for private key data...');

            // In a real implementation, we would:
            // 1. Parse the device file protobuf structure
            // 2. Extract the private key
            // 3. Use it to decrypt the license response
            // 4. Extract the content keys

            // For demonstration, let\'s show what we would need:
            Log::info('Required for key extraction:');
            Log::info('- Device private key (from device.wvd)');
            Log::info('- License server URL: ' . ($licenseUrl ?? 'Not provided'));
            Log::info('- PSSH data for license request');
            Log::info('- Widevine CDM implementation');

            // Now let's try actual license request using Python Widevine API
            if ($licenseUrl) {
                Log::info('Step 3: Using Python Widevine API...');

                // Try to use Python Widevine API with real pywidevine
                $extractedResult = $this->extractKeyUsingPythonApi($pssh, $licenseUrl, $kid);
                if ($extractedResult) {
                    if (is_array($extractedResult)) {
                        Log::info('✓ Key extracted using Python API: ' . $extractedResult['key']);
                        return $extractedResult; // Return array with key and kid
                    } else {
                        Log::info('✓ Key extracted using Python API: ' . $extractedResult);
                        return $extractedResult; // Return just the key (backward compatibility)
                    }
                }

                Log::error('Python API failed to extract key');
                return null;
            }

            Log::error('No license URL available');
            return null;

        } catch (\Exception $e) {
            Log::error('Key extraction attempt failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract key using Python Widevine API
     */
    private function extractKeyUsingPythonApi($pssh, $licenseUrl, $targetKid)
    {
        try {
            Log::info('--- DECRYPTION API APPROACH ---');
            Log::info('Calling Decryption API with real pywidevine');

            // Get decryption API URL from settings
            $baseApiUrl = SettingsHelper::getDecryptionApiUrl();
            $apiUrl = SettingsHelper::getDecryptionApiExtractKeyUrl();

            // Check if API is available
            if (!$this->isDecryptionApiAvailable($baseApiUrl)) {
                Log::error('Decryption API is not available at: ' . $baseApiUrl);
                return null;
            }

            // Prepare request data
            $requestData = [
                'pssh' => $pssh,
                'license_url' => $licenseUrl,
                'target_kid' => $targetKid
            ];

            Log::info('Sending request to Python API...');
            Log::info('API URL: ' . $apiUrl);
            Log::info('Request data: ' . json_encode($requestData, JSON_PRETTY_PRINT));

            // Send request to Python API
            $response = $this->proxyService->getHttpClient(['timeout' => 60])->post($apiUrl, $requestData);

            if ($response->successful()) {
                $result = $response->json();
                Log::info('Python API response: ' . json_encode($result, JSON_PRETTY_PRINT));

                if ($result && isset($result['success']) && $result['success']) {
                    if (isset($result['key'])) {
                        Log::info('✓ Key extracted successfully via Python API');
                        Log::info('  → Key ID: ' . ($result['kid'] ?? 'N/A'));
                        Log::info('  → Key: ' . $result['key']);
                        Log::info('  → Total keys found: ' . ($result['total_keys'] ?? 1));

                        // Return success format with key and KID
                        return [
                            'success' => true,
                            'key' => $result['key'],
                            'kid' => $result['kid'] ?? $targetKid
                        ];
                    } else {
                        Log::warning('Python API succeeded but no key returned');
                        return [
                            'success' => false,
                            'error' => 'Python API succeeded but no key returned'
                        ];
                    }
                } else {
                    $error = $result['error'] ?? 'Unknown error';
                    Log::error('Python API failed: ' . $error);
                    return [
                        'success' => false,
                        'error' => $error
                    ];
                }
            } else {
                $error = 'Python API request failed with status: ' . $response->status();
                Log::error($error);
                Log::error('Response body: ' . $response->body());
                return [
                    'success' => false,
                    'error' => $error
                ];
            }

        } catch (\Exception $e) {
            Log::error('Python API approach failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check if Decryption API is available
     */
    private function isDecryptionApiAvailable($baseApiUrl)
    {
        try {
            $healthUrl = SettingsHelper::getDecryptionApiHealthUrl();
            $response = $this->proxyService->getHttpClient(['timeout' => 5])->get($healthUrl);

            if ($response->successful()) {
                $health = $response->json();

                // Check if it's the expected API format
                if (isset($health['status'])) {
                    $available = $health['status'] === 'healthy';

                    // Check for pywidevine availability if the field exists
                    if (isset($health['pywidevine_available'])) {
                        $available = $available && $health['pywidevine_available'] === true;
                    }

                    if ($available) {
                        Log::info('✓ Decryption API is healthy and ready at: ' . $baseApiUrl);
                    } else {
                        Log::warning('Decryption API is running but not fully available');
                    }

                    return $available;
                } else {
                    // If no status field, assume it's available if we get a 200 response
                    Log::info('✓ Decryption API responded successfully at: ' . $baseApiUrl);
                    return true;
                }
            } else {
                Log::warning('Decryption API health check failed: ' . $response->status() . ' at: ' . $baseApiUrl);
                return false;
            }
        } catch (\Exception $e) {
            Log::warning('Decryption API not available at ' . $baseApiUrl . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get decryption API URL from settings
     */
    private function getDecryptionApiUrl()
    {
        $url = Setting::getValue('decryption_api_url');
        if (!$url) {
            Log::warning('Decryption API URL not configured in settings');
            return null;
        }

        return rtrim($url, '/');
    }

    /**
     * Parse device file to extract information
     */
    private function parseDeviceFile($deviceData)
    {
        try {
            Log::info('--- PARSING DEVICE FILE ---');

            $info = [
                'size' => strlen($deviceData),
                'type' => 'Widevine Device File'
            ];

            if (strpos($deviceData, 'L1') !== false) {
                $info['security_level'] = 'L1';
                Log::info('✓ Security level: L1');
            } elseif (strpos($deviceData, 'L3') !== false) {
                $info['security_level'] = 'L3';
                Log::info('✓ Security level: L3');
            } else {
                Log::info('Security level not detected');
            }

            // Check for other common patterns
            $patterns = ['client_id', 'private_key', 'certificate'];
            foreach ($patterns as $pattern) {
                if (strpos($deviceData, $pattern) !== false) {
                    $info['contains_' . $pattern] = true;
                    Log::info('✓ Found pattern: ' . $pattern);
                }
            }

            Log::info('Device file analysis complete: ' . json_encode($info));
            return $info;

        } catch (\Exception $e) {
            Log::error('Device file parsing error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get service certificate from DRM server (like original code)
     */
    private function getServiceCertificate($licenseUrl)
    {
        try {
            Log::info('  → Getting service certificate from DRM server...');

            // Send certificate request (like original: requests.post(license_url, b'\x08\x04'))
            $ch = curl_init();

            curl_setopt_array($ch, [
                CURLOPT_URL => $licenseUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => "\x08\x04", // Certificate request payload
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/octet-stream',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'language: EN',
                    'accept-language: en',
                    'Origin: https://shahid.mbc.net',
                    'Referer: https://shahid.mbc.net/',
                    'Content-Length: 2'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3
            ]);

            $responseBody = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error('  → Certificate request cURL error: ' . $error);
                return null;
            }

            if ($httpCode >= 200 && $httpCode < 300) {
                Log::info('  → Certificate received successfully, size: ' . strlen($responseBody) . ' bytes');
                Log::info('  → Certificate preview: ' . bin2hex(substr($responseBody, 0, 50)));

                // Encode certificate as base64 (like original: base64.b64encode(cert))
                $certificateBase64 = base64_encode($responseBody);
                Log::info('  → Certificate base64 encoded, length: ' . strlen($certificateBase64) . ' chars');

                return $certificateBase64;
            } else {
                Log::error('  → Certificate request failed with status: ' . $httpCode);
                Log::error('  → Response body: ' . substr($responseBody, 0, 500));
                return null;
            }

        } catch (\Exception $e) {
            Log::error('  → Certificate request exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create license request from PSSH with certificate
     */
    private function createLicenseRequest($pssh, $deviceData, $certificate = null)
    {
        try {
            Log::info('  → Creating Widevine license request...');

            // Decode PSSH
            $psshBinary = base64_decode($pssh);
            if (!$psshBinary) {
                Log::error('  → Failed to decode PSSH');
                return null;
            }

            Log::info('  → PSSH binary size: ' . strlen($psshBinary) . ' bytes');
            Log::info('  → PSSH hex: ' . bin2hex($psshBinary));

            if ($certificate) {
                Log::info('  → Using service certificate for license request');
                Log::info('  → Certificate length: ' . strlen($certificate) . ' chars');
            } else {
                Log::warning('  → No certificate provided - license request may fail');
            }

            // Create proper Widevine license challenge with certificate
            // Based on Widevine protobuf structure

            // License request protobuf structure:
            // message LicenseRequest {
            //   optional Type type = 1;
            //   optional bytes msg = 2;
            // }

            // Create license challenge protobuf
            $licenseChallenge = $this->createLicenseChallenge($psshBinary, $deviceData, $certificate);

            if (!$licenseChallenge) {
                Log::error('  → Failed to create license challenge');
                return null;
            }

            Log::info('  → License challenge created, size: ' . strlen($licenseChallenge) . ' bytes');
            Log::info('  → License challenge hex: ' . bin2hex($licenseChallenge));

            // Return raw binary data (like original Python code)
            // The original pywidevine sends challenge as raw binary, not base64
            Log::info('  → Returning raw binary license challenge for transmission');

            return $licenseChallenge;

        } catch (\Exception $e) {
            Log::error('  → License request creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create Widevine license challenge with certificate
     */
    private function createLicenseChallenge($psshBinary, $deviceData, $certificate = null)
    {
        try {
            Log::info('    → Building license challenge protobuf...');
            Log::info('    → Input PSSH size: ' . strlen($psshBinary) . ' bytes');

            // Create proper Widevine license request message
            $licenseRequestMsg = $this->createLicenseRequestMessage($psshBinary, $certificate);

            if (!$licenseRequestMsg) {
                Log::error('    → Failed to create license request message');
                return null;
            }

            // The license request message IS the challenge for Widevine
            // No additional wrapper needed - this is the SignedLicenseRequest
            Log::info('    → License challenge (SignedLicenseRequest) created');
            Log::info('    → Challenge size: ' . strlen($licenseRequestMsg) . ' bytes');
            Log::info('    → Challenge hex preview: ' . bin2hex(substr($licenseRequestMsg, 0, 50)));

            return $licenseRequestMsg;

        } catch (\Exception $e) {
            Log::error('    → License challenge creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create license request message with proper Widevine structure
     */
    private function createLicenseRequestMessage($psshBinary, $certificate = null)
    {
        try {
            Log::info('      → Creating proper Widevine license request message...');

            // Based on Widevine protobuf: SignedLicenseRequest
            // This needs to match the exact structure expected by Widevine CDM

            // Create the inner LicenseRequest message first
            $licenseRequestMsg = $this->createInnerLicenseRequest($psshBinary);

            // Create SignedLicenseRequest wrapper
            $signedRequest = '';

            // Field 1: Type = LICENSE_REQUEST (1)
            $signedRequest .= "\x08\x01"; // field 1, wire type 0, value 1

            // Field 2: Msg (the inner LicenseRequest)
            $signedRequest .= "\x12"; // field 2, wire type 2 (length-delimited)
            $signedRequest .= $this->encodeVarint(strlen($licenseRequestMsg));
            $signedRequest .= $licenseRequestMsg;

            // Field 3: Signature (required for Widevine)
            // For now, create a dummy signature - real implementation needs device private key
            $dummySignature = str_repeat("\x00", 256); // 256-byte dummy signature
            $signedRequest .= "\x1a"; // field 3, wire type 2
            $signedRequest .= $this->encodeVarint(strlen($dummySignature));
            $signedRequest .= $dummySignature;

            Log::info('      → Signed license request size: ' . strlen($signedRequest) . ' bytes');
            Log::info('      → Request hex preview: ' . bin2hex(substr($signedRequest, 0, 50)));

            return $signedRequest;

        } catch (\Exception $e) {
            Log::error('      → License request message creation failed: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Create inner LicenseRequest message
     */
    private function createInnerLicenseRequest($psshBinary)
    {
        try {
            Log::info('        → Creating inner LicenseRequest...');

            $message = '';

            // Field 1: ContentId
            $contentId = $this->createContentIdentification($psshBinary);
            $message .= "\x0a"; // field 1, wire type 2
            $message .= $this->encodeVarint(strlen($contentId));
            $message .= $contentId;

            // Field 2: Type = STREAMING (1)
            $message .= "\x10\x01"; // field 2, wire type 0, value 1

            // Field 3: RequestTime
            $timestamp = time();
            $message .= "\x18"; // field 3, wire type 0
            $message .= $this->encodeVarint($timestamp);

            Log::info('        → Inner LicenseRequest size: ' . strlen($message) . ' bytes');

            return $message;

        } catch (\Exception $e) {
            Log::error('        → Inner LicenseRequest creation failed: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Create ContentIdentification with CENC structure
     */
    private function createContentIdentification($psshBinary)
    {
        try {
            Log::info('          → Creating ContentIdentification...');

            $contentId = '';

            // Field 1: CENC
            $cenc = $this->createCENCIdentification($psshBinary);
            $contentId .= "\x0a"; // field 1, wire type 2
            $contentId .= $this->encodeVarint(strlen($cenc));
            $contentId .= $cenc;

            Log::info('          → ContentIdentification size: ' . strlen($contentId) . ' bytes');

            return $contentId;

        } catch (\Exception $e) {
            Log::error('          → ContentIdentification creation failed: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Create CENC identification with PSSH
     */
    private function createCENCIdentification($psshBinary)
    {
        try {
            Log::info('            → Creating CENC identification...');

            $cenc = '';

            // Field 1: Pssh (WidevineCencHeader)
            // For now, use the PSSH data directly
            $cenc .= "\x0a"; // field 1, wire type 2
            $cenc .= $this->encodeVarint(strlen($psshBinary));
            $cenc .= $psshBinary;

            // Field 2: LicenseType = STREAMING (1)
            $cenc .= "\x10\x01"; // field 2, wire type 0, value 1

            Log::info('            → CENC identification size: ' . strlen($cenc) . ' bytes');

            return $cenc;

        } catch (\Exception $e) {
            Log::error('            → CENC identification creation failed: ' . $e->getMessage());
            return '';
        }
    }



    /**
     * Encode varint for protobuf
     */
    private function encodeVarint($value)
    {
        $result = '';
        while ($value >= 0x80) {
            $result .= chr(($value & 0x7F) | 0x80);
            $value >>= 7;
        }
        $result .= chr($value & 0x7F);
        return $result;
    }

    /**
     * Send license request to DRM server
     */
    private function sendLicenseRequest($licenseUrl, $licenseRequest)
    {
        try {
            Log::info('  → Sending POST request to: ' . substr($licenseUrl, 0, 100) . '...');
            Log::info('  → License request binary size: ' . strlen($licenseRequest) . ' bytes');
            Log::info('  → Using Shahid-specific headers (Saudi IP, Arabic language)');

            // Use cURL directly to send raw binary data (like original Python code)
            $ch = curl_init();

            curl_setopt_array($ch, [
                CURLOPT_URL => $licenseUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $licenseRequest, // Raw binary data
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/octet-stream',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'language: EN',
                    'accept-language: en',
                    'Accept: */*',
                    'Origin: https://shahid.mbc.net',
                    'Referer: https://shahid.mbc.net/',
                    'Content-Length: ' . strlen($licenseRequest)
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3
            ]);

            $responseBody = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error('  → cURL error: ' . $error);
                return null;
            }

            Log::info('  → Response status: ' . $httpCode);

            if ($httpCode >= 200 && $httpCode < 300) {
                Log::info('  → License request successful, response size: ' . strlen($responseBody) . ' bytes');
                Log::info('  → Response preview: ' . bin2hex(substr($responseBody, 0, 50)));

                return $responseBody;
            } else {
                Log::error('  → License request failed with status: ' . $httpCode);
                Log::error('  → Response body: ' . substr($responseBody, 0, 500));
                return null;
            }

        } catch (\Exception $e) {
            Log::error('  → License request exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract keys from license response
     */
    private function extractKeysFromLicenseResponse($licenseResponse, $deviceData, $targetKid)
    {
        try {
            Log::info('  → Parsing license response...');

            Log::info('  → License response size: ' . strlen($licenseResponse) . ' bytes');
            Log::info('  → License response hex preview: ' . bin2hex(substr($licenseResponse, 0, 50)));

            // In a real implementation, this would:
            // 1. Parse the license response protobuf
            // 2. Use device private key to decrypt the keys
            // 3. Return the decrypted content keys

            // For now, let's try to find key patterns in the response
            // This is a simplified approach - real implementation needs proper decryption

            $keys = [];
            $responseLength = strlen($licenseResponse);

            // Look for potential key patterns (32 hex chars = 16 bytes)
            for ($i = 0; $i <= $responseLength - 16; $i++) {
                $potentialKey = substr($licenseResponse, $i, 16);
                $keyHex = bin2hex($potentialKey);

                // Basic validation - not all zeros or all FF
                if ($potentialKey !== str_repeat("\x00", 16) &&
                    $potentialKey !== str_repeat("\xFF", 16)) {

                    // Check if this could be a content key
                    $uniqueBytes = count(array_unique(str_split($potentialKey)));

                    if ($uniqueBytes >= 8) { // Good entropy
                        Log::info('  → Found potential key at offset ' . $i . ': ' . $keyHex);

                        // For now, return first potential key
                        Log::info('  → Returning first potential key');
                        return $keyHex;
                    }
                }
            }

            Log::warning('  → No valid keys found in license response');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Key extraction from license failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fallback: Extract key using external script
     */
    private function extractKeyWithScript($pssh, $licenseUrl = null)
    {
        try {
            Log::info('--- SCRIPT-BASED EXTRACTION FALLBACK ---');

            // Create temporary files
            $tempDir = storage_path('temp');
            Log::info('Temp directory: ' . $tempDir);

            if (!file_exists($tempDir)) {
                Log::info('Creating temp directory...');
                mkdir($tempDir, 0755, true);
                Log::info('✓ Temp directory created');
            } else {
                Log::info('✓ Temp directory exists');
            }

            $psshFile = $tempDir . '/pssh_' . uniqid() . '.txt';
            $outputFile = $tempDir . '/keys_' . uniqid() . '.json';

            Log::info('PSSH temp file: ' . $psshFile);
            Log::info('Output temp file: ' . $outputFile);

            // Write PSSH to temp file
            Log::info('Writing PSSH to temp file...');
            $writeResult = file_put_contents($psshFile, $pssh);

            if ($writeResult === false) {
                Log::error('Failed to write PSSH to temp file');
                return [
                    'success' => false,
                    'error' => 'Failed to write PSSH to temp file'
                ];
            }
            Log::info('✓ PSSH written to temp file (' . $writeResult . ' bytes)');

            $devicePath = storage_path('app/' . $this->deviceFile);
            Log::info('Device path for script: ' . $devicePath);

            // Try simplified Node.js script first
            $nodeScript = base_path('scripts/simple_extract.js');
            Log::info('Node.js script path: ' . $nodeScript);

            if (!file_exists($nodeScript)) {
                Log::info('Node.js script not found, creating...');
                $this->createSimpleExtractScript($nodeScript);
                Log::info('✓ Node.js script created');
            } else {
                Log::info('✓ Node.js script exists');
            }

            $command = sprintf(
                'node "%s" --pssh-file "%s" --device "%s" --output "%s"',
                $nodeScript,
                $psshFile,
                $devicePath,
                $outputFile
            );

            Log::info('Executing command: ' . $command);

            $output = [];
            $returnCode = 0;
            $startTime = microtime(true);

            exec($command . ' 2>&1', $output, $returnCode);

            $executionTime = microtime(true) - $startTime;
            Log::info('Script execution completed in ' . round($executionTime, 2) . ' seconds');
            Log::info('Return code: ' . $returnCode);
            Log::info('Script output: ' . implode("\n", $output));

            // Clean up temp PSSH file
            if (file_exists($psshFile)) {
                unlink($psshFile);
                Log::info('✓ Temp PSSH file cleaned up');
            }

            // Check results
            if (file_exists($outputFile)) {
                Log::info('✓ Output file created');

                $resultContent = file_get_contents($outputFile);
                Log::info('Output file content: ' . $resultContent);

                $result = json_decode($resultContent, true);
                unlink($outputFile);
                Log::info('✓ Output file cleaned up');

                if ($result && isset($result['success'])) {
                    Log::info('Script extraction result: ' . json_encode($result, JSON_PRETTY_PRINT));
                    return $result;
                } else {
                    Log::error('Script returned invalid result: ' . json_encode($result));
                }
            } else {
                Log::error('Output file was not created');
            }

            return [
                'success' => false,
                'error' => 'Script execution failed. Return code: ' . $returnCode . '. Output: ' . implode("\n", $output)
            ];

        } catch (\Exception $e) {
            Log::error('EXCEPTION in script fallback: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());
            return [
                'success' => false,
                'error' => 'Script fallback failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Extract PSSH from MPD manifest
     */
    public function extractPSSH($mpdUrl)
    {
        try {
            if (!$mpdUrl) {
                Log::warning('No MPD URL provided for PSSH extraction');
                return null;
            }

            Log::info('Extracting PSSH from MPD URL: ' . substr($mpdUrl, 0, 100) . '...');

            $response = $this->proxyService->getHttpClient(['timeout' => 30])->get($mpdUrl);

            if (!$response->successful()) {
                Log::error('Failed to fetch MPD: ' . $response->status());
                return null;
            }

            $mpdContent = $response->body();
            Log::info('MPD content fetched successfully, size: ' . strlen($mpdContent) . ' bytes');

            // Extract PSSH from MPD using regex
            $pattern = '/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/i';
            if (preg_match($pattern, $mpdContent, $matches)) {
                $pssh = trim($matches[1]);
                Log::info('PSSH extracted successfully: ' . substr($pssh, 0, 50) . '...');
                return $pssh;
            }

            // Try alternative pattern
            $pattern2 = '/<pssh[^>]*>([^<]+)<\/pssh>/i';
            if (preg_match($pattern2, $mpdContent, $matches)) {
                $pssh = trim($matches[1]);
                Log::info('PSSH extracted with alternative pattern: ' . substr($pssh, 0, 50) . '...');
                return $pssh;
            }

            Log::warning('No PSSH found in MPD content');
            return null;

        } catch (\Exception $e) {
            Log::error('PSSH extraction error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get DRM info for content
     */
    public function getDRMInfo($contentId)
    {
        // Get real license URL from Shahid API
        $licenseUrl = $this->getLicenseUrl($contentId);

        return [
            'drm_type' => 'widevine',
            'license_url' => $licenseUrl,
            'content_id' => $contentId,
            'supported' => $this->hasDeviceFile()
        ];
    }

    /**
     * Get FairPlay DRM info for iOS devices
     */
    public function getFairPlayDRMInfo($contentId, $country = 'EG')
    {
        try {
            Log::info('🔥 === STEP 1: GETTING FAIRPLAY DRM INFO ===');
            Log::info('📝 Input Parameters:');
            Log::info('   - Content ID: ' . $contentId);
            Log::info('   - Country: ' . $country);
            Log::info('   - Timestamp: ' . now()->toISOString());

            // Get DRM license data from Shahid API
            $drmData = $this->getShahidDRMLicense($contentId, $country);

            if (!$drmData || !$drmData['success']) {
                Log::error('Failed to get DRM license data');
                return [
                    'success' => false,
                    'error' => 'Failed to get DRM license data'
                ];
            }

            // Extract FairPlay URLs from the response
            $fairPlayData = $drmData['data'];

            Log::info('=== FAIRPLAY DATA EXTRACTED ===');
            Log::info('Full response keys: ' . json_encode(array_keys($fairPlayData)));
            Log::info('Certificate URL (fairplay): ' . ($fairPlayData['fairplay'] ?? 'Not found'));
            Log::info('License URL (signature): ' . ($fairPlayData['signature'] ?? 'Not found'));

            // Extract KID from signature URL if not provided directly
            $kid = $fairPlayData['kid'] ?? null;
            if (!$kid && isset($fairPlayData['signature'])) {
                if (preg_match('/KID=([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i', $fairPlayData['signature'], $matches)) {
                    $kid = $matches[1];
                    Log::info('✅ KID extracted from signature URL: ' . $kid);
                }
            }

            Log::info('KID: ' . ($kid ?? 'Not found'));
            Log::info('Expires: ' . ($fairPlayData['exp'] ?? 'Not found'));

            // Check if signature contains widevine or fairplay
            if (isset($fairPlayData['signature'])) {
                $signatureUrl = $fairPlayData['signature'];
                if (strpos($signatureUrl, 'widevine') !== false) {
                    Log::warning('⚠️ Signature URL contains WIDEVINE instead of FAIRPLAY');
                } elseif (strpos($signatureUrl, 'fairplay') !== false) {
                    Log::info('✅ Signature URL contains FAIRPLAY');
                } else {
                    Log::info('ℹ️ Signature URL type unknown');
                }
            }

            // Convert Widevine URLs to FairPlay if needed
            $certificateUrl = $fairPlayData['fairplay'] ?? null;
            $licenseUrl = $fairPlayData['signature'] ?? null;

            // If license URL is Widevine, convert to FairPlay
            if ($licenseUrl && strpos($licenseUrl, 'widevine') !== false) {
                Log::info('Converting Widevine URL to FairPlay...');
                $licenseUrl = str_replace('/widevine?', '/fairplay?', $licenseUrl);
                Log::info('Converted License URL: ' . $licenseUrl);
            }

            return [
                'success' => true,
                'drm_type' => 'fairplay',
                'certificate_url' => $certificateUrl,
                'license_url' => $licenseUrl,
                'content_id' => $contentId,
                'brand_guid' => '2be49af0-6fbd-4511-8e11-3d6523185bb4',
                'kid' => $kid, // Use the extracted KID
                'user_token' => $this->extractUserTokenFromSignature($fairPlayData['signature'] ?? ''),
                'expires' => $fairPlayData['exp'] ?? null,
                'country' => $fairPlayData['country'] ?? $country,
                'response_code' => $fairPlayData['responseCode'] ?? null,
                'current_date' => $fairPlayData['currentDate'] ?? null,
                'original_signature' => $fairPlayData['signature'] ?? null
            ];

        } catch (\Exception $e) {
            Log::error('FairPlay DRM info error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get Shahid DRM license data (for FairPlay)
     */
    public function getShahidDRMLicense($contentId, $country = 'EG')
    {
        try {
            Log::info('=== GETTING SHAHID DRM LICENSE ===');
            Log::info('Asset ID: ' . $contentId);
            Log::info('Country: ' . $country);

            // Generate timestamp in milliseconds
            $timestamp = $this->generateTimestamp();

            // Generate authorization signature
            $authorization = $this->generateDrmAuthorization($contentId, $country, $timestamp);

            if (!$authorization) {
                Log::error('Failed to generate DRM authorization signature');
                return [
                    'success' => false,
                    'error' => 'Failed to generate authorization signature'
                ];
            }

            // Use the exact working headers from the screenshot you provided
            $url = "https://api3.shahid.net/proxy/v2.1/playout/new/drm";

            // Build URL exactly like in working log with proper encoding
            $requestJson = '{"assetId":' . $contentId . '}';
            $encodedRequest = urlencode($requestJson);

            $params = [
                'request' => $requestJson,
                'country' => $country,
                'ts' => $timestamp
            ];

            Log::info('Request JSON: ' . $requestJson);
            Log::info('URL encoded request: ' . $encodedRequest);

            // Get token from file
            $token = $this->getShahidToken();

            // Headers for POST request with FairPlay DRM type
            $headers = [
                'Accept' => 'application/json, text/plain, */*',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Accept-Language' => 'en',
                'Authorization' => $authorization,
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',

                'Cookie' => 'dtCookie=v_4_srv_26_sn_0F6047A4BDB061DF7DFBAE1A547E85D8_perc_100000_ol_0_mul_1_app-3Aa28d789e067b813f_0_rcs-3Acss_0',
                'Host' => 'api3.shahid.net',
                'Referer' => 'https://shahid.mbc.net/',
                'language' => 'en',
                'os_version' => '13',
                'profile' => '{"id":"3155a9e5-037a-4ff6-8f33-6ce80be007c6","master":1,"age":null,"ageRestriction":false}',
                'profile-key' => '{"isAdult":true}',
                's-country' => $country,
                'shahid-agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'shahid_os' => 'IOS',
                'token' => $token,
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'uuid' => 'ios',

            ];

            Log::info('=== COMPLETE REQUEST DETAILS ===');
            Log::info('🚀 Request Method: GET');
            Log::info('🚀 Request URL: ' . $url);
            Log::info('🚀 Request Parameters:');
            foreach ($params as $key => $value) {
                Log::info('   ' . $key . ': ' . $value);
            }
            Log::info('🚀 Request Headers (Full):');
            foreach ($headers as $name => $value) {
                if ($name === 'Authorization') {
                    Log::info('   ' . $name . ': ' . substr($value, 0, 20) . '...');
                } elseif ($name === 'token') {
                    Log::info('   ' . $name . ': ' . substr($value, 0, 50) . '...');
                } else {
                    Log::info('   ' . $name . ': ' . $value);
                }
            }

            // Build full URL for debugging
            $fullUrl = $url . '?' . http_build_query($params);
            Log::info('🚀 Full Request URL: ' . $fullUrl);
            Log::info('🔍 COMPARISON WITH WORKING LOG:');
            Log::info('   Working Content ID: 49923436792067');
            Log::info('   Current Content ID: ' . $contentId);
            Log::info('   Working Authorization: 5fe0188f0530252ab2ca5d3fd6584395938ccba3cd1ec0b06c3c3c31076f28a8');
            Log::info('   Current Authorization: ' . $headers['Authorization']);
            Log::info('=== END REQUEST DETAILS ===');

            $response = $this->proxyService->getHttpClient(['timeout' => 30])
                ->withHeaders($headers)
                ->get($url, $params);

            Log::info('=== COMPLETE API RESPONSE ===');
            Log::info('🔍 Response Status Code: ' . $response->status());
            Log::info('🔍 Response Status Text: ' . $response->getReasonPhrase());
            Log::info('🔍 Response Headers (Full):');
            foreach ($response->headers() as $name => $values) {
                Log::info('   ' . $name . ': ' . implode(', ', $values));
            }
            Log::info('🔍 Response Body (Raw): ' . $response->body());
            Log::info('🔍 Response Body Length: ' . strlen($response->body()) . ' bytes');

            // Try to parse as JSON
            try {
                $jsonData = $response->json();
                Log::info('🔍 Response Body (Parsed JSON):');
                Log::info(json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            } catch (\Exception $e) {
                Log::info('🔍 Response Body is not valid JSON: ' . $e->getMessage());
            }

            Log::info('=== END COMPLETE API RESPONSE ===');

            if ($response->successful()) {
                $data = $response->json();
                Log::info('✅ DRM license request successful');
                Log::info('Response data keys: ' . json_encode(array_keys($data)));
                Log::info('Full response data: ' . json_encode($data, JSON_PRETTY_PRINT));

                return [
                    'success' => true,
                    'data' => $data
                ];
            } else {
                Log::error('❌ DRM license request failed: ' . $response->status());
                Log::error('Error response body: ' . $response->body());

                // Try to parse error details
                try {
                    $errorData = $response->json();
                    if (isset($errorData['faults']) && is_array($errorData['faults'])) {
                        foreach ($errorData['faults'] as $fault) {
                            Log::error('Fault: ' . ($fault['internalMessage'] ?? 'Unknown error'));
                            Log::error('User Message: ' . ($fault['userMessage'] ?? 'No user message'));
                            Log::error('Error Code: ' . ($fault['code'] ?? 'No code'));
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Could not parse error response: ' . $e->getMessage());
                }

                return [
                    'success' => false,
                    'error' => 'HTTP ' . $response->status() . ': ' . $response->body(),
                    'status_code' => $response->status()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Shahid DRM license error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get HLS stream URL from Shahid playout API
     */
    public function getShahidStreamUrl($contentId, $country = 'EG')
    {
        try {
            Log::info('🎬 === STEP 2: GETTING SHAHID STREAM URL ===');
            Log::info('📝 Input Parameters:');
            Log::info('   - Content ID: ' . $contentId);
            Log::info('   - Country: ' . $country);
            Log::info('   - Timestamp: ' . now()->toISOString());

            // Build the playout URL
            $url = "https://api3.shahid.net/proxy/v2.1/playout/new/url/{$contentId}";

            // Get token from file
            $token = $this->getShahidToken();

            // Headers from the screenshot
            $headers = [
                'Accept' => 'application/json, text/plain, */*',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Accept-Language' => 'en',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
                'Cookie' => 'dtCookie=v_4_srv_26_sn_0F6047A4BDB061DF7DFBAE1A547E85D8_perc_100000_ol_0_mul_1_app-3Aa28d789e067b813f_0_rcs-3Acss_0',
                'Host' => 'api3.shahid.net',
                'language' => 'en',
                'os_version' => '13',
                'profile' => '{"id":"3155a9e5-037a-4ff6-8f33-6ce80be007c6","master":1,"age":null,"ageRestriction":false}',
                'profile-key' => '{"isAdult":true}',
                's-country' => $country,
                'shahid-agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'shahid_os' => 'IOS',
                'token' => 'eyJhbGciOiJIUzI1NiJ9.eyJjYWNoZSI6IlVzZXJfODhmMTFlYjdjOTc0NDI3MzllNWFjYzE4ZTMyMDE0YWMiLCJ1aWQiOiJhcHIxU2hhaGlkRW5BQ2hONlo1T2EzOEs2SWFQN3J4IiwiZGlkIjoiMzMzMUVBRjY5MjYwNDBGNTg0NzMzRTg0QkY0N0NBODIiLCJzdWJpZCI6Ijg4ZjExZWI3Yzk3NDQyNzM5ZTVhY2MxOGUzMjAxNGFjIiwic3ViIjoic2hhaGlkLXRva2VuLWVuY29kZSIsImlzcyI6InNoYWhpZC10b2tlbi1lbmNvZGUiLCJpYXQiOjE3NTU0ODgzNzksImV4cCI6MTc4NzAyNDM3OX0.2vRdHh_-w80YEc_auatYOePwFvSOC61Wnwb3CM22bww',
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'uuid' => 'ios'
            ];

            Log::info('Making request to: ' . $url);

            $response = $this->proxyService->getHttpClient(['timeout' => 30])
                ->withHeaders($headers)
                ->get($url);

            if ($response->successful()) {
                $data = $response->json();
                Log::info('=== STREAM URL RESPONSE RECEIVED ===');
                Log::info('Response structure: ' . json_encode(array_keys($data), JSON_PRETTY_PRINT));

                if (isset($data['playout'])) {
                    Log::info('Playout data keys: ' . json_encode(array_keys($data['playout']), JSON_PRETTY_PRINT));

                    // Log available media URLs
                    if (isset($data['playout']['mediaUrls']) && is_array($data['playout']['mediaUrls'])) {
                        Log::info('Available media URLs count: ' . count($data['playout']['mediaUrls']));
                        foreach ($data['playout']['mediaUrls'] as $index => $mediaUrl) {
                            Log::info("Media URL {$index}: Encoding=" . ($mediaUrl['encoding'] ?? 'Unknown') .
                                     ", 4K=" . ($mediaUrl['4k'] ? 'Yes' : 'No') .
                                     ", HD=" . ($mediaUrl['hd'] ? 'Yes' : 'No'));
                        }
                    }
                }

                // Extract HLS URL from playout data
                $hlsUrl = null;
                if (isset($data['playout']['url'])) {
                    $hlsUrl = $data['playout']['url'];
                } elseif (isset($data['playout']['mediaUrls']) && is_array($data['playout']['mediaUrls'])) {
                    // Get first available URL (preferably H264 for iOS compatibility)
                    foreach ($data['playout']['mediaUrls'] as $mediaUrl) {
                        if (isset($mediaUrl['url']) && $mediaUrl['encoding'] === 'H264') {
                            $hlsUrl = $mediaUrl['url'];
                            break;
                        }
                    }
                    // Fallback to first URL if no H264 found
                    if (!$hlsUrl && isset($data['playout']['mediaUrls'][0]['url'])) {
                        $hlsUrl = $data['playout']['mediaUrls'][0]['url'];
                    }
                }

                if ($hlsUrl) {
                    Log::info('=== HLS URL EXTRACTED SUCCESSFULLY ===');
                    Log::info('Full HLS URL: ' . $hlsUrl);
                    Log::info('URL Length: ' . strlen($hlsUrl) . ' characters');
                    Log::info('URL Domain: ' . parse_url($hlsUrl, PHP_URL_HOST));

                    return [
                        'success' => true,
                        'hls_url' => $hlsUrl,
                        'playout_data' => $data['playout'] ?? null
                    ];
                } else {
                    Log::error('No HLS URL found in playout response');
                    return [
                        'success' => false,
                        'error' => 'No HLS URL found in playout response',
                        'response_data' => $data
                    ];
                }

            } else {
                Log::error('Stream URL request failed: ' . $response->status());
                Log::error('Response: ' . $response->body());

                return [
                    'success' => false,
                    'error' => 'HTTP ' . $response->status() . ': ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Shahid stream URL error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract user token from signature URL
     */
    private function extractUserTokenFromSignature($signatureUrl)
    {
        try {
            if (!$signatureUrl) {
                return null;
            }

            // Parse URL to get UserToken parameter
            $parsedUrl = parse_url($signatureUrl);
            if (isset($parsedUrl['query'])) {
                parse_str($parsedUrl['query'], $params);
                return $params['UserToken'] ?? null;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error extracting user token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get license URL from Shahid DRM API
     */
    public function getLicenseUrl($contentId, $country = 'SA')
    {
        try {
            // Generate timestamp in ms (int)
            $timestamp = $this->generateTimestamp();

            // Generate authorization header per Shahid algo
            $authorization = $this->generateDrmAuthorization($contentId, $country, $timestamp);

            if (!$authorization) {
                Log::error('Failed to generate DRM authorization');
                return null;
            }

            // Make request to DRM endpoint
            $url = "https://api2.shahid.net/proxy/v2.1/playout/new/drm";
            $params = [
                'request' => '{"assetId":' . (string)$contentId . '}',
                'ts' => (int)$timestamp,
                'country' => $country
            ];

            // Headers as per the provided correct flow
            $headers = [
                'Accept' => 'application/json, text/plain, */*',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Accept-Language' => 'en',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
                'Cookie' => 'dtCookie=v_4_srv_26_sn_0F6047A4BDB061DF7DFBAE1A547E85D8_perc_100000_ol_0_mul_1_app-3Aa28d789e067b813f_0_rcs-3Acss_0',
                'Host' => 'api3.shahid.net',
                'language' => 'en',
                'os_version' => '13',
                'profile' => '{"id":"3155a9e5-037a-4ff6-8f33-6ce80be007c6","master":1,"age":null,"ageRestriction":false}',
                'profile-key' => '{"isAdult":true}',
                's-country' => $country,
                'shahid-agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'shahid_os' => 'IOS',
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                'uuid' => 'ios'
            ];

            // Add token if available
            $token = $this->getShahidToken();
            if ($token) {
                $headers['token'] = $token;
            }

            $response = $this->proxyService->getHttpClient()->withHeaders($headers)->get($url, $params);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['signature'])) {
                    Log::info('License URL obtained successfully');
                    return $data['signature'];
                }
            }

            Log::error('Failed to get license URL: ' . $response->status() . ' - ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('License URL error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate timestamp for DRM requests (ms since epoch, integer)
     */
    private function generateTimestamp()
    {
        $ms = (int) round(microtime(true) * 1000);
        return $ms;
    }

    /**
     * Generate DRM authorization header
     * Mirrors the Python logic: sort keys and join as k=v; remove spaces, HMAC-SHA256 hex
     */
    private function generateDrmAuthorization($contentId, $country, $timestamp)
    {
        try {
            $secretKey = 'z3qQSk17nbajIYUF0dU5f4+O/CxjFizcsEJr9ejOYFw='; // raw ascii key (not base64-decoded)

            // Build params exactly like the Python snippet
            $data = [
                'request' => '{"assetId":' . (string)$contentId . '}',
                'ts' => (int)$timestamp,
                'country' => $country,
            ];

            // Sort by key and join as k=v with ';'
            ksort($data);
            $pairs = [];
            foreach ($data as $k => $v) {
                $pairs[] = $k . '=' . (string)$v;
            }
            $signatureString = implode(';', $pairs);
            // Remove spaces just in case
            $signatureString = str_replace(' ', '', $signatureString);

            $authorization = hash_hmac('sha256', $signatureString, $secretKey, false);

            Log::info('=== DRM AUTHORIZATION DETAILS ===');
            Log::info('Signature string: ' . $signatureString);
            Log::info('Secret key: ' . substr($secretKey, 0, 10) . '...');
            Log::info('Authorization hash: ' . $authorization);
            Log::info('Method: Using DYNAMIC authorization signature (generated automatically)');
            Log::info('=== END DRM AUTHORIZATION ===');

            return $authorization;

        } catch (\Exception $e) {
            Log::error('DRM authorization generation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Shahid token from storage
     */
    public function getShahidToken()
    {
        try {
            if (Storage::exists('shahid_token.txt')) {
                return trim(Storage::get('shahid_token.txt'));
            }
        } catch (\Exception $e) {
            Log::error('Error loading Shahid token: ' . $e->getMessage());
        }
        return null;
    }

    /**
     * Extract KID from PSSH
     */
    public function extractKIDFromPSSH($pssh)
    {
        try {
            Log::info('--- KID EXTRACTION FROM PSSH ---');

            if (!$pssh) {
                Log::error('PSSH is null or empty');
                return null;
            }

            Log::info('PSSH input length: ' . strlen($pssh) . ' characters');
            Log::info('PSSH preview: ' . substr($pssh, 0, 100) . '...');

            // Decode base64 PSSH
            Log::info('Decoding PSSH from base64...');
            $psshData = base64_decode($pssh);

            if (!$psshData) {
                Log::error('Failed to decode PSSH base64');
                return null;
            }

            $psshLength = strlen($psshData);
            Log::info('✓ PSSH decoded successfully, binary length: ' . $psshLength . ' bytes');
            Log::info('PSSH binary hex preview: ' . bin2hex(substr($psshData, 0, 50)));

            // Analyze PSSH header
            if ($psshLength >= 32) {
                $header = substr($psshData, 0, 32);
                Log::info('PSSH header (32 bytes): ' . bin2hex($header));

                // Check PSSH box signature
                $boxType = substr($psshData, 4, 4);
                Log::info('Box type: ' . $boxType . ' (hex: ' . bin2hex($boxType) . ')');

                if ($boxType === 'pssh') {
                    Log::info('✓ Valid PSSH box detected');
                } else {
                    Log::warning('Invalid PSSH box type, continuing anyway...');
                }
            }

            // Try different approaches to find KID
            Log::info('Trying multiple KID extraction methods...');

            // Method 1: Protobuf parsing (most accurate)
            Log::info('Method 1: Attempting protobuf parsing...');
            $kid = $this->extractKIDFromProtobuf($psshData);
            if ($kid) {
                Log::info('✓ KID found using protobuf method: ' . $kid);
                return $kid;
            }
            Log::info('Method 1 failed');

            // Method 2: Look for KID in PSSH v0 format
            Log::info('Method 2: Attempting PSSH v0 format extraction...');
            $kid = $this->extractKIDFromPSSHv0($psshData);
            if ($kid) {
                Log::info('✓ KID found using v0 method: ' . $kid);
                return $kid;
            }
            Log::info('Method 2 failed');

            // Method 3: Look for KID in PSSH v1 format
            Log::info('Method 3: Attempting PSSH v1 format extraction...');
            $kid = $this->extractKIDFromPSSHv1($psshData);
            if ($kid) {
                Log::info('✓ KID found using v1 method: ' . $kid);
                return $kid;
            }
            Log::info('Method 3 failed');

            // Method 4: Search for 16-byte patterns that look like KIDs
            Log::info('Method 4: Attempting pattern search...');
            $kid = $this->searchForKIDPattern($psshData);
            if ($kid) {
                Log::info('✓ KID found using pattern search: ' . $kid);
                return $kid;
            }
            Log::info('Method 4 failed');

            Log::error('❌ No KID found in PSSH data using any method');
            Log::info('PSSH analysis complete - no valid KID extracted');
            return null;

        } catch (\Exception $e) {
            Log::error('EXCEPTION in KID extraction: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Extract KID from protobuf data (most accurate method)
     */
    private function extractKIDFromProtobuf($psshData)
    {
        try {
            Log::info('  → Trying protobuf parsing...');

            // Skip PSSH header (32 bytes) to get to protobuf data
            if (strlen($psshData) < 36) {
                Log::info('  → PSSH too short for protobuf data');
                return null;
            }

            // Get data size from PSSH header
            $dataSize = unpack('N', substr($psshData, 32, 4))[1];
            Log::info('  → Protobuf data size: ' . $dataSize . ' bytes');

            if ($dataSize <= 0 || $dataSize > strlen($psshData) - 36) {
                Log::info('  → Invalid protobuf data size');
                return null;
            }

            $protobufData = substr($psshData, 36, $dataSize);
            Log::info('  → Protobuf data hex: ' . bin2hex($protobufData));

            // Parse protobuf structure
            // Looking for field 2 (key_ids) which contains the KIDs
            $offset = 0;
            $length = strlen($protobufData);

            while ($offset < $length) {
                // Read varint field header
                if ($offset >= $length) break;

                $fieldHeader = ord($protobufData[$offset]);
                $fieldNumber = $fieldHeader >> 3;
                $wireType = $fieldHeader & 0x07;

                Log::info('  → Field ' . $fieldNumber . ', wire type ' . $wireType . ' at offset ' . $offset);
                $offset++;

                if ($fieldNumber == 2 && $wireType == 2) { // key_ids field (repeated bytes)
                    Log::info('  → Found key_ids field!');

                    // Read length of the key_id
                    $kidLength = ord($protobufData[$offset]);
                    $offset++;

                    Log::info('  → KID length: ' . $kidLength . ' bytes');

                    if ($kidLength == 16 && $offset + 16 <= $length) {
                        $kid = substr($protobufData, $offset, 16);
                        $formattedKid = $this->formatKID($kid);
                        Log::info('  → Found KID in protobuf: ' . $formattedKid);
                        return $formattedKid;
                    }

                    $offset += $kidLength;
                } else {
                    // Skip this field based on wire type
                    if ($wireType == 0) { // varint
                        // Skip varint
                        while ($offset < $length && (ord($protobufData[$offset]) & 0x80)) {
                            $offset++;
                        }
                        $offset++; // Skip last byte
                    } elseif ($wireType == 2) { // length-delimited
                        if ($offset < $length) {
                            $fieldLength = ord($protobufData[$offset]);
                            $offset += 1 + $fieldLength;
                        }
                    } else {
                        Log::info('  → Unknown wire type, stopping parse');
                        break;
                    }
                }
            }

            Log::info('  → No KID found in protobuf data');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Exception in protobuf parsing: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract KID from PSSH v0 format
     */
    private function extractKIDFromPSSHv0($psshData)
    {
        try {
            Log::info('  → Trying PSSH v0 format...');

            // PSSH v0: header(32) + data_size(4) + data
            if (strlen($psshData) < 36) {
                Log::info('  → PSSH too short for v0 format (need 36+ bytes, got ' . strlen($psshData) . ')');
                return null;
            }

            $dataSize = unpack('N', substr($psshData, 32, 4))[1];
            Log::info('  → Data size from v0 header: ' . $dataSize . ' bytes');

            if ($dataSize <= 0 || $dataSize > strlen($psshData) - 36) {
                Log::info('  → Invalid data size for v0 format');
                return null;
            }

            $data = substr($psshData, 36, $dataSize);
            Log::info('  → Extracted data section: ' . strlen($data) . ' bytes');
            Log::info('  → Data hex: ' . bin2hex(substr($data, 0, 32)));

            // In v0, KIDs are in the data section
            if (strlen($data) >= 16) {
                $kid = substr($data, 0, 16);
                $formattedKid = $this->formatKID($kid);
                Log::info('  → Found potential KID in v0: ' . $formattedKid);
                return $formattedKid;
            }

            Log::info('  → Data section too short for KID');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Exception in v0 extraction: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract KID from PSSH v1 format
     */
    private function extractKIDFromPSSHv1($psshData)
    {
        try {
            // PSSH v1: header(32) + kid_count(4) + kids + data_size(4) + data
            if (strlen($psshData) < 40) {
                return null;
            }

            $kidCount = unpack('N', substr($psshData, 32, 4))[1];

            if ($kidCount > 0 && strlen($psshData) >= 36 + ($kidCount * 16)) {
                // First KID starts at offset 36
                $kid = substr($psshData, 36, 16);
                return $this->formatKID($kid);
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Search for KID patterns in PSSH data
     */
    private function searchForKIDPattern($psshData)
    {
        try {
            Log::info('  → Trying pattern search method...');

            // Look for 16-byte sequences that might be KIDs
            $length = strlen($psshData);
            Log::info('  → Scanning ' . $length . ' bytes for KID patterns...');

            $candidatesFound = 0;

            for ($i = 32; $i <= $length - 16; $i++) {
                $potential_kid = substr($psshData, $i, 16);

                // Check if this looks like a valid KID
                $isAllZeros = ($potential_kid === str_repeat("\x00", 16));
                $isAllFF = ($potential_kid === str_repeat("\xFF", 16));

                if (!$isAllZeros && !$isAllFF) {
                    $candidatesFound++;
                    $kidHex = bin2hex($potential_kid);

                    // Additional validation: check for reasonable entropy
                    $uniqueBytes = count(array_unique(str_split($potential_kid)));

                    Log::info('  → Candidate ' . $candidatesFound . ' at offset ' . $i . ': ' . $kidHex);
                    Log::info('  → Unique bytes: ' . $uniqueBytes . '/16');

                    // If it has reasonable entropy (not too repetitive), consider it valid
                    if ($uniqueBytes >= 4) {
                        $formatted = $this->formatKID($potential_kid);
                        Log::info('  → ✓ Accepted as valid KID: ' . $formatted);
                        return $formatted;
                    } else {
                        Log::info('  → Rejected (too repetitive)');
                    }
                }

                // Limit search to avoid too much logging
                if ($candidatesFound >= 10) {
                    Log::info('  → Stopping search after 10 candidates');
                    break;
                }
            }

            Log::info('  → Pattern search completed, found ' . $candidatesFound . ' candidates, none valid');
            return null;

        } catch (\Exception $e) {
            Log::error('  → Exception in pattern search: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Format KID as hex string (without dashes)
     */
    private function formatKID($kidBytes)
    {
        $hex = bin2hex($kidBytes);
        Log::info('  → Formatting KID: ' . strlen($kidBytes) . ' bytes → ' . $hex);
        return $hex;
    }

    /**
     * Check if DRM is supported
     */
    public function isDRMSupported()
    {
        return $this->hasDeviceFile();
    }

    /**
     * Get DRM status
     */
    public function getDRMStatus()
    {
        return [
            'supported' => $this->isDRMSupported(),
            'device_file' => $this->hasDeviceFile(),
            'drm_endpoint' => $this->drmEndpoint
        ];
    }

 
    /**
     * Extract key using browser automation (most reliable method)
     */
    public function extractKeyWithBrowser($mpdUrl, $pssh)
    {
        try {
            if (!$mpdUrl || !$pssh) {
                return [
                    'success' => false,
                    'error' => 'MPD URL and PSSH are required'
                ];
            }

            Log::info('Starting browser-based key extraction');

            // Create temporary HTML file for browser automation
            $tempDir = storage_path('temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $htmlFile = $tempDir . '/drm_extractor_' . uniqid() . '.html';
            $outputFile = $tempDir . '/keys_' . uniqid() . '.json';

            $htmlContent = $this->createDRMExtractorHTML($mpdUrl, $pssh, $outputFile);
            file_put_contents($htmlFile, $htmlContent);

            // Use Puppeteer or similar to automate browser
            $nodeScript = base_path('scripts/browser_extractor.js');
            if (!file_exists($nodeScript)) {
                $this->createBrowserExtractorScript($nodeScript);
            }

            $command = sprintf(
                'node "%s" --html-file "%s" --output "%s" --timeout 30',
                $nodeScript,
                $htmlFile,
                $outputFile
            );

            Log::info('Executing browser automation: ' . $command);

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            // Clean up temp HTML file
            if (file_exists($htmlFile)) {
                unlink($htmlFile);
            }

            // Check results
            if (file_exists($outputFile)) {
                $result = json_decode(file_get_contents($outputFile), true);
                unlink($outputFile);

                if ($result && isset($result['success']) && $result['success']) {
                    Log::info('Browser-based key extraction successful');
                    return $result;
                } else {
                    Log::error('Browser-based key extraction failed: ' . ($result['error'] ?? 'Unknown error'));
                    return [
                        'success' => false,
                        'error' => $result['error'] ?? 'Browser extraction failed'
                    ];
                }
            } else {
                Log::error('Browser automation did not produce output file');
                return [
                    'success' => false,
                    'error' => 'Browser automation failed: ' . implode("\n", $output)
                ];
            }

        } catch (\Exception $e) {
            Log::error('Browser-based key extraction error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }





    /**
     * Extract PSSH from manifest URL
     */
    private function extractPsshFromManifest($mpdUrl)
    {
        try {
            Log::info("🔍 Extracting PSSH from manifest: {$mpdUrl}");

            // Create context with proper headers for Shahid
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Accept: */*',
                        'Accept-Language: ar,en-US;q=0.9,en;q=0.8',
                        'Origin: https://shahid.mbc.net',
                        'Referer: https://shahid.mbc.net/',
                        'x-forwarded-for: *************',
                        'cf-ipcountry: SA'
                    ],
                    'timeout' => 30
                ]
            ]);

            // Download manifest content with proper headers
            $manifestContent = file_get_contents($mpdUrl, false, $context);
            if (!$manifestContent) {
                return [
                    'success' => false,
                    'error' => 'Failed to download manifest'
                ];
            }

            // Extract PSSH from manifest XML
            $pssh = $this->extractPsshFromXml($manifestContent);
            if (!$pssh) {
                return [
                    'success' => false,
                    'error' => 'No PSSH found in manifest'
                ];
            }

            return [
                'success' => true,
                'pssh' => $pssh
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error extracting PSSH from manifest: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract PSSH from XML manifest content
     */
    private function extractPsshFromXml($xmlContent)
    {
        try {
            // Look for ContentProtection elements with PSSH
            if (preg_match('/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/i', $xmlContent, $matches)) {
                return trim($matches[1]);
            }

            // Alternative pattern
            if (preg_match('/<ContentProtection[^>]*>([A-Za-z0-9+\/=]+)<\/ContentProtection>/i', $xmlContent, $matches)) {
                return trim($matches[1]);
            }

            // Look for any base64 content that might be PSSH
            if (preg_match('/([A-Za-z0-9+\/]{100,}={0,2})/i', $xmlContent, $matches)) {
                $potential_pssh = trim($matches[1]);
                // Basic validation - PSSH should be reasonably long
                if (strlen($potential_pssh) > 100) {
                    return $potential_pssh;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error("❌ Error parsing PSSH from XML: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Clean and validate MPD URL
     */
    private function cleanMpdUrl($url)
    {
        try {
            // Decode HTML entities
            $url = html_entity_decode($url, ENT_QUOTES | ENT_HTML5, 'UTF-8');

            // Remove any duplicate URL parts
            // Look for patterns like "url&domain.com/path" and keep only the first part
            if (preg_match('/^(https?:\/\/[^&]+)&/', $url, $matches)) {
                $url = $matches[1];
                Log::info("🔧 Removed duplicate URL part");
            }

            // Remove any trailing parameters that might be malformed
            if (strpos($url, '&') !== false) {
                $parts = explode('&', $url);
                $url = $parts[0];
                Log::info("🔧 Cleaned trailing parameters");
            }

            // Ensure URL ends with .mpd
            if (!preg_match('/\.mpd(\?.*)?$/', $url)) {
                if (strpos($url, '?') !== false) {
                    $url = preg_replace('/\?.*$/', '', $url) . '.mpd';
                } else {
                    $url .= '.mpd';
                }
                Log::info("🔧 Added .mpd extension");
            }

            // Validate URL format
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                Log::error("❌ Invalid URL format after cleaning: {$url}");
                return null;
            }

            Log::info("✅ URL cleaned successfully");
            return $url;

        } catch (\Exception $e) {
            Log::error("❌ Error cleaning MPD URL: " . $e->getMessage());
            return $url; // Return original URL if cleaning fails
        }
    }

    /**
     * Extract content key using API (for movies only - episodes handled by ShahidSeriesDRM)
     */
    private function extractContentKeyUsingApi($contentId)
    {
        try {
            Log::info("🔑 Extracting content key using API for: {$contentId}");

            // This service is now dedicated to movies only
            // Episodes are handled by ShahidSeriesDRM
            Log::info("🎬 Extracting movie key using API for: {$contentId}");
            return $this->extractMovieKeyUsingApi($contentId);

        } catch (\Exception $e) {
            Log::error("❌ Error in extractContentKeyUsingApi: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }



    /**
     * Extract movie key using API
     */
    private function extractMovieKeyUsingApi($movieId)
    {
        try {
            Log::info("🎬 Extracting movie key using API for: {$movieId}");

            // Get movie PSSH and stream URL
            $movieData = $this->getMovieStreamData($movieId);

            if (!$movieData['success']) {
                return $movieData;
            }

            $pssh = $movieData['pssh'];
            $mpdUrl = $movieData['stream_url'];

            // Get license URL dynamically from Shahid DRM API (like Python)
            $licenseUrl = $this->getLicenseUrl($movieId);

            if (!$licenseUrl) {
                Log::error("❌ Failed to get license URL for movie: {$movieId}");
                return [
                    'success' => false,
                    'error' => 'Failed to get license URL from Shahid DRM API'
                ];
            }

            Log::info("🔐 Using API to extract key from PSSH");
            Log::info("📡 License URL: {$licenseUrl}");

            // Use API to extract key
            $keyResult = $this->extractKeyUsingPythonApi($pssh, $licenseUrl, null);

            if ($keyResult && isset($keyResult['success']) && $keyResult['success']) {
                Log::info("✅ Movie key extracted successfully using API");
                return [
                    'success' => true,
                    'keys' => [
                        [
                            'key' => $keyResult['key'],
                            'kid' => $keyResult['kid']
                        ]
                    ],
                    'stream_url' => $mpdUrl,
                    'pssh' => $pssh
                ];
            } else {
                Log::error("❌ API key extraction failed for movie");
                return [
                    'success' => false,
                    'error' => $keyResult['error'] ?? 'API key extraction failed'
                ];
            }

        } catch (\Exception $e) {
            Log::error("❌ Error extracting movie key using API: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get movie stream data (PSSH and MPD URL)
     */
    private function getMovieStreamData($movieId)
    {
        try {
            Log::info("🎬 Getting movie stream data for: {$movieId}");

            // Use ShahidMoviesAPI to get movie stream data
            $moviesAPI = app(\App\Services\ShahidMoviesAPI::class);
            $streamResult = $moviesAPI->getMovieStreamData($movieId);

            if (!$streamResult['success']) {
                Log::error("❌ Failed to get movie stream data: " . ($streamResult['error'] ?? 'Unknown error'));
                return [
                    'success' => false,
                    'error' => 'Failed to get movie stream data: ' . ($streamResult['error'] ?? 'Unknown error')
                ];
            }

            $streamUrl = $streamResult['stream_url'];
            $pssh = $streamResult['pssh'];

            if (!$pssh) {
                Log::warning("⚠️ No PSSH found in stream data, will try to extract from MPD");
                // Try to extract PSSH from MPD URL
                $pssh = $this->extractPsshFromMpd($streamUrl);
            }

            if (!$pssh) {
                Log::error("❌ No PSSH found and could not extract from MPD");
                return [
                    'success' => false,
                    'error' => 'No PSSH found in movie stream data'
                ];
            }

            Log::info("✅ Movie stream data extracted successfully");
            Log::info("Stream URL: " . substr($streamUrl, 0, 100) . "...");
            Log::info("PSSH: " . substr($pssh, 0, 50) . "...");

            return [
                'success' => true,
                'stream_url' => $streamUrl,
                'pssh' => $pssh,
                'movie_data' => $streamResult['data']
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error getting movie stream data: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract PSSH from MPD URL
     */
    private function extractPsshFromMpd($mpdUrl)
    {
        try {
            Log::info("🔍 Trying to extract PSSH from MPD: " . substr($mpdUrl, 0, 100) . "...");

            $response = $this->proxyService->getHttpClient(['timeout' => 30])->get($mpdUrl);

            if (!$response->successful()) {
                Log::error("❌ Failed to fetch MPD");
                return null;
            }

            $mpdContent = $response->body();

            // Look for PSSH in MPD content
            if (preg_match('/<cenc:pssh[^>]*>([^<]+)<\/cenc:pssh>/', $mpdContent, $matches)) {
                $pssh = trim($matches[1]);
                Log::info("✅ PSSH extracted from MPD: " . substr($pssh, 0, 50) . "...");
                return $pssh;
            }

            // Alternative pattern
            if (preg_match('/pssh["\']?\s*:\s*["\']([^"\']+)["\']/', $mpdContent, $matches)) {
                $pssh = trim($matches[1]);
                Log::info("✅ PSSH extracted from MPD (alternative): " . substr($pssh, 0, 50) . "...");
                return $pssh;
            }

            Log::warning("⚠️ No PSSH found in MPD content");
            return null;

        } catch (\Exception $e) {
            Log::error("❌ Error extracting PSSH from MPD: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create FairPlay player configuration for iOS
     */
    public function createFairPlayPlayerConfig($hlsUrl = null, $contentId, $country = 'EG', $options = [])
    {
        try {
            Log::info('=== CREATING FAIRPLAY PLAYER CONFIG ===');
            Log::info('Content ID: ' . $contentId);

            // Get HLS URL if not provided
            if (!$hlsUrl) {
                Log::info('HLS URL not provided, fetching from Shahid API...');
                $streamData = $this->getShahidStreamUrl($contentId, $country);

                if (!$streamData['success']) {
                    Log::error('Failed to get HLS URL: ' . $streamData['error']);
                    return [
                        'success' => false,
                        'error' => 'Failed to get HLS URL: ' . $streamData['error']
                    ];
                }

                $hlsUrl = $streamData['hls_url'];
                Log::info('HLS URL fetched: ' . substr($hlsUrl, 0, 100) . '...');
            } else {
                Log::info('HLS URL provided: ' . substr($hlsUrl, 0, 100) . '...');
            }

            // Get FairPlay DRM info
            $drmInfo = $this->getFairPlayDRMInfo($contentId, $country);

            if (!$drmInfo['success']) {
                Log::error('Failed to get FairPlay DRM info: ' . $drmInfo['error']);
                return [
                    'success' => false,
                    'error' => 'Failed to get FairPlay DRM info: ' . $drmInfo['error']
                ];
            }

            // Build player configuration
            $config = [
                // Video source
                'file' => $hlsUrl,

                // FairPlay DRM configuration
                'drm' => [
                    'fairplay' => [
                        // Certificate URL
                        'certificateUrl' => $drmInfo['certificate_url'],

                        // License URL
                        'licenseUrl' => $drmInfo['license_url'],

                        // Content ID
                        'contentId' => $contentId,

                        // Certificate request headers
                        'certificateRequestHeaders' => [
                            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                            'Accept' => 'application/octet-stream',
                            'Origin' => 'https://shahid.mbc.net',
                            'Referer' => 'https://shahid.mbc.net/'
                        ],

                        // License request headers
                        'licenseRequestHeaders' => [
                            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                            'Content-Type' => 'application/octet-stream',
                            'Accept' => 'application/octet-stream',
                            'Origin' => 'https://shahid.mbc.net',
                            'Referer' => 'https://shahid.mbc.net/'
                        ]
                    ]
                ],

                // iOS optimizations
                'hlsjsdefault' => false, // Use native HLS on iOS
                'preload' => 'metadata',
                'autostart' => false,
                'mute' => true,
                'playsinline' => true,
                'controls' => true,

                // Disable unnecessary features
                'sharing' => false,
                'related' => false,
                'logo' => false,
                'abouttext' => '',
                'aboutlink' => '',

                // Custom styling for Shahid
                'skin' => [
                    'name' => 'shahid-ios',
                    'active' => '#ff6b35',
                    'inactive' => '#ffffff',
                    'background' => '#000000'
                ],

                // Merge additional options
                ...$options
            ];

            Log::info('FairPlay player config created successfully');
            Log::info('Certificate URL: ' . $drmInfo['certificate_url']);
            Log::info('License URL: ' . substr($drmInfo['license_url'], 0, 100) . '...');

            return [
                'success' => true,
                'config' => $config,
                'drm_info' => $drmInfo
            ];

        } catch (\Exception $e) {
            Log::error('FairPlay config creation error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create complete FairPlay setup (HLS + DRM) for content ID only
     */
    public function createCompleteFairPlaySetup($contentId, $country = 'EG', $options = [])
    {
        try {
            Log::info('🚀 === STEP 3: CREATING COMPLETE FAIRPLAY SETUP ===');
            Log::info('📝 Input Parameters:');
            Log::info('   - Content ID: ' . $contentId);
            Log::info('   - Country: ' . $country);
            Log::info('   - Options: ' . json_encode($options));
            Log::info('   - Timestamp: ' . now()->toISOString());

            // Step 1: Get HLS stream URL
            Log::info('Step 1: Getting HLS stream URL...');
            $streamData = $this->getShahidStreamUrl($contentId, $country);

            if (!$streamData['success']) {
                Log::error('Failed to get HLS URL: ' . $streamData['error']);
                return [
                    'success' => false,
                    'step' => 'stream_url',
                    'error' => 'Failed to get HLS URL: ' . $streamData['error']
                ];
            }

            $hlsUrl = $streamData['hls_url'];
            Log::info('✅ HLS URL obtained: ' . substr($hlsUrl, 0, 100) . '...');

            // Step 2: Get FairPlay DRM info
            Log::info('Step 2: Getting FairPlay DRM info...');
            $drmInfo = $this->getFairPlayDRMInfo($contentId, $country);

            if (!$drmInfo['success']) {
                Log::error('Failed to get FairPlay DRM info: ' . $drmInfo['error']);
                return [
                    'success' => false,
                    'step' => 'drm_info',
                    'error' => 'Failed to get FairPlay DRM info: ' . $drmInfo['error']
                ];
            }

            Log::info('✅ FairPlay DRM info obtained');

            // Step 3: Create player configuration
            Log::info('Step 3: Creating player configuration...');
            $playerConfig = $this->createFairPlayPlayerConfig($hlsUrl, $contentId, $country, $options);

            if (!$playerConfig['success']) {
                return [
                    'success' => false,
                    'step' => 'player_config',
                    'error' => $playerConfig['error']
                ];
            }

            Log::info('✅ Complete FairPlay setup created successfully');
            Log::info('=== FINAL HLS URL FOR COMPLETE SETUP ===');
            Log::info('HLS URL: ' . $hlsUrl);
            Log::info('URL Length: ' . strlen($hlsUrl) . ' characters');
            Log::info('=== END FINAL HLS URL ===');

            return [
                'success' => true,
                'hls_url' => $hlsUrl,
                'drm_info' => $drmInfo,
                'player_config' => $playerConfig['config'],
                'playout_data' => $streamData['playout_data'] ?? null,
                'setup_complete' => true,
                'message' => 'Complete FairPlay setup ready for iOS playback'
            ];

        } catch (\Exception $e) {
            Log::error('Complete FairPlay setup error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test FairPlay DRM with sample data
     */
    public function testFairPlayDRM($contentId = '4992343679206767', $country = 'EG')
    {
        try {
            Log::info('=== TESTING FAIRPLAY DRM ===');
            Log::info('Test Content ID: ' . $contentId);
            Log::info('Test Country: ' . $country);

            // Test getting DRM license
            $drmData = $this->getShahidDRMLicense($contentId, $country);

            if (!$drmData['success']) {
                return [
                    'success' => false,
                    'step' => 'drm_license',
                    'error' => $drmData['error']
                ];
            }

            // Test getting FairPlay info
            $fairPlayInfo = $this->getFairPlayDRMInfo($contentId, $country);

            if (!$fairPlayInfo['success']) {
                return [
                    'success' => false,
                    'step' => 'fairplay_info',
                    'error' => $fairPlayInfo['error']
                ];
            }

            // Test certificate URL
            $certificateTest = $this->testFairPlayCertificate($fairPlayInfo['certificate_url']);

            return [
                'success' => true,
                'drm_data' => $drmData['data'],
                'fairplay_info' => $fairPlayInfo,
                'certificate_test' => $certificateTest,
                'message' => 'FairPlay DRM test completed successfully'
            ];

        } catch (\Exception $e) {
            Log::error('FairPlay DRM test error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test FairPlay certificate URL
     */
    private function testFairPlayCertificate($certificateUrl)
    {
        try {
            if (!$certificateUrl) {
                return [
                    'success' => false,
                    'error' => 'No certificate URL provided'
                ];
            }

            Log::info('Testing FairPlay certificate: ' . $certificateUrl);

            $response = $this->proxyService->getHttpClient(['timeout' => 10])
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
                    'Accept' => 'application/octet-stream',
                    'Origin' => 'https://shahid.mbc.net',
                    'Referer' => 'https://shahid.mbc.net/'
                ])
                ->get($certificateUrl);

            if ($response->successful()) {
                $certificateSize = strlen($response->body());
                Log::info('Certificate downloaded successfully, size: ' . $certificateSize . ' bytes');

                return [
                    'success' => true,
                    'size' => $certificateSize,
                    'status' => $response->status()
                ];
            } else {
                Log::error('Certificate download failed: ' . $response->status());
                return [
                    'success' => false,
                    'error' => 'HTTP ' . $response->status(),
                    'status' => $response->status()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Certificate test error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}

