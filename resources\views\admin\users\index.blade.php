@extends('admin.layouts.app')

@section('title', 'User Registration')
@section('page-title', 'User Registration')

@section('breadcrumb')
<li class="breadcrumb-item">
    <a href="{{ route('admin.users.index') }}" style="color: #667eea; text-decoration: none;">Users</a>
</li>
<li class="breadcrumb-item active">Registration</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Register new users with subscription plans for API access.</p>
    </div>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
        <i class="fas fa-user-plus me-2"></i>Register New User
    </button>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <input type="text" class="form-control" id="searchInput" placeholder="Search by email, phone, or country...">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="countryFilter">
                    <option value="">All Countries</option>
                    <option value="EG">Egypt</option>
                    <option value="SA">Saudi Arabia</option>
                    <option value="AE">UAE</option>
                    <option value="KW">Kuwait</option>
                    <option value="QA">Qatar</option>
                    <option value="BH">Bahrain</option>
                    <option value="OM">Oman</option>
                    <option value="JO">Jordan</option>
                    <option value="LB">Lebanon</option>
                    <option value="IQ">Iraq</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="subscriptionFilter">
                    <option value="">All Subscriptions</option>
                    <option value="active">Active Subscription</option>
                    <option value="expired">Expired</option>
                    <option value="none">No Subscription</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-secondary" id="refreshBtn" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-outline-warning" id="clearFilters" title="Clear Filters">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="btn btn-outline-secondary" id="exportBtn" title="Export">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>User Info</th>
                        <th>Phone</th>
                        <th>Country</th>
                        <th>Status</th>
                        <th>Subscription</th>
                        <th>Access Status</th>
                        <th>Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Users pagination">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination will be loaded here -->
            </ul>
        </nav>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content border-0 shadow-lg">
            <!-- Modern Header with Gradient -->
            <div class="modal-header bg-gradient-primary text-white border-0 p-4">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                        <i class="fas fa-user-plus fa-lg"></i>
                    </div>
                    <div>
                        <h4 class="modal-title mb-0 fw-bold">Register New User</h4>
                        <small class="opacity-75">Create a new user account with subscription</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>

            <form id="createUserForm">
                <div class="modal-body p-4">
                    <!-- Progress Steps -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="step-item active" data-step="1">
                                    <div class="step-circle">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <span class="step-label">Personal Info</span>
                                </div>
                                <div class="step-line"></div>
                                <div class="step-item" data-step="2">
                                    <div class="step-circle">
                                        <i class="fas fa-crown"></i>
                                    </div>
                                    <span class="step-label">Subscription</span>
                                </div>
                                <div class="step-line"></div>
                                <div class="step-item" data-step="3">
                                    <div class="step-circle">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <span class="step-label">Confirmation</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Personal Information -->
                    <div class="step-content" id="step-1">
                        <div class="row g-4">
                            <div class="col-12">
                                <h5 class="text-primary fw-bold mb-3">
                                    <i class="fas fa-user-circle me-2"></i>Personal Information
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control form-control-lg" id="userName" name="name" placeholder="Full Name" required>
                                    <label for="userName">
                                        <i class="fas fa-user me-2 text-primary"></i>Full Name *
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control form-control-lg" id="userEmail" name="email" placeholder="Email Address" required>
                                    <label for="userEmail">
                                        <i class="fas fa-envelope me-2 text-primary"></i>Email Address *
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="password" class="form-control form-control-lg" id="userPassword" name="password" placeholder="Password" required>
                                    <label for="userPassword">
                                        <i class="fas fa-lock me-2 text-primary"></i>Password *
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>Minimum 6 characters required
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control form-control-lg" id="userPhone" name="phone" placeholder="Phone Number" required>
                                    <label for="userPhone">
                                        <i class="fas fa-phone me-2 text-primary"></i>Phone Number *
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select form-select-lg" id="userCountry" name="country" required>
                                        <option value="">Select Country</option>
                                        <option value="SA">🇸🇦 Saudi Arabia</option>
                                        <option value="AE">🇦🇪 United Arab Emirates</option>
                                        <option value="EG">🇪🇬 Egypt</option>
                                        <option value="JO">🇯🇴 Jordan</option>
                                        <option value="LB">🇱🇧 Lebanon</option>
                                        <option value="KW">🇰🇼 Kuwait</option>
                                        <option value="QA">🇶🇦 Qatar</option>
                                        <option value="BH">🇧🇭 Bahrain</option>
                                        <option value="OM">🇴🇲 Oman</option>
                                        <option value="IQ">🇮🇶 Iraq</option>
                                        <option value="SY">🇸🇾 Syria</option>
                                        <option value="YE">🇾🇪 Yemen</option>
                                        <option value="PS">🇵🇸 Palestine</option>
                                        <option value="LY">🇱🇾 Libya</option>
                                        <option value="TN">🇹🇳 Tunisia</option>
                                        <option value="DZ">🇩🇿 Algeria</option>
                                        <option value="MA">🇲🇦 Morocco</option>
                                        <option value="SD">🇸🇩 Sudan</option>
                                    </select>
                                    <label for="userCountry">
                                        <i class="fas fa-globe me-2 text-primary"></i>Country *
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select form-select-lg" id="userApplication" name="application">
                                        <option value="ALL">All Applications</option>
                                        <option value="SHAHID">Shahid Only</option>
                                        <option value="NETFLIX">Netflix Only</option>
                                    </select>
                                    <label for="userApplication">
                                        <i class="fas fa-tv me-2 text-primary"></i>Application Access
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Subscription -->
                    <div class="step-content d-none" id="step-2">
                        <div class="row g-4">
                            <div class="col-12">
                                <h5 class="text-warning fw-bold mb-3">
                                    <i class="fas fa-crown me-2"></i>Subscription Plan
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select form-select-lg" id="subscriptionPlan" name="subscription_plan_id" required>
                                        <option value="">Select Plan</option>
                                        <!-- Plans will be loaded dynamically -->
                                    </select>
                                    <label for="subscriptionPlan">
                                        <i class="fas fa-star me-2 text-warning"></i>Subscription Plan *
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control form-control-lg" id="subscriptionDuration" name="subscription_duration" min="1" max="365" value="30" placeholder="Duration" required>
                                    <label for="subscriptionDuration">
                                        <i class="fas fa-calendar-alt me-2 text-warning"></i>Duration (Days) *
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>Enter number of days (1-365)
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="card bg-light border-0">
                                    <div class="card-body">
                                        <h6 class="card-title text-info">
                                            <i class="fas fa-calculator me-2"></i>Subscription Summary
                                        </h6>
                                        <div id="subscriptionSummary" class="text-muted">
                                            Select a plan to see pricing details
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="userActive" name="is_active" checked>
                                    <label class="form-check-label fw-semibold" for="userActive">
                                        <i class="fas fa-toggle-on me-2 text-success"></i>Activate User Account
                                    </label>
                                    <div class="form-text">User will be able to access the system immediately</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Confirmation -->
                    <div class="step-content d-none" id="step-3">
                        <div class="row g-4">
                            <div class="col-12">
                                <h5 class="text-success fw-bold mb-3">
                                    <i class="fas fa-check-circle me-2"></i>Confirm Registration
                                </h5>
                            </div>

                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-check me-2"></i>User Details
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="confirmationDetails">
                                            <!-- Details will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Footer with Navigation -->
                <div class="modal-footer bg-light border-0 p-4">
                    <div class="d-flex justify-content-between w-100">
                        <button type="button" class="btn btn-outline-secondary px-4" id="prevBtn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </button>
                        <div class="ms-auto">
                            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-primary px-4" id="nextBtn">
                                Next<i class="fas fa-arrow-right ms-2"></i>
                            </button>
                            <button type="submit" class="btn btn-success px-4" id="submitBtn" style="display: none;">
                                <i class="fas fa-user-plus me-2"></i>Register User
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title fw-bold">
                    <i class="fas fa-user-edit me-2"></i>Edit User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm">
                <div class="modal-body p-4">
                    <input type="hidden" id="editUserId" name="user_id">

                    <!-- User Information Section -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-primary fw-bold mb-3">
                                <i class="fas fa-info-circle me-2"></i>User Information
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="editUserName" class="form-label fw-semibold">
                                <i class="fas fa-user me-1 text-primary"></i>Name *
                            </label>
                            <input type="text" class="form-control border-2" id="editUserName" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="editUserEmail" class="form-label fw-semibold">
                                <i class="fas fa-envelope me-1 text-info"></i>Email
                            </label>
                            <input type="email" class="form-control border-2" id="editUserEmail" name="email">
                        </div>
                        <div class="col-md-6">
                            <label for="editUserPassword" class="form-label fw-semibold">
                                <i class="fas fa-lock me-1 text-warning"></i>New Password
                            </label>
                            <input type="password" class="form-control border-2" id="editUserPassword" name="password" placeholder="Leave blank to keep current">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>Leave blank to keep current password
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="editUserApplication" class="form-label fw-semibold">
                                <i class="fas fa-mobile-alt me-1 text-success"></i>Application *
                            </label>
                            <select class="form-select border-2" id="editUserApplication" name="application" required>
                                <option value="ALL">All Applications</option>
                                <option value="SHAHID">Shahid Only</option>
                                <option value="NETFLIX">Netflix Only</option>
                            </select>
                        </div>
                    </div>

                    <!-- Subscription Section -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-success fw-bold mb-3">
                                <i class="fas fa-crown me-2"></i>Subscription Settings
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="editSubscriptionPlan" class="form-label fw-semibold">
                                <i class="fas fa-crown me-1 text-warning"></i>Subscription Plan
                            </label>
                            <select class="form-select border-2" id="editSubscriptionPlan" name="subscription_plan_id">
                                <option value="">No Subscription</option>
                                <!-- Plans will be loaded dynamically -->
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>Select a subscription plan for the user
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="editSubscriptionExpiry" class="form-label fw-semibold">
                                <i class="fas fa-calendar-alt me-1 text-warning"></i>Subscription Expiry
                            </label>
                            <input type="datetime-local" class="form-control border-2" id="editSubscriptionExpiry" name="subscription_expiry">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>Leave empty for unlimited subscription
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="editRemainingDays" class="form-label fw-semibold">
                                <i class="fas fa-clock me-1 text-info"></i>Remaining Days
                            </label>
                            <input type="number" class="form-control border-2" id="editRemainingDays" name="remaining_days" min="0" placeholder="Auto-calculated">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>Auto-calculated from expiry date
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-info-circle me-1 text-primary"></i>Current Plan Info
                            </label>
                            <div class="bg-light rounded-2 p-2 border">
                                <div id="currentPlanInfo" class="text-muted small">
                                    <i class="fas fa-spinner fa-spin me-1"></i>Loading current plan...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Section -->
                    <div class="row g-3">
                        <div class="col-12">
                            <h6 class="text-danger fw-bold mb-3">
                                <i class="fas fa-toggle-on me-2"></i>Account Status
                            </h6>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="editUserActive" name="is_active">
                                <label class="form-check-label fw-semibold" for="editUserActive">
                                    <i class="fas fa-user-check me-1 text-success"></i>Active User
                                </label>
                                <div class="form-text">Enable or disable user access to the system</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light border-0 p-3">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="fas fa-save me-2"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentPage = 1;
let currentFilters = {};

// Multi-step form variables - Global scope
let currentStep = 1;
const totalSteps = 3;

$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    loadUsers();
    loadSubscriptionPlans();
    
    // Search and filter events
    $('#searchInput').on('input', debounce(function() {
        currentPage = 1;
        loadUsers();
    }, 500));
    
    $('#statusFilter, #countryFilter, #subscriptionFilter').on('change', function() {
        currentPage = 1;
        loadUsers();
    });

    $('#refreshBtn').on('click', function() {
        loadUsers();
    });

    // Clear filters button
    $('#clearFilters').on('click', function() {
        $('#searchInput').val('');
        $('#statusFilter').val('');
        $('#countryFilter').val('');
        $('#subscriptionFilter').val('');
        currentPage = 1;
        loadUsers();
    });
    
    // Form submissions
    $('#createUserForm').on('submit', function(e) {
        e.preventDefault();
        createUser();
    });
    
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        updateUser();
    });

    // Auto-calculate remaining days when subscription expiry changes
    $(document).on('change', '#editSubscriptionExpiry', function() {
        const expiryDate = $(this).val();
        if (expiryDate) {
            const expiry = new Date(expiryDate);
            const now = new Date();
            const diffTime = expiry - now;
            const diffDays = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
            $('#editRemainingDays').val(diffDays);
        } else {
            $('#editRemainingDays').val(0);
        }
    });

    // Update plan info when plan selection changes
    $(document).on('change', '#editSubscriptionPlan', function() {
        updatePlanInfo();
    });

    // Load subscription plans on page load
    loadSubscriptionPlans();

    // Next button click
    $('#nextBtn').on('click', function() {
        if (validateStep(currentStep)) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
                updateStepProgress();
            }
        }
    });

    // Previous button click
    $('#prevBtn').on('click', function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
            updateStepProgress();
        }
    });

    // Reset form when modal is closed
    $('#createUserModal').on('hidden.bs.modal', function() {
        currentStep = 1;
        showStep(currentStep);
        updateStepProgress();
        $('#createUserForm')[0].reset();
        $('#subscriptionSummary').html('Select a plan to see pricing details');
        $('#confirmationDetails').html('');
    });

    // Load subscription plans when modal is opened
    $('#createUserModal').on('shown.bs.modal', function() {
        if ($('#subscriptionPlan option').length <= 1) {
            loadSubscriptionPlansForCreate();
        }
    });

    // Update subscription summary when plan or duration changes
    $('#subscriptionPlan, #subscriptionDuration').on('change', function() {
        updateSubscriptionSummary();
    });

    // Auto-update duration when plan changes
    $('#subscriptionPlan').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const duration = selectedOption.data('duration');

        console.log('Plan changed, duration:', duration);

        if (duration) {
            $('#subscriptionDuration').val(duration);
            console.log('Duration updated to:', duration);
            updateSubscriptionSummary();
        }
    });
});

function loadUsers() {
    const filters = {
        page: currentPage
    };

    // Only add non-empty filters
    const search = $('#searchInput').val();
    if (search && search.trim() !== '') {
        filters.search = search.trim();
    }

    const status = $('#statusFilter').val();
    if (status && status !== '') {
        filters.status = status;
    }

    const country = $('#countryFilter').val();
    if (country && country !== '') {
        filters.country = country;
    }

    const subscriptionStatus = $('#subscriptionFilter').val();
    if (subscriptionStatus && subscriptionStatus !== '') {
        filters.subscription_status = subscriptionStatus;
    }

    currentFilters = filters;

    // Show loading state
    $('#usersTableBody').html(`
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="loading-spinner me-2"></div>
                Loading users...
            </td>
        </tr>
    `);

    $.get('/admin/ajax/users', filters)
        .done(function(response) {
            if (response.success) {
                renderUsersTable(response.data.data);
                renderPagination(response.data);
            }
        })
        .fail(function() {
            $('#usersTableBody').html('<tr><td colspan="8" class="text-center text-danger">Failed to load users</td></tr>');
        });
}

function loadSubscriptionPlans() {
    $.get('/admin/ajax/subscription-plans', { per_page: 1000 })
        .done(function(response) {
            if (response.success) {
                let options = '<option value="">Select Subscription Plan</option>';
                response.data.data.forEach(plan => {
                    options += `<option value="${plan.id}">${plan.name} (${plan.currency} ${plan.price})</option>`;
                });
                $('#subscriptionPlan').html(options);
            }
        });
}

function renderUsersTable(users) {
    let html = '';
    
    if (users.length === 0) {
        html = '<tr><td colspan="8" class="text-center text-muted">No users found</td></tr>';
    } else {
        users.forEach(user => {
            const statusBadge = user.is_active ? 
                '<span class="badge bg-success">Active</span>' : 
                '<span class="badge bg-secondary">Inactive</span>';
            
            const typeBadge = user.user_type === 'admin' ? 
                '<span class="badge bg-warning">Admin</span>' : 
                '<span class="badge bg-primary">User</span>';
            
            let subscriptionInfo = 'No Subscription';
            if (user.active_subscription) {
                const plan = user.active_subscription.subscription_plan;
                subscriptionInfo = `<div class="small fw-bold">${plan.name}</div>
                                   <div class="text-muted small">${plan.price} ${plan.currency}</div>`;
            } else if (user.subscriptions && user.subscriptions.length > 0) {
                // Show latest subscription if no active one
                const latestSub = user.subscriptions[0];
                if (latestSub.subscription_plan) {
                    const plan = latestSub.subscription_plan;
                    subscriptionInfo = `<div class="small fw-bold text-muted">${plan.name} (Expired)</div>
                                       <div class="text-muted small">${plan.price} ${plan.currency}</div>`;
                }
            }
            
            const countryName = getCountryName(user.country);

            html += `
                <tr>
                    <td>
                        <div class="fw-bold">${user.name}</div>
                        <div class="text-muted small">${user.email}</div>
                    </td>
                    <td>${user.phone || 'N/A'}</td>
                    <td>
                        <span class="badge bg-info">${countryName}</span>
                    </td>
                    <td>${statusBadge}</td>
                    <td>${subscriptionInfo}</td>
                    <td>
                        <div class="small">
                            <div class="fw-bold text-success">API Enabled</div>
                            <div class="text-muted">Email/Password Auth</div>
                        </div>
                    </td>
                    <td>${new Date(user.created_at).toLocaleDateString()}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-primary rounded-start" onclick="editUser(${user.id})" title="Edit User">
                                <i class="fas fa-edit me-1"></i>Edit
                            </button>
                            <button class="btn btn-outline-${user.is_active ? 'warning' : 'success'}"
                                    onclick="toggleUserStatus(${user.id})"
                                    title="${user.is_active ? 'Deactivate User' : 'Activate User'}">
                                <i class="fas fa-${user.is_active ? 'pause' : 'play'} me-1"></i>
                                ${user.is_active ? 'Deactivate' : 'Activate'}
                            </button>
                            <button class="btn btn-outline-danger rounded-end" onclick="deleteUser(${user.id})" title="Delete User">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#usersTableBody').html(html);
}

function renderPagination(data) {
    let html = '';
    
    if (data.last_page > 1) {
        // Previous button
        html += `<li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.current_page - 1})">Previous</a>
                 </li>`;
        
        // Page numbers
        for (let i = 1; i <= data.last_page; i++) {
            if (i === data.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else if (i === 1 || i === data.last_page || (i >= data.current_page - 2 && i <= data.current_page + 2)) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
            } else if (i === data.current_page - 3 || i === data.current_page + 3) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        // Next button
        html += `<li class="page-item ${data.current_page === data.last_page ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.current_page + 1})">Next</a>
                 </li>`;
    }
    
    $('#pagination').html(html);
}

function changePage(page) {
    currentPage = page;
    loadUsers();
}

function createUser() {
    // Validate all steps before submission
    let allValid = true;
    for (let step = 1; step <= totalSteps - 1; step++) {
        if (!validateStep(step)) {
            allValid = false;
            // Go back to the first invalid step
            currentStep = step;
            showStep(currentStep);
            updateStepProgress();
            break;
        }
    }

    if (!allValid) {
        return;
    }

    const formData = new FormData($('#createUserForm')[0]);

    // Add CSRF token
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

    // Convert checkbox to proper boolean
    const isActive = $('#userActive').is(':checked');
    formData.set('is_active', isActive ? '1' : '0');

    // Show loading state
    const submitBtn = $('#submitBtn');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Creating User...');

    console.log('Creating user with data:', Object.fromEntries(formData));

    $.ajax({
        url: '/admin/ajax/users',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('Create user response:', response);

            if (response.success) {
                $('#createUserModal').modal('hide');
                $('#createUserForm')[0].reset();

                // Reset form state
                currentStep = 1;
                showStep(currentStep);
                updateStepProgress();

                loadUsers();
                showAlert('success', response.message || 'User created successfully!');
            } else {
                showAlert('error', response.message || 'Failed to create user');
            }
        },
        error: function(xhr) {
            console.error('Create user error:', xhr);

            let errorMessage = 'Failed to create user';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                if (xhr.responseJSON.errors) {
                    showFormErrors('createUserForm', xhr.responseJSON.errors);

                    // Go to the step with errors
                    const errorFields = Object.keys(xhr.responseJSON.errors);
                    if (errorFields.some(field => ['name', 'email', 'password', 'phone', 'country'].includes(field))) {
                        currentStep = 1;
                    } else if (errorFields.some(field => ['subscription_plan_id', 'subscription_duration'].includes(field))) {
                        currentStep = 2;
                    }
                    showStep(currentStep);
                    updateStepProgress();
                    return;
                }
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred. Please try again.';
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to create users.';
            } else if (xhr.status === 422) {
                errorMessage = 'Please check your input data.';
            }

            showAlert('error', errorMessage);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function editUser(userId) {
    // Show loading state
    const loadingHtml = '<div class="text-center p-3"><div class="loading-spinner me-2"></div>Loading user data...</div>';

    $.get(`/admin/ajax/users/${userId}`)
        .done(function(response) {
            if (response.success) {
                const user = response.data;
                $('#editUserId').val(user.id);

                // Basic user info
                $('#editUserName').val(user.name || '');
                $('#editUserEmail').val(user.email || '');
                $('#editUserApplication').val(user.application || 'ALL');
                $('#editUserActive').prop('checked', user.is_active || false);

                // Subscription info
                if (user.subscription_expiry) {
                    try {
                        // Convert to local datetime format for input
                        const expiryDate = new Date(user.subscription_expiry);
                        if (!isNaN(expiryDate.getTime())) {
                            const localDateTime = new Date(expiryDate.getTime() - expiryDate.getTimezoneOffset() * 60000)
                                .toISOString().slice(0, 16);
                            $('#editSubscriptionExpiry').val(localDateTime);
                        } else {
                            $('#editSubscriptionExpiry').val('');
                        }
                    } catch (e) {
                        console.warn('Error parsing subscription expiry date:', e);
                        $('#editSubscriptionExpiry').val('');
                    }
                } else {
                    $('#editSubscriptionExpiry').val('');
                }

                $('#editRemainingDays').val(user.remaining_days || 0);

                // Set current subscription plan
                let currentPlanId = '';
                let currentPlanInfo = 'No active subscription';

                // Check for active subscription
                if (user.active_subscription && user.active_subscription.subscription_plan) {
                    currentPlanId = user.active_subscription.subscription_plan_id;
                    const plan = user.active_subscription.subscription_plan;
                    currentPlanInfo = `
                        <div class="d-flex align-items-center">
                            <i class="fas fa-crown text-warning me-2"></i>
                            <div>
                                <div class="fw-semibold text-primary">${plan.name}</div>
                                <small class="text-muted">${plan.price} ${plan.currency} / ${plan.duration_days} days</small>
                            </div>
                        </div>
                    `;
                } else if (user.subscriptions && user.subscriptions.length > 0) {
                    // Get the latest subscription
                    const latestSub = user.subscriptions[0];
                    if (latestSub.subscription_plan) {
                        currentPlanId = latestSub.subscription_plan_id;
                        const plan = latestSub.subscription_plan;
                        currentPlanInfo = `
                            <div class="d-flex align-items-center">
                                <i class="fas fa-history text-secondary me-2"></i>
                                <div>
                                    <div class="fw-semibold text-secondary">${plan.name} (Expired)</div>
                                    <small class="text-muted">${plan.price} ${plan.currency} / ${plan.duration_days} days</small>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // Check user_info for plan information
                    try {
                        const userInfo = typeof user.user_info === 'string' ? JSON.parse(user.user_info) : user.user_info;
                        if (userInfo && (userInfo.subscription_plan || userInfo.plan)) {
                            const planName = userInfo.subscription_plan || userInfo.plan;
                            currentPlanInfo = `
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    <div>
                                        <div class="fw-semibold text-info">${planName}</div>
                                        <small class="text-muted">Legacy subscription</small>
                                    </div>
                                </div>
                            `;
                        }
                    } catch (e) {
                        console.warn('Error parsing user_info:', e);
                    }
                }

                $('#editSubscriptionPlan').val(currentPlanId);
                $('#currentPlanInfo').html(currentPlanInfo);

                // Ensure subscription plans are loaded
                if ($('#editSubscriptionPlan option').length <= 1) {
                    loadSubscriptionPlans();
                    // Wait a bit for plans to load, then set the value
                    setTimeout(() => {
                        $('#editSubscriptionPlan').val(currentPlanId);
                    }, 500);
                }

                $('#editUserModal').modal('show');
            } else {
                showAlert('error', response.message || 'Failed to load user data');
            }
        })
        .fail(function(xhr) {
            console.error('Error loading user data:', xhr);
            let errorMessage = 'Failed to load user data';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred. Please try again.';
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to view this user.';
            } else if (xhr.status === 404) {
                errorMessage = 'User not found.';
            }

            showAlert('error', errorMessage);
        });
}

function loadSubscriptionPlans() {
    $.get('/admin/ajax/subscription-plans-select')
        .done(function(response) {
            console.log('Subscription plans response:', response);

            const select = $('#editSubscriptionPlan');
            select.empty();
            select.append('<option value="">No Subscription</option>');

            if (response.success && Array.isArray(response.data)) {
                if (response.data.length > 0) {
                    response.data.forEach(function(plan) {
                        select.append(`
                            <option value="${plan.id}" data-price="${plan.price}" data-currency="${plan.currency}" data-duration="${plan.duration_days}">
                                ${plan.name} - ${plan.price} ${plan.currency} (${plan.duration_days} days)
                            </option>
                        `);
                    });
                } else {
                    console.warn('No subscription plans found');
                    addDefaultPlans(select);
                }
            } else {
                console.warn('API response not successful or invalid format:', response);
                addDefaultPlans(select);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load subscription plans:', xhr);

            const select = $('#editSubscriptionPlan');
            select.empty();
            select.append('<option value="">No Subscription</option>');
            addDefaultPlans(select);

            // Show user-friendly message
            if (xhr.status === 403) {
                console.warn('Permission denied for subscription plans');
            } else if (xhr.status === 500) {
                console.error('Server error loading subscription plans');
            }
        });
}

function addDefaultPlans(select) {
    select.append('<option value="basic">باقة شاهد الأساسية</option>');
    select.append('<option value="premium">باقة شاهد المميزة</option>');
    select.append('<option value="gold">باقة شاهد الذهبية</option>');
}

function loadSubscriptionPlansForCreate() {
    $.get('/admin/ajax/subscription-plans-select')
        .done(function(response) {
            console.log('Subscription plans for create:', response);

            const select = $('#subscriptionPlan');
            select.empty();
            select.append('<option value="">Select Plan</option>');

            if (response.success && Array.isArray(response.data)) {
                if (response.data.length > 0) {
                    response.data.forEach(function(plan) {
                        select.append(`
                            <option value="${plan.id}" data-price="${plan.price}" data-currency="${plan.currency}" data-duration="${plan.duration_days}">
                                ${plan.name} - ${plan.price} ${plan.currency} (${plan.duration_days} days)
                            </option>
                        `);
                    });
                } else {
                    console.warn('No subscription plans found for create');
                    addDefaultPlansForCreate(select);
                }
            } else {
                console.warn('API response not successful for create:', response);
                addDefaultPlansForCreate(select);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load subscription plans for create:', xhr);

            const select = $('#subscriptionPlan');
            select.empty();
            select.append('<option value="">Select Plan</option>');
            addDefaultPlansForCreate(select);
        });
}

function addDefaultPlansForCreate(select) {
    select.append('<option value="basic">باقة شاهد الأساسية - 30 أيام</option>');
    select.append('<option value="premium">باقة شاهد المميزة - 60 أيام</option>');
    select.append('<option value="gold">باقة شاهد الذهبية - 90 أيام</option>');
}

// Multi-step form functions
function showStep(step) {
    // Hide all steps
    $('.step-content').addClass('d-none');

    // Show current step
    $(`#step-${step}`).removeClass('d-none');

    // Update buttons
    if (step === 1) {
        $('#prevBtn').hide();
        $('#nextBtn').show();
        $('#submitBtn').hide();
    } else if (step === totalSteps) {
        $('#prevBtn').show();
        $('#nextBtn').hide();
        $('#submitBtn').show();
        updateConfirmationDetails();
    } else {
        $('#prevBtn').show();
        $('#nextBtn').show();
        $('#submitBtn').hide();
    }
}

function updateStepProgress() {
    $('.step-item').each(function(index) {
        const stepNumber = index + 1;
        const $this = $(this);

        if (stepNumber < currentStep) {
            $this.addClass('completed').removeClass('active');
        } else if (stepNumber === currentStep) {
            $this.addClass('active').removeClass('completed');
        } else {
            $this.removeClass('active completed');
        }
    });

    // Update step lines
    $('.step-line').each(function(index) {
        const $this = $(this);
        if (index + 1 < currentStep) {
            $this.addClass('completed');
        } else {
            $this.removeClass('completed');
        }
    });
}

function validateStep(step) {
    let isValid = true;

    if (step === 1) {
        // Validate personal information
        const requiredFields = ['userName', 'userEmail', 'userPassword', 'userPhone', 'userCountry'];
        requiredFields.forEach(fieldId => {
            const field = $(`#${fieldId}`);
            if (!field.val().trim()) {
                field.addClass('is-invalid');
                isValid = false;
            } else {
                field.removeClass('is-invalid');
            }
        });

        // Validate email format
        const email = $('#userEmail').val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $('#userEmail').addClass('is-invalid');
            isValid = false;
        }

        // Validate password length
        const password = $('#userPassword').val();
        if (password && password.length < 6) {
            $('#userPassword').addClass('is-invalid');
            isValid = false;
        }
    } else if (step === 2) {
        // Validate subscription information
        const requiredFields = ['subscriptionPlan', 'subscriptionDuration'];
        requiredFields.forEach(fieldId => {
            const field = $(`#${fieldId}`);
            if (!field.val()) {
                field.addClass('is-invalid');
                isValid = false;
            } else {
                field.removeClass('is-invalid');
            }
        });

        // Validate duration range
        const duration = parseInt($('#subscriptionDuration').val());
        if (duration && (duration < 1 || duration > 365)) {
            $('#subscriptionDuration').addClass('is-invalid');
            isValid = false;
        }
    }

    if (!isValid) {
        showAlert('error', 'Please fill in all required fields correctly.');
    }

    return isValid;
}

function updateSubscriptionSummary() {
    const planSelect = $('#subscriptionPlan');
    const duration = $('#subscriptionDuration').val();

    if (planSelect.val() && duration) {
        const selectedOption = planSelect.find('option:selected');
        const planName = selectedOption.text();

        const summary = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Selected Plan:</strong><br>
                    <span class="text-primary">${planName}</span>
                </div>
                <div class="col-md-6">
                    <strong>Duration:</strong><br>
                    <span class="text-warning">${duration} days</span>
                </div>
            </div>
        `;

        $('#subscriptionSummary').html(summary);
    } else {
        $('#subscriptionSummary').html('Select a plan and duration to see details');
    }
}

function updateConfirmationDetails() {
    const formData = {
        name: $('#userName').val(),
        email: $('#userEmail').val(),
        phone: $('#userPhone').val(),
        country: $('#userCountry option:selected').text(),
        application: $('#userApplication option:selected').text(),
        plan: $('#subscriptionPlan option:selected').text(),
        duration: $('#subscriptionDuration').val(),
        active: $('#userActive').is(':checked')
    };

    // Calculate expiry date
    const startDate = new Date();
    const expiryDate = new Date(startDate);
    expiryDate.setDate(startDate.getDate() + parseInt(formData.duration || 0));
    const formattedExpiryDate = expiryDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    const details = `
        <div class="row g-3">
            <div class="col-md-6">
                <strong><i class="fas fa-user me-2 text-primary"></i>Name:</strong><br>
                <span>${formData.name}</span>
            </div>
            <div class="col-md-6">
                <strong><i class="fas fa-envelope me-2 text-primary"></i>Email:</strong><br>
                <span>${formData.email}</span>
            </div>
            <div class="col-md-6">
                <strong><i class="fas fa-phone me-2 text-primary"></i>Phone:</strong><br>
                <span>${formData.phone}</span>
            </div>
            <div class="col-md-6">
                <strong><i class="fas fa-globe me-2 text-primary"></i>Country:</strong><br>
                <span>${formData.country}</span>
            </div>
            <div class="col-md-6">
                <strong><i class="fas fa-tv me-2 text-primary"></i>Application:</strong><br>
                <span>${formData.application}</span>
            </div>
            <div class="col-md-6">
                <strong><i class="fas fa-crown me-2 text-warning"></i>Subscription:</strong><br>
                <span>${formData.plan} (${formData.duration} days)</span>
            </div>
            <div class="col-md-6">
                <strong><i class="fas fa-calendar-times me-2 text-danger"></i>Expires On:</strong><br>
                <span class="text-danger fw-bold">${formattedExpiryDate}</span>
            </div>
            <div class="col-12">
                <strong><i class="fas fa-toggle-on me-2 text-success"></i>Status:</strong><br>
                <span class="badge ${formData.active ? 'bg-success' : 'bg-secondary'}">
                    ${formData.active ? 'Active' : 'Inactive'}
                </span>
            </div>
        </div>
    `;

    $('#confirmationDetails').html(details);
}

function updatePlanInfo() {
    const selectedOption = $('#editSubscriptionPlan option:selected');
    const planId = selectedOption.val();

    if (planId) {
        const planName = selectedOption.text();
        const price = selectedOption.data('price') || 'N/A';
        const currency = selectedOption.data('currency') || '';
        const duration = selectedOption.data('duration') || 'N/A';

        const planInfo = `
            <div class="d-flex align-items-center">
                <i class="fas fa-crown text-warning me-2"></i>
                <div>
                    <div class="fw-semibold text-primary">${selectedOption.text()}</div>
                    <small class="text-muted">Selected plan</small>
                </div>
            </div>
        `;

        $('#currentPlanInfo').html(planInfo);

        // Auto-set expiry date if duration is available
        if (duration && duration !== 'N/A') {
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + parseInt(duration));
            const localDateTime = new Date(expiryDate.getTime() - expiryDate.getTimezoneOffset() * 60000)
                .toISOString().slice(0, 16);
            $('#editSubscriptionExpiry').val(localDateTime);
            $('#editRemainingDays').val(duration);
        }
    } else {
        $('#currentPlanInfo').html('<span class="text-muted">No subscription selected</span>');
        $('#editSubscriptionExpiry').val('');
        $('#editRemainingDays').val(0);
    }
}

function updateUser() {
    console.log('🔄 updateUser function called');

    const userId = $('#editUserId').val();
    console.log('👤 User ID:', userId);
    console.log('📝 Form exists:', $('#editUserForm').length);

    if (!userId) {
        console.error('❌ User ID is missing');
        showAlert('error', 'User ID is missing');
        return;
    }

    const formData = new FormData($('#editUserForm')[0]);

    // Add CSRF token
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

    // Convert checkbox values
    const isActive = $('#editUserActive').is(':checked');
    formData.set('is_active', isActive ? '1' : '0');

    // Show loading state
    const submitBtn = $('#editUserForm button[type="submit"]');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Updating...');

    console.log('Updating user:', userId);
    console.log('Form data:', Object.fromEntries(formData));

    $.ajax({
        url: `/admin/ajax/users/${userId}`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-HTTP-Method-Override': 'PUT',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('Update response:', response);

            if (response.success) {
                $('#editUserModal').modal('hide');
                loadUsers();
                showAlert('success', response.message || 'User updated successfully!');
            } else {
                showAlert('error', response.message || 'Failed to update user');
            }
        },
        error: function(xhr) {
            console.error('Update error:', xhr);

            let errorMessage = 'Failed to update user';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                if (xhr.responseJSON.errors) {
                    showFormErrors('editUserForm', xhr.responseJSON.errors);
                    return;
                }
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred. Please try again.';
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to update this user.';
            } else if (xhr.status === 404) {
                errorMessage = 'User not found.';
            } else if (xhr.status === 422) {
                errorMessage = 'Please check your input data.';
            }

            showAlert('error', errorMessage);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function toggleUserStatus(userId) {
    if (confirm('Are you sure you want to change this user\'s status?')) {
        $.post(`/admin/ajax/users/${userId}/toggle-status`)
            .done(function(response) {
                if (response.success) {
                    loadUsers();
                    showAlert('success', response.message);
                }
            })
            .fail(function() {
                showAlert('error', 'Failed to update user status');
            });
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        $.ajax({
            url: `/admin/ajax/users/${userId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    loadUsers();
                    showAlert('success', 'User deleted successfully!');
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to delete user';
                showAlert('error', message);
            }
        });
    }
}

function showAlert(type, message) {
    console.log(`🔔 Showing alert: ${type} - ${message}`);

    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    const icon = type === 'success' ? 'fa-check-circle' :
                 type === 'error' ? 'fa-exclamation-circle' :
                 type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="margin-bottom: 20px;">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts first
    $('.alert').remove();

    // Try multiple selectors to find the right container
    if ($('.main-content').length) {
        $('.main-content').prepend(alert);
    } else if ($('.content-area').length) {
        $('.content-area').prepend(alert);
    } else if ($('.container-fluid').length) {
        $('.container-fluid').prepend(alert);
    } else {
        $('body').prepend(alert);
    }

    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut(500, function() {
            $(this).remove();
        });
    }, 5000);
}

function showFormErrors(formId, errors) {
    // Clear previous errors
    $(`#${formId} .is-invalid`).removeClass('is-invalid');
    $(`#${formId} .invalid-feedback`).remove();
    
    // Show new errors
    Object.keys(errors).forEach(field => {
        const input = $(`#${formId} [name="${field}"]`);
        input.addClass('is-invalid');
        input.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
    });
}

function getCountryName(countryCode) {
    const countries = {
        'EG': 'Egypt',
        'SA': 'Saudi Arabia',
        'AE': 'UAE',
        'KW': 'Kuwait',
        'QA': 'Qatar',
        'BH': 'Bahrain',
        'OM': 'Oman',
        'JO': 'Jordan',
        'LB': 'Lebanon',
        'IQ': 'Iraq'
    };
    return countries[countryCode] || countryCode;
}



function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
@endsection

@push('styles')
<style>
/* Custom Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Modal Enhancements */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    border-radius: 15px 15px 0 0 !important;
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: 8px 0 0 8px;
}

.btn-group .btn:last-child {
    border-radius: 0 8px 8px 0;
}

.btn-group .btn:only-child {
    border-radius: 8px;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Form Sections */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Table Enhancements */
.table th {
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 6px;
}

/* Step Progress Styles */
.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
}

.step-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border: 3px solid #e9ecef;
}

.step-item.active .step-circle {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: scale(1.1);
}

.step-item.completed .step-circle {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.step-label {
    margin-top: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    transition: color 0.3s ease;
}

.step-item.active .step-label {
    color: #667eea;
    font-weight: 600;
}

.step-item.completed .step-label {
    color: #28a745;
    font-weight: 600;
}

.step-line {
    height: 3px;
    background: #e9ecef;
    flex: 1;
    margin: 0 10px;
    margin-top: 25px;
    transition: background 0.3s ease;
}

.step-line.completed {
    background: #28a745;
}

/* Form Floating Enhancements */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    color: #667eea;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Card Animations */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Step Content Animation */
.step-content {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .btn-group .btn {
        font-size: 0.875rem;
        padding: 6px 12px;
    }

    .modal-dialog {
        margin: 1rem;
    }

    .step-item {
        margin-bottom: 1rem;
    }

    .step-line {
        display: none;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-label {
        font-size: 0.75rem;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.75rem;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.alert {
    border: none;
    border-radius: 8px;
}

pre {
    font-size: 0.875rem;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm > .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
@endpush
