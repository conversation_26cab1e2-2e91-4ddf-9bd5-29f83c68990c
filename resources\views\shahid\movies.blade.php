@extends('layouts.app')

@section('title', 'Movies - Shahid Play')

@section('styles')
<style>
.bg-gradient-dark {
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    transition: all 0.3s ease;
}

.movie-poster-container:hover .bg-gradient-dark {
    background: linear-gradient(to top, rgba(255, 107, 107, 0.8), transparent);
}

.movie-poster-container:hover .bg-gradient-dark h6 {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    transform: translateY(-2px);
}

.movie-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border-radius: 10px;
    overflow: hidden;
    background: #fff;
    height: auto;
}

.movie-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.movie-card img {
    transition: transform 0.3s ease;
}

.movie-card:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.1);
}

.movie-poster-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 2/3;
}

.movie-poster-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.movie-info-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    color: white;
    z-index: 2;
}

.movie-info-overlay h6 {
    margin: 0;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.movie-year {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 4px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.country-selector {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.country-btn {
    padding: 8px 16px;
    border: 2px solid #dee2e6;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.country-btn:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.country-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.refresh-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cache-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 12px;
    margin-top: 15px;
    font-size: 14px;
}

.cache-info.cached {
    background: #e8f5e8;
    border-color: #c8e6c9;
    color: #2e7d32;
}

.cache-info.fresh {
    background: #fff3e0;
    border-color: #ffcc02;
    color: #f57c00;
}

.movie-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .movie-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .country-selector {
        justify-content: center;
    }
}

.no-movies {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-movies i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
}

.search-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    font-size: 14px;
}

.search-indicator.searching {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Proxy Status: Active (check logs for details) -->

    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🎬 Shahid Movies</h2>
                    <p class="text-muted mb-0">Discover and watch your favorite movies</p>
                    <!-- Proxy Status -->
                    <div class="mt-2">
                        <span id="proxyStatus" class="badge bg-secondary">
                            <i class="fas fa-spinner fa-spin me-1"></i>Checking proxy status...
                        </span>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn refresh-btn" onclick="refreshMoviesCache()" id="refreshBtn">
                        <i class="fas fa-sync-alt me-2"></i>Refresh Cache
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-number" id="totalMovies">-</div>
                <div class="stats-label">Total Movies</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="stats-number" id="currentCountry">EG</div>
                <div class="stats-label">Current Country</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="stats-number" id="cacheStatus">-</div>
                <div class="stats-label">Cache Status</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="stats-number" id="loadTime">-</div>
                <div class="stats-label">Load Time</div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label fw-bold">Country Selection:</label>
                <div class="country-selector">
                    <button class="country-btn active" data-country="EG">🇪🇬 Egypt</button>
                    <button class="country-btn" data-country="SA">🇸🇦 Saudi Arabia</button>
                    <button class="country-btn" data-country="AE">🇦🇪 UAE</button>
                    <button class="country-btn" data-country="KW">🇰🇼 Kuwait</button>
                </div>
            </div>
            <div class="col-md-6">
                <label class="form-label fw-bold">Search Movies:</label>
                <div class="position-relative">
                    <input type="text" class="form-control" id="searchInput" placeholder="Search by title, year, or genre...">
                    <div class="search-indicator" id="searchIndicator">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cache Info -->
        <div class="cache-info" id="cacheInfo" style="display: none;">
            <i class="fas fa-info-circle me-2"></i>
            <span id="cacheInfoText">Loading cache information...</span>
        </div>
    </div>

    <!-- Movies Grid -->
    <div class="movie-grid" id="moviesGrid">
        <!-- Movies will be loaded here -->
    </div>

    <!-- Loading State -->
    <div class="text-center py-5" id="loadingState">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Loading movies...</p>
    </div>

    <!-- No Movies State -->
    <div class="no-movies" id="noMoviesState" style="display: none;">
        <i class="fas fa-film"></i>
        <h4>No Movies Found</h4>
        <p>Try selecting a different country or refreshing the cache.</p>
    </div>

    <!-- Error State -->
    <div class="error-message" id="errorState" style="display: none;">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <span id="errorMessage">An error occurred while loading movies.</span>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Global variables
let currentCountry = 'EG';
let allMovies = [];
let filteredMovies = [];

// Initialize when page loads
$(document).ready(function() {
    console.log('🎬 Movies page loaded, initializing...');

    // Load movies for default country
    loadMovies(currentCountry);

    // Check proxy status
    checkProxyStatus();

    // Setup country selector
    $('.country-btn').click(function() {
        const country = $(this).data('country');

        // Update active state
        $('.country-btn').removeClass('active');
        $(this).addClass('active');

        // Update current country and load movies
        currentCountry = country;
        loadMovies(country);
    });

    // Setup search functionality
    let searchTimeout;
    $('#searchInput').on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().toLowerCase().trim();

        // Show search indicator
        $('#searchIndicator').addClass('searching');

        searchTimeout = setTimeout(() => {
            filterMovies(query);
            $('#searchIndicator').removeClass('searching');
        }, 300);
    });
});

/**
 * Refresh movies cache
 */
function refreshMoviesCache() {
    const refreshBtn = document.getElementById('refreshBtn');
    const originalText = refreshBtn.innerHTML;

    // Disable button and show loading
    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';

    $.ajax({
        url: '/shahid/api/movies-cache/refresh',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', `Movies cache refreshed successfully! Total: ${response.total_movies} movies`);
                // Reload movies with fresh data
                loadMovies(currentCountry, true);
            } else {
                showAlert('danger', response.message || 'Failed to refresh cache');
            }
        },
        error: function(xhr) {
            console.error('Error refreshing cache:', xhr);
            showAlert('danger', 'Error refreshing cache');
        },
        complete: function() {
            // Re-enable button
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = originalText;
        }
    });
}

/**
 * Load movies with cache support
 */
function loadMovies(country = 'EG', forceRefresh = false) {
    const startTime = Date.now();

    // Show loading state
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('moviesGrid').style.display = 'none';
    document.getElementById('noMoviesState').style.display = 'none';
    document.getElementById('errorState').style.display = 'none';

    const params = {
        country: country,
        limit: 999 // Get all movies
    };

    if (forceRefresh) {
        params.refresh = true;
    }

    $.ajax({
        url: '/shahid/api/movies',
        method: 'GET',
        data: params,
        success: function(response) {
            const loadTime = ((Date.now() - startTime) / 1000).toFixed(1);

            if (response.success && response.data) {
                displayMovies(response.data);
                updateStats(response.total_movies, country, response.cached ? 'Cached' : 'Fresh', loadTime);
                updateCacheInfo(response.cached, response.cache_date);
            } else {
                showNoMovies();
                showAlert('warning', response.message || 'No movies found');
            }
        },
        error: function(xhr) {
            console.error('Error loading movies:', xhr);
            showError('Failed to load movies. Please try again.');
            updateStats(0, country, 'Error', '0.0');
        },
        complete: function() {
            document.getElementById('loadingState').style.display = 'none';
        }
    });
}

/**
 * Update statistics display
 */
function updateStats(totalMovies, country, cacheStatus, loadTime) {
    document.getElementById('totalMovies').textContent = totalMovies;
    document.getElementById('currentCountry').textContent = country;
    document.getElementById('cacheStatus').textContent = cacheStatus;
    document.getElementById('loadTime').textContent = loadTime + 's';
}

/**
 * Update cache information
 */
function updateCacheInfo(isCached, cacheDate) {
    const cacheInfo = document.getElementById('cacheInfo');
    const cacheInfoText = document.getElementById('cacheInfoText');

    if (isCached && cacheDate) {
        const date = new Date(cacheDate);
        const timeAgo = getTimeAgo(date);

        cacheInfo.className = 'cache-info cached';
        cacheInfoText.textContent = `Using cached data from ${timeAgo}`;
        cacheInfo.style.display = 'block';
    } else {
        cacheInfo.className = 'cache-info fresh';
        cacheInfoText.textContent = 'Fresh data loaded from API';
        cacheInfo.style.display = 'block';
    }
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
}

/**
 * Filter movies based on search query
 */
function filterMovies(query) {
    if (!query) {
        filteredMovies = allMovies;
    } else {
        filteredMovies = allMovies.filter(movie => {
            return movie.title.toLowerCase().includes(query) ||
                   (movie.year && movie.year.toString().includes(query)) ||
                   (movie.genres && movie.genres.some(genre => genre.toLowerCase().includes(query)));
        });
    }

    displayMovies(filteredMovies);
    updateStats(filteredMovies.length, currentCountry, 'Filtered', '0.0');
}

/**
 * Display movies in grid
 */
function displayMovies(movies) {
    const grid = document.getElementById('moviesGrid');
    const noMoviesState = document.getElementById('noMoviesState');

    if (!movies || movies.length === 0) {
        grid.style.display = 'none';
        noMoviesState.style.display = 'block';
        return;
    }

    // Store all movies for filtering
    allMovies = movies;
    filteredMovies = movies;

    let html = '';
    movies.forEach(movie => {
        const posterUrl = movie.poster_url || 'https://via.placeholder.com/300x450/1a1a1a/ffffff?text=' + encodeURIComponent(movie.title);
        const year = movie.year ? `(${movie.year})` : '';
        const duration = movie.duration ? `${movie.duration} min` : '';

        html += `
            <div class="movie-card" onclick="playMovie('${movie.id}', '${movie.title}')">
                <div class="movie-poster-container">
                    <img src="${posterUrl}" alt="${movie.title}" loading="lazy">
                    <div class="bg-gradient-dark">
                        <div class="movie-info-overlay">
                            <h6>${movie.title}</h6>
                            <div class="movie-year">${year} ${duration}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    grid.innerHTML = html;
    grid.style.display = 'grid';
    noMoviesState.style.display = 'none';
}

/**
 * Show no movies state
 */
function showNoMovies() {
    document.getElementById('moviesGrid').style.display = 'none';
    document.getElementById('noMoviesState').style.display = 'block';
}

/**
 * Show error state
 */
function showError(message) {
    const errorState = document.getElementById('errorState');
    const errorMessage = document.getElementById('errorMessage');

    errorMessage.textContent = message;
    errorState.style.display = 'block';

    document.getElementById('moviesGrid').style.display = 'none';
    document.getElementById('noMoviesState').style.display = 'none';
}

/**
 * Show alert message
 */
function showAlert(type, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Play movie function
 */
function playMovie(movieId, title) {
    if (!movieId) {
        showAlert('warning', 'Invalid movie ID');
        return;
    }

    console.log(`🎬 Starting smart playback for: ${title} (${movieId})`);

    // Show loading screen
    showPlaybackLoading(title);

    // Use Universal Player - detects device and chooses appropriate DRM
    $.ajax({
        url: `/universal/player-data/${movieId}`,
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            console.log('✅ Universal player data extracted:', response);

            if (response.success && response.player_url) {
                console.log(`🎯 Device: ${response.platform}, DRM: ${response.drm_type}`);
                console.log('🎬 Redirecting to universal player:', response.player_url);
                
                // Redirect to device-appropriate player
                window.location.href = response.player_url;
            } else {
                hidePlaybackLoading();
                console.error('❌ Universal player failed:', response.error);
                showAlert('danger', `Failed to prepare player: ${response.error || 'Unknown error'}`);
            }
        },
        error: function(xhr) {
            hidePlaybackLoading();
            console.error('❌ Universal player error:', xhr);

            let errorMessage = 'Error occurred while preparing universal player';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }

            showAlert('danger', errorMessage);
        }
    });
}

/**
 * Show playback loading
 */
function showPlaybackLoading(title) {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'playbackLoading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 18px;
    `;
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="width: 80px; height: 80px; border: 4px solid rgba(0, 123, 255, 0.3); border-left: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div style="font-size: 20px; margin-bottom: 10px;">Preparing movie...</div>
            <div style="font-size: 16px; font-weight: bold; color: #007bff;">${title}</div>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

/**
 * Hide playback loading
 */
function hidePlaybackLoading() {
    const loadingDiv = document.getElementById('playbackLoading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

/**
 * Check proxy status
 */
function checkProxyStatus() {
    console.log('🔍 Checking proxy status...');
    $.ajax({
        url: '/api/proxy-status',
        method: 'GET',
        success: function(response) {
            console.log('✅ Proxy status response:', response);
            const statusElement = $('#proxyStatus');
            if (response.active) {
                statusElement.removeClass('bg-secondary bg-danger bg-warning')
                           .addClass('bg-success')
                           .html(`<i class="fas fa-shield-alt me-1"></i>Proxy Active: ${response.proxy_name}`);
                console.log('🟢 Proxy is active:', response.proxy_name);
            } else {
                statusElement.removeClass('bg-secondary bg-success bg-warning')
                           .addClass('bg-danger')
                           .html(`<i class="fas fa-exclamation-triangle me-1"></i>No Proxy Active`);
                console.log('🔴 No proxy active');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Proxy status check failed:', {xhr, status, error});
            $('#proxyStatus').removeClass('bg-secondary bg-success')
                           .addClass('bg-warning')
                           .html(`<i class="fas fa-question-circle me-1"></i>Proxy Status Unknown`);
        }
    });
}
</script>
@endpush
