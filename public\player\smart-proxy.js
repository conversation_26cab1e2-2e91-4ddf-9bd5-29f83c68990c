/**
 * Smart Proxy Handler
 * Automatically handles all video streaming URLs without manual intervention
 */

console.log('🚀 Smart Proxy Handler loaded');

// Configuration
const PROXY_CONFIG = {
    baseUrl: window.location.origin + '/video-proxy',
    supportedDomains: [
        'mbcvod-enc.edgenextcdn.net',
        'shls-live-enc.edgenextcdn.net',
        'shahid.mbc.net',
        'cdn.shahid.net',
        'mbc.net',
        'cdxaws-ak.akamaized.net',
        'akamaized.net',
        'amazonaws.com',
        'cloudfront.net',
        'httpbin.org'
    ],
    debug: true
};

// URL transformation utilities
const SmartProxy = {
    // Check if URL needs proxying
    needsProxy: function(url) {
        if (!url || typeof url !== 'string') return false;

        // Skip dynamic channel proxy URLs (they're already proxied)
        if (url.includes('/shahid/channel/')) {
            return false;
        }

        // Skip local URLs (relative paths)
        if (url.startsWith('/') && !url.startsWith('//')) {
            return false;
        }

        return PROXY_CONFIG.supportedDomains.some(domain =>
            url.includes(domain)
        );
    },

    // Transform URL to use proxy
    transformUrl: function(url, baseUrl = null) {
        if (!this.needsProxy(url)) {
            return url;
        }

        // Handle template URLs
        if (url.includes('$Number$') || url.includes('$Time$') || url.includes('$Bandwidth$')) {
            return this.handleTemplateUrl(url, baseUrl);
        }

        // Handle manifest URLs
        if (url.includes('.mpd') || url.includes('.m3u8') || url.includes('manifest')) {
            return `${PROXY_CONFIG.baseUrl}/manifest?url=${encodeURIComponent(url)}`;
        }

        // Handle regular segment URLs
        const encodedUrl = btoa(url);
        return `${PROXY_CONFIG.baseUrl}/segment/${encodedUrl}`;
    },

    // Handle template URLs by creating a dynamic resolver
    handleTemplateUrl: function(templateUrl, baseUrl) {
        // For now, return the template URL as-is and let the player handle it
        // The fetch interceptor will resolve it when actually requested
        return templateUrl;
    },

    // Resolve template variables
    resolveTemplate: function(templateUrl, context = {}) {
        let resolved = templateUrl;

        // Default values
        const defaults = {
            Number: context.segmentNumber || Math.floor(Date.now() / 1000) % 10000,
            Time: context.time || Date.now(),
            Bandwidth: context.bandwidth || 1000000
        };

        // Replace template variables
        resolved = resolved.replace(/\$Number\$/g, defaults.Number);
        resolved = resolved.replace(/\$Time\$/g, defaults.Time);
        resolved = resolved.replace(/\$Bandwidth\$/g, defaults.Bandwidth);

        if (PROXY_CONFIG.debug) {
            console.log('🔧 Template resolved:', {
                template: templateUrl,
                resolved: resolved,
                context: context
            });
        }

        return resolved;
    }
};

// Intercept XMLHttpRequest
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    if (SmartProxy.needsProxy(url)) {
        // Handle template URLs at request time
        if (url.includes('$')) {
            url = SmartProxy.resolveTemplate(url);
        }
        
        const transformedUrl = SmartProxy.transformUrl(url);
        
        if (PROXY_CONFIG.debug) {
            console.log('🔄 XHR URL transformed:', {
                original: arguments[1],
                transformed: transformedUrl
            });
        }
        
        arguments[1] = transformedUrl;
    }
    
    return originalXHROpen.apply(this, arguments);
};

// Intercept fetch requests
const originalFetch = window.fetch;
window.fetch = function(input, init) {
    let url = input;
    
    if (typeof input === 'object' && input.url) {
        url = input.url;
    }
    
    if (typeof url === 'string' && SmartProxy.needsProxy(url)) {
        // Handle template URLs at request time
        if (url.includes('$')) {
            url = SmartProxy.resolveTemplate(url);
        }
        
        const transformedUrl = SmartProxy.transformUrl(url);
        
        if (PROXY_CONFIG.debug) {
            console.log('🔄 Fetch URL transformed:', {
                original: typeof input === 'string' ? input : input.url,
                transformed: transformedUrl
            });
        }
        
        if (typeof input === 'object') {
            input = { ...input, url: transformedUrl };
        } else {
            input = transformedUrl;
        }
    }
    
    return originalFetch.call(this, input, init);
};

// Shaka Player integration
if (typeof shaka !== 'undefined') {
    // Wait for Shaka to be ready
    const initShaka = () => {
        if (shaka.net && shaka.net.NetworkingEngine) {
            const originalRequest = shaka.net.NetworkingEngine.prototype.request;
            
            shaka.net.NetworkingEngine.prototype.request = function(type, request, opt_context) {
                if (request.uris && request.uris.length > 0) {
                    request.uris = request.uris.map(uri => {
                        if (SmartProxy.needsProxy(uri)) {
                            // Handle template URLs with context
                            if (uri.includes('$')) {
                                const context = {
                                    segmentNumber: opt_context?.segment?.startTime || 0,
                                    time: opt_context?.time || Date.now(),
                                    bandwidth: opt_context?.bandwidth || 1000000
                                };
                                uri = SmartProxy.resolveTemplate(uri, context);
                            }
                            
                            const transformed = SmartProxy.transformUrl(uri);
                            
                            if (PROXY_CONFIG.debug) {
                                console.log('🔄 Shaka URL transformed:', {
                                    original: uri,
                                    transformed: transformed,
                                    type: type,
                                    context: opt_context
                                });
                            }
                            
                            return transformed;
                        }
                        return uri;
                    });
                }
                
                return originalRequest.call(this, type, request, opt_context);
            };
            
            console.log('✅ Shaka Player proxy integration complete');
        } else {
            // Retry after a short delay
            setTimeout(initShaka, 100);
        }
    };
    
    initShaka();
}

// Export for manual use
window.SmartProxy = SmartProxy;

console.log('✅ Smart Proxy Handler ready');

// Test proxy connectivity on load with fallback
setTimeout(() => {
    fetch(`${PROXY_CONFIG.baseUrl}/manifest?url=${encodeURIComponent('https://httpbin.org/get')}`, {
        method: 'HEAD',
        headers: {
            'Accept': '*/*',
            'Cache-Control': 'no-cache'
        }
    }).then(response => {
        if (response.ok) {
            console.log('✅ Smart Proxy connectivity test passed');
            window.SMART_PROXY_AVAILABLE = true;
        } else {
            console.warn('⚠️ Smart Proxy connectivity test failed:', response.status);
            if (response.status === 403) {
                console.log('🔄 Trying alternative proxy configuration...');
                // Try with different headers or fallback
                window.SMART_PROXY_AVAILABLE = false;
            }
        }
    }).catch(error => {
        console.error('❌ Smart Proxy connectivity test error:', error);
        console.log('🔄 Falling back to direct streaming...');
        window.SMART_PROXY_AVAILABLE = false;
    });
}, 1000);
