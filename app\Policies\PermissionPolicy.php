<?php

namespace App\Policies;

use App\Models\User;
use Spatie\Permission\Models\Permission;
use Illuminate\Auth\Access\HandlesAuthorization;

class PermissionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any permissions.
     */
    public function viewAny(User $user): bool
    {
        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('view permissions') ||
               $user->hasRole(['admin', 'permission-manager']);
    }

    /**
     * Determine whether the user can view the permission.
     */
    public function view(User $user, Permission $permission): bool
    {
        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('view permissions') ||
               $user->hasRole(['admin', 'permission-manager']);
    }

    /**
     * Determine whether the user can create permissions.
     */
    public function create(User $user): bool
    {
        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('create permissions');
    }

    /**
     * Determine whether the user can update the permission.
     */
    public function update(User $user, Permission $permission): bool
    {
        // System permissions cannot be updated
        $systemPermissions = [
            'manage admins',
            'manage roles',
            'manage permissions',
            'super admin access'
        ];

        if (in_array($permission->name, $systemPermissions) && !$user->isSuperAdmin()) {
            return false;
        }

        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('update permissions');
    }

    /**
     * Determine whether the user can delete the permission.
     */
    public function delete(User $user, Permission $permission): bool
    {
        // System permissions cannot be deleted
        $systemPermissions = [
            'manage admins',
            'manage roles',
            'manage permissions',
            'super admin access',
            'view users',
            'create users',
            'update users',
            'delete users'
        ];

        if (in_array($permission->name, $systemPermissions)) {
            return false;
        }

        // Check if permission is in use
        if ($permission->roles()->count() > 0 || $permission->users()->count() > 0) {
            return false;
        }

        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('delete permissions');
    }

    /**
     * Determine whether the user can assign the permission.
     */
    public function assign(User $user, Permission $permission): bool
    {
        // Super admin permissions can only be assigned by super admin
        $superAdminPermissions = [
            'manage admins',
            'super admin access'
        ];

        if (in_array($permission->name, $superAdminPermissions) && !$user->isSuperAdmin()) {
            return false;
        }

        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('assign permissions') ||
               $user->hasRole(['admin', 'permission-manager']);
    }

    /**
     * Determine whether the user can revoke the permission.
     */
    public function revoke(User $user, Permission $permission): bool
    {
        // Super admin permissions can only be revoked by super admin
        $superAdminPermissions = [
            'manage admins',
            'super admin access'
        ];

        if (in_array($permission->name, $superAdminPermissions) && !$user->isSuperAdmin()) {
            return false;
        }

        return $user->isSuperAdmin() ||
               $user->safeHasPermissionTo('revoke permissions') ||
               $user->hasRole(['admin', 'permission-manager']);
    }
}
