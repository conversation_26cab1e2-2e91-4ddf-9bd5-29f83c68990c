<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Under Maintenance - {{ config('app.name', 'Shahid Play') }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        /* Animated Background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>');
            animation: float 20s ease-in-out infinite;
            z-index: 1;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .maintenance-container {
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
            margin: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            padding: 60px 40px;
        }
        
        .maintenance-icon {
            font-size: 5rem;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
            animation: pulse 2s ease-in-out infinite;
            margin-bottom: 30px;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            color: #2d3748;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .maintenance-message {
            font-size: 1.2rem;
            color: #64748b;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .progress-container {
            margin: 40px 0;
        }
        
        .progress {
            height: 8px;
            border-radius: 10px;
            background: #e2e8f0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            animation: progressAnimation 3s ease-in-out infinite;
        }
        
        @keyframes progressAnimation {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        .features-list {
            text-align: left;
            margin: 40px 0;
            background: #f8fafc;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #e2e8f0;
        }
        
        .features-list h5 {
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #64748b;
        }
        
        .feature-item i {
            color: #667eea;
            margin-right: 12px;
            font-size: 1.1rem;
        }
        
        .contact-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-top: 30px;
        }
        
        .contact-info h6 {
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .contact-info a {
            color: #ffd700;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-info a:hover {
            color: #ffed4e;
            text-decoration: underline;
        }
        
        .estimated-time {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        
        .social-links {
            margin-top: 30px;
        }
        
        .social-links a {
            display: inline-block;
            width: 50px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            margin: 0 10px;
            font-size: 1.2rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .social-links a:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        /* Responsive Design */
        @media (max-width: 576px) {
            .maintenance-container {
                margin: 10px;
                padding: 40px 20px;
                border-radius: 20px;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-message {
                font-size: 1rem;
            }
            
            .maintenance-icon {
                font-size: 4rem;
            }
        }
        
        /* Animation Classes */
        .animate-in {
            animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container animate-in">
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <h1 class="maintenance-title">Under Maintenance</h1>
        
        <p class="maintenance-message">
            {{ $message ?? 'We are currently performing scheduled maintenance to improve your experience. Please check back soon.' }}
        </p>
        
        <div class="estimated-time">
            <i class="fas fa-clock me-2"></i>
            <strong>Estimated completion:</strong> We'll be back shortly
        </div>
        
        <div class="progress-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar"></div>
            </div>
            <small class="text-muted mt-2 d-block">Updating system components...</small>
        </div>
        
        <div class="features-list">
            <h5><i class="fas fa-wrench me-2"></i>What we're working on:</h5>
            <div class="feature-item">
                <i class="fas fa-server"></i>
                <span>Server optimization and performance improvements</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>Security updates and system hardening</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-database"></i>
                <span>Database maintenance and optimization</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-bug"></i>
                <span>Bug fixes and stability improvements</span>
            </div>
        </div>
        
        <div class="contact-info">
            <h6><i class="fas fa-headset me-2"></i>Need immediate assistance?</h6>
            <p class="mb-2">
                <i class="fas fa-envelope me-2"></i>
                Email: <a href="mailto:{{ $contact_email ?? '<EMAIL>' }}">{{ $contact_email ?? '<EMAIL>' }}</a>
            </p>
            <p class="mb-0">
                <i class="fas fa-phone me-2"></i>
                Emergency support: Available 24/7
            </p>
        </div>
        
        <div class="social-links">
            <a href="#" title="Facebook"><i class="fab fa-facebook-f"></i></a>
            <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
            <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                This page will automatically refresh when maintenance is complete
            </small>
        </div>
    </div>

    <!-- Auto-refresh script -->
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bar
            const progressBar = document.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = '0%';
                setTimeout(() => {
                    progressBar.style.transition = 'width 3s ease-in-out';
                    progressBar.style.width = '75%';
                }, 500);
            }
        });
    </script>
</body>
</html>
