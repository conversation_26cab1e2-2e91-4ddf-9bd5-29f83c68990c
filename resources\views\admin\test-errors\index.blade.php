@extends('admin.layouts.app')

@section('title', 'Error Pages Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>Error Pages Test
                    </h4>
                    <p class="text-muted mb-0">Test different error pages within the admin dashboard</p>
                </div>
                <div class="card-body">
                    
                    <!-- Warning Notice -->
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Development Only:</strong> These test routes are only available in development environment.
                    </div>

                    <!-- Error Tests -->
                    <div class="row">
                        <!-- 403 Error -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-ban text-danger fa-3x mb-3"></i>
                                    <h5>403 - Access Denied</h5>
                                    <p class="text-muted small">Test permission denied error</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('test.error.403') }}" class="btn btn-outline-danger btn-sm">
                                            Trigger Error
                                        </a>
                                        <a href="{{ route('test.error.preview', 403) }}" class="btn btn-outline-secondary btn-sm">
                                            Preview Only
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 404 Error -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-search text-warning fa-3x mb-3"></i>
                                    <h5>404 - Not Found</h5>
                                    <p class="text-muted small">Test page not found error</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('test.error.404') }}" class="btn btn-outline-warning btn-sm">
                                            Trigger Error
                                        </a>
                                        <a href="{{ route('test.error.preview', 404) }}" class="btn btn-outline-secondary btn-sm">
                                            Preview Only
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 419 Error -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock text-info fa-3x mb-3"></i>
                                    <h5>419 - Session Expired</h5>
                                    <p class="text-muted small">Test session timeout error</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('test.error.419') }}" class="btn btn-outline-info btn-sm">
                                            Trigger Error
                                        </a>
                                        <a href="{{ route('test.error.preview', 419) }}" class="btn btn-outline-secondary btn-sm">
                                            Preview Only
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 500 Error -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                                    <h5>500 - Server Error</h5>
                                    <p class="text-muted small">Test internal server error</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('test.error.500') }}" class="btn btn-outline-danger btn-sm">
                                            Trigger Error
                                        </a>
                                        <a href="{{ route('test.error.preview', 500) }}" class="btn btn-outline-secondary btn-sm">
                                            Preview Only
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 503 Error -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-secondary">
                                <div class="card-body text-center">
                                    <i class="fas fa-wrench text-secondary fa-3x mb-3"></i>
                                    <h5>503 - Service Unavailable</h5>
                                    <p class="text-muted small">Test maintenance mode error</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('test.error.503') }}" class="btn btn-outline-secondary btn-sm">
                                            Trigger Error
                                        </a>
                                        <a href="{{ route('test.error.preview', 503) }}" class="btn btn-outline-secondary btn-sm">
                                            Preview Only
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Error -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-dark">
                                <div class="card-body text-center">
                                    <i class="fas fa-question-circle text-dark fa-3x mb-3"></i>
                                    <h5>Custom Error</h5>
                                    <p class="text-muted small">Test custom error codes</p>
                                    <div class="input-group mb-2">
                                        <input type="number" class="form-control form-control-sm" id="customErrorCode" 
                                               placeholder="Error code" value="422" min="400" max="599">
                                        <button class="btn btn-outline-dark btn-sm" onclick="triggerCustomError()">
                                            Trigger
                                        </button>
                                    </div>
                                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="previewCustomError()">
                                        Preview Only
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="mt-4">
                        <h6>Instructions:</h6>
                        <ul class="text-muted small">
                            <li><strong>Trigger Error:</strong> Actually throws the error and shows the error page</li>
                            <li><strong>Preview Only:</strong> Shows the error page without throwing an actual error</li>
                            <li>All error pages are designed to work within the admin dashboard layout</li>
                            <li>Error pages include navigation back to dashboard and other helpful actions</li>
                        </ul>
                    </div>

                    <!-- Back to Dashboard -->
                    <div class="mt-4 pt-3 border-top">
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function triggerCustomError() {
    const code = document.getElementById('customErrorCode').value;
    if (code >= 400 && code <= 599) {
        window.location.href = `{{ route('test.error.custom', '') }}/${code}`;
    } else {
        alert('Please enter a valid HTTP error code (400-599)');
    }
}

function previewCustomError() {
    const code = document.getElementById('customErrorCode').value;
    if (code >= 400 && code <= 599) {
        window.location.href = `{{ route('test.error.preview', '') }}/${code}`;
    } else {
        alert('Please enter a valid HTTP error code (400-599)');
    }
}
</script>
@endsection
