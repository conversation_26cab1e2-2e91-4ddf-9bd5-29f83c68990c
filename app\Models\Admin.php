<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Carbon\Carbon;

class Admin extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    protected $guard_name = 'admin';

    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'department',
        'position',
        'phone',
        'bio',
        'avatar_url',
        'is_active',
        'is_super_admin',
        'can_manage_admins',
        'legacy_permissions', // Renamed to avoid conflict with <PERSON>tie relationship
        'last_login_at',
        'last_login_ip',
        'last_activity_at',
        'password_changed_at',
        'force_password_change',
        'suspended_reason',
        'suspended_at',
        'created_by',
        'suspended_by'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'suspended_at' => 'datetime',
        'is_active' => 'boolean',
        'is_super_admin' => 'boolean',
        'legacy_permissions' => 'array' // Renamed to avoid conflict with Spatie relationship
    ];

    /**
     * Get the is_super_admin attribute safely
     */
    public function getIsSuperAdminAttribute($value)
    {
        return (bool) $value;
    }

    /**
     * Check if admin is suspended
     */
    public function isSuspended()
    {
        return $this->suspended_at !== null;
    }

    /**
     * Check if admin is super admin
     */
    public function isSuperAdmin()
    {
        return $this->is_super_admin;
    }

    /**
     * Check if admin can manage other admins
     */
    public function canManageAdmins()
    {
        // Check if this object is properly initialized
        if (!isset($this->attributes) || !$this->id) {
            return false;
        }

        // Check super admin status directly from attributes
        if (isset($this->attributes['is_super_admin']) && $this->attributes['is_super_admin']) {
            return true;
        }

        return $this->hasPermission('manage admins');
    }

    /**
     * Update last activity
     */
    public function updateLastActivity()
    {
        $this->update([
            'last_activity_at' => now(),
            'last_login_at' => now(),
            'last_login_ip' => request()->ip()
        ]);
    }

    /**
     * Check if admin has permission
     */
    public function hasPermission($permission)
    {
        // Check if this object is properly initialized
        if (!isset($this->attributes) || !$this->id) {
            return false;
        }

        // Check super admin status first - this should always work
        if (isset($this->attributes['is_super_admin']) && $this->attributes['is_super_admin']) {
            return true;
        }

        // SIMPLIFIED PERMISSION SYSTEM
        // Since Spatie has issues, use a role-based approach
        try {
            $userRoles = $this->roles->pluck('name')->toArray();

            // Super Admin role gets everything
            if (in_array('Super Admin', $userRoles)) {
                return true;
            }

            // Manager role gets most admin permissions
            if (in_array('Manager', $userRoles)) {
                $managerPermissions = [
                    'view users', 'create users', 'edit users', 'update users', 'delete users',
                    'suspend users', 'reset user passwords', 'manage user roles',
                    'view subscriptions', 'create subscriptions', 'edit subscriptions', 'delete subscriptions',
                    'view reports', 'view settings', 'edit settings',
                    'view admins', 'create admins', 'edit admins', 'delete admins', 'manage admins',
                    'view roles', 'create roles', 'edit roles', 'delete roles', 'manage roles',
                    'view permissions', 'create permissions', 'edit permissions', 'delete permissions', 'manage permissions',
                    'view admin panel', 'view dashboard', 'manage admin permissions', 'manage role permissions',
                    'manage proxies'
                ];

                return in_array($permission, $managerPermissions);
            }

            // Editor role gets limited permissions
            if (in_array('Editor', $userRoles)) {
                $editorPermissions = [
                    'view users', 'edit users', 'update users',
                    'view subscriptions', 'edit subscriptions',
                    'view reports', 'view settings',
                    'view admin panel', 'view dashboard'
                ];

                return in_array($permission, $editorPermissions);
            }

            // Viewer role gets read-only permissions
            if (in_array('Viewer', $userRoles)) {
                $viewerPermissions = [
                    'view users', 'view subscriptions', 'view reports', 'view settings',
                    'view admin panel', 'view dashboard'
                ];

                return in_array($permission, $viewerPermissions);
            }

        } catch (\Exception $e) {
            \Log::error('Role-based permission check failed: ' . $e->getMessage());
        }

        // If no role matches, deny access
        return false;
    }

    /**
     * Check if admin has role
     */
    public function hasRole($role)
    {
        // Check if model exists
        if (!$this->exists) {
            return false;
        }

        if ($this->is_super_admin) {
            return true;
        }

        try {
            // Use the trait's hasRole method directly
            return $this->roles()->where('name', $role)->exists();
        } catch (\Exception $e) {
            \Log::warning('Spatie role check failed: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Get admin activity logs
     */
    public function activityLogs()
    {
        return $this->hasMany(AdminActivityLog::class, 'admin_id');
    }

    /**
     * Get users created by this admin
     * Note: Temporarily disabled due to table structure mismatch
     */
    // public function createdUsers()
    // {
    //     return $this->hasMany(User::class, 'created_by_admin');
    // }

    /**
     * Get users suspended by this admin
     * Note: Temporarily disabled due to table structure mismatch
     */
    // public function suspendedUsers()
    // {
    //     return $this->hasMany(User::class, 'suspended_by');
    // }

    /**
     * Get the admin who suspended this admin
     */
    public function suspendedBy()
    {
        return $this->belongsTo(Admin::class, 'suspended_by');
    }

    /**
     * Get the admin who created this admin
     */
    public function createdByAdmin()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * Scope for active admins
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for super admins
     */
    public function scopeSuperAdmin($query)
    {
        return $query->where('is_super_admin', true);
    }


}
