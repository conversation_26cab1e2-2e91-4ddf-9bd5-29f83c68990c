@extends('admin.layouts.app')

@section('title', 'Permission Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Create and manage system permissions for fine-grained access control.</p>
    </div>
    <div class="d-flex gap-2">
        @can('create', Spatie\Permission\Models\Permission::class)
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkCreateModal">
            <i class="fas fa-layer-group me-2"></i>Bulk Create
        </button>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPermissionModal">
            <i class="fas fa-plus me-2"></i>Create Permission
        </button>
        @endcan
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Permissions</h6>
                        <h3 class="mb-0" id="total-permissions">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-key fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Used Permissions</h6>
                        <h3 class="mb-0" id="used-permissions">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Unused</h6>
                        <h3 class="mb-0" id="unused-permissions">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">System Permissions</h6>
                        <h3 class="mb-0" id="system-permissions">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cog fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Search</label>
                <input type="text" class="form-control" id="search-input" placeholder="Search permissions...">
            </div>
            <div class="col-md-2">
                <label class="form-label">Category</label>
                <select class="form-select" id="category-filter">
                    <option value="">All Categories</option>
                    <option value="user_management">User Management</option>
                    <option value="admin_management">Admin Management</option>
                    <option value="role_management">Role Management</option>
                    <option value="permission_management">Permission Management</option>
                    <option value="subscription_management">Subscription Management</option>
                    <option value="system">System</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Guard</label>
                <select class="form-select" id="guard-filter">
                    <option value="">All Guards</option>
                    <option value="web">Web</option>
                    <option value="api">API</option>
                </select>
            </div>
            <div class="col-md-5">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" id="apply-filters">
                        <i class="fas fa-filter me-1"></i>Apply
                    </button>
                    <button class="btn btn-outline-secondary" id="clear-filters">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                    <button class="btn btn-outline-info" id="view-categories">
                        <i class="fas fa-list me-1"></i>Categories
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Permissions</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="permissions-table">
                <thead>
                    <tr>
                        <th>Permission Name</th>
                        <th>Guard</th>
                        <th>Roles</th>
                        <th>Users</th>
                        <th>Category</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="permissions-tbody">
                    <!-- Dynamic content -->
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Permissions pagination" id="pagination-container">
            <!-- Dynamic pagination -->
        </nav>
    </div>
</div>

<!-- Create Permission Modal -->
@can('create', Spatie\Permission\Models\Permission::class)
<div class="modal fade" id="createPermissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Permission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="create-permission-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Permission Name *</label>
                        <input type="text" class="form-control" name="name" required>
                        <div class="form-text">Use lowercase with spaces (e.g., "manage users")</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Guard Name</label>
                        <select class="form-select" name="guard_name">
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Permission</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
<div class="modal fade" id="bulkCreateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Create Permissions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulk-create-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Permission Template</label>
                        <select class="form-select" id="permission-template">
                            <option value="">Select a template...</option>
                            <option value="crud">CRUD Operations (Create, Read, Update, Delete)</option>
                            <option value="user_management">User Management</option>
                            <option value="content_management">Content Management</option>
                            <option value="custom">Custom Permissions</option>
                        </select>
                    </div>
                    <div class="mb-3" id="resource-name-group" style="display: none;">
                        <label class="form-label">Resource Name</label>
                        <input type="text" class="form-control" id="resource-name" placeholder="e.g., posts, products">
                        <div class="form-text">This will generate permissions like "create posts", "edit posts", etc.</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions to Create</label>
                        <div id="permissions-preview" class="border rounded p-3" style="min-height: 100px;">
                            <p class="text-muted">Select a template to see permissions preview</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Guard Name</label>
                        <select class="form-select" name="guard_name">
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Permissions</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan

<!-- Edit Permission Modal -->
<div class="modal fade" id="editPermissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Permission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="edit-permission-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Permission Name *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Guard Name</label>
                        <select class="form-select" name="guard_name">
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Permission</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Permission Categories Modal -->
<div class="modal fade" id="categoriesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Permission Categories</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="categories-content">
                <!-- Dynamic content -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.permission-category-card {
    border-left: 4px solid #007bff;
    margin-bottom: 1rem;
}

.permission-badge {
    font-size: 0.75rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.system-permission {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.unused-permission {
    opacity: 0.6;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.permission-usage {
    font-size: 0.8rem;
    color: #6c757d;
}

#permissions-preview {
    background-color: #f8f9fa;
}

.permission-item {
    padding: 0.25rem 0.5rem;
    margin: 0.25rem 0;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    display: inline-block;
    margin-right: 0.5rem;
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/admin/permission-management.js') }}"></script>
@endpush
