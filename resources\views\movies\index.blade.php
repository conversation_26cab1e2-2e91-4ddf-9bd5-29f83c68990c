<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبة الأفلام - Shahid Player</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(0, 0, 0, 0.9) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .movie-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            padding: 30px 0;
        }
        
        .movie-card {
            position: relative;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            user-select: none;
        }
        
        .movie-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        .movie-poster {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
        }
        
        .movie-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }
        
        .movie-card:hover .movie-poster img {
            transform: scale(1.1);
        }
        
        .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s ease;
            z-index: 10;
        }

        .movie-card:hover .play-overlay {
            opacity: 1;
            visibility: visible;
        }

        .play-button {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            transition: all 0.3s ease;
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
            cursor: pointer;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .play-button:hover {
            transform: scale(1.2);
            box-shadow: 0 20px 50px rgba(255, 107, 107, 0.8);
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }

        .play-button i {
            margin-left: 5px; /* تعديل موضع أيقونة التشغيل */
        }
        
        .movie-info {
            padding: 20px;
        }
        
        .movie-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .movie-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .page-title {
            text-align: center;
            margin: 40px 0;
            color: #ffffff;
        }
        
        .page-title h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* تأثيرات إضافية للتأكد من ظهور زر التشغيل */
        .movie-card {
            will-change: transform;
        }

        .movie-card:hover {
            z-index: 100;
        }

        .play-overlay {
            backdrop-filter: blur(5px);
        }

        /* تأثيرات للأجهزة المحمولة */
        .movie-card.touch-hover .play-overlay {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* تحسينات responsive */
        @media (max-width: 768px) {
            .movie-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 20px;
                padding: 20px 0;
            }

            .movie-poster {
                height: 300px;
            }

            .play-button {
                width: 80px;
                height: 80px;
                font-size: 30px;
            }

            .page-title h1 {
                font-size: 2rem;
            }
        }

        /* إظهار زر التشغيل دائماً على الأجهزة المحمولة */
        @media (hover: none) {
            .play-overlay {
                opacity: 0.8;
                visibility: visible;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-play-circle me-2"></i>
                Shahid Player
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <div class="page-title">
            <h1><i class="fas fa-film me-3"></i>مكتبة الأفلام</h1>
            <p class="lead">اختر فيلمك المفضل واستمتع بالمشاهدة</p>
        </div>

        <!-- Movies Grid -->
        <div class="movie-grid">
            @foreach($movies as $movie)
            <div class="movie-card" onclick="playMovie('{{ $movie['id'] }}', '{{ $movie['title'] }}')">
                <div class="movie-poster">
                    <img src="{{ $movie['poster'] }}" alt="{{ $movie['title'] }}" loading="lazy">
                    <div class="play-overlay">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">{{ $movie['title'] }}</h3>
                    <p class="movie-description">{{ $movie['description'] }}</p>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h4>جاري تحضير الفيلم...</h4>
            <p>يتم استخراج المفاتيح وتحضير المشغل</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعداد CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        /**
         * تشغيل فيلم مع استخراج المفاتيح بشكل صامت
         */
        async function playMovie(contentId, title) {
            console.log(`🎬 Starting playback for: ${title} (${contentId})`);
            
            // إظهار شاشة التحميل
            showLoading();
            
            try {
                // الانتقال مباشرة إلى صفحة المشغل
                // المشغل سيقوم باستخراج المفاتيح بشكل صامت
                window.location.href = `/movies/play/${contentId}`;
                
            } catch (error) {
                console.error('❌ Error starting playback:', error);
                hideLoading();
                alert('حدث خطأ أثناء تشغيل الفيلم. يرجى المحاولة مرة أخرى.');
            }
        }
        
        /**
         * إظهار شاشة التحميل
         */
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        /**
         * إخفاء شاشة التحميل
         */
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        // إخفاء شاشة التحميل عند تحميل الصفحة
        window.addEventListener('load', hideLoading);

        // تحسين تأثيرات hover للأجهزة المحمولة
        document.addEventListener('DOMContentLoaded', function() {
            const movieCards = document.querySelectorAll('.movie-card');

            movieCards.forEach(card => {
                // إضافة تأثيرات للأجهزة المحمولة
                card.addEventListener('touchstart', function() {
                    this.classList.add('touch-hover');
                });

                card.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.classList.remove('touch-hover');
                    }, 300);
                });

                // تحسين تأثيرات الماوس
                card.addEventListener('mouseenter', function() {
                    console.log('Mouse entered movie card');
                    const overlay = this.querySelector('.play-overlay');
                    if (overlay) {
                        overlay.style.opacity = '1';
                        overlay.style.visibility = 'visible';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    console.log('Mouse left movie card');
                    const overlay = this.querySelector('.play-overlay');
                    if (overlay) {
                        overlay.style.opacity = '0';
                        overlay.style.visibility = 'hidden';
                    }
                });
            });
        });
    </script>
</body>
</html>
