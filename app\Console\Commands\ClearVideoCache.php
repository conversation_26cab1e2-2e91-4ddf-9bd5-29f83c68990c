<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearVideoCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'video:clear-cache {--force : Force clear all cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear video proxy cache to improve streaming performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Clearing video cache...');

        try {
            if ($this->option('force')) {
                // Clear all cache
                Cache::flush();
                $this->info('✅ All cache cleared successfully');
            } else {
                // Clear only video-related cache
                $patterns = [
                    'video_segment_*',
                    'manifest_*',
                    'movies_list_*',
                    'series_list_*'
                ];

                $cleared = 0;
                foreach ($patterns as $pattern) {
                    // This is a simplified approach - in production you might want to use Redis SCAN
                    $keys = Cache::getRedis()->keys($pattern);
                    foreach ($keys as $key) {
                        Cache::forget($key);
                        $cleared++;
                    }
                }

                $this->info("✅ Cleared {$cleared} video cache entries");
            }

            $this->info('🚀 Video streaming should be faster now!');
            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Failed to clear cache: ' . $e->getMessage());
            return 1;
        }
    }
}
