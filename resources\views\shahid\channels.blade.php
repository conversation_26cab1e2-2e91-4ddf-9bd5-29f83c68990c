@extends('layouts.app')

@section('title', 'Live Channels - Shahid Play')

@section('styles')
<style>
.bg-gradient-dark {
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
}

.channel-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    border-radius: 12px !important;
}

.channel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.channel-card .position-relative {
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.channel-card img {
    transition: transform 0.3s ease;
    object-fit: contain !important;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    padding: 15px !important;
    border-radius: 8px 8px 0 0 !important;
}

.channel-card:hover img {
    transform: scale(1.02);
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
}

/* Ensure logos display properly */
.channel-card .card-img-top {
    width: 100% !important;
    height: 288px !important;
    object-fit: contain !important;
    object-position: center !important;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    padding: 15px !important;
    box-shadow: inset 0 0 20px rgba(0,0,0,0.1) !important;
}

/* Add subtle pattern overlay for better logo visibility */
.channel-card .card-img-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.05) 100%);
    pointer-events: none;
}

.live-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Hover Effects */
.btn-refresh:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
    transform: translateY(-2px);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6) !important;
    background: rgba(255, 255, 255, 0.2) !important;
}

.filter-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6) !important;
    background: rgba(255, 255, 255, 0.2) !important;
}

.filter-select option {
    background: #2c3e50;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column !important;
        gap: 15px !important;
    }

    .page-title {
        flex-direction: column !important;
        gap: 15px !important;
        text-align: center !important;
    }

    .search-filter-bar {
        flex-direction: column !important;
        gap: 15px !important;
    }

    .search-wrapper,
    .filter-wrapper {
        min-width: 100% !important;
    }
}

/* Enhanced Header Styles */
.enhanced-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 30px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
}

.title-section {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 20px;
    margin: 0;
}

.title-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px;
    font-size: 24px;
}

.title-text {
    display: flex;
    flex-direction: column;
}

.main-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.sub-title {
    font-size: 1rem;
    opacity: 0.9;
    margin-top: 5px;
}

.actions-section {
    display: flex;
    gap: 15px;
}

.btn-refresh {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-refresh:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
}

/* Search and Filter Bar */
.search-filter-bar {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-wrapper {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
}

.filter-wrapper {
    min-width: 200px;
}

.filter-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
}

.filter-select option {
    background: #2c3e50;
    color: white;
}

/* Alert Styles */
.alert-container {
    margin: 0 auto;
    max-width: 800px;
}

.alert {
    border: none;
    border-radius: 15px;
    padding: 0;
    overflow: hidden;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.alert-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.alert-text {
    flex: 1;
}

.alert-text strong {
    display: block;
    font-size: 18px;
    margin-bottom: 5px;
}

.alert-text p {
    margin: 0;
    opacity: 0.9;
}

.btn-alert-action {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: inherit;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-alert-action:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: inherit;
}

/* Loading Styles */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 60px 20px;
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loading-spinner {
    margin-bottom: 30px;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text h4 {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 10px;
}

.loading-text p {
    color: #6c757d;
    margin: 0;
}

/* Results Info */
.results-info {
    display: flex;
    justify-content: center;
}

.info-card {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 15px 30px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.info-icon {
    font-size: 18px;
}

/* Channels Grid */
.channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    padding: 20px 0;
}

/* No Results */
.no-results-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 60px 20px;
}

.no-results-content {
    text-align: center;
    max-width: 400px;
}

.no-results-icon {
    font-size: 80px;
    color: #6c757d;
    margin-bottom: 30px;
}

.no-results-text h3 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.no-results-text p {
    color: #6c757d;
    margin-bottom: 30px;
}

.btn-retry {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-retry:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-header {
        padding: 20px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .page-title {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .search-filter-bar {
        flex-direction: column;
        gap: 15px;
    }

    .search-wrapper,
    .filter-wrapper {
        min-width: 100%;
    }

    .channels-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* ===== RESPONSIVE DESIGN IMPROVEMENTS ===== */

/* Mobile optimizations */
@media (max-width: 767px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        width: 50%; /* 2 columns on mobile */
        padding: 0.5rem;
    }

    .channel-card {
        margin-bottom: 1rem;
    }

    .channel-card .card-img-top {
        height: 200px !important;
    }

    .h3 {
        font-size: 1.3rem;
    }
}

/* Very small screens */
@media (max-width: 575px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        width: 100%; /* 1 column on very small screens */
    }

    .channel-card .card-img-top {
        height: 180px !important;
    }

    .h3 {
        font-size: 1.1rem;
    }
}

/* Landscape على الموبايل */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        width: 25%; /* 4 أعمدة في landscape */
    }
}

/* ===== HEADER RESPONSIVE IMPROVEMENTS ===== */

/* تحسين الـ header للشاشات المختلفة */
.page-header h1 {
    font-size: clamp(1.5rem, 4vw, 2rem);
    line-height: 1.2;
}

.page-header .btn {
    white-space: nowrap;
    min-width: auto;
}

/* Mobile header adjustments */
@media (max-width: 767px) {
    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .page-header p {
        font-size: 0.9rem;
        margin-bottom: 0;
    }
}

/* Very small screens */
@media (max-width: 575px) {
    .page-header h1 {
        font-size: 1.3rem;
    }

    .page-header p {
        font-size: 0.85rem;
    }
}

/* iPhone specific */
@media only screen and (max-width: 320px) {
    .page-header h1 {
        font-size: 1.2rem;
    }

    .page-header p {
        font-size: 0.8rem;
    }
}
</style>
@endsection

@section('content')
<div class="container-fluid" dir="ltr">
    <!-- Header -->
    <div class="row mb-4 page-header">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1 text-center text-md-start">
                    <h1 class="h3 mb-2 mb-md-0 fw-bold">
                        <i class="fas fa-broadcast-tower me-2 text-primary"></i>
                        Shahid Channels
                    </h1>
                    <p class="text-muted mb-0 small">Watch live TV channels from Shahid</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label for="countrySelect" class="form-label">Country</label>
                            <select class="form-select" id="countrySelect">
                                <option value="">All Countries</option>
                                <option value="EG">Egypt</option>
                                <option value="SA">Saudi Arabia</option>
                                <option value="AE">UAE</option>
                                <option value="KW">Kuwait</option>
                                <option value="QA">Qatar</option>
                                <option value="BH">Bahrain</option>
                                <option value="OM">Oman</option>
                                <option value="JO">Jordan</option>
                                <option value="LB">Lebanon</option>
                                <option value="SY">Syria</option>
                                <option value="IQ">Iraq</option>
                                <option value="YE">Yemen</option>
                                <option value="LY">Libya</option>
                                <option value="TN">Tunisia</option>
                                <option value="DZ">Algeria</option>
                                <option value="MA">Morocco</option>
                                <option value="SD">Sudan</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">Search Channels</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search by channel name...">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchChannels()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadChannels()">
                                <i class="fas fa-sync-alt me-2"></i>
                                Load Channels
                            </button>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="groupBySource" onchange="toggleGroupBySource()">
                                <label class="form-check-label" for="groupBySource">
                                    Group by Source
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-secondary w-100" onclick="refreshChannels()">
                                <i class="fas fa-sync-alt me-2"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Welcome Message -->
    <div id="welcomeMessage" class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-broadcast-tower fa-4x text-primary"></i>
        </div>
        <h4 class="text-primary">Welcome to Shahid Channels</h4>
        <p class="text-muted mb-4">Choose your country and click "Load Channels" to browse available live channels.</p>
        <div class="d-flex justify-content-center gap-3">
            <button class="btn btn-primary" onclick="loadChannels()">
                <i class="fas fa-play me-2"></i>
                Load Channels
            </button>
            <button class="btn btn-secondary" onclick="refreshChannels()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="text-center py-5 d-none">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 class="text-primary">Loading Channels</h5>
        <p class="text-muted">Please wait while we fetch the latest channels...</p>
    </div>

    <!-- Results Info -->
    <div id="resultsInfo" class="row mb-3 d-none">
        <div class="col-12">
            <div class="alert alert-info d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <span>Found <strong id="totalChannels">0</strong> live channels</span>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <div id="searchResultsInfo" class="row mb-3 d-none">
        <div class="col-12">
            <div class="alert alert-success d-flex align-items-center justify-content-between">
                <div>
                    <i class="fas fa-search me-2"></i>
                    <span>Search results for "<strong id="searchTerm"></strong>": <strong id="searchCount">0</strong> channels found</span>
                </div>
                <button class="btn btn-sm btn-outline-success" onclick="clearSearch()">
                    <i class="fas fa-times me-1"></i>
                    Clear Search
                </button>
            </div>
        </div>
    </div>

    <!-- Channels Grid -->
    <div id="channelsContainer" class="row d-none">
        <!-- Channel cards will be inserted here -->
    </div>

    <!-- No Channels Found -->
    <div id="noResults" class="text-center py-5 d-none">
        <div class="mb-4">
            <i class="fas fa-broadcast-tower fa-3x mb-3"></i>
            <h5>No Channels Found</h5>
            <p>No live channels were found. Please check your token settings and connection.</p>
            <button class="btn btn-primary" onclick="refreshChannels()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
    </div>
</div>

<!-- Token Modal -->
<div class="modal fade" id="tokenModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>Add Shahid Token
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="shahidToken" class="form-label">Shahid Token</label>
                    <textarea class="form-control" id="shahidToken" rows="4" placeholder="Enter your Shahid token here..."></textarea>
                    <div class="form-text">
                        <i class="fas fa-info-circle me-1"></i>
                        You can get the token from your browser when logged into Shahid
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="saveToken()">
                    <i class="fas fa-save me-2"></i>Save Token
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Load channels on page load
    loadChannels();

    // Search functionality
    $('#channelSearch').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterChannels(searchTerm);
    });

    // Country filter functionality
    $('#countryFilter').on('change', function() {
        const selectedCountry = $(this).val();
        filterChannelsByCountry(selectedCountry);
    });
});

let allChannels = []; // Store all channels for filtering

function filterChannels(searchTerm) {
    const filteredChannels = allChannels.filter(channel => {
        const title = (channel.title || '').toLowerCase();
        const country = (channel.country_name || channel.country || '').toLowerCase();
        return title.includes(searchTerm) || country.includes(searchTerm);
    });

    // Store filtered channels for group toggle
    window.filteredChannels = filteredChannels;

    displayChannels(filteredChannels);
    $('#totalChannels').text(filteredChannels.length);
}

function filterChannelsByCountry(selectedCountry) {
    let filteredChannels;

    if (!selectedCountry) {
        filteredChannels = allChannels;
    } else {
        filteredChannels = allChannels.filter(channel => {
            const country = channel.country_name || channel.country || '';
            return country === selectedCountry;
        });
    }

    // Store filtered channels for group toggle
    window.filteredChannels = filteredChannels;

    displayChannels(filteredChannels);
    $('#totalChannels').text(filteredChannels.length);
}

function populateCountryFilter(channels) {
    const countries = [...new Set(channels.map(channel =>
        channel.country_name || channel.country || ''
    ).filter(country => country))].sort();

    const countryFilter = $('#countryFilter');
    countryFilter.empty();
    countryFilter.append('<option value="">All Countries</option>');

    countries.forEach(country => {
        countryFilter.append(`<option value="${country}">${country}</option>`);
    });
}

function loadChannels() {
    console.log('loadChannels function called');

    // Add loading state to refresh buttons
    $('.refresh-btn').addClass('loading');

    showLoading();
    hideResults();
    hideWelcome();
    
    $.ajax({
        url: '/api/shahid/channels',
        method: 'GET',
        data: {
            refresh: false // Use cached data by default
        },
        success: function(response) {
            hideLoading();
            
            if (response.success && response.data && response.data.length > 0) {
                allChannels = response.data; // Store all channels for filtering
                displayChannels(response.data);
                showResults();
                $('#totalChannels').text(response.total_channels || response.data.length);
                $('#resultsInfo').removeClass('d-none');
                $('#tokenWarning').addClass('d-none');

                // Populate country filter
                populateCountryFilter(response.data);
            } else {
                showNoResults();
                $('#resultsInfo').addClass('d-none');
                
                // Check if it's a token issue
                if (response.message && response.message.includes('token')) {
                    $('#tokenWarning').removeClass('d-none');
                }
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error loading channels';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
                
                // Check if it's a token issue
                if (errorMessage.includes('token')) {
                    $('#tokenWarning').removeClass('d-none');
                }
            }
            
            showAlert('danger', errorMessage);
            showNoResults();
        }
    });
}

// Refresh channels (force refresh from API)
function refreshChannels() {
    console.log('refreshChannels function called');

    // Add loading state to refresh buttons
    $('.refresh-btn').addClass('loading');

    showLoading();
    hideResults();
    hideWelcome();

    $.ajax({
        url: '/api/shahid/channels',
        method: 'GET',
        data: {
            refresh: true // Force refresh from API
        },
        success: function(response) {
            hideLoading();

            if (response.success && response.data && response.data.length > 0) {
                allChannels = response.data; // Store all channels for filtering
                currentChannels = response.data; // Update current channels
                displayChannels(response.data);
                showResults();
                $('#totalChannels').text(response.total_channels || response.data.length);
                $('#resultsInfo').removeClass('d-none');
                $('#tokenWarning').addClass('d-none');

                // Populate country filter
                populateCountryFilter(response.data);

                // Show success message
                showAlert('success', `Refreshed ${response.data.length} channels successfully`);
            } else {
                showNoResults();
                $('#resultsInfo').addClass('d-none');

                // Check if it's a token issue
                if (response.message && response.message.includes('token')) {
                    $('#tokenWarning').removeClass('d-none');
                }
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error refreshing channels';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;

                // Check if it's a token issue
                if (errorMessage.includes('token')) {
                    $('#tokenWarning').removeClass('d-none');
                }
            }

            showAlert('danger', errorMessage);
            showNoResults();
        }
    });
}

function cleanImageUrl(url) {
    if (!url) return url;

    let cleanUrl = url;

    // Replace placeholders with the exact values you want
    cleanUrl = cleanUrl.replace('{height}', 'auto');
    cleanUrl = cleanUrl.replace('{width}', '288');
    cleanUrl = cleanUrl.replace('{croppingPoint}', '');

    // If it's a mediaObject URL, ensure it has the exact format you want
    if (cleanUrl.includes('mediaObject/')) {
        // Extract the mediaObject ID
        const mediaObjectMatch = cleanUrl.match(/mediaObject\/([^?]+)/);
        if (mediaObjectMatch) {
            const mediaObjectId = mediaObjectMatch[1];
            // Rebuild URL with exact format
            cleanUrl = `https://shahid.mbc.net/mediaObject/${mediaObjectId}?height=auto&width=288&croppingPoint=&version=1&type=webp`;
        }
    }

    console.log('Original URL:', url);
    console.log('Cleaned URL:', cleanUrl);

    return cleanUrl;
}

function displayChannels(channelsList) {
    const container = $('#channelsContainer');
    container.empty();

    const groupBySource = $('#groupBySource').is(':checked');

    if (groupBySource) {
        displayChannelsGrouped(channelsList, container);
    } else {
        channelsList.forEach(function(channel) {
            const channelCard = createChannelCard(channel);
            container.append(channelCard);
        });
    }
}

function displayChannelsGrouped(channelsList, container) {
    // Group channels by country
    const groupedChannels = {};

    channelsList.forEach(function(channel) {
        const country = channel.country_name || channel.country || 'Unknown';
        if (!groupedChannels[country]) {
            groupedChannels[country] = [];
        }
        groupedChannels[country].push(channel);
    });

    // Display each group
    Object.keys(groupedChannels).sort().forEach(function(country) {
        const channels = groupedChannels[country];

        // Add country header with enhanced styling
        const countryHeader = `
            <div class="col-12 mb-3">
                <div class="card border-0 bg-light">
                    <div class="card-body py-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <h4 class="text-primary mb-0">
                                    <i class="fas fa-flag me-2"></i>
                                    ${country}
                                </h4>
                                <span class="badge bg-primary ms-3">${channels.length} channels</span>
                            </div>
                            <div class="text-muted small">
                                <i class="fas fa-broadcast-tower me-1"></i>
                                Live Channels
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.append(countryHeader);

        // Add channels for this country
        channels.forEach(function(channel) {
            const channelCard = createChannelCard(channel);
            container.append(channelCard);
        });

        // Add spacing between groups
        container.append('<div class="col-12 mb-4"></div>');
    });
}

function toggleGroupBySource() {
    if (allChannels && allChannels.length > 0) {
        displayChannels(window.filteredChannels || allChannels);
    }
}

function createChannelCard(channel) {
    const title = channel.title || 'قناة غير محددة';

    // Try multiple possible logo/image sources (prioritize processed logo_url)
    let poster = '/images/no-poster.svg';

    // Check for different possible image properties
    if (channel.logo_url) {
        poster = channel.logo_url;
    } else if (channel.logoTitleImage) {
        poster = channel.logoTitleImage;
    } else if (channel.image && channel.image.posterClean) {
        poster = channel.image.posterClean;
    } else if (channel.image && channel.image.posterImage) {
        poster = channel.image.posterImage;
    } else if (channel.image && channel.image.posterHero) {
        poster = channel.image.posterHero;
    } else if (channel.thumbnailImage) {
        poster = channel.thumbnailImage;
    } else if (channel.image && channel.image.landscape) {
        poster = channel.image.landscape;
    }

    const country = channel.country_name || channel.country || '';
    const channelId = channel.id || '';

    // Clean and optimize the poster URL
    if (poster && poster !== '/images/no-poster.svg') {
        // Remove unwanted parameters and optimize
        poster = cleanImageUrl(poster);
    }

    console.log('Channel:', title, 'Logo:', poster);

    return `
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100 border-0 shadow-sm channel-card" data-channel-id="${channelId}">
                <div class="position-relative">
                    <img src="${poster}" class="card-img-top" alt="${title}"
                         style="height: 288px; object-fit: contain; background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 15px;"
                         onerror="this.onerror=null; this.style.display='none'; this.parentNode.innerHTML='<div style=\\'height:288px;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg, #2c3e50 0%, #34495e 100%);color:#ecf0f1;font-size:18px;\\'>📺<br>No Logo</div>';">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-danger live-indicator">
                            <i class="fas fa-circle me-1"></i>LIVE
                        </span>
                    </div>
                    <div class="position-absolute top-0 start-0 m-2">
                        ${country ? `<span class="badge bg-dark">${country}</span>` : ''}
                    </div>
                    <div class="position-absolute bottom-0 start-0 end-0 bg-gradient-dark p-3">
                        <h6 class="text-white mb-0 fw-bold">${title}</h6>
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="mt-auto">
                        <div class="d-grid gap-2">
                            <button class="btn btn-danger btn-sm" onclick="watchChannel('${channelId}')">
                                <i class="fas fa-play me-2"></i>
                                Watch Live
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="getChannelDetails('${channelId}')">
                                <i class="fas fa-info-circle me-2"></i>
                                Details
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function watchChannel(channelId) {
    if (!channelId) {
        showAlert('warning', 'Invalid channel ID');
        return;
    }

    // 🎯 Log channel click in browser console and send to server
    const clickTime = new Date().toISOString();
    console.log(`🎬 CHANNEL CLICKED: ${channelId} at ${clickTime}`);

    // Find channel details from loaded channels
    const channel = allChannels.find(ch => ch.id === channelId);
    const channelTitle = channel ? channel.title : `Channel ${channelId}`;

    console.log(`📺 Channel Details:`, {
        id: channelId,
        title: channelTitle,
        timestamp: clickTime,
        userAgent: navigator.userAgent
    });

    // Show loading indicator
    const loadingDiv = document.createElement('div');
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 18px;
    `;
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="width: 60px; height: 60px; border: 4px solid rgba(116, 185, 255, 0.2); border-left: 4px solid #74b9ff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div>🚀 Preparing Channel for Playback...</div>
            <div style="font-size: 14px; opacity: 0.8; margin-top: 10px;">Checking channel encryption...</div>
            <div style="font-size: 12px; opacity: 0.6; margin-top: 5px;">This may take a few seconds...</div>
        </div>
    `;
    document.body.appendChild(loadingDiv);

    // First, try to extract basic channel data to check if it's encrypted
    $.ajax({
        url: `/api/shahid/channels/${channelId}/extract`,
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                const channelData = response.data;

                // Check if channel is encrypted
                if (channelData.is_encrypted || channelData.drm_info) {
                    // Channel is encrypted - extract DRM data
                    extractEncryptedChannelData(channelId, loadingDiv);
                } else {
                    // Channel is not encrypted - play directly
                    playUnencryptedChannel(channelData, channelId, loadingDiv);
                }
            } else {
                // Fallback to DRM extraction if basic extraction fails
                extractEncryptedChannelData(channelId, loadingDiv);
            }
        },
        error: function(xhr) {
            // Fallback to DRM extraction if basic extraction fails
            extractEncryptedChannelData(channelId, loadingDiv);
        }
    });
}

function extractEncryptedChannelData(channelId, loadingDiv) {
    // Update loading message for encrypted channels
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="width: 60px; height: 60px; border: 4px solid rgba(116, 185, 255, 0.2); border-left: 4px solid #74b9ff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div>🔐 Extracting DRM Keys...</div>
            <div style="font-size: 14px; opacity: 0.8; margin-top: 10px;">Processing encrypted channel</div>
            <div style="font-size: 12px; opacity: 0.6; margin-top: 5px;">This may take longer...</div>
        </div>
    `;

    // Extract channel DRM data using dedicated channel extractor
    $.ajax({
        url: `/api/shahid/channels/${channelId}/extract-drm`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: JSON.stringify({
            channel_id: channelId,
            channel_title: `Channel ${channelId}`
        }),
        success: function(response) {
            // Remove loading indicator
            document.body.removeChild(loadingDiv);

            if (response.success && response.data) {
                // 🎯 Log extracted channel data including stream URLs
                console.log(`📺 CHANNEL DATA EXTRACTED for ${channelId}:`, {
                    channelId: channelId,
                    streamUrl: response.data.stream_url,
                    mpdUrl: response.data.mpd_url,
                    hlsUrl: response.data.hls_url,
                    hasDrmKey: !!response.data.drm_key,
                    keyId: response.data.key_id,
                    timestamp: new Date().toISOString()
                });

                // Open player with extracted data
                openPlayerWithChannelData(response.data, channelId);
            } else {
                showAlert('danger', `Error extracting channel data: ${response.message}`);
            }
        },
        error: function(xhr) {
            // Remove loading indicator
            document.body.removeChild(loadingDiv);

            let errorMessage = 'Error extracting channel data';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function playUnencryptedChannel(channelData, channelId, loadingDiv) {
    // Update loading message for unencrypted channels
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="width: 60px; height: 60px; border: 4px solid rgba(116, 185, 255, 0.2); border-left: 4px solid #74b9ff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div>📺 Preparing Stream...</div>
            <div style="font-size: 14px; opacity: 0.8; margin-top: 10px;">Channel is not encrypted</div>
            <div style="font-size: 12px; opacity: 0.6; margin-top: 5px;">Opening player...</div>
        </div>
    `;

    try {
        // Cache the stream for dynamic proxy
        $.ajax({
            url: '/shahid/api/channels/cache-stream',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: JSON.stringify({
                channel_id: channelId,
                stream_url: channelData.stream_url,
                token: channelData.token || ''
            }),
            success: function(cacheResponse) {
                // Remove loading indicator
                document.body.removeChild(loadingDiv);

                if (cacheResponse.success) {
                    // Open player with unencrypted stream
                    const playerData = {
                        stream_url: cacheResponse.proxy_url,
                        stream_type: channelData.stream_type || 'hls',
                        title: `Channel ${channelId}`,
                        is_encrypted: false
                    };

                    openPlayerWithChannelData(playerData, channelId);
                } else {
                    showAlert('danger', 'Failed to cache stream for playback');
                }
            },
            error: function(xhr) {
                // Remove loading indicator
                document.body.removeChild(loadingDiv);
                showAlert('danger', 'Error caching stream for playback');
            }
        });
    } catch (error) {
        // Remove loading indicator
        document.body.removeChild(loadingDiv);
        showAlert('danger', 'Error preparing unencrypted channel: ' + error.message);
    }
}

function getChannelDetails(channelId) {
    if (!channelId) {
        showAlert('warning', 'Invalid channel ID');
        return;
    }
    
    showLoading();
    
    $.ajax({
        url: '/api/shahid/content-details',
        method: 'GET',
        data: {
            content_id: channelId
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                showChannelDetailsModal(response.data);
            } else {
                showAlert('danger', response.message || 'Failed to fetch channel details');
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error fetching details';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function showChannelDetailsModal(channelData) {
    // Extract channel information
    let data = channelData.data || channelData;
    let productModel = data.productModel || {};
    
    let title = productModel.title || 'غير متوفر';
    let description = productModel.description || productModel.shortDescription || 'لا توجد معلومات متاحة';
    let channelId = productModel.id || data.id || 'غير متوفر';
    
    // Extract poster image
    let posterImage = '';
    if (productModel.image && productModel.image.posterImage) {
        posterImage = productModel.image.posterImage;
    } else if (productModel.thumbnailImage) {
        posterImage = productModel.thumbnailImage;
    }
    
    const modalHtml = `
        <div class="modal fade" id="channelDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: none; border-radius: 15px;">
                    <div class="modal-header border-0" style="background: rgba(255,255,255,0.1); border-radius: 15px 15px 0 0;">
                        <h5 class="modal-title text-white fw-bold">
                            <i class="fas fa-broadcast-tower me-2 text-danger"></i>تفاصيل القناة
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="row">
                            ${posterImage ? `
                            <div class="col-md-4 mb-3">
                                <img src="${posterImage}" alt="${title}" class="img-fluid rounded shadow-lg" style="width: 100%; max-height: 300px; object-fit: cover;">
                            </div>
                            ` : ''}
                            <div class="${posterImage ? 'col-md-8' : 'col-12'}">
                                <div class="channel-info">
                                    <h3 class="text-white mb-3 fw-bold">${title}</h3>
                                    
                                    <div class="info-item mb-3">
                                        <span class="badge bg-danger me-2">
                                            <i class="fas fa-hashtag me-1"></i>Channel ID
                                        </span>
                                        <span class="text-light">${channelId}</span>
                                    </div>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-success me-2">
                                            <i class="fas fa-circle me-1"></i>Status
                                        </span>
                                        <span class="text-light">Live</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="description-section">
                                    <h5 class="text-warning mb-3">
                                        <i class="fas fa-info-circle me-2"></i>وصف القناة
                                    </h5>
                                    <div class="description-content p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #dc3545;">
                                        <p class="text-light mb-0 lh-lg" style="font-size: 16px;">${description}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer border-0" style="background: rgba(255,255,255,0.05); border-radius: 0 0 15px 15px;">
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    $('#channelDetailsModal').remove();
    
    // Add new modal to body
    $('body').append(modalHtml);
    
    // Show modal
    $('#channelDetailsModal').modal('show');
}

function showTokenModal() {
    $('#tokenModal').modal('show');
}

function saveToken() {
    const token = $('#shahidToken').val().trim();
    
    if (!token) {
        showAlert('warning', 'Please enter the token');
        return;
    }
    
    $.ajax({
        url: '/api/shahid/auth/token',
        method: 'POST',
        data: {
            token: token,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Token saved successfully');
                $('#tokenModal').modal('hide');
                $('#shahidToken').val('');
                loadChannels(); // Reload channels
            } else {
                showAlert('danger', response.message || 'Failed to save token');
            }
        },
        error: function(xhr) {
            let errorMessage = 'Error saving token';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function showLoading() {
    $('#loadingIndicator').removeClass('d-none');
}

function hideLoading() {
    $('#loadingIndicator').addClass('d-none');
    // Remove loading state from refresh buttons
    $('.refresh-btn').removeClass('loading');
}

function hideWelcome() {
    $('#welcomeMessage').addClass('d-none');
}

function showWelcome() {
    $('#welcomeMessage').removeClass('d-none');
}

function showResults() {
    $('#channelsContainer').removeClass('d-none');
    $('#noResults').addClass('d-none');
}

function hideResults() {
    $('#channelsContainer').addClass('d-none');
}

function showNoResults() {
    $('#noResults').removeClass('d-none');
    $('#channelsContainer').addClass('d-none');
}

function showDRMResults(drmData, title, contentId) {
    const modalHtml = `
        <div class="modal fade" id="drmResultsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: none; border-radius: 15px;">
                    <div class="modal-header border-0" style="background: rgba(255,255,255,0.1); border-radius: 15px 15px 0 0;">
                        <h5 class="modal-title text-white fw-bold">
                            <i class="fas fa-key me-2 text-warning"></i>DRM Information Extracted
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-success border-0" style="background: rgba(40, 167, 69, 0.2); border-left: 4px solid #28a745 !important;">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-check-circle me-2"></i>Successfully Extracted DRM Data
                                    </h6>
                                    <p class="text-light mb-0">Content: <strong>${title}</strong> (ID: ${contentId})</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="drm-info-card p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #74b9ff;">
                                    <h6 class="text-info mb-3">
                                        <i class="fas fa-film me-2"></i>Stream Information
                                    </h6>
                                    <div class="info-item mb-2">
                                        <small class="text-muted">Stream URL:</small>
                                        <div class="bg-dark p-2 rounded mt-1">
                                            <code class="text-light" style="font-size: 12px; word-break: break-all;">${drmData.stream_url || 'N/A'}</code>
                                        </div>
                                        <button class="btn btn-outline-info btn-sm mt-1" onclick="copyToClipboard('${drmData.stream_url || ''}', 'Stream URL')">
                                            <i class="fas fa-copy me-1"></i>Copy
                                        </button>
                                    </div>
                                    <div class="info-item mb-2">
                                        <small class="text-muted">Stream Type:</small>
                                        <span class="badge bg-info ms-2">${drmData.stream_type || 'Unknown'}</span>
                                    </div>
                                    <div class="info-item">
                                        <small class="text-muted">Encryption:</small>
                                        <span class="badge ${drmData.is_encrypted ? 'bg-warning' : 'bg-success'} ms-2">
                                            ${drmData.is_encrypted ? 'Encrypted (' + (drmData.drm_type || 'Unknown') + ')' : 'Not Encrypted'}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="drm-info-card p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #e74c3c;">
                                    <h6 class="text-danger mb-3">
                                        <i class="fas fa-shield-alt me-2"></i>DRM Keys
                                    </h6>
                                    ${drmData.is_encrypted ? `
                                    <div class="info-item mb-2">
                                        <small class="text-muted">Key ID (KID):</small>
                                        <div class="bg-dark p-2 rounded mt-1">
                                            <code class="text-warning" style="font-size: 12px; word-break: break-all;">${drmData.kid || 'N/A'}</code>
                                        </div>
                                        <button class="btn btn-outline-warning btn-sm mt-1" onclick="copyToClipboard('${drmData.kid || ''}', 'KID')">
                                            <i class="fas fa-copy me-1"></i>Copy
                                        </button>
                                    </div>
                                    <div class="info-item">
                                        <small class="text-muted">Decryption Key:</small>
                                        <div class="bg-dark p-2 rounded mt-1">
                                            <code class="text-success" style="font-size: 12px; word-break: break-all;">${drmData.key || 'N/A'}</code>
                                        </div>
                                        <button class="btn btn-outline-success btn-sm mt-1" onclick="copyToClipboard('${drmData.key || ''}', 'Key')">
                                            <i class="fas fa-copy me-1"></i>Copy
                                        </button>
                                    </div>
                                    ` : `
                                    <div class="alert alert-info border-0" style="background: rgba(23, 162, 184, 0.2);">
                                        <i class="fas fa-info-circle me-2"></i>
                                        This channel is not encrypted and doesn't require DRM keys.
                                    </div>
                                    `}
                                </div>
                            </div>
                        </div>

                        ${drmData.is_encrypted ? `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="drm-info-card p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #9b59b6;">
                                    <h6 class="text-purple mb-3" style="color: #9b59b6;">
                                        <i class="fas fa-lock me-2"></i>PSSH Data
                                    </h6>
                                    <div class="info-item">
                                        <small class="text-muted">PSSH (Protection System Specific Header):</small>
                                        <div class="bg-dark p-2 rounded mt-1" style="max-height: 120px; overflow-y: auto;">
                                            <code class="text-light" style="font-size: 11px; word-break: break-all;">${drmData.pssh || 'N/A'}</code>
                                        </div>
                                        <button class="btn btn-outline-light btn-sm mt-1" onclick="copyToClipboard('${drmData.pssh || ''}', 'PSSH')">
                                            <i class="fas fa-copy me-1"></i>Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${drmData.is_encrypted && drmData.formatted_key ? `
                        <div class="row">
                            <div class="col-12">
                                <div class="drm-info-card p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #f39c12;">
                                    <h6 class="text-warning mb-3">
                                        <i class="fas fa-tools me-2"></i>Formatted Key (for tools)
                                    </h6>
                                    <div class="info-item">
                                        <small class="text-muted">KID:Key format:</small>
                                        <div class="bg-dark p-2 rounded mt-1">
                                            <code class="text-warning" style="font-size: 12px; word-break: break-all;">${drmData.formatted_key || 'N/A'}</code>
                                        </div>
                                        <button class="btn btn-outline-warning btn-sm mt-1" onclick="copyToClipboard('${drmData.formatted_key || ''}', 'Formatted Key')">
                                            <i class="fas fa-copy me-1"></i>Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${drmData.quality_info && drmData.quality_info.length > 0 ? `
                        <div class="row">
                            <div class="col-12">
                                <div class="drm-info-card p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #17a2b8;">
                                    <h6 class="text-info mb-3">
                                        <i class="fas fa-video me-2"></i>Quality Information
                                    </h6>
                                    <div class="row">
                                        ${drmData.quality_info.map(quality => `
                                            <div class="col-md-4 mb-2">
                                                <div class="bg-dark p-2 rounded">
                                                    <small class="text-muted">Resolution:</small>
                                                    <div class="text-info">${quality.width}x${quality.height}</div>
                                                    <small class="text-muted">Bandwidth:</small>
                                                    <div class="text-light">${Math.round(quality.bandwidth / 1000)} Kbps</div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                    </div>
                    <div class="modal-footer border-0" style="background: rgba(255,255,255,0.05); border-radius: 0 0 15px 15px;">
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="button" class="btn btn-warning" onclick="copyAllDRMData('${JSON.stringify(drmData).replace(/'/g, "\\'")}')">
                            <i class="fas fa-copy me-2"></i>Copy All Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#drmResultsModal').remove();

    // Add new modal to body
    $('body').append(modalHtml);

    // Show modal
    $('#drmResultsModal').modal('show');
}

function copyToClipboard(text, label) {
    if (!text) {
        showAlert('warning', `No ${label} to copy`);
        return;
    }

    navigator.clipboard.writeText(text).then(function() {
        showAlert('success', `${label} copied to clipboard!`);
    }).catch(function(err) {
        showAlert('danger', `Failed to copy ${label}: ` + err);
    });
}

function copyAllDRMData(drmDataJson) {
    try {
        const drmData = JSON.parse(drmDataJson);
        const allData = `MPD URL: ${drmData.stream_url || 'N/A'}
KID: ${drmData.kid || 'N/A'}
Key: ${drmData.key || 'N/A'}
PSSH: ${drmData.pssh || 'N/A'}
Formatted Key: ${drmData.formatted_key || 'N/A'}
License URL: ${drmData.license_url || 'N/A'}`;

        navigator.clipboard.writeText(allData).then(function() {
            showAlert('success', 'All DRM data copied to clipboard!');
        }).catch(function(err) {
            showAlert('danger', 'Failed to copy data: ' + err);
        });
    } catch (e) {
        showAlert('danger', 'Error copying data: ' + e.message);
    }
}

function showAlert(type, message) {
    // Convert old alert types to new Toast system
    switch(type) {
        case 'success':
            showSuccess('Success', message);
            break;
        case 'danger':
        case 'error':
            showError('Error', message);
            break;
        case 'warning':
            showWarning('Warning', message);
            break;
        case 'info':
        default:
            showInfo('Information', message);
            break;
    }
}

// Search channels function
function searchChannels() {
    const query = $('#searchInput').val().trim();

    if (!query) {
        showAlert('warning', 'Please enter a search term');
        return;
    }

    console.log('Searching for:', query);

    showLoading();
    hideResults();
    hideWelcome();
    hideSearchResults();

    $('#loadingIndicator p').text('Searching channels...');

    // Filter current channels if they exist
    if (currentChannels && currentChannels.length > 0) {
        const filteredChannels = currentChannels.filter(channel =>
            channel.name.toLowerCase().includes(query.toLowerCase())
        );

        hideLoading();

        if (filteredChannels.length > 0) {
            displayChannels(filteredChannels);
            showResults();
            showSearchResults(query, filteredChannels.length);
        } else {
            showNoResults();
        }
    } else {
        // Load channels first, then search
        loadChannels();
    }
}

// Clear search and reload channels
function clearSearch() {
    $('#searchInput').val('');
    hideSearchResults();
    if (currentChannels && currentChannels.length > 0) {
        displayChannels(currentChannels);
        showResults();
    } else {
        loadChannels();
    }
}

// Show search results info
function showSearchResults(query, count) {
    $('#searchTerm').text(query);
    $('#searchCount').text(count);
    $('#searchResultsInfo').removeClass('d-none');
}

// Hide search results info
function hideSearchResults() {
    $('#searchResultsInfo').addClass('d-none');
}

// Add Enter key support for search
$(document).ready(function() {
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            searchChannels();
        }
    });

    // Ensure Toast functions are available for channels page
    if (typeof Toast === 'undefined' || typeof showError === 'undefined') {
        console.warn('Toast functions not found, creating local instances');

        // Create local Toast instance
        const LocalToast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer);
                toast.addEventListener('mouseleave', Swal.resumeTimer);
            }
        });

        // Define functions locally if not available globally
        if (typeof showSuccess === 'undefined') {
            window.showSuccess = function(title, message = '') {
                LocalToast.fire({
                    iconHtml: '<i class="fas fa-check-circle" style="color: #28a745;"></i>',
                    title: title || 'Success!',
                    text: message
                });
            };
        }

        if (typeof showError === 'undefined') {
            window.showError = function(title, message = '') {
                LocalToast.fire({
                    iconHtml: '<i class="fas fa-times-circle" style="color: #dc3545;"></i>',
                    title: title || 'Error!',
                    text: message,
                    timer: 4000
                });
            };
        }

        if (typeof showWarning === 'undefined') {
            window.showWarning = function(title, message = '') {
                LocalToast.fire({
                    iconHtml: '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>',
                    title: title || 'Warning!',
                    text: message,
                    timer: 3500
                });
            };
        }

        if (typeof showInfo === 'undefined') {
            window.showInfo = function(title, message = '') {
                LocalToast.fire({
                    iconHtml: '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>',
                    title: title || 'Information',
                    text: message
                });
            };
        }

        console.log('✅ Local Toast functions created for channels page');
    }
});

/**
 * Open player with extracted channel data
 */
async function openPlayerWithChannelData(channelData, channelId) {
    try {
        // 🎯 Enhanced logging for channel player opening
        console.log(`🎬 OPENING PLAYER FOR CHANNEL: ${channelId}`);
        console.log('📺 Channel Data:', channelData);

        // Extract required data
        const originalStreamUrl = channelData.stream_url;
        const keyId = channelData.kid;
        const key = channelData.key;
        const title = channelData.channel_title || `Channel ${channelId}`;
        const streamType = channelData.stream_type || 'dash';
        const token = channelData.token || null;
        const isEncrypted = channelData.is_encrypted || false;

        // 🎯 Log all extracted stream details
        console.log(`📡 STREAM DETAILS:`, {
            channelId: channelId,
            title: title,
            originalStreamUrl: originalStreamUrl,
            streamType: streamType,
            isEncrypted: isEncrypted,
            hasKey: !!key,
            keyId: keyId,
            hasToken: !!token,
            timestamp: new Date().toISOString()
        });

        // Validate required data
        if (!originalStreamUrl) {
            showAlert('danger', 'No stream URL found for this channel');
            return;
        }

        // Detect stream type from URL (more reliable than API response)
        let actualStreamType = 'dash'; // default
        if (originalStreamUrl.includes('.m3u8')) {
            actualStreamType = 'hls';
        } else if (originalStreamUrl.includes('.mpd')) {
            actualStreamType = 'dash';
        }

        let finalStreamUrl = originalStreamUrl;

        // For HLS streams, use dynamic proxy (Flask-style)
        if (actualStreamType === 'hls') {
            try {
                console.log('🔄 Using dynamic proxy for HLS stream...');

                // Cache stream in session for dynamic proxy
                const cacheResponse = await fetch('/shahid/api/channels/cache-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        channel_id: channelId,
                        stream_url: originalStreamUrl,
                        token: token
                    })
                });

                const cacheResult = await cacheResponse.json();

                if (cacheResult.success) {
                    // ✅ Use dynamic proxy URL
                    finalStreamUrl = cacheResult.proxy_url;
                    console.log('✅ Stream cached successfully! Channel ID:', channelId);
                    console.log('🔗 Using dynamic proxy URL:', finalStreamUrl);
                } else {
                    console.warn('⚠️ Cache failed, using original URL:', cacheResult.error);
                    showAlert('warning', 'Dynamic proxy setup failed, using direct URL. Playback may be limited.');
                }
            } catch (cacheError) {
                console.error('❌ Cache error:', cacheError);
                console.log('🔄 Falling back to original URL');
                showAlert('warning', 'Dynamic proxy setup failed, using direct URL. Playback may be limited.');
            }
        } else if (actualStreamType === 'dash') {
            // For DASH/MPD streams, use VideoProxyController
            console.log('🎬 Using VideoProxyController for DASH stream...');
            finalStreamUrl = `/video-proxy/manifest?url=${encodeURIComponent(originalStreamUrl)}`;
            console.log('🔗 Using VideoProxy URL:', finalStreamUrl);
        }

        // Build player URL using the same route format as movies/episodes
        let playerUrl = `/shahid/play/channel/${channelId}?`;
        const params = new URLSearchParams();

        // Add stream URL with correct type (same format as movies)
        if (actualStreamType === 'hls') {
            params.append('hls', finalStreamUrl);
            params.append('type', 'hls');
            console.log('🎬 Using HLS stream:', finalStreamUrl.includes('/shahid/channel/') ? 'Dynamic Proxy' : 'Direct');
        } else {
            params.append('mpd', finalStreamUrl);
            params.append('type', 'dash');
            console.log('🎬 Using DASH stream:', finalStreamUrl.includes('/video-proxy/') ? 'VideoProxyController' : 'Direct');
        }

        // Add title
        params.append('title', title);

        // Add DRM keys if available (for encrypted channels)
        if (channelData.is_encrypted && keyId && key) {
            params.append('keyId', keyId);
            params.append('key', key);
            console.log('✅ Channel is encrypted, adding DRM keys:', { keyId, key: key.substring(0, 8) + '...' });
        } else if (channelData.is_encrypted && (!keyId || !key)) {
            console.warn('⚠️ Channel is encrypted but keys are missing');
            showAlert('warning', 'Channel is encrypted but DRM keys could not be extracted. Playback may fail.');
        } else {
            console.log('ℹ️ Channel is not encrypted, no DRM keys needed');
        }

        playerUrl += params.toString();

        console.log('🚀 Opening channel player URL:', playerUrl);

        // Open player in same tab (like movies/episodes)
        window.location.href = playerUrl;

    } catch (error) {
        console.error('Error opening player:', error);
        showAlert('danger', 'Error opening player: ' + error.message);
    }
}
</script>
@endsection
