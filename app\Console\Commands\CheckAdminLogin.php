<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;

class CheckAdminLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:check-login';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check admin login credentials';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking admin login...');

        $admin = Admin::where('email', '<EMAIL>')->first();

        if (!$admin) {
            $this->error('Admin not found!');
            return;
        }

        $this->info('Admin found: ' . $admin->name);
        $this->info('Email: ' . $admin->email);
        $this->info('Active: ' . ($admin->is_active ? 'Yes' : 'No'));
        $this->info('Super Admin: ' . ($admin->is_super_admin ? 'Yes' : 'No'));

        $passwordCheck = Hash::check('admin123', $admin->password);
        $this->info('Password check: ' . ($passwordCheck ? 'PASS' : 'FAIL'));

        if (!$passwordCheck) {
            $this->warn('Updating password...');
            $admin->update(['password' => Hash::make('admin123')]);
            $this->info('Password updated!');
        }
    }
}
