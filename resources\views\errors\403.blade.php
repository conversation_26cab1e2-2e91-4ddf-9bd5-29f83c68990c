@extends('admin.layouts.app')

@section('title', 'Access Denied - 403')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <div class="error-icon mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #ff6b6b, #ee5a52); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-ban text-white" style="font-size: 60px;"></i>
                        </div>
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-danger mb-3">403</h1>
                    
                    <!-- Error Title -->
                    <h2 class="h3 text-dark mb-3">Access Denied</h2>
                    
                    <!-- Error Message -->
                    <p class="text-muted mb-4 lead">
                        You don't have permission to access this resource. 
                        <br>Please contact your administrator if you believe this is an error.
                    </p>

                    <!-- Additional Info -->
                    <div class="alert alert-light border-0 mb-4" style="background-color: #f8f9fa;">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <i class="fas fa-info-circle text-info fa-2x"></i>
                            </div>
                            <div class="col-md-10 text-start">
                                <h6 class="mb-1">What can you do?</h6>
                                <ul class="mb-0 text-muted small">
                                    <li>Go back to the previous page</li>
                                    <li>Return to the dashboard</li>
                                    <li>Contact your system administrator</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <button onclick="history.back()" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary px-4">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                        @if(auth()->user() && auth()->user()->isSuperAdmin())
                        <a href="{{ route('admin.admin-management.index') }}" class="btn btn-outline-info px-4">
                            <i class="fas fa-users-cog me-2"></i>Manage Permissions
                        </a>
                        @endif
                    </div>

                    <!-- User Info -->
                    @if(auth()->user())
                    <div class="mt-4 pt-4 border-top">
                        <small class="text-muted">
                            Logged in as: <strong>{{ auth()->user()->name }}</strong>
                            @if(auth()->user()->roles->count() > 0)
                                ({{ auth()->user()->roles->pluck('name')->join(', ') }})
                            @endif
                        </small>
                    </div>
                    @endif
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
}



.btn {
    border-radius: 8px;
    font-weight: 500;
}
</style>
@endsection
