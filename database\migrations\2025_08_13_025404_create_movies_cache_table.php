<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('movies_cache', function (Blueprint $table) {
            $table->id();
            $table->string('country', 10)->index(); // EG, SA, etc.
            $table->string('movie_id')->index(); // Shahid movie ID
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('poster_url')->nullable();
            $table->json('metadata')->nullable(); // year, genres, cast, etc.
            $table->json('movie_data')->nullable(); // Complete movie data from API
            $table->integer('duration')->nullable(); // Duration in minutes
            $table->integer('year')->nullable(); // Release year
            $table->boolean('is_complete')->default(false); // Has complete data or just basic info
            $table->timestamp('last_updated')->useCurrent();
            $table->timestamps();

            // Composite unique index
            $table->unique(['country', 'movie_id']);

            // Additional indexes
            $table->index(['country', 'is_complete']);
            $table->index(['country', 'last_updated']);
            $table->index('year');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('movies_cache');
    }
};
