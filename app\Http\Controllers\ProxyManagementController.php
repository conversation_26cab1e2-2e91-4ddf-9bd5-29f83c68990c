<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProxySettings;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Services\ProxyService;

class ProxyManagementController extends Controller
{
    /**
     * Display proxy management page
     */
    public function index()
    {
        $proxies = ProxySettings::orderBy('is_default', 'desc')
                               ->orderBy('created_at', 'desc')
                               ->get();

        return view('admin.proxy-management', compact('proxies'));
    }

    /**
     * Store a new proxy
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:proxy_settings',
            'host' => 'required|string|max:255',
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
            'type' => 'required|in:http,https,socks4,socks5',
            'description' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $proxy = ProxySettings::create($request->all());

            Log::info("🔧 New proxy added: {$proxy->name}");

            return response()->json([
                'success' => true,
                'message' => 'Proxy added successfully',
                'proxy' => $proxy->getDisplayInfo()
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error adding proxy: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error adding proxy: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update proxy settings
     */
    public function update(Request $request, ProxySettings $proxy)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:proxy_settings,name,' . $proxy->id,
            'host' => 'required|string|max:255',
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
            'type' => 'required|in:http,https,socks4,socks5',
            'description' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $proxy->update($request->all());

            Log::info("🔧 Proxy updated: {$proxy->name}");

            return response()->json([
                'success' => true,
                'message' => 'Proxy updated successfully',
                'proxy' => $proxy->getDisplayInfo()
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error updating proxy: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error updating proxy: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete proxy
     */
    public function destroy(ProxySettings $proxy)
    {
        try {
            $proxyName = $proxy->name;
            $proxy->delete();

            Log::info("🗑️ Proxy deleted: {$proxyName}");

            return response()->json([
                'success' => true,
                'message' => 'Proxy deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error deleting proxy: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting proxy: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test proxy connection
     */
    public function testProxy(ProxySettings $proxy)
    {
        try {
            $results = $proxy->testConnection();

            return response()->json([
                'success' => true,
                'message' => 'Proxy test completed',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error testing proxy: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error testing proxy: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Activate proxy (set as default)
     */
    public function activate(ProxySettings $proxy)
    {
        try {
            $proxy->setAsDefault();

            Log::info("✅ Proxy activated: {$proxy->name}");

            return response()->json([
                'success' => true,
                'message' => "Proxy '{$proxy->name}' activated successfully"
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error activating proxy: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error activating proxy: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate all proxies
     */
    public function deactivateAll()
    {
        try {
            ProxySettings::query()->update([
                'is_active' => false,
                'is_default' => false
            ]);

            Log::info("🔴 All proxies deactivated");

            return response()->json([
                'success' => true,
                'message' => 'All proxies deactivated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error deactivating proxies: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deactivating proxies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current proxy status
     */
    public function getProxyStatus()
    {
        try {
            $activeProxy = ProxySettings::where('is_active', true)->first();

            return response()->json([
                'active' => $activeProxy ? true : false,
                'proxy_name' => $activeProxy ? $activeProxy->name : null,
                'proxy_host' => $activeProxy ? $activeProxy->host : null,
                'proxy_port' => $activeProxy ? $activeProxy->port : null,
                'proxy_type' => $activeProxy ? $activeProxy->type : null
            ]);
        } catch (\Exception $e) {
            Log::error("❌ Error getting proxy status: " . $e->getMessage());
            return response()->json([
                'active' => false,
                'error' => 'Unable to check proxy status'
            ], 500);
        }
    }

    /**
     * Test current proxy connection
     */
    public function testCurrentProxy()
    {
        try {
            $proxyService = app(ProxyService::class);
            $testResult = $proxyService->testCurrentProxy();

            return response()->json([
                'success' => $testResult['success'],
                'message' => $testResult['message'],
                'data' => $testResult
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error testing current proxy: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error testing proxy: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk import proxies from text format
     */
    public function bulkImport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'proxy_list' => 'required|string',
            'default_type' => 'required|in:http,https,socks4,socks5',
            'name_prefix' => 'required|string|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $proxyList = $request->input('proxy_list');
            $defaultType = $request->input('default_type');
            $namePrefix = $request->input('name_prefix');

            $lines = array_filter(array_map('trim', explode("\n", $proxyList)));
            $imported = 0;
            $failed = 0;
            $errors = [];

            foreach ($lines as $index => $line) {
                try {
                    $proxyData = $this->parseProxyUrl($line, $defaultType);

                    if ($proxyData) {
                        // Check if proxy already exists
                        $exists = ProxySettings::where('host', $proxyData['host'])
                                              ->where('port', $proxyData['port'])
                                              ->exists();

                        if (!$exists) {
                            $proxyData['name'] = $namePrefix . ' ' . ($index + 1);
                            $proxyData['description'] = 'Bulk imported proxy';

                            ProxySettings::create($proxyData);
                            $imported++;

                            Log::info("📥 Bulk imported proxy: {$proxyData['host']}:{$proxyData['port']}");
                        } else {
                            $failed++;
                            $errors[] = "Line " . ($index + 1) . ": Proxy already exists";
                        }
                    } else {
                        $failed++;
                        $errors[] = "Line " . ($index + 1) . ": Invalid proxy format";
                    }
                } catch (\Exception $e) {
                    $failed++;
                    $errors[] = "Line " . ($index + 1) . ": " . $e->getMessage();
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Bulk import completed",
                'imported_count' => $imported,
                'failed_count' => $failed,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error in bulk import: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error importing proxies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Parse proxy URL format: protocol://username:password@host:port
     */
    private function parseProxyUrl($url, $defaultType = 'http')
    {
        // Remove whitespace
        $url = trim($url);

        if (empty($url)) {
            return null;
        }

        // Parse URL
        $parsed = parse_url($url);

        if (!$parsed || !isset($parsed['host'])) {
            return null;
        }

        return [
            'host' => $parsed['host'],
            'port' => $parsed['port'] ?? 8080,
            'username' => $parsed['user'] ?? null,
            'password' => $parsed['pass'] ?? null,
            'type' => $parsed['scheme'] ?? $defaultType,
            'is_active' => false,
            'is_default' => false
        ];
    }

}
