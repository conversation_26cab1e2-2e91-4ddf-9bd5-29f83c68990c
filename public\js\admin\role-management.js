// Role Management JavaScript
class RoleManagement {
    constructor() {
        this.currentPage = 1;
        this.perPage = 15;
        this.filters = {};
        this.init();
    }

    init() {
        this.loadStatistics();
        this.loadRoles();
        this.loadPermissions();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Prevent duplicate event listeners
        if (this.listenersSetup) {
            return;
        }
        this.listenersSetup = true;

        // Search and filters
        document.getElementById('search-input').addEventListener('input',
            this.debounce(() => this.applyFilters(), 500));

        document.getElementById('apply-filters').addEventListener('click', () => this.applyFilters());
        document.getElementById('clear-filters').addEventListener('click', () => this.clearFilters());

        // Form submissions
        const createForm = document.getElementById('create-role-form');
        if (createForm) {
            createForm.addEventListener('submit', (e) => this.handleCreateRole(e));
        }

        const editForm = document.getElementById('edit-role-form');
        if (editForm) {
            editForm.addEventListener('submit', (e) => this.handleEditRole(e));
        }

        // Save permissions button
        const savePermissionsBtn = document.getElementById('save-permissions');
        if (savePermissionsBtn) {
            savePermissionsBtn.addEventListener('click', () => this.handleSavePermissions());
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async loadStatistics() {
        try {
            const response = await fetch('/admin/roles/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.updateStatistics(data.data);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateStatistics(stats) {
        document.getElementById('total-roles').textContent = stats.total_roles || 0;
        document.getElementById('active-roles').textContent = stats.roles_with_users || 0;
        document.getElementById('empty-roles').textContent = stats.empty_roles || 0;
        document.getElementById('system-roles').textContent = stats.system_roles || 0;
    }

    async loadRoles(page = 1) {
        try {
            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage,
                ...this.filters
            });

            const response = await fetch(`/admin/roles/api?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderRoles(data.data.data);
                this.renderPagination(data.data);
                this.currentPage = page;
            }
        } catch (error) {
            console.error('Error loading roles:', error);
            this.showAlert('Error loading roles', 'danger');
        }
    }

    renderRoles(roles) {
        const tbody = document.getElementById('roles-tbody');
        tbody.innerHTML = '';

        roles.forEach(role => {
            const row = this.createRoleRow(role);
            tbody.appendChild(row);
        });
    }

    createRoleRow(role) {
        const row = document.createElement('tr');
        
        // System role styling
        const isSystemRole = ['super-admin', 'admin', 'user'].includes(role.name);
        if (isSystemRole) {
            row.classList.add('system-role');
        }

        // Permissions preview
        const permissionsPreview = role.permissions.slice(0, 3).map(p => 
            `<span class="badge bg-secondary permission-badge">${p.name}</span>`
        ).join('');
        const morePermissions = role.permissions.length > 3 ? 
            `<span class="text-muted">+${role.permissions.length - 3} more</span>` : '';

        row.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <div>
                        <div class="fw-bold">${role.name}</div>
                        ${isSystemRole ? '<small class="text-muted">System Role</small>' : ''}
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-info">${role.guard_name}</span>
            </td>
            <td>
                <span class="badge bg-primary">${role.users_count}</span>
            </td>
            <td>
                <div class="permissions-preview">
                    ${permissionsPreview}
                    ${morePermissions}
                </div>
            </td>
            <td>
                <small class="text-muted">${new Date(role.created_at).toLocaleDateString()}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="roleManagement.viewRole(${role.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="roleManagement.managePermissions(${role.id}, '${role.name}')" title="Manage Permissions">
                        <i class="fas fa-key"></i>
                    </button>
                    ${!isSystemRole ? `
                        <button class="btn btn-outline-warning" onclick="roleManagement.editRole(${role.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="roleManagement.deleteRole(${role.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        `;

        return row;
    }

    renderPagination(paginationData) {
        const container = document.getElementById('pagination-container');
        if (!paginationData.links || paginationData.links.length <= 3) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<ul class="pagination justify-content-center">';
        
        paginationData.links.forEach(link => {
            const isActive = link.active ? 'active' : '';
            const isDisabled = !link.url ? 'disabled' : '';
            const page = this.extractPageFromUrl(link.url);
            
            paginationHTML += `
                <li class="page-item ${isActive} ${isDisabled}">
                    <a class="page-link" href="#" onclick="roleManagement.loadRoles(${page}); return false;">
                        ${link.label}
                    </a>
                </li>
            `;
        });
        
        paginationHTML += '</ul>';
        container.innerHTML = paginationHTML;
    }

    extractPageFromUrl(url) {
        if (!url) return 1;
        const match = url.match(/page=(\d+)/);
        return match ? parseInt(match[1]) : 1;
    }

    applyFilters() {
        this.filters = {
            search: document.getElementById('search-input').value,
            guard: document.getElementById('guard-filter').value,
        };

        // Remove empty filters
        Object.keys(this.filters).forEach(key => {
            if (!this.filters[key]) {
                delete this.filters[key];
            }
        });

        this.loadRoles(1);
    }

    clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('guard-filter').value = '';
        
        this.filters = {};
        this.loadRoles(1);
    }

    async loadPermissions() {
        try {
            const response = await fetch('/admin/permissions/api/all');
            const data = await response.json();

            if (data.success) {
                this.permissions = data.data.data;
                this.renderPermissionsForCreate();
            }
        } catch (error) {
            console.error('Error loading permissions:', error);
        }
    }

    renderPermissionsForCreate() {
        const container = document.getElementById('permissions-list');
        if (!container || !this.permissions) return;

        // Group permissions by category
        const categories = this.groupPermissionsByCategory(this.permissions);
        
        container.innerHTML = '';
        
        Object.keys(categories).forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'permission-category';
            categoryDiv.innerHTML = `
                <h6 class="text-primary">${this.formatCategoryName(category)}</h6>
            `;
            
            categories[category].forEach(permission => {
                const permissionDiv = document.createElement('div');
                permissionDiv.className = 'permission-item';
                permissionDiv.innerHTML = `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="permissions[]" value="${permission.name}" id="perm-${permission.id}">
                        <label class="form-check-label" for="perm-${permission.id}">
                            ${permission.name}
                        </label>
                    </div>
                `;
                categoryDiv.appendChild(permissionDiv);
            });
            
            container.appendChild(categoryDiv);
        });
    }

    groupPermissionsByCategory(permissions) {
        const categories = {
            user_management: [],
            admin_management: [],
            role_management: [],
            permission_management: [],
            subscription_management: [],
            system: []
        };

        permissions.forEach(permission => {
            if (permission.name.includes('user')) {
                categories.user_management.push(permission);
            } else if (permission.name.includes('admin')) {
                categories.admin_management.push(permission);
            } else if (permission.name.includes('role')) {
                categories.role_management.push(permission);
            } else if (permission.name.includes('permission')) {
                categories.permission_management.push(permission);
            } else if (permission.name.includes('subscription')) {
                categories.subscription_management.push(permission);
            } else {
                categories.system.push(permission);
            }
        });

        return categories;
    }

    formatCategoryName(category) {
        const names = {
            user_management: 'User Management',
            admin_management: 'Admin Management',
            role_management: 'Role Management',
            permission_management: 'Permission Management',
            subscription_management: 'Subscription Management',
            system: 'System'
        };
        return names[category] || category;
    }

    async handleCreateRole(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());
        
        // Handle multiple permissions
        const permissions = formData.getAll('permissions[]');
        data.permissions = permissions;

        try {
            const response = await fetch('/admin/roles/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Role created successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createRoleModal')).hide();
                e.target.reset();
                this.loadRoles();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error creating role', 'danger');
            }
        } catch (error) {
            console.error('Error creating role:', error);
            this.showAlert('Error creating role', 'danger');
        }
    }

    async handleEditRole(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());
        const roleId = data.id;

        try {
            const response = await fetch(`/admin/roles/api/${roleId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Role updated successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editRoleModal')).hide();
                this.loadRoles();
                this.loadStatistics();
            } else {
                this.showAlert(result.message || 'Error updating role', 'danger');
            }
        } catch (error) {
            console.error('Error updating role:', error);
            this.showAlert('Error updating role', 'danger');
        }
    }

    async viewRole(roleId) {
        try {
            const response = await fetch(`/admin/roles/api/${roleId}`);
            const data = await response.json();

            if (data.success) {
                this.showRoleDetails(data.data);
            }
        } catch (error) {
            console.error('Error loading role details:', error);
            this.showAlert('Error loading role details', 'danger');
        }
    }

    async editRole(roleId) {
        try {
            const response = await fetch(`/admin/roles/api/${roleId}`);
            const data = await response.json();

            if (data.success) {
                const role = data.data;

                // Populate edit form
                document.getElementById('edit-role-id').value = role.id;
                document.getElementById('edit-role-name').value = role.name;
                document.getElementById('edit-role-description').value = role.description || '';
                document.getElementById('edit-role-guard').value = role.guard_name;

                // Show edit modal
                const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
                modal.show();
            }
        } catch (error) {
            console.error('Error loading role for edit:', error);
            this.showAlert('Error loading role for edit', 'danger');
        }
    }

    showRoleDetails(role) {
        const modal = new bootstrap.Modal(document.getElementById('roleDetailsModal'));
        const content = document.getElementById('role-details-content');
        
        const permissionsList = role.permissions.map(p => 
            `<span class="badge bg-secondary me-1 mb-1">${p.name}</span>`
        ).join('');

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Role Information</h6>
                    <p><strong>Name:</strong> ${role.name}</p>
                    <p><strong>Guard:</strong> ${role.guard_name}</p>
                    <p><strong>Users Count:</strong> ${role.users_count}</p>
                    <p><strong>Created:</strong> ${new Date(role.created_at).toLocaleString()}</p>
                </div>
                <div class="col-md-6">
                    <h6>Permissions (${role.permissions.length})</h6>
                    <div style="max-height: 200px; overflow-y: auto;">
                        ${permissionsList || '<p class="text-muted">No permissions assigned</p>'}
                    </div>
                </div>
            </div>
        `;
        
        modal.show();
    }

    async managePermissions(roleId, roleName) {
        this.currentRoleId = roleId;

        try {
            const url = `/admin/roles/api/${roleId}`;
            console.log('Fetching role data from:', url);
            const response = await fetch(url);
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                console.error('Response not OK:', response.status, response.statusText);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success) {
                document.getElementById('role-name-display').textContent = roleName;
                this.renderPermissionsForRole(data.data);
                const modal = new bootstrap.Modal(document.getElementById('managePermissionsModal'));
                modal.show();
            }
        } catch (error) {
            console.error('Error loading role permissions:', error);
            this.showAlert('Error loading role permissions', 'danger');
        }
    }

    renderPermissionsForRole(role) {
        const container = document.getElementById('role-permissions-list');
        if (!container || !this.permissions) return;

        const rolePermissions = role.permissions.map(p => p.name);
        const categories = this.groupPermissionsByCategory(this.permissions);
        
        container.innerHTML = '';
        
        Object.keys(categories).forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'permission-category';
            categoryDiv.innerHTML = `
                <h6 class="text-primary">${this.formatCategoryName(category)}</h6>
            `;
            
            categories[category].forEach(permission => {
                const isChecked = rolePermissions.includes(permission.name);
                const permissionDiv = document.createElement('div');
                permissionDiv.className = 'permission-item';
                permissionDiv.innerHTML = `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="role_permissions[]" value="${permission.name}" 
                               id="role-perm-${permission.id}" ${isChecked ? 'checked' : ''}>
                        <label class="form-check-label" for="role-perm-${permission.id}">
                            ${permission.name}
                        </label>
                    </div>
                `;
                categoryDiv.appendChild(permissionDiv);
            });
            
            container.appendChild(categoryDiv);
        });
    }

    async handleSavePermissions() {
        const checkboxes = document.querySelectorAll('input[name="role_permissions[]"]:checked');
        const permissions = [...new Set(Array.from(checkboxes).map(cb => cb.value))]; // Remove duplicates

        console.log('Saving permissions for role:', this.currentRoleId);
        console.log('Selected permissions:', permissions);

        try {
            const url = `/admin/roles/api/${this.currentRoleId}/sync-permissions`;
            console.log('Making request to:', url);
            console.log('Request method: POST');
            console.log('Request body:', JSON.stringify({ permissions }));

            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            console.log('CSRF Token:', csrfToken);

            if (!csrfToken) {
                throw new Error('CSRF token not found');
            }

            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ permissions }),
                credentials: 'same-origin'
            };

            console.log('Request options:', requestOptions);

            const response = await fetch(url, requestOptions);

            console.log('Response status:', response.status);
            console.log('Response URL:', response.url);
            console.log('Response redirected:', response.redirected);
            console.log('Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const text = await response.text();
                console.log('Error response text:', text);
                console.log('Error response content type:', response.headers.get('content-type'));

                // Try to parse as JSON if possible
                try {
                    const errorJson = JSON.parse(text);
                    console.log('Error response JSON:', errorJson);
                } catch (e) {
                    console.log('Error response is not JSON');
                }

                throw new Error(`HTTP ${response.status}: ${text}`);
            }

            const result = await response.json();

            if (result.success) {
                this.showAlert('Permissions updated successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('managePermissionsModal')).hide();
                this.loadRoles();
            } else {
                this.showAlert(result.message || 'Error updating permissions', 'danger');
            }
        } catch (error) {
            console.error('Error updating permissions:', error);
            this.showAlert('Error updating permissions', 'danger');
        }
    }

    async deleteRole(roleId) {
        // Show confirmation dialog
        if (!confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/admin/roles/api/${roleId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Role deleted successfully!', 'success');
                this.loadRoles(); // Reload the roles list
            } else {
                this.showAlert(result.message || 'Error deleting role', 'danger');
            }
        } catch (error) {
            console.error('Error deleting role:', error);
            this.showAlert('Error deleting role', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Prevent multiple initializations
    if (window.roleManagement) {
        return;
    }
    window.roleManagement = new RoleManagement();
});
