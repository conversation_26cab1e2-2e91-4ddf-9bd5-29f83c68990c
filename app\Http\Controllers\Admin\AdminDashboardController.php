<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        // Middleware is handled by routes
    }

    /**
     * Display admin dashboard.
     */
    public function index()
    {
        return view('admin.dashboard.index');
    }

    /**
     * Get dashboard statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            // User Statistics
            'users' => [
                'total' => User::count(),
                'active' => User::where('is_active', true)->count(),
                'inactive' => User::where('is_active', false)->count(),
                'new_this_month' => User::whereMonth('created_at', now()->month)
                                       ->whereYear('created_at', now()->year)
                                       ->count(),
            ],

            // Subscription Statistics
            'subscriptions' => [
                'total' => UserSubscription::count(),
                'active' => UserSubscription::where('status', 'active')
                                           ->where('expires_at', '>', now())
                                           ->count(),
                'expired' => UserSubscription::where('expires_at', '<=', now())->count(),
                'expiring_soon' => UserSubscription::where('status', 'active')
                                                  ->where('expires_at', '>', now())
                                                  ->where('expires_at', '<=', now()->addDays(7))
                                                  ->count(),
            ],

            // Plan Statistics
            'plans' => [
                'total' => SubscriptionPlan::count(),
                'active' => SubscriptionPlan::where('is_active', true)->count(),
                'inactive' => SubscriptionPlan::where('is_active', false)->count(),
            ],

            // Revenue Statistics
            'revenue' => [
                'this_month' => UserSubscription::whereMonth('created_at', now()->month)
                                               ->whereYear('created_at', now()->year)
                                               ->sum('amount_paid'),
                'last_month' => UserSubscription::whereMonth('created_at', now()->subMonth()->month)
                                               ->whereYear('created_at', now()->subMonth()->year)
                                               ->sum('amount_paid'),
                'this_year' => UserSubscription::whereYear('created_at', now()->year)
                                              ->sum('amount_paid'),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Dashboard statistics retrieved successfully'
        ]);
    }

    /**
     * Get recent activities.
     */
    public function recentActivities(): JsonResponse
    {
        $recentSubscriptions = UserSubscription::with(['user:id,name,email', 'subscriptionPlan:id,name'])
                                              ->latest()
                                              ->limit(10)
                                              ->get()
                                              ->map(function ($subscription) {
                                                  return [
                                                      'type' => 'subscription_created',
                                                      'message' => "New subscription created for {$subscription->user->name}",
                                                      'user' => $subscription->user->name,
                                                      'plan' => $subscription->subscriptionPlan->name,
                                                      'created_at' => $subscription->created_at,
                                                  ];
                                              });

        $recentUsers = User::where('user_type', 'user')
                          ->latest()
                          ->limit(5)
                          ->get()
                          ->map(function ($user) {
                              return [
                                  'type' => 'user_registered',
                                  'message' => "New user registered: {$user->name}",
                                  'user' => $user->name,
                                  'email' => $user->email,
                                  'created_at' => $user->created_at,
                              ];
                          });

        $activities = $recentSubscriptions->concat($recentUsers)
                                         ->sortByDesc('created_at')
                                         ->take(15)
                                         ->values();

        return response()->json([
            'success' => true,
            'data' => $activities,
            'message' => 'Recent activities retrieved successfully'
        ]);
    }

    /**
     * Get subscription trends.
     */
    public function subscriptionTrends(): JsonResponse
    {
        $last30Days = collect();
        
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = UserSubscription::whereDate('created_at', $date)->count();
            
            $last30Days->push([
                'date' => $date->format('Y-m-d'),
                'count' => $count,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $last30Days,
            'message' => 'Subscription trends retrieved successfully'
        ]);
    }

    /**
     * Get revenue trends.
     */
    public function revenueTrends(): JsonResponse
    {
        $last12Months = collect();
        
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $revenue = UserSubscription::whereMonth('created_at', $date->month)
                                      ->whereYear('created_at', $date->year)
                                      ->sum('amount_paid');
            
            $last12Months->push([
                'month' => $date->format('Y-m'),
                'revenue' => (float) $revenue,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $last12Months,
            'message' => 'Revenue trends retrieved successfully'
        ]);
    }

    /**
     * Get top subscription plans.
     */
    public function topPlans(): JsonResponse
    {
        $topPlans = SubscriptionPlan::withCount('activeUserSubscriptions')
                                   ->orderByDesc('active_user_subscriptions_count')
                                   ->limit(5)
                                   ->get()
                                   ->map(function ($plan) {
                                       return [
                                           'id' => $plan->id,
                                           'name' => $plan->name,
                                           'price' => $plan->formatted_price,
                                           'subscribers' => $plan->active_user_subscriptions_count,
                                           'revenue' => $plan->activeUserSubscriptions()->sum('amount_paid'),
                                       ];
                                   });

        return response()->json([
            'success' => true,
            'data' => $topPlans,
            'message' => 'Top subscription plans retrieved successfully'
        ]);
    }

    /**
     * Get expiring subscriptions alert.
     */
    public function expiringSubscriptions(): JsonResponse
    {
        $expiringSoon = UserSubscription::with(['user:id,name,email', 'subscriptionPlan:id,name'])
                                       ->where('status', 'active')
                                       ->where('expires_at', '>', now())
                                       ->where('expires_at', '<=', now()->addDays(7))
                                       ->orderBy('expires_at')
                                       ->get()
                                       ->map(function ($subscription) {
                                           return [
                                               'id' => $subscription->id,
                                               'user_name' => $subscription->user->name,
                                               'user_email' => $subscription->user->email,
                                               'plan_name' => $subscription->subscriptionPlan->name,
                                               'expires_at' => $subscription->expires_at,
                                               'remaining_days' => $subscription->remaining_days,
                                           ];
                                       });

        return response()->json([
            'success' => true,
            'data' => $expiringSoon,
            'message' => 'Expiring subscriptions retrieved successfully'
        ]);
    }

    /**
     * Get system information.
     */
    public function systemInfo(): JsonResponse
    {
        $info = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . ' MB',
            'disk_free_space' => disk_free_space('.') ? round(disk_free_space('.') / 1024 / 1024 / 1024, 2) . ' GB' : 'Unknown',
            'database_size' => $this->getDatabaseSize(),
        ];

        return response()->json([
            'success' => true,
            'data' => $info,
            'message' => 'System information retrieved successfully'
        ]);
    }

    /**
     * Get database size (simplified).
     */
    private function getDatabaseSize(): string
    {
        try {
            $tables = ['users', 'subscription_plans', 'user_subscriptions', 'content'];
            $totalRows = 0;
            
            foreach ($tables as $table) {
                $count = \DB::table($table)->count();
                $totalRows += $count;
            }
            
            return number_format($totalRows) . ' records';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
