<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('password')->nullable()->after('email');
            $table->enum('user_type', ['user', 'admin'])->default('user')->after('password');
            $table->timestamp('last_login_at')->nullable()->after('login_time');
            $table->string('last_login_ip')->nullable()->after('last_login_at');
            $table->boolean('is_admin')->default(false)->after('is_active');
            $table->json('permissions')->nullable()->after('is_admin');
            $table->timestamp('password_changed_at')->nullable()->after('permissions');
            $table->boolean('force_password_change')->default(false)->after('password_changed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'password',
                'user_type',
                'last_login_at',
                'last_login_ip',
                'is_admin',
                'permissions',
                'password_changed_at',
                'force_password_change'
            ]);
        });
    }
};
