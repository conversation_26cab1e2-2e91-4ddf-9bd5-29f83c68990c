<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSuspendedMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();

            // Check if user is suspended
            if ($user->isSuspended()) {
                auth()->logout();
                
                $message = 'Your account has been suspended.';
                if ($user->suspended_reason) {
                    $message .= ' Reason: ' . $user->suspended_reason;
                }

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message,
                        'suspended' => true
                    ], 403);
                }

                return redirect()->route('admin.login')->with('error', $message);
            }

            // Update last activity for admin users
            if ($user->isAdmin()) {
                $user->updateLastActivity();
            }
        }

        return $next($request);
    }
}
