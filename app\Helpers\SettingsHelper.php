<?php

namespace App\Helpers;

use App\Models\Setting;

class SettingsHelper
{
    /**
     * Get decryption API URL from settings
     */
    public static function getDecryptionApiUrl()
    {
        $url = Setting::getValue('decryption_api_url', 'http://127.0.0.1:5000');
        return rtrim($url, '/');
    }

    /**
     * Get decryption API extract-key endpoint
     */
    public static function getDecryptionApiExtractKeyUrl()
    {
        return self::getDecryptionApiUrl() . '/extract-key';
    }

    /**
     * Get decryption API health endpoint
     */
    public static function getDecryptionApiHealthUrl()
    {
        return self::getDecryptionApiUrl() . '/health';
    }

    /**
     * Check if caching is enabled
     */
    public static function isCachingEnabled()
    {
        return Setting::getValue('enable_caching', true);
    }

    /**
     * Check if logging is enabled
     */
    public static function isLoggingEnabled()
    {
        return Setting::getValue('enable_logging', true);
    }

    /**
     * Check if auto retry is enabled
     */
    public static function isAutoRetryEnabled()
    {
        return Setting::getValue('auto_retry', true);
    }

    /**
     * Get max retry attempts
     */
    public static function getMaxRetryAttempts()
    {
        return Setting::getValue('max_retry_attempts', 3);
    }

    /**
     * Get cache duration in seconds
     */
    public static function getCacheDuration()
    {
        return Setting::getValue('cache_duration', 3600);
    }

    /**
     * Get all application settings as array
     */
    public static function getAllSettings()
    {
        return Setting::getAllSettings();
    }

    /**
     * Update a setting value
     */
    public static function updateSetting($key, $value, $type = 'string', $description = null)
    {
        return Setting::setValue($key, $value, $type, $description);
    }

    /**
     * Check if decryption API is configured and available
     */
    public static function isDecryptionApiAvailable()
    {
        try {
            $url = self::getDecryptionApiHealthUrl();

            $client = new \GuzzleHttp\Client([
                'timeout' => 5,
                'verify' => false
            ]);

            $response = $client->get($url);

            if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                $data = json_decode($response->getBody(), true);

                // Check if it's the expected API format
                if (isset($data['status'])) {
                    return $data['status'] === 'healthy';
                }

                // If no status field, assume it's available if we get a 200 response
                return true;
            }

            return false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get decryption API status with details
     */
    public static function getDecryptionApiStatus()
    {
        try {
            $url = self::getDecryptionApiHealthUrl();

            $client = new \GuzzleHttp\Client([
                'timeout' => 5,
                'verify' => false
            ]);

            $response = $client->get($url);
            $data = json_decode($response->getBody(), true);

            return [
                'available' => true,
                'status_code' => $response->getStatusCode(),
                'data' => $data,
                'url' => self::getDecryptionApiUrl()
            ];

        } catch (\Exception $e) {
            return [
                'available' => false,
                'error' => $e->getMessage(),
                'url' => self::getDecryptionApiUrl()
            ];
        }
    }
}
