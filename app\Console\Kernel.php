<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Jobs\RefreshChannelStreamsJob;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Refresh all channel streams every 5 minutes
        $schedule->job(new RefreshChannelStreamsJob())
                 ->everyFiveMinutes()
                 ->name('refresh-channel-streams')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Clean up old cache entries every hour
        $schedule->call(function () {
            \Illuminate\Support\Facades\Cache::forget('channel_stream_*');
        })->hourly()->name('cleanup-old-cache');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
