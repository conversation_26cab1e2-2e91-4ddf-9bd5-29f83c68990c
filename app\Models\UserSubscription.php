<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'starts_at',
        'expires_at',
        'status',
        'allowed_applications',
        'max_devices',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'notes',
        'activated_at',
        'deactivated_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'activated_at' => 'datetime',
        'deactivated_at' => 'datetime',
        'allowed_applications' => 'array',
        'max_devices' => 'integer',
        'amount_paid' => 'decimal:2',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the user who created this subscription.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this subscription.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to get expiring soon subscriptions.
     */
    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->where('status', 'active')
                    ->where('expires_at', '>', now())
                    ->where('expires_at', '<=', now()->addDays($days));
    }

    /**
     * Check if subscription is currently active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->starts_at <= now() && 
               $this->expires_at > now();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Get remaining days.
     */
    public function getRemainingDaysAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => $this->isExpired() ? 'danger' : ($this->remaining_days <= 7 ? 'warning' : 'success'),
            'inactive' => 'secondary',
            'expired' => 'danger',
            'cancelled' => 'dark',
            default => 'secondary'
        };
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        if ($this->status === 'active' && $this->isExpired()) {
            return 'Expired';
        }

        return ucfirst($this->status);
    }

    /**
     * Activate subscription.
     */
    public function activate(): bool
    {
        $this->update([
            'status' => 'active',
            'activated_at' => now(),
            'deactivated_at' => null,
        ]);

        return true;
    }

    /**
     * Deactivate subscription.
     */
    public function deactivate(): bool
    {
        $this->update([
            'status' => 'inactive',
            'deactivated_at' => now(),
        ]);

        return true;
    }

    /**
     * Extend subscription.
     */
    public function extend(int $days): bool
    {
        $newExpiryDate = $this->isExpired() ? 
            now()->addDays($days) : 
            $this->expires_at->addDays($days);

        $this->update([
            'expires_at' => $newExpiryDate,
            'status' => 'active',
        ]);

        return true;
    }

    /**
     * Check if subscription allows specific application.
     */
    public function allowsApplication(string $application): bool
    {
        if (empty($this->allowed_applications)) {
            return $this->subscriptionPlan->allowsApplication($application);
        }

        return in_array('ALL', $this->allowed_applications) || 
               in_array($application, $this->allowed_applications);
    }
}
