<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class TestUserAPI extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:user-api {--user-id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the user API endpoint';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        
        $this->info("Testing User API for user ID: {$userId}");
        
        // Test direct model access
        $this->info("\n1. Testing direct model access:");
        try {
            $user = User::find($userId);
            if ($user) {
                $this->info("✅ User found: {$user->name} ({$user->email})");
                $this->info("   - ID: {$user->id}");
                $this->info("   - Active: " . ($user->is_active ? 'Yes' : 'No'));
                $this->info("   - Application: {$user->application}");
                $this->info("   - Subscription Expiry: " . ($user->subscription_expiry ? $user->subscription_expiry->format('Y-m-d H:i:s') : 'None'));
                $this->info("   - Remaining Days: " . ($user->remaining_days ?? 'N/A'));
            } else {
                $this->error("❌ User not found");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Error accessing user: " . $e->getMessage());
            return 1;
        }

        // Test relationships
        $this->info("\n2. Testing relationships:");
        try {
            // Test subscriptions relationship
            if (method_exists($user, 'subscriptions')) {
                $subscriptionsCount = $user->subscriptions()->count();
                $this->info("✅ Subscriptions relationship exists: {$subscriptionsCount} subscriptions");
            } else {
                $this->warn("⚠️  Subscriptions relationship not found");
            }

            // Test activeSubscription relationship
            if (method_exists($user, 'activeSubscription')) {
                $activeSubscription = $user->activeSubscription;
                if ($activeSubscription) {
                    $this->info("✅ Active subscription found: " . $activeSubscription->id);
                } else {
                    $this->info("ℹ️  No active subscription");
                }
            } else {
                $this->warn("⚠️  ActiveSubscription relationship not found");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error testing relationships: " . $e->getMessage());
        }

        // Test controller method directly
        $this->info("\n3. Testing controller method:");
        try {
            $controller = new \App\Http\Controllers\Admin\UserController();
            
            // Create a mock request
            $request = new \Illuminate\Http\Request();
            $request->setUserResolver(function () {
                return User::where('is_admin', true)->first() ?? User::first();
            });
            
            $response = $controller->show($user);
            $responseData = $response->getData(true);
            
            if ($responseData['success']) {
                $this->info("✅ Controller method successful");
                $this->info("   - Message: " . $responseData['message']);
                $this->info("   - User data returned: " . (isset($responseData['data']) ? 'Yes' : 'No'));
            } else {
                $this->error("❌ Controller method failed: " . $responseData['message']);
            }
        } catch (\Exception $e) {
            $this->error("❌ Error testing controller: " . $e->getMessage());
            $this->error("   Stack trace: " . $e->getTraceAsString());
        }

        // Test database tables
        $this->info("\n4. Testing database tables:");
        try {
            $tables = ['users', 'subscription_plans', 'user_subscriptions'];
            foreach ($tables as $table) {
                if (\Schema::hasTable($table)) {
                    $count = \DB::table($table)->count();
                    $this->info("✅ Table '{$table}' exists with {$count} records");
                } else {
                    $this->warn("⚠️  Table '{$table}' does not exist");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Error checking tables: " . $e->getMessage());
        }

        $this->info("\n🎯 Test completed!");
        return 0;
    }
}
