/**
 * CSP Fix Script for Video Player
 * Handles Content Security Policy issues with blob URLs and media playback
 */

console.log('🔧 CSP Fix Script loaded');

// Override URL.createObjectURL to ensure proper blob handling
if (typeof URL !== 'undefined' && URL.createObjectURL) {
    const originalCreateObjectURL = URL.createObjectURL;
    URL.createObjectURL = function(object) {
        try {
            const url = originalCreateObjectURL.call(this, object);
            console.log('✅ Blob URL created:', url);
            return url;
        } catch (error) {
            console.error('❌ Error creating blob URL:', error);
            throw error;
        }
    };
}

// Override URL.revokeObjectURL for cleanup tracking
if (typeof URL !== 'undefined' && URL.revokeObjectURL) {
    const originalRevokeObjectURL = URL.revokeObjectURL;
    URL.revokeObjectURL = function(url) {
        try {
            console.log('🗑️ Revoking blob URL:', url);
            return originalRevokeObjectURL.call(this, url);
        } catch (error) {
            console.error('❌ Error revoking blob URL:', error);
        }
    };
}

// Enhanced video element creation with proper CSP attributes
function createVideoElement() {
    const video = document.createElement('video');
    
    // Set attributes to help with CSP compliance
    video.setAttribute('crossorigin', 'anonymous');
    video.setAttribute('preload', 'metadata');
    video.setAttribute('playsinline', 'true');
    
    // Add event listeners for debugging
    video.addEventListener('loadstart', () => {
        console.log('📺 Video load started');
    });
    
    video.addEventListener('loadedmetadata', () => {
        console.log('✅ Video metadata loaded');
    });
    
    video.addEventListener('error', (e) => {
        console.error('❌ Video error:', e.target.error);
        if (e.target.error && e.target.error.code === 4) {
            console.error('Media load error - possibly CSP related');
        }
    });
    
    return video;
}

// Monkey patch HTMLVideoElement creation
const originalCreateElement = document.createElement;
document.createElement = function(tagName) {
    const element = originalCreateElement.call(this, tagName);
    
    if (tagName.toLowerCase() === 'video') {
        // Apply CSP-friendly attributes
        element.setAttribute('crossorigin', 'anonymous');
        element.setAttribute('preload', 'metadata');
        element.setAttribute('playsinline', 'true');
        
        console.log('📺 Video element created with CSP-friendly attributes');
    }
    
    return element;
};

// Function to check and fix CSP violations
function checkCSPCompliance() {
    // Check if CSP is blocking media
    const testVideo = document.createElement('video');
    const testBlob = new Blob(['test'], { type: 'video/mp4' });
    const testUrl = URL.createObjectURL(testBlob);
    
    testVideo.src = testUrl;
    
    testVideo.addEventListener('error', () => {
        console.warn('⚠️ CSP may be blocking blob URLs for video');
    });
    
    testVideo.addEventListener('loadstart', () => {
        console.log('✅ Blob URLs are working for video');
    });
    
    // Clean up
    setTimeout(() => {
        URL.revokeObjectURL(testUrl);
    }, 1000);
}

// Run CSP compliance check
setTimeout(checkCSPCompliance, 1000);

// Web Worker CSP fix
function createWorkerWithCSPFix(scriptUrl, OriginalWorker) {
    try {
        // Try creating worker directly first using original constructor
        return new OriginalWorker(scriptUrl);
    } catch (error) {
        console.warn('⚠️ Direct worker creation failed, trying blob approach:', error);

        try {
            // Fallback: create worker from blob
            const blob = new Blob([`importScripts('${scriptUrl}');`], { type: 'application/javascript' });
            const blobUrl = URL.createObjectURL(blob);
            console.log('Blob URL created:', blobUrl);
            const worker = new OriginalWorker(blobUrl);

            // Clean up blob URL after worker is created
            worker.addEventListener('message', () => {
                URL.revokeObjectURL(blobUrl);
            }, { once: true });

            worker.addEventListener('error', () => {
                URL.revokeObjectURL(blobUrl);
            }, { once: true });

            console.log('✅ Worker created using blob approach');
            return worker;
        } catch (blobError) {
            console.error('❌ Both worker creation methods failed:', blobError);
            throw blobError;
        }
    }
}

// Store reference to original Worker but don't override globally
if (typeof Worker !== 'undefined') {
    window.Worker.__original__ = Worker;
    console.log('✅ Original Worker constructor stored for CSP fix');
}

// Export utilities
window.CSPFix = {
    createVideoElement,
    checkCSPCompliance,
    createWorkerWithCSPFix: (scriptUrl) => {
        // Use the original Worker constructor to avoid recursion
        const OriginalWorker = window.Worker.__original__ || Worker;
        return createWorkerWithCSPFix(scriptUrl, OriginalWorker);
    },
    // Safe Worker creation that doesn't cause recursion
    createSafeWorker: (scriptUrl, options) => {
        const OriginalWorker = window.Worker.__original__ || Worker;
        return createWorkerWithCSPFix(scriptUrl, OriginalWorker);
    }
};

console.log('✅ CSP Fix Script ready with Worker support');
