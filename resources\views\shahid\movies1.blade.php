@extends('layouts.app')

@section('title', 'Movies - Shahid Play')

@section('styles')
<style>
.bg-gradient-dark {
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    transition: all 0.3s ease;
}

.movie-poster-container:hover .bg-gradient-dark {
    background: linear-gradient(to top, rgba(255, 107, 107, 0.8), transparent);
}

.movie-poster-container:hover .bg-gradient-dark h6 {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    transform: translateY(-2px);
}

.movie-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border-radius: 10px;
    overflow: hidden;
    background: #fff;
    height: auto;
}

.movie-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.movie-card img {
    transition: transform 0.3s ease;
}

.movie-card:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.1);
}

/* Play Overlay Styles */
.movie-poster-container {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
    z-index: 2;
}

.movie-poster-container:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.8);
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.movie-poster-container:hover .play-button {
    transform: scale(1);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
    animation: pulse 2s infinite;
}

.play-button i {
    color: white;
    font-size: 28px;
    margin-left: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes pulse {
    0% { box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4); }
    50% { box-shadow: 0 12px 35px rgba(255, 107, 107, 0.8); }
    100% { box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4); }
}

.movie-poster-container:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 36, 0.1));
    z-index: 1;
    pointer-events: none;
}

.movie-title {
    transition: all 0.3s ease;
    font-size: 1rem;
    line-height: 1.3;
}

.movie-poster-container:hover .movie-title {
    transform: translateY(-2px);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    font-weight: 700;
}

.badge {
    backdrop-filter: blur(10px);
    background: rgba(0,0,0,0.7) !important;
    transition: all 0.3s ease;
}

.movie-poster-container:hover .badge {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
/* تعديل المقاسات فقط مع الحفاظ على التصميم */

/* Desktop Large (1400px+) */
@media (min-width: 1400px) {
    .col-lg-3 {
        flex: 0 0 auto;
        width: 20%; /* 5 أعمدة */
    }

    .play-button {
        width: 90px;
        height: 90px;
    }

    .play-button i {
        font-size: 32px;
    }
}

/* Laptop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .play-button {
        width: 70px;
        height: 70px;
    }

    .play-button i {
        font-size: 24px;
    }

    .movie-title {
        font-size: 0.95rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .col-lg-3 {
        flex: 0 0 auto;
        width: 33.33333333%; /* 3 أعمدة */
    }

    .play-button {
        width: 60px;
        height: 60px;
    }

    .play-button i {
        font-size: 20px;
    }

    .movie-title {
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .col-lg-3,
    .col-md-4 {
        flex: 0 0 auto;
        width: 50%; /* عمودين */
    }

    .play-button {
        width: 50px;
        height: 50px;
    }

    .play-button i {
        font-size: 18px;
    }

    .movie-title {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .badge {
        font-size: 0.6rem;
        padding: 0.1rem 0.25rem;
    }

    .btn {
        padding: 0.35rem 0.7rem;
        font-size: 0.85rem;
    }

    .container-fluid {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
}

/* Mobile Small (أقل من 576px) */
@media (max-width: 575px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        flex: 0 0 auto;
        width: 100%; /* عمود واحد */
        margin-bottom: 1rem;
    }

    .play-button {
        width: 45px;
        height: 45px;
    }

    .play-button i {
        font-size: 16px;
    }

    .movie-title {
        font-size: 0.8rem;
        line-height: 1.1;
    }

    .badge {
        font-size: 0.55rem;
        padding: 0.05rem 0.2rem;
    }

    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }

    .container-fluid {
        padding-right: 0.25rem;
        padding-left: 0.25rem;
    }

    .h3 {
        font-size: 1.2rem;
    }

    .form-control,
    .form-select {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
}

/* iPhone Specific */
@media only screen and (max-width: 320px) {
    .play-button {
        width: 40px;
        height: 40px;
    }

    .play-button i {
        font-size: 14px;
    }

    .movie-title {
        font-size: 0.75rem;
    }

    .h3 {
        font-size: 1.1rem;
    }
}

/* Landscape على الموبايل */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .col-lg-3,
    .col-md-4,
    .col-sm-6 {
        width: 25%; /* 4 أعمدة في landscape */
    }

    .play-button {
        width: 40px;
        height: 40px;
    }

    .play-button i {
        font-size: 16px;
    }
}

/* ===== HEADER RESPONSIVE IMPROVEMENTS ===== */

/* تحسين الـ header للشاشات المختلفة */
.page-header h1 {
    font-size: clamp(1.5rem, 4vw, 2rem);
    line-height: 1.2;
}

.page-header .btn {
    white-space: nowrap;
    min-width: auto;
}

/* Mobile header adjustments */
@media (max-width: 767px) {
    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .page-header p {
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .page-header .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Very small screens */
@media (max-width: 575px) {
    .page-header h1 {
        font-size: 1.3rem;
    }

    .page-header p {
        font-size: 0.85rem;
    }

    .page-header .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}

/* iPhone specific */
@media only screen and (max-width: 320px) {
    .page-header h1 {
        font-size: 1.2rem;
    }

    .page-header p {
        font-size: 0.8rem;
    }
}


</style>
@endsection

@section('content')
<div class="container-fluid" dir="ltr">
    <!-- Header -->
    <div class="row mb-4 page-header">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1 text-center text-md-start">
                    <h1 class="h3 mb-2 mb-md-0 fw-bold">
                        <i class="fas fa-film me-2 text-primary"></i>
                        Shahid Movies
                    </h1>
                    <p class="text-muted mb-0 small">Browse and explore a wide collection of movies</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-2">
                            <label for="countrySelect" class="form-label">Country</label>
                            <select class="form-select" id="countrySelect">
                                <option value="EG">Egypt</option>
                                <option value="SA">Saudi Arabia</option>
                                <option value="AE">UAE</option>
                                <option value="KW">Kuwait</option>
                                <option value="QA">Qatar</option>
                                <option value="BH">Bahrain</option>
                                <option value="OM">Oman</option>
                                <option value="JO">Jordan</option>
                                <option value="LB">Lebanon</option>
                                <option value="IQ">Iraq</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="limitSelect" class="form-label">Results Limit</label>
                            <select class="form-select" id="limitSelect">
                                <option value="20">20</option>
                                <option value="50" selected>50</option>
                                <option value="100">100</option>
                                <option value="all">All Movies</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">Search Movies</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search by title or ID...">
                                <button class="btn btn-outline-primary" onclick="searchMovies()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadMovies()">
                                <i class="fas fa-sync-alt me-2"></i>
                                Load Movies
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadMovies()">
                                <i class="fas fa-sync-alt me-2"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <div id="searchResultsInfo" class="row mb-3 d-none">
        <div class="col-12">
            <div class="alert alert-info d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="searchResultsText">Search results</span>
                </div>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearSearch()">
                    <i class="fas fa-times me-1"></i>
                    Clear Search
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="text-center py-5 d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Loading movies...</p>
    </div>

    <!-- Welcome Message -->
    <div id="welcomeMessage" class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-film fa-4x text-primary"></i>
        </div>
        <h4 class="text-primary">Welcome to Shahid Movies</h4>
        <p class="text-muted mb-4">Choose your country and click "Search" to browse available movies.</p>
        <div class="d-flex justify-content-center gap-3">
            <button class="btn btn-primary" onclick="loadMovies()">
                <i class="fas fa-play me-2"></i>
                Load Movies
            </button>
            <button class="btn btn-primary" onclick="loadMovies()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Movies Grid -->
    <div id="moviesContainer" class="row g-4">
        <!-- Movies will be loaded here -->
    </div>

    <!-- No Results -->
    <div id="noResults" class="text-center py-5 d-none">
        <div class="text-muted">
            <i class="fas fa-film fa-3x mb-3"></i>
            <h5>No Movies Found</h5>
            <p>No movies were found. Please check your token settings and connection.</p>
            <button class="btn btn-primary" onclick="loadMovies()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
    </div>
</div>

<!-- Token Modal -->
<div class="modal fade" id="tokenModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>
                    Setup Shahid Token
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="tokenForm">
                    <div class="mb-3">
                        <label for="shahidToken" class="form-label">Shahid Token</label>
                        <textarea
                            class="form-control"
                            id="shahidToken"
                            rows="3"
                            placeholder="Enter Shahid token here..."
                            required
                        ></textarea>
                        <div class="form-text">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                You can get the token from Shahid website
                            </small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveToken()">
                    <i class="fas fa-save me-2"></i>
                    Save Token
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentMovies = [];

$(document).ready(function() {
    // Auto-loading disabled - user must click "Search" button
    console.log('Movies page loaded. Click "Search" to fetch content.');

    // Force refresh styles
    setTimeout(function() {
        $('head').append('<style>.movie-card img { object-fit: cover !important; }</style>');
    }, 100);
});

function loadMovies() {
    console.log('loadMovies function called');
    const country = $('#countrySelect').val();
    const limit = $('#limitSelect').val();
    const fetchAll = (limit === 'all');

    console.log('Country:', country, 'Limit:', limit, 'Fetch All:', fetchAll);

    // Add loading state to refresh buttons
    $('.refresh-btn').addClass('loading');

    showLoading();
    hideResults();
    hideSearchResults();

    // Update loading message for fetch all
    if (fetchAll) {
        $('#loadingIndicator p').text('Loading all movies... This may take a while.');
    } else {
        $('#loadingIndicator p').text('Loading movies...');
    }

    $.ajax({
        url: '/api/shahid/movies',
        method: 'GET',
        data: {
            country: country,
            limit: fetchAll ? 50 : limit, // Use 50 as base limit for API calls
            fetch_all: fetchAll
        },
        success: function(response) {
            hideLoading();

            if (response.success && response.data && response.data.length > 0) {
                currentMovies = response.data;
                displayMovies(response.data);
                showResults();

                // Show info about loaded movies
                const infoText = fetchAll ?
                    `Loaded all ${response.data.length} movies from ${country}` :
                    `Loaded ${response.data.length} movies from ${country}`;
                showAlert('success', infoText);
            } else {
                showNoResults();
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error loading movies';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showAlert('danger', errorMessage);
            showNoResults();
        }
    });
}

// Search functionality
function searchMovies() {
    const query = $('#searchInput').val().trim();

    if (!query) {
        showAlert('warning', 'Please enter a search term');
        return;
    }

    console.log('Searching for:', query);

    showLoading();
    hideResults();
    hideSearchResults();

    $('#loadingIndicator p').text('Searching movies...');

    $.ajax({
        url: '/api/shahid/search',
        method: 'GET',
        data: {
            query: query,
            limit: 50,
            type: 'MOVIE'
        },
        success: function(response) {
            hideLoading();

            if (response.success && response.data && response.data.length > 0) {
                currentMovies = response.data;
                displayMovies(response.data);
                showResults();
                showSearchResults(query, response.data.length);
            } else {
                showNoResults();
                showAlert('info', `No movies found for "${query}"`);
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error searching movies';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showAlert('danger', errorMessage);
            showNoResults();
        }
    });
}

// Clear search and reload movies
function clearSearch() {
    $('#searchInput').val('');
    hideSearchResults();
    loadMovies();
}

// Show search results info
function showSearchResults(query, count) {
    $('#searchResultsText').text(`Found ${count} movies for "${query}"`);
    $('#searchResultsInfo').removeClass('d-none');
}

// Hide search results info
function hideSearchResults() {
    $('#searchResultsInfo').addClass('d-none');
}

// Handle Enter key in search input
$(document).ready(function() {
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            searchMovies();
        }
    });
});

function displayMovies(movies) {
    const container = $('#moviesContainer');
    container.empty();

    movies.forEach(movie => {
        const movieCard = createMovieCard(movie);
        container.append(movieCard);
    });

    // Force apply styles to new images
    setTimeout(function() {
        $('.card-img-top').css({
            'object-fit': 'cover',
            'height': 'auto',
            'width': '100%'
        });
    }, 100);
}

function createMovieCard(movie) {
    const title = movie.title || 'Untitled';
    const poster = movie.poster_url || '/images/no-poster.svg';
    const duration = movie.duration ? `${movie.duration} min` : '';
    const year = movie.year || '';
    const movieUrl = movie.movie_url || '';

    // Extract movie ID from URL if not provided directly
    let movieId = movie.id;
    if (!movieId && movieUrl) {
        const urlMatch = movieUrl.match(/\/movie\/(\d+)/);
        if (urlMatch) {
            movieId = urlMatch[1];
        }
    }

    return `
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100 border-0 shadow-sm movie-card" data-movie-id="${movieId || ''}" data-movie-url="${movieUrl}">
                <div class="position-relative movie-poster-container" onclick="playMovie('${movieId || ''}', '${title}')">
                    <img src="${poster}" class="card-img-top" alt="${title}" style="width: 100%;"
                         onerror="this.src='/images/no-poster.svg'">
                    <div class="position-absolute top-0 end-0 m-2">
                        ${year ? `<span class="badge bg-dark mb-1 d-block">${year}</span>` : ''}
                        ${duration ? `<span class="badge bg-primary">${duration}</span>` : ''}
                    </div>
                    <div class="position-absolute bottom-0 start-0 end-0 bg-gradient-dark p-3">
                        <h6 class="text-white mb-0 fw-bold movie-title">${title}</h6>
                    </div>
                    <!-- Play Button Overlay -->
                    <div class="play-overlay">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function viewMovie(movieId) {
    if (!movieId) {
        showAlert('warning', 'Invalid movie ID');
        return;
    }

    // Extract movie data
    extractMovieData(movieId);
}

function extractMovieData(movieId) {
    // Show loading indicator
    const loadingDiv = document.createElement('div');
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 18px;
    `;
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="width: 60px; height: 60px; border: 4px solid rgba(116, 185, 255, 0.2); border-left: 4px solid #74b9ff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div>Extracting Movie Data...</div>
            <div style="font-size: 14px; opacity: 0.8; margin-top: 10px;">Getting MPD URL and stream information</div>
        </div>
    `;
    document.body.appendChild(loadingDiv);

    // Extract movie data
    $.ajax({
        url: '/api/shahid/extract_movie_drm',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: JSON.stringify({
            movie_id: movieId,
            movie_title: `Movie ${movieId}`
        }),
        success: function(response) {
            // Remove loading indicator
            document.body.removeChild(loadingDiv);

            if (response.success && response.data) {
                showMovieDataResults(response.data, movieId);
            } else {
                showAlert('danger', `Error extracting movie data: ${response.message}`);
            }
        },
        error: function(xhr) {
            // Remove loading indicator
            document.body.removeChild(loadingDiv);

            let errorMessage = 'Error extracting movie data';
            let alertType = 'danger';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;

                // Check if it requires VIP
                if (xhr.responseJSON.requires_vip || xhr.status === 402) {
                    alertType = 'warning';
                    errorMessage = '🔒 ' + errorMessage;
                }
            }

            showAlert(alertType, errorMessage);
        }
    });
}

function getMovieDetails(movieId) {
    if (!movieId) {
        showAlert('warning', 'Invalid movie ID');
        return;
    }

    showLoading();

    $.ajax({
        url: '/api/shahid/content-details',
        method: 'GET',
        data: {
            content_id: movieId
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                showMovieDetailsModal(response.data);
            } else {
                showAlert('danger', response.message || 'Failed to fetch movie details');
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = 'Error fetching details';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function showMovieDetailsModal(data) {
    // Extract movie information
    let movieData = data.data || data;
    let productModel = movieData.productModel || {};

    let title = productModel.title || 'غير متوفر';
    let description = productModel.description || productModel.shortDescription || 'لا توجد قصة متاحة';
    let releaseDate = productModel.releaseDate || productModel.productionDate || 'غير محدد';
    let movieId = productModel.id || data.id || 'غير متوفر';

    // Extract poster image
    let posterImage = '';
    if (productModel.image && productModel.image.posterClean) {
        posterImage = productModel.image.posterClean;
    } else if (productModel.image && productModel.image.posterHero) {
        posterImage = productModel.image.posterHero;
    } else if (productModel.thumbnailImage) {
        posterImage = productModel.thumbnailImage;
    }

    // Extract cast information (if available)
    let cast = '';
    if (productModel.cast && productModel.cast.length > 0) {
        cast = productModel.cast.map(actor => actor.name || actor).join(', ');
    } else {
        cast = 'غير متوفر';
    }

    // Format release date
    if (releaseDate && releaseDate !== 'غير محدد') {
        try {
            let date = new Date(releaseDate);
            releaseDate = date.getFullYear();
        } catch (e) {
            // Keep original if parsing fails
        }
    }

    const modalHtml = `
        <div class="modal fade" id="movieDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: none; border-radius: 15px;">
                    <div class="modal-header border-0" style="background: rgba(255,255,255,0.1); border-radius: 15px 15px 0 0;">
                        <h5 class="modal-title text-white fw-bold">
                            <i class="fas fa-film me-2 text-warning"></i>تفاصيل الفيلم
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="row">
                            ${posterImage ? `
                            <div class="col-md-4 mb-3">
                                <img src="${posterImage}" alt="${title}" class="img-fluid rounded shadow-lg" style="width: 100%; max-height: 400px; object-fit: cover;">
                            </div>
                            ` : ''}
                            <div class="${posterImage ? 'col-md-8' : 'col-12'}">
                                <div class="movie-info">
                                    <h3 class="text-white mb-3 fw-bold">${title}</h3>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-warning text-dark me-2">
                                            <i class="fas fa-hashtag me-1"></i>معرف الفيلم
                                        </span>
                                        <span class="text-light">${movieId}</span>
                                    </div>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-info me-2">
                                            <i class="fas fa-calendar me-1"></i>سنة الإنتاج
                                        </span>
                                        <span class="text-light">${releaseDate}</span>
                                    </div>

                                    <div class="info-item mb-3">
                                        <span class="badge bg-success me-2">
                                            <i class="fas fa-users me-1"></i>الممثلون
                                        </span>
                                        <span class="text-light">${cast}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="story-section">
                                    <h5 class="text-warning mb-3">
                                        <i class="fas fa-book-open me-2"></i>قصة الفيلم
                                    </h5>
                                    <div class="story-content p-3 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #ffc107;">
                                        <p class="text-light mb-0 lh-lg" style="font-size: 16px;">${description}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer border-0" style="background: rgba(255,255,255,0.05); border-radius: 0 0 15px 15px;">
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#movieDetailsModal').remove();

    // Add new modal to body
    $('body').append(modalHtml);

    // Show modal
    $('#movieDetailsModal').modal('show');
}

function showTokenModal() {
    $('#tokenModal').modal('show');
}

function saveToken() {
    const token = $('#shahidToken').val().trim();
    
    if (!token) {
        showAlert('warning', 'Please enter the token');
        return;
    }
    
    $.ajax({
        url: '/api/shahid/auth/token',
        method: 'POST',
        data: {
            token: token
        },
        success: function(response) {
            if (response.success) {
                $('#tokenModal').modal('hide');
                showAlert('success', response.message);
                loadMovies(); // Reload movies with new token
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            let errorMessage = 'Error saving token';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showAlert('danger', errorMessage);
        }
    });
}

function showLoading() {
    $('#loadingIndicator').removeClass('d-none');
    $('#welcomeMessage').addClass('d-none');
}

function hideLoading() {
    $('#loadingIndicator').addClass('d-none');
}

function showResults() {
    $('#moviesContainer').removeClass('d-none');
    $('#welcomeMessage').addClass('d-none');
    $('#noResults').addClass('d-none');
}

function hideResults() {
    $('#moviesContainer').addClass('d-none');
}

function showNoResults() {
    $('#noResults').removeClass('d-none');
    $('#moviesContainer').addClass('d-none');
    $('#welcomeMessage').addClass('d-none');
}

function showWelcome() {
    $('#welcomeMessage').removeClass('d-none');
    $('#moviesContainer').addClass('d-none');
    $('#noResults').addClass('d-none');
    $('#loadingIndicator').addClass('d-none');
}

function showMovieDataResults(movieData, movieId) {
    const modalHtml = `
        <div class="modal fade" id="movieDataModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: none; border-radius: 15px;">
                    <div class="modal-header border-0" style="background: rgba(255,255,255,0.1); border-radius: 15px 15px 0 0;">
                        <h5 class="modal-title text-white fw-bold">
                            <i class="fas fa-film me-2 text-warning"></i>Movie Stream Data Extracted
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-success border-0" style="background: rgba(40, 167, 69, 0.2); border-left: 4px solid #28a745 !important;">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-check-circle me-2"></i>Successfully Extracted Movie Data
                                    </h6>
                                    <p class="text-light mb-0">Movie ID: <strong>${movieId}</strong></p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="stream-info-card p-4 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #74b9ff;">
                                    <h6 class="text-info mb-3">
                                        <i class="fas fa-video me-2"></i>MPD Stream URL
                                    </h6>
                                    <div class="info-item">
                                        <small class="text-muted">DASH Manifest URL:</small>
                                        <div class="bg-dark p-3 rounded mt-2" style="border: 1px solid #444;">
                                            <code class="text-light" style="font-size: 13px; word-break: break-all; line-height: 1.5;">${movieData.stream_url || 'N/A'}</code>
                                        </div>
                                        <div class="mt-2">
                                            <button class="btn btn-outline-info btn-sm me-2" onclick="copyToClipboard('${movieData.stream_url || ''}', 'MPD URL')">
                                                <i class="fas fa-copy me-1"></i>Copy MPD URL
                                            </button>
                                            <span class="badge bg-info">${movieData.stream_type || 'DASH'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${movieData.pssh ? `
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="drm-info-card p-4 rounded" style="background: rgba(255,255,255,0.1); border-left: 4px solid #9b59b6;">
                                    <h6 class="text-purple mb-3" style="color: #9b59b6;">
                                        <i class="fas fa-shield-alt me-2"></i>DRM Information
                                    </h6>

                                    ${movieData.kid ? `
                                    <div class="info-item mb-3">
                                        <small class="text-muted">KID (Key ID):</small>
                                        <div class="bg-dark p-3 rounded mt-2" style="border: 1px solid #444;">
                                            <code class="text-warning" style="font-size: 13px; word-break: break-all; line-height: 1.5;">${movieData.kid}</code>
                                        </div>
                                        <button class="btn btn-outline-warning btn-sm mt-2" onclick="copyToClipboard('${movieData.kid}', 'KID')">
                                            <i class="fas fa-copy me-1"></i>Copy KID
                                        </button>
                                    </div>
                                    ` : ''}

                                    ${movieData.key ? `
                                    <div class="info-item mb-3">
                                        <small class="text-muted">Key (Decryption Key):</small>
                                        <div class="bg-dark p-3 rounded mt-2" style="border: 1px solid #444;">
                                            <code class="text-success" style="font-size: 13px; word-break: break-all; line-height: 1.5;">${movieData.key}</code>
                                        </div>
                                        <button class="btn btn-outline-success btn-sm mt-2" onclick="copyToClipboard('${movieData.key}', 'Key')">
                                            <i class="fas fa-copy me-1"></i>Copy Key
                                        </button>
                                    </div>
                                    ` : ''}

                                    ${movieData.formatted_key ? `
                                    <div class="info-item mb-3">
                                        <small class="text-muted">Formatted Key (KID:Key):</small>
                                        <div class="bg-dark p-3 rounded mt-2" style="border: 1px solid #444;">
                                            <code class="text-info" style="font-size: 13px; word-break: break-all; line-height: 1.5;">${movieData.formatted_key}</code>
                                        </div>
                                        <button class="btn btn-outline-info btn-sm mt-2" onclick="copyToClipboard('${movieData.formatted_key}', 'Formatted Key')">
                                            <i class="fas fa-copy me-1"></i>Copy Formatted Key
                                        </button>
                                    </div>
                                    ` : ''}

                                    <div class="info-item">
                                        <small class="text-muted">PSSH (Protection System Specific Header):</small>
                                        <div class="bg-dark p-3 rounded mt-2" style="max-height: 120px; overflow-y: auto; border: 1px solid #444;">
                                            <code class="text-light" style="font-size: 11px; word-break: break-all; line-height: 1.4;">${movieData.pssh}</code>
                                        </div>
                                        <button class="btn btn-outline-light btn-sm mt-2" onclick="copyToClipboard('${movieData.pssh}', 'PSSH')">
                                            <i class="fas fa-copy me-1"></i>Copy PSSH
                                        </button>
                                    </div>

                                    ${movieData.key_extraction_error ? `
                                    <div class="alert alert-warning mt-3 border-0" style="background: rgba(255, 193, 7, 0.2); border-left: 4px solid #ffc107 !important;">
                                        <h6 class="text-warning mb-2">
                                            <i class="fas fa-exclamation-triangle me-2"></i>Key Extraction Issue
                                        </h6>
                                        <small class="text-muted">${movieData.key_extraction_error}</small>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Make sure device.wvd file is placed in storage/app/ directory and Python dependencies are installed.
                                            </small>
                                        </div>
                                    </div>
                                    ` : ''}

                                    ${!movieData.drm_supported ? `
                                    <div class="alert alert-info mt-3 border-0" style="background: rgba(23, 162, 184, 0.2); border-left: 4px solid #17a2b8 !important;">
                                        <h6 class="text-info mb-2">
                                            <i class="fas fa-info-circle me-2"></i>DRM Setup Required
                                        </h6>
                                        <small class="text-muted">
                                            To extract decryption keys, place your device.wvd file in storage/app/ directory and install Python dependencies.
                                        </small>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer border-0" style="background: rgba(255,255,255,0.05); border-radius: 0 0 15px 15px;">
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <button type="button" class="btn btn-warning" onclick="copyAllMovieData('${JSON.stringify(movieData).replace(/'/g, "\\'")}')">
                            <i class="fas fa-copy me-2"></i>Copy All Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#movieDataModal').remove();

    // Add new modal to body
    $('body').append(modalHtml);

    // Show modal
    $('#movieDataModal').modal('show');
}

function copyToClipboard(text, label) {
    if (!text) {
        showAlert('warning', `No ${label} to copy`);
        return;
    }

    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(function() {
            showAlert('success', `${label} copied to clipboard!`);
        }).catch(function(err) {
            console.error('Clipboard API failed:', err);
            fallbackCopyToClipboard(text, label);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text, label);
    }
}

function fallbackCopyToClipboard(text, label) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // Select and copy the text
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showAlert('success', `${label} copied to clipboard!`);
        } else {
            throw new Error('execCommand failed');
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);

        // Last resort: show the text in a prompt for manual copy
        const message = `Please copy this ${label} manually:\n\n${text}`;
        if (window.prompt) {
            window.prompt(message, text);
        } else {
            showAlert('info', `${label}: ${text.substring(0, 50)}...`);
        }
    }
}

function copyAllMovieData(movieDataJson) {
    try {
        const movieData = JSON.parse(movieDataJson);
        const allData = `Movie ID: ${movieData.movie_id || 'N/A'}
MPD URL: ${movieData.stream_url || 'N/A'}
Stream Type: ${movieData.stream_type || 'N/A'}
KID: ${movieData.kid || 'N/A'}
Key: ${movieData.key || 'N/A'}
Formatted Key: ${movieData.formatted_key || 'N/A'}
PSSH: ${movieData.pssh || 'N/A'}
License URL: ${movieData.license_url || 'N/A'}`;

        // Use the same copy function with fallback
        copyToClipboard(allData, 'All Movie Data');

    } catch (e) {
        showAlert('danger', 'Error copying data: ' + e.message);
    }
}

function showAlert(type, message) {
    // Convert old alert types to SweetAlert2
    switch(type) {
        case 'success':
            showSuccess('Success', message);
            break;
        case 'danger':
        case 'error':
            showError('Error', message);
            break;
        case 'warning':
            showWarning('Warning', message);
            break;
        case 'info':
        default:
            showInfo('Information', message);
            break;
    }
}
</script>
@endpush

@push('styles')
<!-- النظام المتجاوب مُحمل من الـ layout الرئيسي -->



<script>
/**
 * تشغيل فيلم مع استخراج المفاتيح بشكل صامت
 */
function playMovie(movieId, title) {
    if (!movieId) {
        showAlert('warning', 'Invalid movie ID');
        return;
    }

    console.log(`🎬 Starting playback for: ${title} (${movieId})`);

    // إظهار شاشة التحميل
    showPlaybackLoading(title);

    // استخراج بيانات الفيلم بشكل صامت (نفس Extract Data لكن صامت)
    $.ajax({
        url: '/api/shahid/extract_movie_drm',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: JSON.stringify({
            movie_id: movieId,
            movie_title: title || `Movie ${movieId}`
        }),
        success: function(response) {
            console.log('✅ Movie data extracted successfully:', response);

            if (response.success && response.data) {
                // استخراج البيانات المطلوبة من response structure الصحيح
                const data = response.data;
                const mpdUrl = data.stream_url || data.mpd_url || data.manifest_url;
                const drmKey = data.key || data.drm_key;
                const kid = data.kid;

                console.log('📺 Extracted data:', { mpdUrl, drmKey, kid });

                if (mpdUrl && drmKey) {
                    // تمرير البيانات للمشغل بنفس طريقة المشغل الأصلي
                    const playerUrl = `/shahid/play/${movieId}?mpd=${encodeURIComponent(mpdUrl)}&key=${encodeURIComponent(drmKey)}&keyId=${encodeURIComponent(kid || '')}&title=${encodeURIComponent(title || `Movie ${movieId}`)}&type=dash`;

                    console.log('🎬 Redirecting to player:', playerUrl);
                    window.location.href = playerUrl;
                } else {
                    hidePlaybackLoading();
                    console.error('❌ Missing required data:', { mpdUrl, drmKey });
                    showAlert('danger', 'Failed to get required playback data');
                }
            } else {
                hidePlaybackLoading();
                showAlert('danger', `Error extracting movie data: ${response.message || 'Unknown error'}`);
            }
        },
        error: function(xhr) {
            hidePlaybackLoading();
            console.error('❌ Error extracting movie data:', xhr);

            let errorMessage = 'Error occurred while extracting movie data';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showAlert('danger', errorMessage);
        }
    });
}

/**
 * إظهار شاشة تحميل التشغيل
 */
function showPlaybackLoading(title) {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'playbackLoading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 18px;
    `;
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="width: 80px; height: 80px; border: 4px solid rgba(0, 123, 255, 0.3); border-left: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <div style="font-size: 20px; margin-bottom: 10px;">جاري تحضير الفيلم...</div>
            <div style="font-size: 16px; font-weight: bold; color: #007bff;">${title}</div>
            <div style="font-size: 14px; opacity: 0.8; margin-top: 15px;">يتم استخراج مفاتيح DRM وتحضير المشغل</div>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

/**
 * إخفاء شاشة تحميل التشغيل
 */
function hidePlaybackLoading() {
    const loadingDiv = document.getElementById('playbackLoading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}
</script>
@endpush
