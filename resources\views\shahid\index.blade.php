@extends('layouts.app')

@section('title', 'شاهيد - Shahid Play')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 fw-bold text-primary mb-3">
                    <i class="fas fa-play-circle me-3"></i>
                    مرحباً بك في شاهيد
                </h1>
                <p class="lead text-muted">استكشف عالم من الترفيه مع مجموعة واسعة من الأفلام والمسلسلات</p>
            </div>
        </div>
    </div>

    <!-- Token Status -->
    @if(isset($tokenStatus))
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-{{ $tokenStatus ? 'success' : 'warning' }} border-0 shadow-sm">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-{{ $tokenStatus ? 'check-circle' : 'exclamation-triangle' }} fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-1">
                            حالة الاتصال: {{ $tokenStatus ? 'متصل' : 'غير متصل' }}
                        </h5>
                        <p class="mb-0">
                            @if($tokenStatus)
                                التوكن صالح ومتصل بخدمة شاهيد بنجاح
                            @else
                                التوكن غير صالح أو منتهي الصلاحية - يرجى تحديث التوكن من الإعدادات
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Quick Stats -->
    <div class="row mb-5">
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100 text-center">
                <div class="card-body">
                    <div class="text-primary mb-3">
                        <i class="fas fa-film fa-3x"></i>
                    </div>
                    <h3 class="fw-bold">آلاف الأفلام</h3>
                    <p class="text-muted">مجموعة ضخمة من أحدث الأفلام العربية والعالمية</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100 text-center">
                <div class="card-body">
                    <div class="text-success mb-3">
                        <i class="fas fa-tv fa-3x"></i>
                    </div>
                    <h3 class="fw-bold">مسلسلات حصرية</h3>
                    <p class="text-muted">أحدث المسلسلات العربية والتركية والعالمية</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100 text-center">
                <div class="card-body">
                    <div class="text-info mb-3">
                        <i class="fas fa-broadcast-tower fa-3x"></i>
                    </div>
                    <h3 class="fw-bold">بث مباشر</h3>
                    <p class="text-muted">قنوات مباشرة عالية الجودة على مدار الساعة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="h4 fw-bold mb-4 text-center">ماذا تريد أن تشاهد اليوم؟</h2>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <a href="{{ route('shahid.movies') }}" class="text-decoration-none">
                        <div class="card border-0 shadow-sm h-100 nav-card">
                            <div class="card-body text-center p-4">
                                <div class="text-primary mb-3">
                                    <i class="fas fa-film fa-4x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">الأفلام</h5>
                                <p class="text-muted mb-3">تصفح مجموعة واسعة من الأفلام</p>
                                <div class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    تصفح الآن
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <a href="{{ route('shahid.series') }}" class="text-decoration-none">
                        <div class="card border-0 shadow-sm h-100 nav-card">
                            <div class="card-body text-center p-4">
                                <div class="text-success mb-3">
                                    <i class="fas fa-tv fa-4x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">المسلسلات</h5>
                                <p class="text-muted mb-3">اكتشف أحدث المسلسلات</p>
                                <div class="btn btn-outline-success">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    تصفح الآن
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <a href="{{ route('shahid.channels') }}" class="text-decoration-none">
                        <div class="card border-0 shadow-sm h-100 nav-card">
                            <div class="card-body text-center p-4">
                                <div class="text-info mb-3">
                                    <i class="fas fa-broadcast-tower fa-4x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">البث المباشر</h5>
                                <p class="text-muted mb-3">شاهد القنوات المباشرة</p>
                                <div class="btn btn-outline-info">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    شاهد الآن
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100 nav-card" onclick="showSearchModal()">
                        <div class="card-body text-center p-4">
                            <div class="text-warning mb-3">
                                <i class="fas fa-search fa-4x"></i>
                            </div>
                            <h5 class="fw-bold mb-2">البحث</h5>
                            <p class="text-muted mb-3">ابحث عن محتوى محدد</p>
                            <div class="btn btn-outline-warning">
                                <i class="fas fa-search me-2"></i>
                                ابحث الآن
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <a href="{{ route('shahid.browser.extractor') }}" class="text-decoration-none">
                        <div class="card border-0 shadow-sm h-100 nav-card">
                            <div class="card-body text-center p-4">
                                <div class="text-success mb-3">
                                    <i class="fas fa-browser fa-4x"></i>
                                </div>
                                <h5 class="fw-bold mb-2">Browser DRM</h5>
                                <p class="text-muted mb-3">استخراج مفاتيح DRM بالمتصفح</p>
                                <div class="btn btn-outline-success">
                                    <i class="fas fa-key me-2"></i>
                                    استخراج المفاتيح
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Coming Soon Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-5">
                    <div class="text-muted mb-3">
                        <i class="fas fa-clock fa-3x"></i>
                    </div>
                    <h3 class="fw-bold mb-3">قريباً</h3>
                    <p class="text-muted mb-4">المزيد من المحتوى الرائع قادم قريباً</p>
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="alert alert-info border-0">
                                <i class="fas fa-info-circle me-2"></i>
                                سيتم إضافة المزيد من الميزات والمحتوى في التحديثات القادمة
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>
                    البحث في شاهيد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="searchForm">
                    <div class="mb-3">
                        <label for="searchInput" class="form-label">ابحث عن فيلم أو مسلسل</label>
                        <input 
                            type="text" 
                            class="form-control form-control-lg" 
                            id="searchInput" 
                            placeholder="أدخل اسم الفيلم أو المسلسل أو رابط شاهيد..."
                        >
                        <div class="form-text">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                يمكنك البحث باستخدام الاسم أو رابط شاهيد المباشر
                            </small>
                        </div>
                    </div>
                </form>
                
                <!-- Search Results -->
                <div id="searchResults" class="mt-4 d-none">
                    <h6 class="fw-bold mb-3">نتائج البحث:</h6>
                    <div id="searchResultsContainer">
                        <!-- Results will be displayed here -->
                    </div>
                </div>
                
                <!-- Search Loading -->
                <div id="searchLoading" class="text-center py-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري البحث...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="performSearch()">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.nav-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.nav-card:hover .btn {
    transform: scale(1.05);
}

.display-4 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card {
    border-radius: 15px;
}

.btn {
    border-radius: 10px;
    transition: all 0.3s ease;
}
</style>
@endpush

@push('scripts')
<script>
function showSearchModal() {
    $('#searchModal').modal('show');
    $('#searchInput').focus();
}

function performSearch() {
    const query = $('#searchInput').val().trim();
    
    if (!query) {
        showAlert('warning', 'يرجى إدخال كلمة البحث');
        return;
    }
    
    showSearchLoading();
    hideSearchResults();
    
    $.ajax({
        url: '/api/shahid/search',
        method: 'POST',
        data: {
            content_id_or_url: query
        },
        success: function(response) {
            hideSearchLoading();
            
            if (response.success && response.data) {
                displaySearchResults([response.data]);
                showSearchResults();
            } else {
                showAlert('info', 'لم يتم العثور على نتائج');
            }
        },
        error: function(xhr) {
            hideSearchLoading();
            
            let errorMessage = 'حدث خطأ في البحث';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            showAlert('danger', errorMessage);
        }
    });
}

function displaySearchResults(results) {
    const container = $('#searchResultsContainer');
    container.empty();
    
    results.forEach(item => {
        const resultCard = `
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">${item.title || 'عنوان غير محدد'}</h6>
                    <p class="card-text text-muted">${item.description || 'لا يوجد وصف'}</p>
                    <button class="btn btn-primary btn-sm" onclick="viewContent('${item.id || ''}')">
                        <i class="fas fa-play me-2"></i>
                        مشاهدة
                    </button>
                </div>
            </div>
        `;
        container.append(resultCard);
    });
}

function viewContent(contentId) {
    if (!contentId) {
        showAlert('warning', 'معرف المحتوى غير صحيح');
        return;
    }
    
    showAlert('info', `سيتم تشغيل المحتوى: ${contentId}`);
    $('#searchModal').modal('hide');
}

function showSearchLoading() {
    $('#searchLoading').removeClass('d-none');
}

function hideSearchLoading() {
    $('#searchLoading').addClass('d-none');
}

function showSearchResults() {
    $('#searchResults').removeClass('d-none');
}

function hideSearchResults() {
    $('#searchResults').addClass('d-none');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// Handle Enter key in search input
$('#searchInput').on('keypress', function(e) {
    if (e.which === 13) {
        performSearch();
    }
});
</script>
@endpush
