<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        // Check if this is an admin route
        $isAdminRoute = $request->is('admin/*');

        // Get the appropriate user based on the route
        $user = $isAdminRoute ? auth('admin')->user() : auth()->user();

        // Check if user is authenticated
        if (!$user) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required'
                ], 401);
            }

            return redirect()->route($isAdminRoute ? 'admin.login' : 'login');
        }

        // Check if user has the required permission
        if (!$user->hasPermission($permission)) {
            \Log::warning('Access denied', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'required_permission' => $permission,
                'user_roles' => $user->roles?->pluck('name')?->toArray() ?? []
            ]);

            // If it's an AJAX request, return JSON response
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => "You do not have permission to {$permission}"
                ], 403);
            }

            // For regular requests, abort with 403
            abort(403, "You do not have permission to {$permission}");
        }

        return $next($request);
    }
}
