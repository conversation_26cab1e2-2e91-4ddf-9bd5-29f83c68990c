<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;
use App\Models\AdminActivityLog;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasPermissions;

    protected $guard_name = 'web';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'access_token',
        'name',
        'email',
        'password',
        'user_type',
        'avatar',
        'bio',
        'phone',
        'country',
        'department',
        'position',
        'application',
        'subscription_expiry',
        'remaining_days',
        'is_active',
        'is_admin',
        'is_super_admin',
        'can_manage_admins',
        'email_verified',
        'permissions',
        'settings',
        'user_info',
        'login_time',
        'last_login_at',
        'last_login_ip',
        'last_activity_at',
        'remember_me',
        'device_fingerprint',
        'password_changed_at',
        'force_password_change',
        'suspended_at',
        'suspended_reason',
        'suspended_by',
        'created_by_admin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'access_token',
        'password',
        'remember_token',
        'device_fingerprint',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'subscription_expiry' => 'datetime',
        'login_time' => 'datetime',
        'last_login_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'password_changed_at' => 'datetime',
        'suspended_at' => 'datetime',
        'is_active' => 'boolean',
        'is_admin' => 'boolean',
        'is_super_admin' => 'boolean',
        'can_manage_admins' => 'boolean',
        'email_verified' => 'boolean',
        'remember_me' => 'boolean',
        'force_password_change' => 'boolean',
        'remaining_days' => 'integer',
        'user_info' => 'array',
        'permissions' => 'array',
        'settings' => 'array',
    ];

    /**
     * Initialize permissions relationship if not exists
     */
    public function permissions()
    {
        return $this->morphToMany(
            config('permission.models.permission'),
            'model',
            config('permission.table_names.model_has_permissions'),
            config('permission.column_names.model_morph_key'),
            'permission_id'
        );
    }

    /**
     * Get the user's subscription status
     */
    public function getSubscriptionStatusAttribute()
    {
        if (!$this->subscription_expiry) {
            return 'unknown';
        }

        $remainingDays = now()->diffInDays($this->subscription_expiry, false);

        if ($remainingDays > 30) {
            return 'active';
        } elseif ($remainingDays > 7) {
            return 'expiring_soon';
        } elseif ($remainingDays > 0) {
            return 'expiring_very_soon';
        } else {
            return 'expired';
        }
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription()
    {
        return $this->hasOne(UserSubscription::class)
                    ->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where('expires_at', '>', now())
                    ->latest('created_at');
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        // Check basic admin flags
        if ($this->is_admin || $this->user_type === 'admin') {
            return true;
        }

        // Check if user has any admin-related roles
        $adminRoles = ['admin', 'Admin', 'super-admin', 'Super Admin', 'Admin Manager', 'Role Manager', 'Permission Manager'];
        foreach ($adminRoles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user is super admin.
     */
    public function isSuperAdmin(): bool
    {
        // Check basic super admin flag
        if ($this->is_super_admin) {
            return true;
        }

        // Check if user has super admin roles
        $superAdminRoles = ['super-admin', 'Super Admin'];
        foreach ($superAdminRoles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user can manage other admins.
     */
    public function canManageAdmins(): bool
    {
        // First check simple flags
        if ($this->isSuperAdmin() || $this->can_manage_admins) {
            return true;
        }

        // Check if user has any roles or permissions before checking
        if (!$this->roles()->exists() && !$this->permissions()->exists()) {
            return false;
        }

        // Then check permissions safely
        return $this->safeHasPermissionTo('manage admins');
    }

    /**
     * Safe permission check that won't throw errors.
     */
    public function safeHasPermissionTo(string $permission): bool
    {
        try {
            // Super admin has all permissions
            if ($this->isSuperAdmin()) {
                return true;
            }

            // Check if user has any roles first
            if (!$this->roles()->exists()) {
                return false;
            }

            // Load roles with permissions
            $this->load('roles.permissions');

            // Check if any role has this permission
            foreach ($this->roles as $role) {
                if ($role->permissions->contains('name', $permission)) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if user has permission (enhanced with Spatie).
     */
    public function hasPermission(string $permission): bool
    {
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Check Spatie permissions first
        if ($this->hasPermissionTo($permission)) {
            return true;
        }

        // Fallback to legacy permissions
        return in_array($permission, $this->permissions ?? []);
    }

    /**
     * Check if user is suspended.
     */
    public function isSuspended(): bool
    {
        return !is_null($this->suspended_at);
    }

    /**
     * Check if user is active and not suspended.
     */
    public function isActiveAdmin(): bool
    {
        return $this->is_active && !$this->isSuspended() && $this->isAdmin();
    }

    /**
     * Get user's current subscription status.
     */
    public function getCurrentSubscriptionStatus(): array
    {
        $activeSubscription = $this->activeSubscription;

        if (!$activeSubscription) {
            return [
                'status' => 'no_subscription',
                'text' => 'No Active Subscription',
                'color' => 'danger',
                'remaining_days' => 0
            ];
        }

        $remainingDays = $activeSubscription->remaining_days;

        return [
            'status' => $activeSubscription->status,
            'text' => $activeSubscription->status_text,
            'color' => $activeSubscription->status_color,
            'remaining_days' => $remainingDays,
            'expires_at' => $activeSubscription->expires_at,
            'plan_name' => $activeSubscription->subscriptionPlan->name ?? 'Unknown Plan'
        ];
    }

    /**
     * Check if user has valid subscription
     */
    public function hasValidSubscription()
    {
        return $this->is_active && $this->subscription_expiry && $this->subscription_expiry->isFuture();
    }

    /**
     * Admin who suspended this user.
     */
    public function suspendedBy()
    {
        return $this->belongsTo(User::class, 'suspended_by');
    }

    /**
     * Admin who created this user.
     */
    public function createdByAdmin()
    {
        return $this->belongsTo(User::class, 'created_by_admin');
    }

    /**
     * Users created by this admin.
     */
    public function createdUsers()
    {
        return $this->hasMany(User::class, 'created_by_admin');
    }

    /**
     * Users suspended by this admin.
     */
    public function suspendedUsers()
    {
        return $this->hasMany(User::class, 'suspended_by');
    }

    /**
     * Activity logs for this admin.
     */
    public function activityLogs()
    {
        return $this->hasMany(\App\Models\AdminActivityLog::class, 'admin_id');
    }

    /**
     * Suspend user.
     */
    public function suspend(string $reason, User $suspendedBy): bool
    {
        $this->update([
            'suspended_at' => now(),
            'suspended_reason' => $reason,
            'suspended_by' => $suspendedBy->id,
            'is_active' => false,
        ]);

        return true;
    }

    /**
     * Unsuspend user.
     */
    public function unsuspend(User $unsuspendedBy): bool
    {
        $this->update([
            'suspended_at' => null,
            'suspended_reason' => null,
            'suspended_by' => null,
            'is_active' => true,
        ]);

        return true;
    }

    /**
     * Update last activity.
     */
    public function updateLastActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Get avatar URL.
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/avatars/' . $this->avatar);
        }

        // Generate avatar with initials
        $initials = strtoupper(substr($this->name, 0, 1));
        return "https://ui-avatars.com/api/?name={$initials}&background=667eea&color=fff&size=128";
    }

    /**
     * Get full admin info.
     */
    public function getAdminInfoAttribute(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'avatar_url' => $this->avatar_url,
            'department' => $this->department,
            'position' => $this->position,
            'is_super_admin' => $this->isSuperAdmin(),
            'can_manage_admins' => $this->canManageAdmins(),
            'roles' => $this->roles->pluck('name'),
            'permissions' => $this->getAllPermissions()->pluck('name'),
            'last_activity' => $this->last_activity_at,
            'is_suspended' => $this->isSuspended(),
            'suspended_reason' => $this->suspended_reason,
        ];
    }
}
