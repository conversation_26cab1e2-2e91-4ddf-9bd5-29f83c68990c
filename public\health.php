<?php
/**
 * Shahid Admin Panel - Health Check
 * Simple health check endpoint for monitoring
 */

header('Content-Type: application/json');

$health = [
    'status' => 'ok',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => []
];

// Check if <PERSON><PERSON> is accessible
try {
    if (file_exists('../bootstrap/app.php')) {
        $health['checks']['laravel'] = 'ok';
    } else {
        $health['checks']['laravel'] = 'error';
        $health['status'] = 'error';
    }
} catch (Exception $e) {
    $health['checks']['laravel'] = 'error';
    $health['status'] = 'error';
}

// Check storage directory
if (is_writable('../storage')) {
    $health['checks']['storage'] = 'ok';
} else {
    $health['checks']['storage'] = 'error';
    $health['status'] = 'error';
}

// Check cache directory
if (is_writable('../bootstrap/cache')) {
    $health['checks']['cache'] = 'ok';
} else {
    $health['checks']['cache'] = 'error';
    $health['status'] = 'error';
}

// Check .env file
if (file_exists('../.env')) {
    $health['checks']['env'] = 'ok';
} else {
    $health['checks']['env'] = 'error';
    $health['status'] = 'error';
}

// Set appropriate HTTP status code
if ($health['status'] === 'error') {
    http_response_code(500);
} else {
    http_response_code(200);
}

echo json_encode($health, JSON_PRETTY_PRINT);
?>
