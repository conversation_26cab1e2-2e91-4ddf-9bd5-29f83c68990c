<?php

namespace App\Services;

use App\Models\MoviesCache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class MoviesCacheService
{
    protected $moviesAPI;

    public function __construct(ShahidMoviesAPI $moviesAPI)
    {
        $this->moviesAPI = $moviesAPI;
    }

    /**
     * Get movies with caching - main method
     */
    public function getMovies($country = 'EG', $limit = 50, $offset = 0, $forceRefresh = false)
    {
        try {
            Log::info("🎬 Getting movies with cache - Country: {$country}, Limit: {$limit}, Offset: {$offset}, Force: " . ($forceRefresh ? 'Yes' : 'No'));

            // If force refresh, clear cache first
            if ($forceRefresh) {
                $this->clearCountryCache($country);
                Log::info("🗑️ Cache cleared for country: {$country}");
            }

            // Check if we have cached data
            $cachedMovies = $this->getCachedMovies($country);
            
            if (!empty($cachedMovies) && !$forceRefresh) {
                Log::info("⚡ Using cached movies data - Total: " . count($cachedMovies));

                // Apply pagination to cached data
                // If limit is high (200+), return all cached data
                if ($limit >= 200 || $limit == 999) {
                    $paginatedMovies = $cachedMovies;
                    Log::info("📋 Returning all cached movies: " . count($cachedMovies));
                } else {
                    $paginatedMovies = array_slice($cachedMovies, $offset, $limit);
                    Log::info("📄 Returning paginated movies: " . count($paginatedMovies) . " of " . count($cachedMovies));
                }

                return [
                    'success' => true,
                    'movies' => $paginatedMovies,
                    'total' => count($cachedMovies),
                    'cached' => true,
                    'cache_date' => $this->getLatestCacheDate($country),
                ];
            }

            // No cache or force refresh - fetch from API
            Log::info("🌐 Fetching fresh movies data from API");
            $apiResult = $this->moviesAPI->getMovies($country, 200, 0, true); // fetchAll = true to get all movies

            if (!isset($apiResult['movies']) || empty($apiResult['movies'])) {
                return [
                    'success' => false,
                    'error' => 'No movies data available',
                    'cached' => false,
                ];
            }

            // Cache the fresh data
            $this->cacheMoviesData($country, $apiResult['movies']);
            
            // Apply pagination
            $paginatedMovies = array_slice($apiResult['movies'], $offset, $limit);

            return [
                'success' => true,
                'movies' => $paginatedMovies,
                'total' => count($apiResult['movies']),
                'cached' => false,
                'cache_date' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error in MoviesCacheService::getMovies: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'cached' => false,
            ];
        }
    }

    /**
     * Get movie details with caching
     */
    public function getMovieDetails($movieId, $country = 'EG', $forceRefresh = false)
    {
        try {
            Log::info("🎬 Getting movie details with cache - ID: {$movieId}, Country: {$country}");

            // Check cache first
            $cachedMovie = MoviesCache::getCachedMovie($country, $movieId);
            
            if ($cachedMovie && $cachedMovie->is_complete && !$cachedMovie->needsRefresh() && !$forceRefresh) {
                Log::info("⚡ Using cached movie details");
                return [
                    'success' => true,
                    'movie_data' => $cachedMovie->movie_data,
                    'movie_info' => [
                        'title' => $cachedMovie->title,
                        'description' => $cachedMovie->description,
                        'poster_url' => $cachedMovie->poster_url,
                        'metadata' => $cachedMovie->metadata,
                        'duration' => $cachedMovie->duration,
                        'year' => $cachedMovie->year,
                    ],
                    'cached' => true,
                    'cache_date' => $cachedMovie->last_updated->toISOString(),
                ];
            }

            // Fetch fresh data
            Log::info("🌐 Fetching fresh movie details from API");
            $movieResult = $this->moviesAPI->getMovieDetails($movieId);
            
            if (!$movieResult['success']) {
                return $movieResult;
            }

            // Cache the complete movie data
            $this->cacheCompleteMovieData($country, $movieId, $movieResult);

            return [
                'success' => true,
                'movie_data' => $movieResult['data'],
                'movie_info' => $movieResult['movie_info'] ?? [],
                'cached' => false,
                'cache_date' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::error("❌ Error in getMovieDetails: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'cached' => false,
            ];
        }
    }

    /**
     * Get cached movies for a country
     */
    protected function getCachedMovies($country)
    {
        // First try Laravel cache
        $cacheKey = "movies_list_{$country}";
        $cachedMovies = Cache::get($cacheKey);

        if (!empty($cachedMovies)) {
            Log::info("📦 Found movies in Laravel cache for {$country}: " . count($cachedMovies));
            return $cachedMovies;
        }

        // If not in Laravel cache, try database cache
        $dbMovies = MoviesCache::where('country', $country)->get();

        if ($dbMovies->isNotEmpty()) {
            Log::info("📦 Found movies in database cache for {$country}: " . $dbMovies->count());

            // Convert to array format
            $moviesArray = $dbMovies->map(function ($movie) {
                return [
                    'id' => $movie->movie_id,
                    'title' => $movie->title,
                    'description' => $movie->description,
                    'poster_url' => $movie->poster_url,
                    'year' => $movie->year,
                    'duration' => $movie->duration,
                    'genres' => $movie->metadata['genres'] ?? [],
                    'cast' => $movie->metadata['cast'] ?? [],
                    'country' => $movie->metadata['country'] ?? $movie->country,
                ];
            })->toArray();

            // Store back in Laravel cache for faster access
            Cache::put($cacheKey, $moviesArray, now()->addHours(24));
            Cache::put("movies_cache_date_{$country}", now()->toISOString(), now()->addHours(24));

            return $moviesArray;
        }

        Log::info("📦 No cached movies found for {$country}");
        return [];
    }

    /**
     * Get latest cache date for a country
     */
    protected function getLatestCacheDate($country)
    {
        $cacheKey = "movies_cache_date_{$country}";
        return Cache::get($cacheKey, now()->toISOString());
    }

    /**
     * Clear cache for a specific country
     */
    protected function clearCountryCache($country)
    {
        $cacheKey = "movies_list_{$country}";
        $cacheDateKey = "movies_cache_date_{$country}";
        
        Cache::forget($cacheKey);
        Cache::forget($cacheDateKey);
        
        // Also clear database cache
        MoviesCache::where('country', $country)->delete();
        
        Log::info("🗑️ Cleared all cache for country: {$country}");
    }

    /**
     * Cache movies data
     */
    protected function cacheMoviesData($country, $moviesArray)
    {
        Log::info("💾 Caching " . count($moviesArray) . " movies for country: {$country}");
        
        // Cache in Laravel cache
        $cacheKey = "movies_list_{$country}";
        $cacheDateKey = "movies_cache_date_{$country}";
        
        Cache::put($cacheKey, $moviesArray, now()->addHours(24)); // Cache for 24 hours
        Cache::put($cacheDateKey, now()->toISOString(), now()->addHours(24));
        
        // Also cache in database
        foreach ($moviesArray as $movie) {
            MoviesCache::cacheMovie($country, $movie['id'], [
                'title' => $movie['title'] ?? 'Unknown Movie',
                'description' => $movie['description'] ?? '',
                'poster_url' => $movie['poster_url'] ?? null,
                'metadata' => [
                    'year' => $movie['year'] ?? null,
                    'genres' => $movie['genres'] ?? [],
                    'cast' => $movie['cast'] ?? [],
                    'country' => $movie['country'] ?? $country,
                ],
                'movie_data' => [],
                'duration' => $movie['duration'] ?? null,
                'year' => $movie['year'] ?? null,
                'is_complete' => false,
            ]);
        }
        
        Log::info("✅ Movies cached successfully");
    }

    /**
     * Cache complete movie data with details
     */
    protected function cacheCompleteMovieData($country, $movieId, $movieResult)
    {
        Log::info("💾 Caching complete movie data for: {$movieId}");
        
        MoviesCache::cacheMovie($country, $movieId, [
            'title' => $movieResult['movie_info']['title'] ?? 'Unknown Movie',
            'description' => $movieResult['movie_info']['description'] ?? '',
            'poster_url' => $movieResult['movie_info']['poster_url'] ?? null,
            'metadata' => $movieResult['movie_info']['metadata'] ?? [],
            'movie_data' => $movieResult['data'],
            'duration' => $movieResult['movie_info']['duration'] ?? null,
            'year' => $movieResult['movie_info']['year'] ?? null,
            'is_complete' => true,
        ]);
        
        Log::info("✅ Complete movie data cached successfully");
    }
}
