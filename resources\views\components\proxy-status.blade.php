{{-- Proxy Status Partial --}}
<div id="proxyStatusContainer" class="proxy-status-container mb-3">
    <div class="card border-0 shadow-sm">
        <div class="card-body p-3">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div id="proxyStatusIcon" class="me-2">
                        <i class="fas fa-circle text-secondary" style="font-size: 0.8rem;"></i>
                    </div>
                    <div>
                        <h6 class="mb-0 fw-bold" style="font-size: 0.9rem;">
                            <i class="fas fa-globe me-1"></i>
                            Connection Status
                        </h6>
                        <small id="proxyStatusText" class="text-muted">Checking connection...</small>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <button id="testProxyBtn" class="btn btn-outline-primary btn-sm me-2" onclick="testCurrentProxy()" disabled>
                        <i class="fas fa-sync-alt me-1"></i>
                        Test
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleProxyDetails()">
                        <i id="detailsToggleIcon" class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            
            {{-- Proxy Details (Hidden by default) --}}
            <div id="proxyDetails" class="mt-3" style="display: none;">
                <hr class="my-2">
                <div class="row g-2">
                    <div class="col-md-6">
                        <small class="text-muted d-block">Proxy Name:</small>
                        <span id="proxyName" class="fw-medium">-</span>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted d-block">Address:</small>
                        <span id="proxyAddress" class="fw-medium">-</span>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted d-block">Type:</small>
                        <span id="proxyType" class="fw-medium">-</span>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted d-block">Response Time:</small>
                        <span id="proxyResponseTime" class="fw-medium">-</span>
                    </div>
                    <div class="col-12">
                        <small class="text-muted d-block">Last Tested:</small>
                        <span id="proxyLastTested" class="fw-medium">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.proxy-status-container {
    position: sticky;
    top: 20px;
    z-index: 100;
}

.proxy-status-container .card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.proxy-status-container .card-body {
    transition: all 0.3s ease;
}

.proxy-status-container:hover .card {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

@media (max-width: 768px) {
    .proxy-status-container {
        position: relative;
        top: 0;
    }
}
</style>

<script>
let proxyStatusInterval;

// Initialize proxy status on page load
$(document).ready(function() {
    loadProxyStatus();
    
    // Auto-refresh every 30 seconds
    proxyStatusInterval = setInterval(loadProxyStatus, 30000);
});

// Clean up interval when page unloads
$(window).on('beforeunload', function() {
    if (proxyStatusInterval) {
        clearInterval(proxyStatusInterval);
    }
});

function loadProxyStatus() {
    $.ajax({
        url: '/admin/api/proxy/status',
        method: 'GET',
        success: function(response) {
            if (response.success && response.status) {
                updateProxyStatusDisplay(response.status);
            } else {
                showProxyError('Failed to load proxy status');
            }
        },
        error: function(xhr) {
            console.error('Error loading proxy status:', xhr);
            showProxyError('Connection error');
        }
    });
}

function updateProxyStatusDisplay(status) {
    const statusIcon = $('#proxyStatusIcon i');
    const statusText = $('#proxyStatusText');
    const testBtn = $('#testProxyBtn');
    
    // Update main status
    if (status.enabled) {
        if (status.status === 'Healthy') {
            statusIcon.removeClass().addClass('fas fa-circle text-success');
            statusText.html(`<span class="text-success">Connected via ${status.proxy_name}</span>`);
        } else {
            statusIcon.removeClass().addClass('fas fa-circle text-warning');
            statusText.html(`<span class="text-warning">Proxy active (${status.proxy_name}) - Status unknown</span>`);
        }
        
        // Update details
        $('#proxyName').text(status.proxy_name || '-');
        $('#proxyAddress').text(status.proxy_address || '-');
        $('#proxyType').text(status.proxy_type || '-');
        $('#proxyResponseTime').text(status.response_time || '-');
        $('#proxyLastTested').text(status.last_tested || '-');
        
    } else {
        statusIcon.removeClass().addClass('fas fa-circle text-secondary');
        statusText.html('<span class="text-muted">Direct connection (no proxy)</span>');
        
        // Clear details
        $('#proxyName').text('No proxy active');
        $('#proxyAddress').text('-');
        $('#proxyType').text('-');
        $('#proxyResponseTime').text('-');
        $('#proxyLastTested').text('-');
    }
    
    // Enable test button
    testBtn.prop('disabled', false);
}

function showProxyError(message) {
    const statusIcon = $('#proxyStatusIcon i');
    const statusText = $('#proxyStatusText');
    
    statusIcon.removeClass().addClass('fas fa-circle text-danger');
    statusText.html(`<span class="text-danger">${message}</span>`);
}

function testCurrentProxy() {
    const testBtn = $('#testProxyBtn');
    const originalText = testBtn.html();
    
    testBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Testing...').prop('disabled', true);
    
    $.ajax({
        url: '/admin/api/proxy/test-current',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showAlert('success', `Proxy test successful! Response time: ${response.data.response_time || 'Unknown'}ms`);
                // Refresh status after successful test
                setTimeout(loadProxyStatus, 1000);
            } else {
                showAlert('error', `Proxy test failed: ${response.message}`);
            }
        },
        error: function(xhr) {
            console.error('Error testing proxy:', xhr);
            showAlert('error', 'Failed to test proxy connection');
        },
        complete: function() {
            testBtn.html(originalText).prop('disabled', false);
        }
    });
}

function toggleProxyDetails() {
    const details = $('#proxyDetails');
    const icon = $('#detailsToggleIcon');
    
    if (details.is(':visible')) {
        details.slideUp(200);
        icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
    } else {
        details.slideDown(200);
        icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
    }
}

// Helper function for alerts (if not already defined)
function showAlert(type, message) {
    if (typeof window.showAlert === 'function') {
        window.showAlert(type, message);
    } else {
        // Fallback alert
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Try to find a container for alerts
        let container = $('#alertContainer');
        if (container.length === 0) {
            container = $('body').prepend('<div id="alertContainer" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>').find('#alertContainer');
        }
        
        container.append(alertHtml);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            container.find('.alert').last().fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }
}
</script>
