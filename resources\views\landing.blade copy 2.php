<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teefa Live - Movies, Series & Live Sports</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 0, 150, 0.3);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            color: #ff0080;
            transform: translateY(-2px);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: #ffffff;
            margin: 3px 0;
            transition: 0.3s;
        }

        /* Login Button */
        .login-btn {
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            color: white;
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 128, 0.4);
            color: white;
            text-decoration: none;
        }

        .login-btn i {
            font-size: 0.9rem;
        }

        .nav-actions {
            display: flex;
            align-items: center;
        }

        .mobile-login {
            display: none;
            margin-top: 1rem;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-bg-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .hero::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6));
            z-index: 1;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255, 0, 128, 0.2) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(255, 140, 0, 0.2) 0%, transparent 50%);
            animation: pulseGlow 4s ease-in-out infinite alternate;
        }

        @keyframes pulseGlow {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }

        .hero-content {
            max-width: 800px;
            z-index: 3;
            position: relative;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff0080, #ff8c00, #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: fadeInUp 1s ease;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease 0.3s both;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease 0.6s both;
        }

        .cta-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid #ff0080;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 0, 128, 0.4);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Categories Section */
        .categories {
            padding: 5rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-bottom: 4rem;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }

        .category-card {
            background: linear-gradient(135deg, rgba(255, 0, 128, 0.1) 0%, rgba(255, 140, 0, 0.1) 100%);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            cursor: pointer;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(255, 0, 128, 0.3);
            border-color: rgba(255, 0, 128, 0.5);
        }

        .category-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .category-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .category-card p {
            color: #cccccc;
            line-height: 1.6;
        }

        /* Featured Content */
        .featured {
            background: rgba(255, 255, 255, 0.02);
            padding: 5rem 2rem;
        }

        .featured-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .content-slider {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .content-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content-card:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(255, 0, 128, 0.2);
        }

        .card-image {
            height: 350px;
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
        }

        .card-content {
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .card-meta {
            color: #888;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .watch-btn {
            background: linear-gradient(45deg, #ff0080, #ff8c00);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
        }

        .watch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 0, 128, 0.4);
        }

        /* Footer */
        .footer {
            background: #000000;
            padding: 3rem 2rem 1rem;
            text-align: center;
            border-top: 1px solid rgba(255, 0, 128, 0.3);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #cccccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #ff0080;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .social-links a {
            color: #ffffff;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            color: #ff0080;
            transform: translateY(-3px);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .category-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .category-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }

            .nav-menu {
                position: fixed;
                left: -100%;
                top: 70px;
                flex-direction: column;
                background: rgba(0, 0, 0, 0.95);
                width: 100%;
                text-align: center;
                transition: 0.3s;
                padding: 2rem 0;
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-actions {
                display: none;
            }

            .mobile-login {
                display: block;
            }

            .login-btn {
                margin-left: 0;
                padding: 0.6rem 1.2rem;
                font-size: 0.9rem;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .cta-btn {
                width: 80%;
                justify-content: center;
            }



            .content-slider {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .nav-container {
                padding: 0 1rem;
            }

            .hero {
                padding: 0 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .categories {
                padding: 3rem 1rem;
            }

            .featured {
                padding: 3rem 1rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff0080;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav-container">
            <div class="logo">Teefa Live</div>
            <ul class="nav-menu" id="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#movies">Movies</a></li>
                <li><a href="#series">TV Series</a></li>
                <li><a href="#sports">Live Sports</a></li>
                <li><a href="#genres">Genres</a></li>
                <li class="mobile-login">
                    <a href="/login" class="login-btn">
                        <i class="fas fa-user"></i>
                        تسجيل الدخول
                    </a>
                </li>
            </ul>
            <div class="nav-actions">
                <a href="/login" class="login-btn">
                    <i class="fas fa-user"></i>
                     Login
                </a>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <!-- Hero Background Image -->
        <div class="hero-image">
            <img decoding="async"
                 class="hero-bg-image"
                 sizes="100%"
                 srcset="https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 16w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 32w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 48w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 64w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 96w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 128w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 256w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 320w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 384w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 768w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 1024w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 1280w,
                         https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80 1440w"
                 src="https://shahid.mbc.net/mediaObject/436ea116-cdae-4007-ace6-3c755df16856?width=1920&type=avif&q=80"
                 alt="Teefa Live Background">
        </div>
        <div class="hero-content">
            <h1>Teefa Live</h1>
            <p>Stream the latest movies, binge-watch TV series, and catch live sports - all in one place. Your ultimate entertainment destination.</p>
            <div class="cta-buttons">
                <a href="#featured" class="cta-btn btn-primary">
                    <i class="fas fa-play"></i>
                    Start Watching
                </a>
                <a href="#categories" class="cta-btn btn-secondary">
                    <i class="fas fa-compass"></i>
                    Explore Content
                </a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories" id="categories">
        <h2 class="section-title">What Would You Like to Watch?</h2>
        <div class="category-grid">
            <div class="category-card" data-category="movies">
                <div class="category-icon">
                    <i class="fas fa-film"></i>
                </div>
                <h3>Movies</h3>
                <p>Latest blockbusters, classic films, and exclusive premieres. Discover movies across all genres from action to romance.</p>
            </div>
            <div class="category-card" data-category="series">
                <div class="category-icon">
                    <i class="fas fa-tv"></i>
                </div>
                <h3>TV Series</h3>
                <p>Binge-watch popular series, catch up on episodes, and discover new shows from around the world.</p>
            </div>
            <div class="category-card" data-category="sports">
                <div class="category-icon">
                    <i class="fas fa-futbol"></i>
                </div>
                <h3>Live Sports</h3>
                <p>Watch live matches, sports events, and games in real-time. Never miss your favorite team's action.</p>
            </div>
        </div>
    </section>

    <!-- Featured Content -->
    <section class="featured" id="featured">
        <div class="featured-container">
            <h2 class="section-title">Featured Content</h2>
            <div class="content-slider">
                <div class="content-card">
                    <div class="card-image">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Action Movie Collection</h3>
                        <p class="card-meta">2025 • Action • HD</p>
                        <button class="watch-btn">
                            <i class="fas fa-play"></i> Watch Now
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="card-image">
                        <i class="fas fa-tv"></i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Popular TV Series</h3>
                        <p class="card-meta">2025 • Drama • 4K</p>
                        <button class="watch-btn">
                            <i class="fas fa-play"></i> Watch Now
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="card-image">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Live Sports Events</h3>
                        <p class="card-meta">Live • Sports • HD</p>
                        <button class="watch-btn">
                            <i class="fas fa-broadcast-tower"></i> Watch Live
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="card-image">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Romance Collection</h3>
                        <p class="card-meta">2025 • Romance • HD</p>
                        <button class="watch-btn">
                            <i class="fas fa-play"></i> Watch Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#about">About Us</a>
                <a href="#contact">Contact</a>
                <a href="#privacy">Privacy Policy</a>
                <a href="#terms">Terms of Service</a>
                <a href="#help">Help Center</a>
            </div>
            <div class="social-links">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
            <p>&copy; 2025 StreamHub. All rights reserved. Unlimited entertainment at your fingertips.</p>
        </div>
    </footer>

    <script>
        // Mobile Navigation Toggle
        const hamburger = document.getElementById('hamburger');
        const navMenu = document.getElementById('nav-menu');

        hamburger.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Category card interactions
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                const category = this.dataset.category;
                console.log(`Navigating to ${category} section`);
                // Here you would typically navigate to the specific category page
            });
        });

        // Watch button interactions
        document.querySelectorAll('.watch-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalText = this.innerHTML;
                this.innerHTML = '<div class="loading"></div> Loading...';
                this.disabled = true;
                
                // Simulate loading
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                    alert('This would open the video player!');
                }, 2000);
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(0, 0, 0, 0.95)';
            } else {
                header.style.background = 'rgba(0, 0, 0, 0.9)';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.category-card, .content-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html>
