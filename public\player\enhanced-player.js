/**
 * Enhanced Player with Complete Proxy Support
 * Handles all video streaming through local proxy system
 */

console.log('🚀 Enhanced Player with Proxy Support loaded');

// Enhanced player configuration
window.EnhancedPlayer = {
    config: {
        proxyBaseUrl: window.location.origin + '/video-proxy',
        supportedDomains: [
            'mbcvod-enc.edgenextcdn.net',
            'shahid.mbc.net',
            'cdn.shahid.net',
            'mbc.net'
        ],
        retryAttempts: 3,
        retryDelay: 2000
    },

    /**
     * Transform external URLs to use proxy
     */
    transformUrl: function(url) {
        if (!url || typeof url !== 'string') {
            return url;
        }

        // Check if URL needs proxying
        const needsProxy = this.config.supportedDomains.some(domain => 
            url.includes(domain)
        );

        if (!needsProxy) {
            return url;
        }

        // Determine if it's a manifest or segment
        if (url.includes('.mpd') || url.includes('.m3u8') || url.includes('manifest')) {
            // Manifest file
            return `${this.config.proxyBaseUrl}/manifest?url=${encodeURIComponent(url)}`;
        } else {
            // Video segment
            const encodedUrl = btoa(url);
            return `${this.config.proxyBaseUrl}/segment/${encodedUrl}`;
        }
    },

    /**
     * Enhanced error handling with retry logic
     */
    handleError: function(error, retryCallback, attempt = 1) {
        console.error(`❌ Player Error (Attempt ${attempt}):`, error);

        if (attempt < this.config.retryAttempts) {
            console.log(`🔄 Retrying in ${this.config.retryDelay}ms... (${attempt}/${this.config.retryAttempts})`);
            
            setTimeout(() => {
                retryCallback(attempt + 1);
            }, this.config.retryDelay);
        } else {
            console.error('💥 Max retry attempts reached');
            this.showErrorMessage('فشل في تحميل الفيديو بعد عدة محاولات');
        }
    },

    /**
     * Show user-friendly error message
     */
    showErrorMessage: function(message) {
        const playerContainer = document.getElementById('player');
        if (playerContainer) {
            playerContainer.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    background: #000;
                    color: white;
                    text-align: center;
                    font-family: Arial, sans-serif;
                ">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                        <div style="font-size: 18px; margin-bottom: 10px;">${message}</div>
                        <button onclick="location.reload()" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            cursor: pointer;
                            font-size: 14px;
                        ">إعادة المحاولة</button>
                    </div>
                </div>
            `;
        }
    },

    /**
     * Initialize enhanced player with proxy support
     */
    initialize: function(playerData) {
        console.log('🔧 Initializing Enhanced Player...');
        console.log('📋 Player Data:', playerData);

        // Validate required data
        if (!playerData.streamUrl) {
            this.showErrorMessage('لم يتم توفير رابط الفيديو');
            return;
        }

        if (playerData.streamType === 'dash' && (!playerData.keyId || !playerData.key)) {
            this.showErrorMessage('مفاتيح التشفير مفقودة');
            return;
        }

        // Transform URLs to use proxy
        const transformedData = {
            ...playerData,
            streamUrl: this.transformUrl(playerData.streamUrl)
        };

        console.log('🔄 Transformed stream URL:', transformedData.streamUrl);

        // Initialize player with retry logic
        this.initializeWithRetry(transformedData);
    },

    /**
     * Initialize player with retry mechanism
     */
    initializeWithRetry: function(playerData, attempt = 1) {
        try {
            console.log(`🎬 Initializing player (Attempt ${attempt})...`);

            // Set up play button data
            const playButton = document.getElementById('playStreamButton');
            if (!playButton) {
                throw new Error('Play button not found');
            }

            if (playerData.streamType === 'hls') {
                playButton.dataset.hlsUrl = playerData.streamUrl;
                playButton.dataset.streamType = 'hls';
                playButton.dataset.keyData = '';
                playButton.dataset.token = playerData.token || '';
            } else {
                playButton.dataset.mpdUrl = playerData.streamUrl;
                playButton.dataset.streamType = 'dash';
                playButton.dataset.keyData = playerData.keyId && playerData.key ? 
                    `${playerData.keyId}:${playerData.key}` : '';
                playButton.dataset.licenseUrl = playerData.licenseUrl || '';
            }

            // Auto-click play button
            setTimeout(() => {
                console.log('▶️ Auto-clicking play button...');
                playButton.click();

                // Set up error handling for this attempt
                setTimeout(() => {
                    const player = jwplayer('player');
                    if (player) {
                        player.on('error', (error) => {
                            this.handleError(error, (nextAttempt) => {
                                this.initializeWithRetry(playerData, nextAttempt);
                            }, attempt);
                        });
                    }
                }, 1000);

            }, 500);

        } catch (error) {
            this.handleError(error, (nextAttempt) => {
                this.initializeWithRetry(playerData, nextAttempt);
            }, attempt);
        }
    },

    /**
     * Test proxy connectivity
     */
    testProxy: async function() {
        console.log('🔍 Testing proxy connectivity...');

        try {
            const testUrl = `${this.config.proxyBaseUrl}/manifest?url=${encodeURIComponent('https://httpbin.org/get')}`;
            const response = await fetch(testUrl, { method: 'HEAD' });
            
            if (response.ok) {
                console.log('✅ Proxy is working correctly');
                return true;
            } else {
                console.warn('⚠️ Proxy returned non-OK status:', response.status);
                return false;
            }
        } catch (error) {
            console.error('❌ Proxy test failed:', error);
            return false;
        }
    }
};

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM ready - Enhanced Player available');
    
    // Test proxy on load
    window.EnhancedPlayer.testProxy();
});

console.log('✅ Enhanced Player ready');
