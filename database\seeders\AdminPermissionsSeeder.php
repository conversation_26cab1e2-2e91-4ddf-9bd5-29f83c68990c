<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class AdminPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Define all permissions that are used in the routes
        $permissions = [
            // User management
            'view users',
            'create users', 
            'edit users',
            'delete users',
            
            // Subscription management
            'view subscriptions',
            'create subscriptions',
            'edit subscriptions', 
            'delete subscriptions',
            
            // Reports
            'view reports',
            'create reports',
            'edit reports',
            'delete reports',
            
            // Settings
            'view settings',
            'edit settings',
            
            // Admin management
            'view admins',
            'create admins',
            'edit admins',
            'delete admins',
            'manage admin permissions',
            'manage admins',
            
            // Role management
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
            'manage roles',
            'manage role permissions',
            
            // Permission management
            'view permissions',
            'create permissions',
            'edit permissions',
            'delete permissions',
            'manage permissions',
            
            // Dashboard and general
            'view admin panel',
            'view dashboard',
            'super admin access',
            
            // Analytics
            'view analytics',
            'view activity logs',
        ];

        // Create permissions for admin guard
        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'admin'
            ]);
        }

        // Create a Manager role with comprehensive permissions
        $managerRole = Role::firstOrCreate([
            'name' => 'Manager',
            'guard_name' => 'admin'
        ]);

        // Give Manager role all permissions except super admin access
        $managerPermissions = array_filter($permissions, function($permission) {
            return $permission !== 'super admin access';
        });

        $managerRole->syncPermissions($managerPermissions);

        // Ensure Super Admin role exists and has all permissions
        $superAdminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'admin'
        ]);

        $superAdminRole->syncPermissions($permissions);

        $this->command->info('Admin permissions and roles created successfully!');
    }
}
