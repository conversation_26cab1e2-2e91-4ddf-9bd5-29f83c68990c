<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64 Encoding Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 Base64 Encoding Fix Test</h1>
        <p class="text-muted">Testing the improved base64 decoding for video proxy URLs</p>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Base64 Decoding</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="testUrl" class="form-label">Test URL:</label>
                            <input type="text" class="form-control" id="testUrl" 
                                   placeholder="Enter a URL to encode and test">
                        </div>
                        
                        <div class="mb-3">
                            <label for="testBase64" class="form-label">Or Test Base64 String:</label>
                            <textarea class="form-control" id="testBase64" rows="3" 
                                      placeholder="Enter base64 encoded string to test decoding"></textarea>
                        </div>
                        
                        <button class="btn btn-primary" onclick="testEncoding()">Test Encoding</button>
                        <button class="btn btn-secondary" onclick="testDecoding()">Test Decoding</button>
                        <button class="btn btn-success" onclick="testProxyUrl()">Test Proxy URL</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Quick Tests</h6>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-sm btn-outline-primary mb-2 w-100" 
                                onclick="testSampleUrl()">Test Sample URL</button>
                        <button class="btn btn-sm btn-outline-secondary mb-2 w-100" 
                                onclick="testTemplateUrl()">Test Template URL</button>
                        <button class="btn btn-sm btn-outline-info mb-2 w-100" 
                                onclick="testSpecialChars()">Test Special Characters</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearResults()">Clear</button>
                    </div>
                    <div class="card-body">
                        <div id="results"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(type, message, data = null) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            
            let content = `<strong>[${timestamp}]</strong> ${message}`;
            if (data) {
                content += `<br><small><pre>${JSON.stringify(data, null, 2)}</pre></small>`;
            }
            
            div.innerHTML = content;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testEncoding() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                log('error', 'Please enter a URL to test');
                return;
            }

            try {
                // Test different encoding methods
                const standard = btoa(url);
                const urlSafe = btoa(url).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
                
                log('success', 'Encoding successful', {
                    original: url,
                    standard_base64: standard,
                    url_safe_base64: urlSafe,
                    standard_length: standard.length,
                    url_safe_length: urlSafe.length
                });

                // Test our debug endpoint
                testDebugEndpoint(standard);
                testDebugEndpoint(urlSafe);

            } catch (error) {
                log('error', 'Encoding failed: ' + error.message);
            }
        }

        function testDecoding() {
            const base64 = document.getElementById('testBase64').value.trim();
            if (!base64) {
                log('error', 'Please enter a base64 string to test');
                return;
            }

            try {
                // Test different decoding methods
                let decoded = null;
                let method = '';

                // Method 1: Direct decode
                try {
                    decoded = atob(base64);
                    method = 'Direct decode';
                } catch (e) {
                    // Method 2: URL-safe decode
                    try {
                        const fixed = base64.replace(/-/g, '+').replace(/_/g, '/');
                        const padding = fixed.length % 4;
                        const padded = padding ? fixed + '='.repeat(4 - padding) : fixed;
                        decoded = atob(padded);
                        method = 'URL-safe decode with padding';
                    } catch (e2) {
                        throw new Error('All decode methods failed');
                    }
                }

                log('success', `Decoding successful using: ${method}`, {
                    original_base64: base64,
                    decoded: decoded,
                    original_length: base64.length,
                    decoded_length: decoded.length
                });

                // Test our debug endpoint
                testDebugEndpoint(base64);

            } catch (error) {
                log('error', 'Decoding failed: ' + error.message);
            }
        }

        function testDebugEndpoint(base64) {
            fetch(`/debug/base64/${encodeURIComponent(base64)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        log('success', 'Server decode successful', data);
                    } else {
                        log('error', 'Server decode failed', data);
                    }
                })
                .catch(error => {
                    log('error', 'Server request failed: ' + error.message);
                });
        }

        function testProxyUrl() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                log('error', 'Please enter a URL to test proxy');
                return;
            }

            const encoded = btoa(url);
            const proxyUrl = `/video-proxy/segment/${encoded}`;
            
            log('info', 'Testing proxy URL', {
                original: url,
                encoded: encoded,
                proxy_url: proxyUrl
            });

            // Test the proxy URL
            fetch(proxyUrl, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        log('success', 'Proxy URL accessible');
                    } else {
                        log('error', `Proxy URL failed: ${response.status} ${response.statusText}`);
                    }
                })
                .catch(error => {
                    log('error', 'Proxy URL request failed: ' + error.message);
                });
        }

        function testSampleUrl() {
            const sampleUrl = 'https://ssc-news-live-enc.edgenextcdn.net/out/v1/ef466f43623c4bbaa3f905b566ec35ea/index_video_10_0_$Number$.mp4';
            document.getElementById('testUrl').value = sampleUrl;
            testEncoding();
        }

        function testTemplateUrl() {
            const templateUrl = 'index_video_10_0_$Number$.mp4';
            document.getElementById('testUrl').value = templateUrl;
            testEncoding();
        }

        function testSpecialChars() {
            const specialUrl = 'https://example.com/path/with spaces and $pecial chars!@#$%^&*()';
            document.getElementById('testUrl').value = specialUrl;
            testEncoding();
        }

        // Auto-run basic test on load
        window.onload = function() {
            log('info', 'Base64 Fix Test loaded. Ready for testing!');
        };
    </script>
</body>
</html>
