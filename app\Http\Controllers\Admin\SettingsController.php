<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingsController extends Controller
{
    /**
     * Display settings page
     */
    public function index()
    {
        // Initialize default settings if they don't exist
        $this->initializeDefaultSettings();

        $settings = Setting::getAllSettings();

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Initialize default settings if they don't exist
     */
    private function initializeDefaultSettings()
    {
        $defaults = [
            'site_name' => 'Shahid Play',
            'site_description' => 'Premium streaming platform',
            'contact_email' => '<EMAIL>',
            'maintenance_mode' => false,
            'maintenance_message' => 'We are currently performing scheduled maintenance. Please check back soon.',
            'allow_registration' => true,
            'max_users' => 1000,
            'session_timeout' => 120,
            'decryption_api_url' => 'http://127.0.0.1:5000',
            'enable_caching' => true,
            'cache_duration' => 3600,
            'max_retry_attempts' => 3,
        ];

        foreach ($defaults as $key => $value) {
            if (Setting::getValue($key) === null) {
                $type = is_bool($value) ? 'boolean' : (is_int($value) ? 'integer' : 'string');
                Setting::setValue($key, $value, $type);
            }
        }
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email',
            'maintenance_mode' => 'required|in:0,1',
            'maintenance_message' => 'nullable|string|max:1000',
            'allow_registration' => 'required|in:0,1',
            'max_users' => 'nullable|integer|min:1',
            'session_timeout' => 'required|integer|min:5|max:1440',
            'decryption_api_url' => 'required|url',
            'enable_caching' => 'required|in:0,1',
            'cache_duration' => 'required|integer|min:60|max:86400',
            'max_retry_attempts' => 'required|integer|min:1|max:10',
        ]);

        try {
            // General Settings
            Setting::setValue('site_name', $request->site_name, 'string', 'Site name');
            Setting::setValue('site_description', $request->site_description, 'string', 'Site description');
            Setting::setValue('contact_email', $request->contact_email, 'string', 'Contact email');
            
            // Maintenance Settings
            Setting::setValue('maintenance_mode', (bool)$request->maintenance_mode, 'boolean', 'Maintenance mode status');
            Setting::setValue('maintenance_message', $request->maintenance_message, 'string', 'Maintenance mode message');

            // User Settings
            Setting::setValue('allow_registration', (bool)$request->allow_registration, 'boolean', 'Allow user registration');
            Setting::setValue('max_users', $request->max_users, 'integer', 'Maximum number of users');
            Setting::setValue('session_timeout', $request->session_timeout, 'integer', 'Session timeout in minutes');

            // API Settings
            Setting::setValue('decryption_api_url', $request->decryption_api_url, 'string', 'Decryption API URL');
            Setting::setValue('enable_caching', (bool)$request->enable_caching, 'boolean', 'Enable API caching');
            Setting::setValue('cache_duration', $request->cache_duration, 'integer', 'Cache duration in seconds');
            Setting::setValue('max_retry_attempts', $request->max_retry_attempts, 'integer', 'Maximum retry attempts');

            // Clear cache
            Cache::forget('app_settings');

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Settings updated successfully!'
                ]);
            }

            return redirect()->back()->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating settings: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error updating settings: ' . $e->getMessage());
        }
    }

    /**
     * Reset settings to default
     */
    public function reset()
    {
        try {
            $this->setDefaultSettings();
            Cache::forget('app_settings');

            return redirect()->back()->with('success', 'Settings reset to default values!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error resetting settings: ' . $e->getMessage());
        }
    }

    /**
     * Set default settings
     */
    private function setDefaultSettings()
    {
        $defaults = [
            'site_name' => 'Shahid Play',
            'site_description' => 'Premium streaming platform',
            'contact_email' => '<EMAIL>',
            'maintenance_mode' => false,
            'maintenance_message' => 'We are currently performing scheduled maintenance. Please check back soon.',
            'allow_registration' => true,
            'max_users' => 1000,
            'session_timeout' => 120,
        ];

        foreach ($defaults as $key => $value) {
            $type = is_bool($value) ? 'boolean' : (is_int($value) ? 'integer' : 'string');
            Setting::setValue($key, $value, $type);
        }
    }

    /**
     * Get setting value (API endpoint)
     */
    public function getSetting($key)
    {
        $value = Setting::getValue($key);
        
        return response()->json([
            'success' => true,
            'key' => $key,
            'value' => $value
        ]);
    }

    /**
     * Toggle maintenance mode
     */
    public function toggleMaintenance(Request $request)
    {
        try {
            $currentMode = Setting::getValue('maintenance_mode', false);
            $newMode = !$currentMode;
            
            Setting::setValue('maintenance_mode', $newMode, 'boolean', 'Maintenance mode status');
            Cache::forget('app_settings');

            $message = $newMode ? 'Maintenance mode enabled' : 'Maintenance mode disabled';

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'maintenance_mode' => $newMode
                ]);
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error toggling maintenance mode: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error toggling maintenance mode: ' . $e->getMessage());
        }
    }

    /**
     * Test API connection
     */
    public function testApi(Request $request)
    {
        $request->validate([
            'api_url' => 'required|url'
        ]);

        try {
            $response = \Illuminate\Support\Facades\Http::timeout(10)->get($request->api_url . '/health');

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'message' => 'API connection successful! Status: ' . $response->status()
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'API returned status: ' . $response->status()
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ]);
        }
    }
}
