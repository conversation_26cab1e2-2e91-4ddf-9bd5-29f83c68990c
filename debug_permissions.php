<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Admin;

$admin = Admin::find(4);

if (!$admin) {
    echo "Admin not found\n";
    exit;
}

echo "Name: " . $admin->name . "\n";
echo "Email: " . $admin->email . "\n";
echo "is_super_admin: " . ($admin->is_super_admin ? 'true' : 'false') . "\n";
echo "is_super_admin (raw): " . (isset($admin->attributes['is_super_admin']) && $admin->attributes['is_super_admin'] ? 'true' : 'false') . "\n";
echo "Roles: " . $admin->roles->pluck('name')->join(', ') . "\n";

$permissions = ['view users', 'view admin panel', 'view dashboard', 'manage admins'];

echo "\nPermission checks:\n";
foreach ($permissions as $permission) {
    $hasPermission = $admin->hasPermission($permission);
    $status = $hasPermission ? '✅' : '❌';
    echo "  {$status} {$permission}\n";
}
