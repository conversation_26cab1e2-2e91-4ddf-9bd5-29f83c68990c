<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class MaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip maintenance check for admin routes
        if ($request->is('admin/*') || $request->is('admin') || $request->is('login') || $request->is('logout')) {
            return $next($request);
        }

        // Skip maintenance check for API routes (optional)
        if ($request->is('api/*')) {
            return $next($request);
        }

        // Skip maintenance check for authenticated admin users
        if (auth()->check() && auth()->user()->user_type === 'admin') {
            return $next($request);
        }

        // Check if maintenance mode is enabled
        $maintenanceMode = Cache::remember('maintenance_mode', 300, function () {
            return Setting::getValue('maintenance_mode', false);
        });

        if ($maintenanceMode) {
            // Get maintenance message
            $message = Cache::remember('maintenance_message', 300, function () {
                return Setting::getValue('maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.');
            });

            // Get contact email
            $contactEmail = Cache::remember('contact_email', 300, function () {
                return Setting::getValue('contact_email', '<EMAIL>');
            });

            // Return maintenance page
            return response()->view('maintenance', [
                'message' => $message,
                'contact_email' => $contactEmail
            ], 503);
        }

        return $next($request);
    }
}
