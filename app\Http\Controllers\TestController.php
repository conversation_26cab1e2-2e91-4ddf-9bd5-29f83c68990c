<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ShahidAPI;
use Illuminate\Support\Facades\Log;

class TestController extends Controller
{
    /**
     * Test: Get series seasons
     */
    public function getSeriesSeasons(Request $request)
    {
        $request->validate([
            'series_id' => 'required|string'
        ]);

        try {
            $seriesId = trim($request->series_id);

            Log::info("=== TEST: Getting seasons for series ===", ['series_id' => $seriesId]);

            // Use the new ShahidAPI service
            $shahidAPI = new ShahidAPI();
            $result = $shahidAPI->getSeriesSeasons($seriesId);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ]);
            }

            $seasons = $result['data'] ?? [];

            Log::info("=== TEST: Seasons result ===", [
                'seasons_count' => count($seasons),
                'first_season' => $seasons[0] ?? 'none'
            ]);

            return response()->json([
                'success' => true,
                'data' => $seasons,
                'count' => count($seasons),
                'message' => 'Seasons loaded successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("=== TEST: Error ===", ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Error getting series seasons: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test: Get season episodes
     */
    public function getSeasonEpisodes(Request $request)
    {
        $request->validate([
            'season_id' => 'required|string'
        ]);

        try {
            $seasonId = trim($request->season_id);

            Log::info("=== TEST: Getting episodes for season ===", ['season_id' => $seasonId]);

            // Use the new ShahidAPI service
            $shahidAPI = new ShahidAPI();
            $result = $shahidAPI->getSeasonEpisodes($seasonId);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ]);
            }

            $episodes = $result['data'] ?? [];

            Log::info("=== TEST: Episodes result ===", [
                'episodes_count' => count($episodes),
                'first_episode' => $episodes[0] ?? 'none',
                'last_episode' => $episodes[count($episodes) - 1] ?? 'none',
                'all_episodes_summary' => array_map(function($episode) {
                    return [
                        'id' => $episode['id'],
                        'episode_number' => $episode['episode_number'],
                        'stream_state' => $episode['stream_state'] ?? 'unknown',
                        'availability_status' => $episode['availability_status'] ?? 'unknown',
                        'availability_message' => $episode['availability_message'] ?? 'unknown'
                    ];
                }, $episodes)
            ]);

            return response()->json([
                'success' => true,
                'data' => $episodes,
                'count' => count($episodes),
                'message' => 'Episodes loaded successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("=== TEST: Error ===", ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Error getting season episodes: ' . $e->getMessage()
            ]);
        }
    }
}
