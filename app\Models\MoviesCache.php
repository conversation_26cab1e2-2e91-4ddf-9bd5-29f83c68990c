<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class MoviesCache extends Model
{
    use HasFactory;

    protected $table = 'movies_cache';

    protected $fillable = [
        'country',
        'movie_id',
        'title',
        'description',
        'poster_url',
        'metadata',
        'movie_data',
        'duration',
        'year',
        'is_complete',
        'last_updated'
    ];

    protected $casts = [
        'metadata' => 'array',
        'movie_data' => 'array',
        'is_complete' => 'boolean',
        'last_updated' => 'datetime',
        'duration' => 'integer',
        'year' => 'integer'
    ];

    /**
     * Cache a movie
     */
    public static function cacheMovie($country, $movieId, $data)
    {
        return self::updateOrCreate(
            [
                'country' => $country,
                'movie_id' => $movieId
            ],
            array_merge($data, [
                'last_updated' => now()
            ])
        );
    }

    /**
     * Get cached movie
     */
    public static function getCachedMovie($country, $movieId)
    {
        return self::where('country', $country)
                  ->where('movie_id', $movieId)
                  ->first();
    }

    /**
     * Get all cached movies for a country
     */
    public static function getCachedMovies($country)
    {
        return self::where('country', $country)
                  ->orderBy('title')
                  ->get();
    }

    /**
     * Check if cache needs refresh (older than 24 hours)
     */
    public function needsRefresh()
    {
        return $this->last_updated < Carbon::now()->subHours(24);
    }

    /**
     * Get cache age in hours
     */
    public function getCacheAgeAttribute()
    {
        return $this->last_updated->diffInHours(now());
    }

    /**
     * Scope for complete movies (with full data)
     */
    public function scopeComplete($query)
    {
        return $query->where('is_complete', true);
    }

    /**
     * Scope for incomplete movies (basic info only)
     */
    public function scopeIncomplete($query)
    {
        return $query->where('is_complete', false);
    }

    /**
     * Scope for fresh cache (less than 24 hours old)
     */
    public function scopeFresh($query)
    {
        return $query->where('last_updated', '>', Carbon::now()->subHours(24));
    }

    /**
     * Clear old cache entries
     */
    public static function clearOldCache($olderThanHours = 168) // 7 days default
    {
        return self::where('last_updated', '<', Carbon::now()->subHours($olderThanHours))
                  ->delete();
    }
}
